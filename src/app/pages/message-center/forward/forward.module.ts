import { SitesModule } from 'src/app/components/sites/sites.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { ForwardComponent } from './forward.component';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, SharedModule, TranslateModule, HeaderPlainModule, SearchBarRecipientsModule, SitesModule],
  declarations: [ForwardComponent],
  exports: [ForwardComponent]
})
export class ForwardComponentModule {}
