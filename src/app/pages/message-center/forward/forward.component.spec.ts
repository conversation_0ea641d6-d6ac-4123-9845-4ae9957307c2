import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { IonicModule, NavParams } from '@ionic/angular';
import { ForwardComponent } from './forward.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { of } from 'rxjs';
import { TestConstants } from 'src/app/constants/test-constants';
import { CommonService } from 'src/app/services/common-service/common.service';

describe('ForwardComponent', () => {
  let component: ForwardComponent;
  let fixture: ComponentFixture<ForwardComponent>;
  let sharedService;
  let common: CommonService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ForwardComponent],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        SharedService,
        NavParams,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    common = TestBed.inject(CommonService);
    fixture = TestBed.createComponent(ForwardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute fetchUsers()', () => {
    const response = [];
    component.sitesIds = [1];
    spyOn(sharedService, 'fetchUsers').and.returnValue(of(response));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute loadMore()', () => {
    component.loadMore();
    expect(component.loadMore).toBeTruthy();
    expect(component.loadMore).toBeDefined();
  });
  it('execute searchOperations()', () => {
    component.searchOperations({});
    spyOn(sharedService, 'trackActivity').and.callThrough();
    expect(component.loadMore).toBeTruthy();
    expect(component.loadMore).toBeDefined();
  });
  it('execute loadData', fakeAsync(() => {
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    component.loadData({ target: spy });
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(component.loadData).toBeTruthy();
    });
  }));
  it('execute filter data', () => {
    component.filterSitesData([]);
    expect(component.filterSitesData).toBeDefined();
  });
  it('execute cancel', () => {
    component.cancel();
    expect(component.cancel).toBeDefined();
  });
  it('execute chooseUser', () => {
    const user = {
      userid: 599,
      displayname: 'test'
    };
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.chooseUser(user);
    expect(component.chooseUser).toBeTruthy();
  });
  describe('initialSiteData', () => {
    it('execute initialSiteData with blank array', () => {
      component.initialSiteData([]);
      expect(component.initialSiteData).toBeTruthy();
    });
    it('execute initialSiteData with data', () => {
      const siteIds = [12, 124];
      component.initialSiteData(siteIds);
      expect(component.sitesIds).toBe(siteIds);
    });
  });
  it('execute ionViewWillEnter', () => {
    component.ionViewWillEnter();
    expect(component.ionViewWillEnter).toBeTruthy();
  });
  describe('getNavParamsData', () => {
    it('should set selectedMessageData if available in navParams', () => {
      const messageData = {};
      spyOn(component.navParams, 'get').and.returnValue(messageData);
      component.getNavParamsData();
      expect(component.selectedMessageData).toEqual(messageData);
    });
    it('should set sitesIds and isDisabledSite if available in navParams', () => {
      const siteParams = {
        siteId: 123,
        isDisabledSite: true
      };
      spyOn(component.navParams, 'get').and.returnValue(siteParams);
      component.getNavParamsData();
      expect(component.sitesIds).toEqual([siteParams.siteId]);
      expect(component.isDisabledSite).toEqual(siteParams.isDisabledSite);
    });
    it('should set sitesIds from local storage if not available in navParams', () => {
      const localSelectedSites = '[1, 2, 3]';
      spyOn(localStorage, 'getItem').and.returnValue(localSelectedSites);

      component.getNavParamsData();

      expect(component.sitesIds).toEqual(JSON.parse(localSelectedSites));
    });
  });
});
