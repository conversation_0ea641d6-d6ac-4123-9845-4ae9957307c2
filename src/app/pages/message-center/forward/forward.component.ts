
import { Component } from '@angular/core';
import { Constants } from 'src/app/constants/constants';
import { ModalController, NavParams } from '@ionic/angular';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Activity } from 'src/app/constants/activity';
import { isBlank, isPresent } from 'src/app/utils/utils';
import { getValueFromLocalStorage } from 'src/app/utils/storage-utils';

@Component({
  selector: 'app-forward',
  templateUrl: './forward.component.html',
  styleUrls: ['./forward.component.scss']
})
export class ForwardComponent {
  usersList = [];
  userTypes = Constants.userTypes;
  pageCount = 0;
  searchText = '';
  noData: boolean;
  sitesIds: any;
  selectedMessageData: any;
  isDisabledSite: boolean = false;
  errorMessage = this.commonService.getTranslateData('MESSAGES.NO_USERS_AVAILABLE');
  constructor(
    private readonly sharedService: SharedService,
    private readonly commonService: CommonService,
    private readonly modalController: ModalController,
    public navParams: NavParams
  ) {
    this.getNavParamsData();
  }

  ionViewWillEnter() {
    this.fetchUsers();
  }

  fetchUsers(): void {
    this.sharedService
      .fetchUsers({
        pageCount: this.pageCount,
        searchKeyword: this.searchText,
        siteIds: !isBlank(this.sitesIds) ? this.sitesIds.toString() : '0'
      })
      .subscribe((response) => {
        if (this.pageCount === 0) {
          this.usersList = [];
        }
        this.usersList = this.pageCount === 0 ? response : [...this.usersList, ...response];
        this.noData = !isBlank(response) ? false : true;
      });
  }

  cancel(): void {
    this.modalController.dismiss();
  }

  chooseUser(user: any): void {
    const customAlertMessage = this.commonService.getTranslateData('MESSAGES.FORWARDING_THIS_THREAD_MESSAGE_TO');
    const alertData = {
      message: `${customAlertMessage} ${user.displayname}`,
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.commonService.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        this.modalController.dismiss({
          toId: user.userId,
          fromUser: user.displayname
        });
      }
    });
  }

  loadMore(): void {
    this.pageCount++;
    this.fetchUsers();
  }

  searchOperations(searchAction: any): void {
    this.searchText = searchAction.value;
    this.pageCount = Constants.defaultPageCount;
    this.usersList = [];
    this.fetchUsers();
    this.sharedService.trackActivity({
      type: Activity.messaging,
      name: Activity.searchUser,
      des: {
        data: {
          displayName: this.sharedService.userData.displayName,
          keyword: this.searchText
        },
        desConstant: Activity.searchUserDes
      }
    });
  }

  loadData(event: any) {
    //TODO! CHP-3598
    setTimeout(() => {
      event.target.complete();
      this.loadMore();
    }, 1500);
  }

  filterSitesData(data: []): void {
    this.pageCount = 0;
    this.sitesIds = data;
    this.fetchUsers();
  }

  initialSiteData(data: any): void {
    if (!this.isDisabledSite) {
      this.sitesIds = data;
    }
  }

  getNavParamsData(): void {
    if (this.navParams.get('selectedMessageData')) {
      this.selectedMessageData = this.navParams.get('selectedMessageData');
    }
    if (this.navParams.get('passSiteParams')) {
      const siteParams = this.navParams.get('passSiteParams');
      this.sitesIds = Number(siteParams?.siteId) === 0 ? [] : [siteParams?.siteId];
      this.isDisabledSite = siteParams?.isDisabledSite;
    }
    const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    if (isBlank(this.sitesIds) && isPresent(localSelectedSites)) {
      this.sitesIds = JSON.parse(localSelectedSites);
    }
  }
}
