<app-header-plain headerTitle="TITLES.FORWARD" (close)="cancel()"></app-header-plain>
<div>
    <app-sites (filterSites)="filterSitesData($event)" [singleSiteSelection]="false" [selectedSiteIds]="sitesIds"
        [disableFilter]="isDisabledSite" (onLoadSites)="initialSiteData($event)">
    </app-sites>
</div>
<app-search-bar-recipients (searchAction)="searchOperations($event)"></app-search-bar-recipients>
<ion-content>
    <div class="common-list">
        <ion-item tappable lines="none" *ngFor="let user of usersList" (click)="chooseUser(user)" id="choose-user">
            <span class="common-capitalize-text">{{user.displayname}}</span>
            <span class="common-na-space common-capitalize-text" *ngIf="user.naTags && user.naTags !== ''">
                &nbsp;({{user.naTagNames}})</span>
            [{{user.role}}]
        </ion-item>
        <div class="common-no-items" *ngIf="noData">{{errorMessage}}</div>
    </div>
    <!-- TODO! CHP-3598-->
    <ion-infinite-scroll *ngIf="usersList && usersList.length>0" threshold="100px" (ionInfinite)="loadData($event)">
        <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{'BUTTONS.LOAD_MORE' | translate}}">
        </ion-infinite-scroll-content>
    </ion-infinite-scroll>
</ion-content>