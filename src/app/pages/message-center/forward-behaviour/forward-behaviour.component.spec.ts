import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { ForwardBehaviourComponent } from './forward-behaviour.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('ForwardBehaviourComponent', () => {
  let component: ForwardBehaviourComponent;
  let fixture: ComponentFixture<ForwardBehaviourComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ForwardBehaviourComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot()],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(ForwardBehaviourComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call cancel', () => {
    component.cancel();
    expect(component.cancel).toBeDefined();
  });

  it('should call closeAction', () => {
    component.closeAction('Remove me from chat session');
    expect(component.closeAction).toBeDefined();
  });
});
