import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { ForwardBehaviourComponent } from './forward-behaviour.component';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        SharedModule,
        TranslateModule,
        HeaderPlainModule
    ],
    declarations: [ForwardBehaviourComponent],
    exports: [ForwardBehaviourComponent]
})
export class ForwardBehaviourComponentModule { }
