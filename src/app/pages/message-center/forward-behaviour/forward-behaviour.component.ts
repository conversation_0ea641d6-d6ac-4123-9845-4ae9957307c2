import { Component, OnInit } from '@angular/core';
import { Constants } from 'src/app/constants/constants';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-forward-behaviour',
  templateUrl: './forward-behaviour.component.html',
  styleUrls: ['./forward-behaviour.component.scss']
})
export class ForwardBehaviourComponent {
  userTypes = Constants.userTypes;
  options = Constants.messageForwardingBehaviourOptions;
  constructor(private readonly modalController: ModalController) {}

  cancel(): void {
    this.modalController.dismiss();
  }

  closeAction(forwardBehaviour: string): void {
    this.modalController.dismiss({ forwardBehaviour });
  }
}
