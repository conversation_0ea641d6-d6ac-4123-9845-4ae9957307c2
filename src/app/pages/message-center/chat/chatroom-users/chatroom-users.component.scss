.avatar-img {
  border-radius: 7%;
  width: 100px;
  height: 100px;
  object-fit: cover;
  margin: 6px 0px;
  background: #e6e5eb;
}
ion-avatar {
  background: #e6e5eb;
}
.removed, .removed:hover {
  color: grey;
}
.restore-role {
  transform: rotateY(180deg);
}
.user-label {
  font-weight: 700;
}
.users-padding {
  padding: 5px;
}
.info-text-container {
  ion-text {
    font-size: 14px;
    padding-left: 9px;
    padding-top: 9px;
    padding-right: 9px;
    ion-icon {
      position: absolute;
    }
    span {
      margin-left: 1rem;
      line-height: 20px;
    }
  }
}