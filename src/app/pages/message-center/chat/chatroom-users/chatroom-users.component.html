<app-header-plain headerTitle="LABELS.PARTICIPANTS_ROLES" (close)="goBack()" type="rightCloseIcon"></app-header-plain>
<ion-content>
  <ion-list>
    <ion-accordion-group expand="inset" mode="md" [value]="[participantType.USER, participantType.ROLE]" [multiple]="true">
      @if (chatParticipants?.length > 0) {
        <ion-accordion [value]="participantType.USER">
          <ion-item slot="header" color="light">
            <ion-label translate>LABELS.CHATROOM_PARTICIPANTS</ion-label>
            <ion-icon
              class="icon-margin-right"
              name="information-circle-outline"
              id="info-chat-participant"
              (click)="showInfoToast('MESSAGES.USER_PARTICIPANTS_INFO', $event)"
            ></ion-icon>
          </ion-item>
          <div slot="content">
            <div class="users-padding">
              <ng-container *ngTemplateOutlet="userListTemplate; context: { $implicit: chatParticipants, type: participantType.USER }"></ng-container>
            </div>
          </div>
        </ion-accordion>
      }
      @if (roleParticipants?.length > 0) {
        <ion-accordion [value]="participantType.ROLE">
          <ion-item slot="header" color="light">
            <ion-label translate>LABELS.CHATROOM_ROLES</ion-label>
            <ion-icon
              class="icon-margin-right"
              name="information-circle-outline"
              id="info-chat-participant"
              (click)="showInfoToast('MESSAGES.ROLE_PARTICIPANTS_INFO', $event)"
            ></ion-icon>
          </ion-item>
          <div slot="content">
            <ion-accordion-group expand="inset" mode="md" #accordionGroupRoles [multiple]="true">
              @for (role of roleParticipants; track role.id) {
                <ion-accordion value="{{ participantType.ROLE }}-{{ role.id }}" toggleIconSlot="start" id="{{ participantType.ROLE }}-{{ role.id }}">
                  <ion-item slot="header">
                    <ion-label>{{ role.name }}</ion-label>
                    @if (isManageParticipantEnabled) {
                      @if (role?.deletedUsersCount > 0) {
                        <ion-icon
                          class="icon-margin-right"
                          name="information-circle-outline"
                          id="info-restore-role-{{ role.id }}"
                          (click)="clickRole($event, role)"
                        ></ion-icon>
                        <ion-icon
                          class="icon-margin-right"
                          class="restore-role"
                          name="refresh-outline"
                          id="restore-role-{{ role.id }}"
                          (click)="clickRole($event, role)"
                        ></ion-icon>
                      }
                      <ion-icon
                        class="icon-margin-right"
                        name="close-outline"
                        slot="end"
                        id="remove-role-{{ role.id }}"
                        (click)="clickRole($event, role)"
                      ></ion-icon>
                    }
                  </ion-item>
                  <div class="ion-padding" slot="content">
                    <ng-container
                      *ngTemplateOutlet="userListTemplate; context: { $implicit: users, type: participantType.ROLE, role: role }"
                    ></ng-container>
                  </div>
                </ion-accordion>
              }
            </ion-accordion-group>
          </div>
        </ion-accordion>
      }
    </ion-accordion-group>
  </ion-list>
</ion-content>
<ng-template #userListTemplate let-users let-type="type" let-role="role">
  @for (user of users; track user.userId) {
    @if (type === participantType.USER || (type === participantType.ROLE && user?.assignedRoles?.includes(+role?.id))) {
      <ion-chip
        (click)="clickUser($event, user)"
        id="{{ participantType.USER }}-{{ user.userId }}"
        [ngClass]="{ removed: type === participantType.ROLE && +user.deleted }"
      >
      <div [ngClass]="{'circle-badge-big': expandedUserId === +user.userId}">
        <img alt="avatar" [src]="user.avatar" [hidden]="expandedUserId !== +user.userId" class="avatar-img" 
         (error)="handleImageError($event)" />
        <ion-avatar [hidden]="expandedUserId === +user.userId" outOfOfficeStatus [oooInfo]="user.oooInfo" 
        [customClass]="'chatroom-circle-badge'"
       >
          <img alt="avatar" [src]="user.avatar" (error)="handleImageError($event)" />
        </ion-avatar>
      </div>
        <ion-grid>
          <ion-row>
            <ion-col class="user-label">
              @if (!user.caregiver_userid) {
                {{ user.displayName }}
              } @else {
                {{ user.caregiver_displayname }} ({{ user.displayName }})
              }
              @if (+user.userId === +sharedService.userData.userId) {
                {{ meLabel }}
              }
              @if (!user.passwordStatus) {
                {{ virtualLabel }}
              }
              @if (user.tenantid !== sharedService.userData.tenantId) {
                [{{ user.tenantName }}]
              }
            </ion-col>
          </ion-row>
          @if (type === participantType.ROLE && +user.deleted) {
            <ion-row>
              <ion-col class="removed-text">({{ 'MESSAGES.NO_LONGER_IN_CHAT' | translate }})</ion-col>
            </ion-row>
          }
          @if (expandedUserId === +user.userId) {
            @if (user?.relation && user?.roleName === constants.alternateContact) {
              <ion-row>
                <ion-col><ion-icon class="icon-margin-right" name="people-outline" color="dark"></ion-icon>{{ user.relation }}</ion-col>
              </ion-row>
            }
            @if (user.mobile) {
              <ion-row>
                <ion-col
                  ><ion-icon class="icon-margin-right" name="call-outline" color="dark"></ion-icon><span [innerHTML]="user.mobile | autolinker"></span
                ></ion-col>
              </ion-row>
            }
            @if (user.dob) {
              <ion-row>
                <ion-col>
                  <ion-icon class="icon-margin-right" name="calendar-outline" color="dark"></ion-icon>
                  {{ (user.caregiver_dob ? user.caregiver_dob : user.dob ? user.dob : '') | date: constants.dateFormat.mmddyyyy }}
                </ion-col>
              </ion-row>
            }
            @if ((user.caregiver_identityvalue && user.caregiver_userid) || user.IdentityValue) {
              <ion-row>
                <ion-col>
                  <ion-icon class="icon-margin-right" name="id-card-outline" color="dark"></ion-icon>
                  {{ user.caregiver_identityvalue ? user.caregiver_identityvalue : user.IdentityValue }}
                </ion-col>
              </ion-row>
            }
          }
        </ion-grid>
        @if (type === participantType.USER && isPatient && (+user.userId === +sharedService.userData.userId || isManageParticipantEnabled)) {
          <ion-icon class="icon-margin-right" name="close-outline" id="remove-user-{{ user.userId }}"></ion-icon>
        }
        @if (type === participantType.ROLE && +user.deleted && isManageParticipantEnabled) {
          <ion-icon
            class="icon-margin-right"
            name="information-circle-outline"
            id="info-restore-user-{{ role.id }}"
            (click)="clickRole($event, role)"
          ></ion-icon>
          <ion-icon class="icon-margin-right" name="refresh-outline" id="restore-user-{{ user.userId }}" class="restore-role"></ion-icon>
        }
      </ion-chip>
    }
  } @empty {
    <ion-item>
      <ion-label translate>MESSAGES.NO_USERS_AVAILABLE</ion-label>
    </ion-item>
  }
</ng-template>
