import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, USE_PERMISSIONS_STORE, NgxPermissionsStore } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';
import { ChatRoomUsersComponent } from 'src/app/pages/message-center/chat/chatroom-users/chatroom-users.component';
import { Keepalive } from '@ng-idle/keepalive';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of } from 'rxjs';
import { TestConstants } from 'src/app/constants/test-constants';

describe('ChatRoomUsersComponent', () => {
  let component: ChatRoomUsersComponent;
  let fixture: ComponentFixture<ChatRoomUsersComponent>;
  let httpService: HttpService;
  let sharedService: SharedService;
  let commonService: CommonService;
  let modalController: ModalController;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ChatRoomUsersComponent],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        NavParams,
        CommonService,
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(ChatRoomUsersComponent);
    component = fixture.componentInstance;
    sharedService = TestBed.inject(SharedService);
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'dismiss').and.stub();
    Object.defineProperty(sharedService, 'userData', { value: TestConstants.userData });
    httpService = TestBed.inject(HttpService);
    commonService = TestBed.inject(CommonService);
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    component.users = [
      { userId: '1', displayName: 'User 1', assignedRoles: [1, 2], deleted: '1' },
      { userId: '2', displayName: 'User 2', assignedRoles: [1], deleted: '0' },
      { userId: '3', displayName: 'User 3', assignedRoles: [1, 2], deleted: '1' },
      { userId: '4', displayName: 'User 4', assignedRoles: [1, 2], deleted: '1' }
    ] as any;
    component.chatParticipants = [
      { userId: '1', displayName: 'User 1', assignedRoles: [1, 2], deleted: '1' },
      { userId: '3', displayName: 'User 3', assignedRoles: [1, 2], deleted: '1' }
    ] as any;
    component.roomId = 123;
    component.roleParticipants = [
      { id: '1', name: 'Role 1', deletedUsersCount: 2 },
      { id: '2', name: 'Role 2', deletedUsersCount: 0 }
    ];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  describe('restoreRoleUsers', () => {
    it('should restore all users to the chat room when roleParticipant is provided', fakeAsync(() => {
      const roleParticipant = { id: '1', name: 'Role 1' };
      const expectedResponse = {
        success: true,
        data: {}
      };
      const postSpy = spyOn(httpService, 'doPost').and.returnValue(of(expectedResponse));
      spyOn(commonService, 'showToast');
      component.restoreRoleUsers({ roleParticipant });
      tick(1000);
      expect(component.restoreRoleUsers).toBeTruthy();
      expect(postSpy).toHaveBeenCalled();
    }));
    it('Fail to restore all users to the chat room when roleParticipant is provided', fakeAsync(() => {
      const roleParticipant = { id: '1', name: 'Role 1' };
      const expectedResponse = {
        success: false,
        data: {
          errors: [{ message: 'Error' }]
        }
      };
      const postSpy = spyOn(httpService, 'doPost').and.returnValue(of(expectedResponse));
      spyOn(commonService, 'showToast');
      component.restoreRoleUsers({ roleParticipant });
      tick(1000);
      expect(component.restoreRoleUsers).toBeTruthy();
      expect(postSpy).toHaveBeenCalled();
    }));
    it('should restore user to the chat room when userParticipant is provided', fakeAsync(() => {
      const userParticipant = { userId: '1', displayName: 'Role 1', assignedRoles: [1, 2], deleted: '1' };
      const expectedResponse = {
        success: true,
        data: {}
      };
      const postSpy = spyOn(httpService, 'doPost').and.returnValue(of(expectedResponse));
      spyOn(commonService, 'showToast');
      component.restoreRoleUsers({ userParticipant: userParticipant as any });
      expect(component.restoreRoleUsers).toBeTruthy();
      tick(1000);
      expect(postSpy).toHaveBeenCalled();
    }));
    it('Fail to restore user to the chat room when userParticipant is provided', fakeAsync(() => {
      const userParticipant = { userId: '1', displayName: 'Role 1', assignedRoles: [1, 2], deleted: '1' };
      const expectedResponse = {
        success: false,
        data: {
          errors: [{ message: 'Error' }]
        }
      };
      const postSpy = spyOn(httpService, 'doPost').and.returnValue(of(expectedResponse));
      spyOn(commonService, 'showToast');
      component.restoreRoleUsers({ userParticipant: userParticipant as any });
      tick(1000);
      expect(component.restoreRoleUsers).toBeTruthy();
      expect(postSpy).toHaveBeenCalled();
    }));
  });
  describe('removeParticipant', () => {
    it('should remove user from chat room when userParticipant is provided', fakeAsync(() => {
      const userParticipant = { userId: '1', displayName: 'User 1', assignedRoles: [1, 2], deleted: '1' };
      const expectedResponse = {
        status: true,
        data: {}
      };
      spyOn(httpService, 'doPost').and.returnValue(of(expectedResponse));
      spyOn(commonService, 'showToast');
      component.removeParticipant(userParticipant as any);
      expect(component.removeParticipant).toBeTruthy();
    }));
    it('Fail to remove user from chat room when userParticipant is provided', fakeAsync(() => {
      const userParticipant = { userId: '1', displayName: 'User 1', assignedRoles: [1, 2], deleted: '1' };
      const expectedResponse = {
        status: false,
        data: {
          errors: [{ message: 'Error' }]
        }
      };
      spyOn(httpService, 'doPost').and.returnValue(of(expectedResponse));
      spyOn(commonService, 'showToast');
      component.removeParticipant(userParticipant as any);
      expect(component.removeParticipant).toBeTruthy();
    }));
  });
  describe('removeParticipant', () => {
    it('should remove user from chat room when roleParticipant is provided', fakeAsync(() => {
      const roleParticipant = { id: '1', name: 'Role 1' };
      const expectedResponse = {
        success: true,
        data: {}
      };
      spyOn(httpService, 'doDelete').and.returnValue(of(expectedResponse));
      spyOn(commonService, 'showToast');
      component.removeRole(roleParticipant as any);
      expect(component.removeRole).toBeTruthy();
    }));
    it('Fail to remove user from chat room when roleParticipant is provided', fakeAsync(() => {
      const roleParticipant = { id: '1', name: 'Role 1' };
      const expectedResponse = {
        success: false,
        data: {
          errors: [{ message: 'Error' }]
        }
      };
      spyOn(httpService, 'doDelete').and.returnValue(of(expectedResponse));
      spyOn(commonService, 'showToast');
      component.removeRole(roleParticipant as any);
      expect(component.removeRole).toBeTruthy();
    }));
  });
  describe('clickRole', () => {
    const role = { id: '1', name: 'Role 1' } as any;
    it('should call removeRole method when remove button is clicked', () => {
      const removeButtonId = `remove-role-${role.id}`;
      const event = { stopPropagation: jasmine.createSpy('stopPropagation'), target: { id: removeButtonId } };
      spyOn(component, 'removeRole');
      component.clickRole(event, role);
      expect(event.stopPropagation).toHaveBeenCalled();
      expect(component.removeRole).toHaveBeenCalledWith(role);
    });
    it('should call restoreRoleUsers method when restore button is clicked', () => {
      const restoreButtonId = `restore-role-${role.id}`;
      const event = { stopPropagation: jasmine.createSpy('stopPropagation'), target: { id: restoreButtonId } };
      spyOn(component, 'restoreRoleUsers');
      component.clickRole(event, role);
      expect(event.stopPropagation).toHaveBeenCalled();
      expect(component.restoreRoleUsers).toHaveBeenCalledWith({ roleParticipant: role });
    });
    it('should call showInfoToast method when info button for restore all users is clicked', () => {
      const infoButtonId = `info-restore-role-${role.id}`;
      const event = { stopPropagation: jasmine.createSpy('stopPropagation'), target: { id: infoButtonId } };
      spyOn(component, 'showInfoToast');
      component.clickRole(event, role);
      expect(event.stopPropagation).toHaveBeenCalled();
    });
    it('should call showInfoToast method when info button for restore user is clicked', () => {
      const infoButtonId = `info-restore-user-${role.id}`;
      const event = { stopPropagation: jasmine.createSpy('stopPropagation'), target: { id: infoButtonId } };
      spyOn(component, 'showInfoToast');
      component.clickRole(event, role);
      expect(event.stopPropagation).toHaveBeenCalled();
    });
    it('should not call removeRole or restoreRoleUsers or showInfoToast method when other element is clicked', () => {
      const event = { stopPropagation: jasmine.createSpy('stopPropagation'), target: { id: 'other-element' } };
      spyOn(component, 'removeRole');
      spyOn(component, 'restoreRoleUsers');
      spyOn(component, 'showInfoToast');
      component.clickRole(event, role);
      expect(component.removeRole).not.toHaveBeenCalled();
      expect(component.restoreRoleUsers).not.toHaveBeenCalled();
      expect(component.showInfoToast).not.toHaveBeenCalled();
    });
  });
  describe('showInfoToast', () => {
    it('should show a toast message with the provided message', () => {
      const message = 'This is an info message';
      spyOn(commonService, 'showToast');
      component.showInfoToast(message);
      expect(commonService.showToast).toHaveBeenCalledWith({
        message,
        color: 'dark'
      });
    });
    it('should stop event propagation if an event is provided', () => {
      const message = 'This is an info message';
      const event = {
        stopPropagation: jasmine.createSpy('stopPropagation')
      };
      spyOn(commonService, 'showToast');
      component.showInfoToast(message, event);
      expect(event.stopPropagation).toHaveBeenCalled();
    });
  });
  describe('showDeleteOption', () => {
    it('should return true if the userId is not the same as the logged-in user or if manageChatRoomParticipants permission is enabled and the logged-in user is not a patient', () => {
      sharedService.userData.group = '2';
      sharedService.userData.privileges = 'manageChatRoomParticipants';
      sharedService.userData.userId = '123';
      const result1 = component.showDeleteOption('456');
      expect(result1).toBe(true);
    });

    it('should return true if the userId is the same as the logged-in user and manageChatRoomParticipants permission is disabled and not a patient', () => {
      sharedService.userData.group = '2';
      sharedService.userData.privileges = '123';
      const result = component.showDeleteOption('123');
      expect(result).toBe(true);
    });

    it('should return false if the logged-in user is a patient', () => {
      sharedService.userData.group = '3';
      const result = component.showDeleteOption('456');
      expect(result).toBe(false);
    });
  });
  describe('clickUser', () => {
    let user;
    beforeEach(() => {
      user = { userId: '1', displayName: 'User 1', assignedRoles: [1, 2], deleted: '1' } as any;
      sharedService.userData.group = '2';
    });
    it('should call removeParticipant method when remove button is clicked', () => {
      const event = { target: { id: `remove-user-${user.userId}` } };
      spyOn(component, 'removeParticipant');
      component.clickUser(event, user);
      expect(component.removeParticipant).toHaveBeenCalledWith(user);
    });
    it('should call restoreRoleUsers method when restore button is clicked', () => {
      const event = { target: { id: `restore-user-${user.userId}` } };
      spyOn(component, 'restoreRoleUsers');
      component.clickUser(event, user);
      expect(component.restoreRoleUsers).toHaveBeenCalledWith({ userParticipant: user });
    });
    it('should toggle expandedUserId when other element is clicked', () => {
      const event = { target: { id: 'other-element' } };
      component.expandedUserId = null;
      component.clickUser(event, user);
      expect(component.expandedUserId).toBe(+user.userId);
      component.expandedUserId = +user.userId;
      component.clickUser(event, user);
      expect(component.expandedUserId).toBeNull();
    });
  });
  describe('goBack', () => {
    it('should dismiss the modal with the correct data and action', fakeAsync(() => {
      // Arrange
      component.users = [{ userId: '1', displayName: 'User 1' }] as any;
      component.roleParticipants = [{ id: '1', name: 'Role 1' }] as any;
      component.chatParticipants = [{ userId: '2', displayName: 'User 2' }] as any;
      component.roomId = 123;
      component.goBack();
      expect(component.goBack).toBeTruthy();
    }));
  });
  describe('ngOnInit', () => {
    it('should initialize component properties and subscribe to chatUsersListUpdated', () => {
      const users = [{ userId: '1', displayName: 'User 1' }];
      const roleParticipants = [{ id: '1', name: 'Role 1' }];
      const chatParticipants = [{ userId: '2', displayName: 'User 2' }];
      spyOn(sharedService, 'loggedUserIsPatient').and.returnValue(false);
      sharedService.userPermissions = 'manageChatRoomParticipants';
      component.ngOnInit();
      sharedService.chatUsersListUpdated.next({ users, roleParticipants, chatParticipants });
      expect(component.users).toEqual(users as any);
      expect(component.roleParticipants).toEqual(roleParticipants);
      expect(component.chatParticipants).toEqual(chatParticipants as any);
      expect(component.meLabel).toBeDefined();
      expect(component.virtualLabel).toBeDefined();
      expect(component.isPatient).toBeTrue();
      expect(component.isManageParticipantEnabled).toBeTrue();
      expect(sharedService.loggedUserIsPatient).toHaveBeenCalled();
    });
  });
});
