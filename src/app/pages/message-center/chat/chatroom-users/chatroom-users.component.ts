import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { IonAccordionGroup, ModalController } from '@ionic/angular';
import { HttpService } from 'src/app/services/http-service/http.service';
import { APIs } from 'src/app/constants/apis';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Activity } from 'src/app/constants/activity';
import { Constants, ParticipantType, UserGroup } from 'src/app/constants/constants';
import { Urls } from 'src/app/constants/urls';
import { ChatRoomParticipant, RoleParticipant } from 'src/app/interfaces/messages';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Permissions } from 'src/app/constants/permissions';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-chatroom-users',
  templateUrl: './chatroom-users.component.html',
  styleUrls: ['./chatroom-users.component.scss']
})
export class ChatRoomUsersComponent implements OnInit {
  @ViewChild(IonAccordionGroup) accordionGroupRoles: IonAccordionGroup;
  expandedRole: number;
  @Input() users: ChatRoomParticipant[] = [];
  @Input() roleParticipants: RoleParticipant[] = [];
  @Input() chatParticipants: ChatRoomParticipant[] = [];
  @Input() roomId: number;
  meLabel: string;
  virtualLabel: string;
  isPatient = false;
  constants = Constants;
  expandedUserId: number;
  participantType = ParticipantType;
  chatUsersListUpdatedSubscription: Subscription;
  constructor(
    private readonly httpService: HttpService,
    public sharedService: SharedService,
    private permissionService: PermissionService,
    private readonly modalController: ModalController,
    private readonly commonService: CommonService
  ) {}
  ngOnInit(): void {
    this.meLabel = `(${this.commonService.getTranslateData('LABELS.ME')})`;
    this.virtualLabel = `(${this.commonService.getTranslateData('LABELS.VIRTUAL')})`;
    this.isPatient = !this.sharedService.loggedUserIsPatient();
    this.chatUsersListUpdatedSubscription = this.sharedService.chatUsersListUpdated.subscribe((data: any) => {
      const { users, roleParticipants, chatParticipants } = data;
      if (users && roleParticipants && chatParticipants) {
        this.users = users;
        this.roleParticipants = roleParticipants;
        this.chatParticipants = chatParticipants;
      }
    });
  }
  get isManageParticipantEnabled(): boolean {
    return this.permissionService.userHasPermission(Permissions.manageChatRoomParticipants);
  }
  showDeleteOption(userId): boolean {
    return (+userId !== +this.sharedService.userData.userId || this.isManageParticipantEnabled) && !this.sharedService.loggedUserIsPatient();
  }
  /**
   * Handles the click event for a role.
   * If the event target's id matches the remove button for the role, it calls the `removeRole` method.
   *
   * @param event - The click event object.
   * @param role - The role object containing the `id` property.
   */
  clickRole(event: any, role: RoleParticipant) {
    event.stopPropagation();
    if (event?.target?.id === `remove-role-${role.id}`) {
      this.removeRole(role);
    } else if (event?.target?.id === `restore-role-${role.id}`) {
      this.restoreRoleUsers({ roleParticipant: role });
    } else if (event?.target?.id === `info-restore-role-${role.id}`) {
      this.showInfoToast('MESSAGES.RESTORE_ROLE_INFO');
    } else if (event?.target?.id === `info-restore-user-${role.id}`) {
      this.showInfoToast('MESSAGES.RESTORE_USER_INFO');
    }
  }
  showInfoToast(message: string, event?) {
    event?.stopPropagation();
    this.commonService.showToast({
      message,
      color: 'dark'
    });
  }
  /**
   * Handles the click event on a user in the popover.
   * If the event target has an id of `remove-user-{userId}`, it calls the `removeParticipant` method with the user.
   * Otherwise, it toggles the `expandedUserId` property between the current user's id and `null`.
   *
   * @param event - The click event object.
   * @param user - The user object associated with the clicked element.
   */
  clickUser(event: any, user: ChatRoomParticipant) {
    if (event?.target?.id === `remove-user-${user.userId}`) {
      this.removeParticipant(user);
    } else if (event?.target?.id === `restore-user-${user.userId}`) {
      this.restoreRoleUsers({ userParticipant: user });
    } else if (+this.sharedService.userData.group !== UserGroup.PATIENT) {
      this.expandedUserId = this.expandedUserId === +user.userId ? null : +user.userId;
    }
  }
  /**
   * Handles the error event when an image fails to load.
   * Sets the source of the event target to a default image URL.
   *
   * @param event - The error event object.
   */
  handleImageError(event: any) {
    const { target } = event;
    target.src = Urls.noImage;
  }
  /**
   * Navigates back to the previous page and dismisses the popover.
   *
   * @remarks
   * This method is called when the user clicks the "Go Back" button.
   * It dismisses the popover and passes the necessary data back to the previous page.
   *
   * @returns void
   */
  goBack() {
    this.chatUsersListUpdatedSubscription.unsubscribe();
    this.modalController.dismiss(
      { allParticipants: this.users, roleParticipants: this.roleParticipants, chatParticipants: this.chatParticipants },
      'close',
      'chatroom-users'
    );
  }
  /**
   * Restores participant/s to the chat room.
   *
   * @param roleParticipant - The role id of which all users to be restored.
   * @param userParticipant - The user participant to be restored.
   */
  restoreRoleUsers({ roleParticipant, userParticipant }: { roleParticipant?: RoleParticipant; userParticipant?: ChatRoomParticipant }): void {
    let confirmMessage = '';
    if (roleParticipant) {
      confirmMessage = this.commonService.getTranslateDataWithParam('MESSAGES.RESTORE_ALL_USERS_TO_ROOM', { roleName: roleParticipant.name });
    } else {
      confirmMessage = this.commonService.getTranslateDataWithParam('MESSAGES.RESTORE_USER_TO_ROOM', { userName: userParticipant.displayName });
    }
    this.commonService
      .showAlert({
        message: confirmMessage,
        header: 'MESSAGES.ARE_YOU_SURE'
      })
      .then((confirmation: boolean) => {
        if (confirmation) {
          this.httpService
            .doPost({
              endpoint: APIs.manageChatParticipants,
              version: Constants.apiVersions.apiV5,
              payload: {
                data: {
                  type: roleParticipant ? 'role' : 'roleUser',
                  id: +roleParticipant?.id || +userParticipant?.userId,
                  chatRoomId: this.roomId
                },
                action: 'restore'
              }
            })
            .subscribe(({ success }) => {
              if (success) {
                let message = '';
                const users = [];
                if (roleParticipant) {
                  this.users?.forEach((user, index) => {
                    if (user?.assignedRoles?.includes(+roleParticipant.id) && user.deleted === Constants.configTrue) {
                      users.push(user.displayName);
                      this.users[index].deleted = Constants.configFalse;
                    }
                  });
                  this.roleParticipants.find((role) => role.id === roleParticipant.id).deletedUsersCount = 0;
                  message = this.commonService.getTranslateDataWithParam('MESSAGES.CHATROOM_RESTORE_USERS_SUCCESS', {
                    users: users.join(', ')
                  });
                } else {
                  this.users.find((user) => user.userId === userParticipant.userId).deleted = Constants.configFalse;
                  userParticipant.assignedRoles.forEach((roleId) => {
                    const roleData = this.roleParticipants.find((role) => +role.id === +roleId);
                    if (roleData) {
                      roleData.deletedUsersCount = roleData.deletedUsersCount > 0 ? roleData.deletedUsersCount - 1 : 0;
                    }
                  });
                  users.push(userParticipant.displayName);
                }
                this.sharedService.chatUsersListUpdated.next({ refetch: true });
                message = this.commonService.getTranslateDataWithParam('MESSAGES.CHATROOM_RESTORE_USERS_SUCCESS', {
                  users: users.join(', ')
                });
                this.commonService.showToast({ message, color: 'success' });
              } else {
                this.commonService.showToast({ message: 'MESSAGES.CHATROOM_RESTORE_USERS_FAILURE', color: 'danger' });
              }
            });
        }
      });
  }
  /**
   * Removes a role from the chat room.
   * @param role - The role to be removed.
   */
  removeRole(role: RoleParticipant): void {
    const confirmMessage = this.commonService.getTranslateDataWithParam('MESSAGES.YOU_WANT_TO_REMOVE_ROLE', {
      name: role.name
    });
    this.commonService
      .showAlert({
        header: confirmMessage,
        message: ''
      })
      .then((confirmation: boolean) => {
        if (confirmation) {
          const doubleConfirmMessage = this.commonService.getTranslateData('MESSAGES.REMOVE_ROLE_CONFIRM');
          this.commonService
            .showAlert({ message: doubleConfirmMessage, header: 'MESSAGES.ARE_YOU_SURE', cssClass: 'common-alert alert-danger-custom' })
            .then((doubleConfirmation: boolean) => {
              if (doubleConfirmation) {
                const payload = {
                  chatRoomId: +this.roomId,
                  roleId: +role?.id
                };
                this.httpService
                  .doDelete({ endpoint: APIs.removeRoleParticipantFromChatRoom, payload, version: Constants.apiVersions.apiV5 })
                  .subscribe(({ success }) => {
                    if (success) {
                      this.roleParticipants = this.roleParticipants.filter((r) => r.id !== role.id);
                      this.sharedService.chatUsersListUpdated.next({ refetch: true });
                      this.sharedService.trackActivity({
                        type: Activity.messaging,
                        name: Activity.removeFromChatSession,
                        des: {
                          data: {
                            removedBy: this.sharedService.userData.displayName,
                            removedUser: role.name,
                            chatroomId: this.roomId,
                            id: role.id
                          },
                          desConstant: Activity.removeFromChatSessionDes
                        }
                      });
                    }
                    const message = success
                      ? this.commonService.getTranslateDataWithParam('MESSAGES.CHATROOM_REMOVE_ROLE_SUCCESS', {
                          roleName: role.name
                        })
                      : this.commonService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
                    this.commonService.showToast({
                      message,
                      color: success ? 'success' : 'danger'
                    });
                  });
              }
            });
        }
      });
  }

  /**
   * Removes a participant from the chat room.
   * @param participant - The participant to be removed.
   */
  removeParticipant(participant: ChatRoomParticipant): void {
    const yourselfLabel = this.commonService.getTranslateData('LABELS.YOURSELF');
    const name = this.sharedService.userData.userId === participant?.userId ? yourselfLabel : participant?.displayName;
    const confirmMessage = this.commonService.getTranslateDataWithParam('MESSAGES.YOU_WANT_TO_REMOVE_USER', { name });
    this.commonService
      .showAlert({
        message: confirmMessage,
        header: 'MESSAGES.ARE_YOU_SURE'
      })
      .then((confirmation: boolean) => {
        if (confirmation) {
          const payload = {
            chatroomId: this.roomId,
            userId: participant?.userId
          };
          this.httpService.doPost({ endpoint: APIs.removeChatRoomParticipants, payload, contentType: 'form' }).subscribe((response) => {
            const { success, data } = response;
            let message = '';
            if (success) {
              this.chatParticipants = this.chatParticipants.filter((u) => u.userId !== participant.userId);
              this.users = this.users.filter((u) => u.userId !== participant.userId);
              this.sharedService.chatUsersListUpdated.next({ refetch: true });
              this.sharedService.trackActivity({
                type: Activity.messaging,
                name: Activity.removeFromChatSession,
                des: {
                  data: {
                    removedBy: this.sharedService.userData.displayName,
                    removedUser: participant.displayName,
                    chatroomId: this.roomId,
                    id: participant.userId
                  },
                  desConstant: Activity.removeFromChatSessionDes
                }
              });
              message = this.commonService.getTranslateDataWithParam(
                +participant.userId === +this.sharedService.userData.userId
                  ? 'MESSAGES.CHATROOM_REMOVE_SELF_SUCCESS'
                  : 'MESSAGES.CHATROOM_REMOVE_PARTICIPANT_SUCCESS',
                {
                  participantName: participant.displayName,
                  fromName: this.sharedService.userData.displayName
                }
              );
            } else {
              message = this.commonService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
            }
            this.commonService.showToast({
              message,
              color: success ? 'success' : 'danger'
            });
          });
        }
      });
  }
}
