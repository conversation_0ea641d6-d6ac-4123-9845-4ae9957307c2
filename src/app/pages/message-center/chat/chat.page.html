<app-header [headerTitle]="title"></app-header>
<div class="chat-sub-header">
    <div class="left-sec">
        <ion-icon class="filter-icon" [name]="isFiltered() ? 'filter-circle' : 'filter-circle-outline'" id="filter-button-chat"></ion-icon>
    </div>
    <ion-popover trigger="filter-button-chat" [dismissOnSelect]="true" mode="ios">
        <ng-template>
            <ion-content class="mail-inbox-head">
                <ion-list>
                    <ion-item [button]="true" [detail]="false" (click)="filterTags()"
                        *ngIf="!isPatient && !isPartner && enableTagging">
                        <ion-icon tappable class="ion-margin-end"
                            [name]="(filteredTags.length ? 'pricetags' : 'pricetags-outline')"></ion-icon>
                        <ion-label>{{'LABELS.FILTER_BY_TAGS' | translate}}</ion-label>
                        <ion-icon name="checkmark-outline" color="primary" *ngIf="filteredTags.length"></ion-icon>
                    </ion-item>
                    <ion-item [button]="true" [detail]="false" (click)="priorityActionSheet(TYPE.PRIORITY, true)">
                        <ion-icon tappable class="ion-margin-end" class="{{getPriority(priorityFilterValue)}}"
                            name="{{getPriority(priorityFilterValue)}}"></ion-icon>
                        <ion-label>{{'LABELS.FILTER_BY_PRIORITY' | translate}}</ion-label>
                        <ion-icon name="checkmark-outline" color="primary" *ngIf="priorityFilterValue !== 0"></ion-icon>
                    </ion-item>
                    <ion-item [button]="true" [detail]="false" (click)="priorityActionSheet(TYPE.FLAG, true)"
                        *ngIf="enableFlagging">
                        <ion-icon id="filter" class="ion-margin-end"
                            class="ion-flag-{{getIonFlagClass(flagFilterValue)}}" tappable
                            src="assets/icon/material-svg/filter_sort.svg"></ion-icon>
                        <ion-label>{{'BUTTONS.FLAG' | translate}}</ion-label>
                        <ion-icon name="checkmark-outline" color="primary"
                            *ngIf="flagFilterValue !== constants.flagTypes.noFlag"></ion-icon>
                    </ion-item>
                    <ion-item [button]="true" [detail]="false"
                        (click)="showMentionMessages = !showMentionMessages;fetchChatMessages();">
                        <ion-icon id="filter-users" class="ion-margin-end" class="ion-margin-end ion-user-mention"
                            tappable name="at"></ion-icon>
                        <ion-label>{{'LABELS.MY_MENTIONS' | translate}}</ion-label>
                        <ion-icon name="checkmark-outline" color="primary" *ngIf="showMentionMessages"></ion-icon>
                    </ion-item>
                    <ion-item [button]="true" [detail]="false" (click)="messageTypeActionSheet()"
                        *ngIf="showChatFilterOption">
                        <ion-icon tappable class="ion-margin-end" name="chatbox-ellipses-outline"></ion-icon>
                        <ion-label>{{'LABELS.MESSAGE_TYPE' | translate}}</ion-label>
                        <ion-icon name="checkmark-outline" color="primary" *ngIf="filterOptions !== '1'"></ion-icon>
                    </ion-item>
                </ion-list>
            </ion-content>
        </ng-template>
    </ion-popover>

  <div class="right-sec" *ngIf="!multiTagSelectionIsActive && !sharedService.isAppLessHomeLoggedIn()">
    <ion-icon
      tappable
      [name]="enableApplessMode ? 'globe-outline' : 'phone-portrait-outline'"
      id="appless-mode"
      *ngIf="applessMessaging && !isVirtualUserExist; else appLessDisabled"
      (click)="switchApplessMode()">
    >
    </ion-icon>
    <ng-template #appLessDisabled>
      <ion-icon [name]="'globe-outline'" id="appless-mode" class="disabled-view"></ion-icon>
    </ng-template>
        <ion-icon tappable id="translate-language" (click)="enableTranslation()" *ngIf="toggleTranslate"
            [ngClass]="!toggleTranslationCheck ? 'translate-selected' : ''" class="icon-language"
            src="assets/icon/material-svg/language.svg"></ion-icon>
        <ion-icon *ngIf="(!joinChat || !joined) && showVideoCallFeature && !isBroadcast" tappable name="videocam"
            (click)="goToVideoChat()" id="goto-video-chat">
        </ion-icon>
        <button *ngIf="joinChat && joined" class="video-join" (click)="goToVideoChat()" id="join">Join</button>
        <ion-icon
            *ngIf="roomUsers?.length && !isBroadcast && (messageType !== maskedMessageType || !canReplyForMaskedMessage)"
            src="assets/icon/chat/people-chat-group-icon.svg" tappable (click)="presentUserPopover($event)"
            id="room-users">
        </ion-icon>
        <span class="count"
            *ngIf="roomUsers.length && !isBroadcast && (messageType !== maskedMessageType || !canReplyForMaskedMessage)">{{roomUsers.length}}</span>
    </div>
    <div *ngIf="multiTagSelectionIsActive" class="right-sec" (click)="clearTagMessageConfirm()">
        <ion-icon slot="end" name="close" size="large"></ion-icon>
    </div>
    <div class="chat-sub-header-subject">
        <div class="middle-sec-subject wrap-ellipsis" *ngIf="groupSubject">
            <ion-text class="subject-text wrap-ellipsis">{{groupSubject}}</ion-text>
            <ion-icon tappable name="create-outline" (click)="presentSubjectModal()"></ion-icon>
        </div>
        <div class="middle-sec-subject" *ngIf="multiTagSelectionIsActive"> {{getSelectedTagCount}}
            {{'MESSAGES.SELECTED_MESSAGE_COUNT' |
            translate}}</div>
    </div>
</div>
<ion-content>
    <div *ngIf="isLoadingMore">
        <app-skeleton-loader [type]="1"></app-skeleton-loader>
    </div>
    <ion-list>
        <div class="common-load-more-small blue-button-dark" *ngIf="chatMessages?.length===0">
            <button id="load-more-messages">{{'BUTTONS.NO_MESSAGE_AVAILABLE' | translate}}</button>
        </div>
        <div class=" common-load-more-small blue-button" *ngIf="showLoadMore && !isLoadingMore">
            <button id="load-more-messages" (click)="loadMoreMessages()">{{'BUTTONS.LOAD_EARLIER_MESSAGES' |
                translate}}</button>
        </div>
        <div class="list" #chatMessageRef>
            <ion-item-sliding lines="none" *ngFor="let message of chatMessages; let i = index; trackByIdentifier: 'id'"
                [ngSwitch]="+message.userid" #swipeList>
                <ion-item lines="none" *ngSwitchCase="+currentUser" (click)="chatItemClick($event, message)"
                    id="chat-item-click" [attr.message-id]="message.id">
                    <div class="message-section message-sent-chat">
                        <div class="msg-info">
                            <time>{{message.sent * 1000|shortDateTime}}</time>
                            <div *ngIf="message.sign===falseAsString && enableSign && !multiTagSelectionIsActive && !isMessageSending(message.id) && message.messageStatus"
                                id="open-sign-modal-{{message.id}}" class="chat-signature"
                                (click)="presentSignatureModal(message, i)"></div>
                        </div>
                        <div class="msg-middle" dirLongPress (longPressed)="longPressOptions(message, i)">
                            <h3 *ngIf="message.messageStatus && message.priorityId !== MESSAGE_PRIORITY.NORMAL; else normalMsg"
                                class="ion-no-margin priority-msg"><ion-icon
                                    *ngIf="message.priorityId === MESSAGE_PRIORITY.HIGH"
                                    class="alert-fill danger"></ion-icon><ion-icon
                                    *ngIf="message.priorityId === MESSAGE_PRIORITY.LOW"
                                    class="arrowdown-fill primary"></ion-icon></h3>
                            <div class="msg-content chat-arrow">
                                <div class="notranslate">
                                    <span>{{'LABELS.ME'| translate}}:</span>
                                    <span class="msg-info-icon" (click)="infoModal(message)"
                                        *ngIf="!isMessageSending(message.id)">
                                        <ion-icon name="information-outline"></ion-icon>
                                    </span>
                                    <span class="read-check" (click)="presentReadPopover($event, message)"
                                        *ngIf="!isMessageSending(message.id)&&lastUserActivity>=message.sent && message?.readUsers?.length"
                                        id="read">
                                        <ion-icon name="checkmark-sharp"></ion-icon>
                                        {{'BUTTONS.READ'| translate}}
                                    </span>
                                    <span class="unread-span"
                                        *ngIf="!isMessageSending(message.id) && (message.sent > lastUserActivity || lastUserActivity >= message.sent && !message?.readUsers?.length)">
                                        {{'LABELS.SENT'| translate}}
                                    </span>
                                    <span class="unsend-span" [ngClass]="{'hide':!isMessageSending(message.id)}"
                                        (click)="showMessageFailedPopover()">
                                        <ion-icon src="/assets/icon/chat/icons8-clock.svg"></ion-icon>
                                        {{'LABELS.SENDING'| translate}}
                                    </span>
                                </div>
                                <ion-badge *ngIf="message.priorityId !== MESSAGE_PRIORITY.NORMAL && message.messageStatus" mode="md"
                                    class="priority-bg"
                                    color="{{message.priorityId === MESSAGE_PRIORITY.HIGH ? 'danger' : 'primary'}}">{{
                                    ('PRIORITIES.' + (messagePriorityData | filter:message.priorityId:'key')[0]?.value)
                                    | translate }}</ion-badge>
                                <div class="message-content-parent">
                                    <div [ngClass]="{'overlay':message.uploading}"></div>
                                    <ion-spinner name="dots" *ngIf="message.uploading"></ion-spinner>
                                    <p class="msg-cont-text" [ngClass]="{'image-uploading': message.uploading}">
                                        <ng-container *ngIf="message.messageStatus === 1 || !message.hasOwnProperty('messageStatus'); else elseBlock">
                                          <span [innerHtml]="message.message | unicodeConvert | autolinker | safe:'html'"></span>
                                        </ng-container>
                                        <ng-template #elseBlock>
                                          <span class="undo-cont-text">
                                            {{'LABELS.MESSAGE_WAS_DELETED' | translate: {message : message.message, deletedOn: getDeletedDateTime(message.messageDeletedTime) } }}
                                            <ion-icon *ngIf="isUndoAvailable(message.messageDeletedTime) && +message.userid === this.currentUser" name="reload-circle" color="black" (click)="deleteUndoMessageConfirm(message, i, 'undo')" class="undo-delete flip-180"></ion-icon>
                                          </span>
                                        </ng-template>
                                      </p>
                                </div>
                                <app-tags *ngIf="message.tag===trueAsString" [tags]="message.tagedItems"
                                    [messageId]="message.id" [chatRoomId]="roomId"
                                    (removeMessageTags)="removeMessageTags($event, message.id)" [isThread]="false"
                                    [maxAllowedTags]="1" [threadLevel]="false"></app-tags>
                                <div class="msg-sign" *ngIf="message.sign && message.sign!==falseAsString && message.messageStatus">
                                    <img [src]="fullImageUrl(message.sign)" class="chat-sign-image"
                                        alt="chat-sign-image">
                                    <span class="remove-chat-sign" tappable (click)="removeSign(message, i)"
                                        id="remove-sign-{{i}}">
                                        <ion-icon name="trash-outline"></ion-icon>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="avatar">
                            <div class="flag-container" *ngIf="getFlagClass(message.msg_flag) && enableFlagging">
                                <div class="common-flag-icon" [ngClass]="getFlagClass(message.msg_flag)">M
                                </div>
                            </div>
                            <div class="checkbox-container" *ngIf="multiTagSelectionIsActive">
                                <ion-checkbox [id]="'mult-tag-checkbox'+i" class="msg-checkbox" slot="start"
                                    [(ngModel)]="message.checked" (ionChange)="getTotalCheckedCount()" mode="ios">
                                </ion-checkbox>
                            </div>
                            <img *ngIf="!multiTagSelectionIsActive" class="avatar-img" [src]="message.avatar"
                                alt="avatar" outOfOfficeStatus [oooInfo]="sharedService.userData.oooInfo" 
                                [customClass]="'chatroom-list-right-circle-badge'" />
                        </div>
                    </div>
                </ion-item>
                <ng-template #normalMsg>
                    <h3 class="ion-no-margin priority-msg"><ion-icon *ngIf="message.showMention" class="at-fill danger"
                            color="danger"></ion-icon></h3><span
                        *ngIf="!message.showMention">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                </ng-template>
                <ion-item lines="none" *ngSwitchCase="constants.administratorId">
                    <div class="message-section message-activity">
                        <div class="msg-middle">
                            <div class="msg-content system-message">
                                <p class="msg-cont-text">
                                    <span [innerHtml]="message.message"></span>
                                </p>
                                <app-tags *ngIf="message.tag===trueAsString" [tags]="message.tagedItems"
                                    [messageId]="message.id" [chatRoomId]="roomId"
                                    (removeMessageTags)="removeMessageTags($event, message.id)" [isThread]="false"
                                    [maxAllowedTags]="1"></app-tags>
                            </div>
                        </div>
                        <div class="msg-end">
                            <time>{{message.sent * 1000|shortDateTime}}</time>
                        </div>
                    </div>
                </ion-item>
                <ion-item lines="none" *ngSwitchDefault (click)="chatItemClick($event, message)" id="chat-item-click"
                    [attr.message-id]="message.id">
                    <div class="message-section message-received-chat">
                        <div class="msg-info">
                            <div class="checkbox-container" *ngIf="multiTagSelectionIsActive">
                                <ion-checkbox slot="start" [id]="'checkbox-chat-'+i" [(ngModel)]="message.checked"
                                    mode="ios" (ionChange)="getTotalCheckedCount()">
                                </ion-checkbox>
                            </div>
                            <img *ngIf="!multiTagSelectionIsActive" class="avatar-img" [src]="message.avatar"
                                alt="avatar" outOfOfficeStatus [oooInfo]="getOooInfo(+message.userid)" 
                                [customClass]="'chatroom-list-circle-badge'" />
                        </div>
                        <div class="msg-middle d-flex" dirLongPress (longPressed)="longPressOptions(message, i)">
                            <div class="msg-content chat-arrow">
                                <span class="msg-info-icon" (click)="infoModal(message)">
                                    <ion-icon name="information-circle-sharp"></ion-icon>
                                </span>
                                <div class="notranslate">
                                    <span>{{message.displayName}}:</span>
                                </div>
                                <ion-badge *ngIf="message.priorityId !== MESSAGE_PRIORITY.NORMAL && message.messageStatus" mode="md"
                                    class="priority-bg"
                                    color="{{message.priorityId === MESSAGE_PRIORITY.HIGH ? 'danger' : 'primary'}}">{{
                                    ('PRIORITIES.' + (messagePriorityData | filter:message.priorityId:'key')[0]?.value)
                                    | translate }}</ion-badge>
                                <p class="msg-cont-text">
                                    <ng-container *ngIf="message.messageStatus === 1 || !message.hasOwnProperty('messageStatus'); else elseBlock">
                                      <span [innerHtml]="message.message | unicodeConvert | autolinker | safe:'html'"></span>
                                    </ng-container>
                                    <ng-template #elseBlock>
                                      <span class="undo-cont-text">
                                        {{'LABELS.MESSAGE_WAS_DELETED' | translate: {message : message.message, deletedOn: getDeletedDateTime(message.messageDeletedTime) } }}
                                      </span>
                                    </ng-template>
                                  </p>
                                <app-tags *ngIf="message.tag===trueAsString" [tags]="message.tagedItems"
                                    [messageId]="message.id" [chatRoomId]="roomId"
                                    (removeMessageTags)="removeMessageTags($event, message.id)" [isThread]="false"
                                    [maxAllowedTags]="1"></app-tags>
                                <div class="msg-sign" *ngIf="message.sign && message.sign!==falseAsString && message.messageStatus">
                                    <img [src]="fullImageUrl(message.sign)" class="chat-sign-image" alt="chat-sign">
                                </div>
                            </div>
                            <h3 *ngIf="message.messageStatus && message.priorityId !== MESSAGE_PRIORITY.NORMAL; else normalMsg"
                                class="ion-no-margin priority-msg"><ion-icon
                                    *ngIf="message.priorityId === MESSAGE_PRIORITY.HIGH" class="alert-fill danger"
                                    color="danger"></ion-icon><ion-icon
                                    *ngIf="message.priorityId === MESSAGE_PRIORITY.LOW"
                                    class="arrowdown-fill primary"></ion-icon></h3>
                        </div>
                        <div class="msg-end">
                            <time>{{message.sent * 1000|shortDateTime}}</time>
                            <div class="flag-container" *ngIf="getFlagClass(message.msg_flag) && enableFlagging">
                                <div class="common-flag-icon" [ngClass]="getFlagClass(message.msg_flag)">M
                                </div>
                            </div>
                        </div>
                    </div>
                </ion-item>
                <ion-item-options side="start" class="common-swipe-buttons" *ngIf="message.messageStatus && +message.userid !== constants.administratorId">
                    <ion-item-option *ngIf="!isBroadcast && enableFlagging && !multiTagSelectionIsActive" class="reply"
                        (click)="replyMessage(message, i, swipeList)" id="reply">
                        <ion-icon src="assets/images/reply.svg" slot="top"></ion-icon>
                        <ion-label>{{'BUTTONS.REPLY' | translate}}</ion-label>
                    </ion-item-option>
                </ion-item-options>
                <ion-item-options side="end" class="common-swipe-buttons"
                    *ngIf="message.userid!=='0' && !isArchivedMessage && message.messageStatus && +message.userid !== constants.administratorId">
                    <ion-item-option *ngIf="enableFlagging && !multiTagSelectionIsActive" class="flag"
                        (click)="presentActionSheetForFlag(message, i, swipeList)" id="flag">
                        <ion-icon name="flag" slot="top"></ion-icon>
                        <ion-label>{{'BUTTONS.FLAG' | translate}}</ion-label>
                    </ion-item-option>
                </ion-item-options>
            </ion-item-sliding>
        </div>
    </ion-list>
</ion-content>

<ng-container *ngIf="selectedMessageData">
    <app-out-of-office [chatParticipants]="allParticipants" *ngIf="!isArchivedMessage"></app-out-of-office>
    <div *ngIf="showMentionList" class="mention-list">
        <ion-list lines="full">
            <ion-item color="light item-3_5rem" [button]="true"
                *ngFor="let user of filteredMentionUsers; let i= index;let last = last"
                (click)="userSelectHandle(user)">
                <ion-avatar aria-hidden="true" slot="start" class="avatar-shadow">
                    <img alt="" [src]="user.avatar" />
                </ion-avatar>
                <ion-label>
                    <h2>{{user.displayName}}</h2>
                </ion-label>
            </ion-item>
        </ion-list>
    </div>
    <div class="multi-select-actions" *ngIf="multiTagSelectionIsActive">
        <div tappable class="multi-tag-button cancel-button" id="multi-tsg-cancel" (click)="cancelMultiTag()">
            {{"BUTTONS.CANCEL" | translate}}
        </div>
        <div tappable class="multi-tag-button tag-button" id="multi-tag-do-tag" (click)="presentTagModal()">
            {{"BUTTONS.TAG_MESSAGES" | translate}}
        </div>
    </div>
    <div class="above-footer-container"
        [ngClass]="{'disable-virtual':chatWithUserType === constants.virtualUser && !applessMessaging}"
        *ngIf="!isArchivedMessage && !multiTagSelectionIsActive && !selectedMessageData?.archived">
        <div class="chat-information" *ngIf="isBroadcast">
            {{'MESSAGES.DO_NOT_REPLY_THIS_MESSAGE' | translate}}
        </div>
        <div class="chat-information"
            *ngIf="roomUsers?.length > 1 && (isEnabledDoNotReplySection || isShowDoubleVerification) && isGeneralMessageOrGroup">
            {{((isShowDoubleVerification && !isEnabledDoNotReplySection)?('MESSAGES.DOUBLE_VERIFICATION' | translate) : allowChatPermission ? (( 'MESSAGES.DO_NOT_REPLY_THIS_MESSAGE_PATIENT' | translate:{clinicianName:clinicianName}) + ('MESSAGES.NEW_CHAT' | translate)) :
            (( 'MESSAGES.DO_NOT_REPLY_THIS_MESSAGE_PATIENT' | translate:{clinicianName:clinicianName}))) }}
            <div class="ion-text-center chat-button"
                *ngIf='!sessionService.applessMessagingFlow && !isShowDoubleVerification && allowChatPermission'>
                <ion-button (click)="statNewChatMessage()" size="small" class="ion-text-capitalize">
                    {{"BUTTONS.CHAT" | translate}}
                    <ion-icon slot="start" shape="round" name="chatbox-ellipses-outline"></ion-icon>
                    <!-- TODO: Add dynamic name for staff and add improve conditions -->
                </ion-button>
            </div>
            <div class='double-verification' *ngIf='isShowDoubleVerification && !isEnabledDoNotReplySection'>
                <ion-row>
                    <ion-col size='4'>
                        <div tappable class='cnf-double-verificaion' id='double-verification-urgent'
                            (click)='setDoubleVerificationStatus(constants.configFalse)'>
                            {{'BUTTONS.DOUBLE_VERIFICATION_URGENT' | translate}}
                        </div>
                    </ion-col>
                    <ion-col size='8'>
                        <div tappable class='cnf-double-verificaion' id='mdouble-verification-not-urgent'
                            (click)='setDoubleVerificationStatus(constants.configTrue)'>
                            {{'BUTTONS.DOUBLE_VERIFICATION_NOT_URGENT' | translate}}
                        </div>
                    </ion-col>
                </ion-row>
            </div>
        </div>
        <!-- TODO: Show message on user Caregiver -->
        <div *ngIf="isChatHasClose && !isVirtualPatient">
            <div class="chat-information">
                {{"MESSAGES.THIS_CHAT_HAS_BEEN_CLOSED" | translate}}
                <div class="ion-text-center chat-button">
                    <ion-button size="small" (click)="statNewChatMessage()" class="ion-text-capitalize">
                        {{"BUTTONS.START_NEW_CHAT" | translate}}
                        <ion-icon slot="start" shape="round" name="chatbox-ellipses"></ion-icon>
                    </ion-button>
                </div>
            </div>
        </div>
        <div class="chat-normal-group"
            *ngIf="roomUsers?.length > 1 && (!isEnabledDoNotReplySection && !isShowDoubleVerification) && !isBroadcast">
            <div class="attachments-preview" *ngIf="filesInMemory && filesInMemory.length">
                <div class="file-section" *ngFor="let file of filesInMemory;let i=index">
                    <div *ngIf=" file?.ext === 'pngimage'|| file?.ext === 'jpgimage'; else otherTypes">
                        <img [src]="file?.data" alt="attach-image" />
                    </div>
                    <ng-template #otherTypes>
                        <img src="assets/images/doc-types/{{file.ext}}.png" alt="other-types">
                    </ng-template>
                    <span class="remove-file" (click)="removeFileFromArray(i)" id="remove-file-{{i}}">x</span>
                    <div *ngIf="file?.fileName" class="file-name">{{file?.fileName}}</div>
                </div>
            </div>
            <div class="hidden-textarea" *ngIf="messageType === maskedMessageType && canReplyForMaskedMessage">
                <span class="masked-main-thread-reply" (click)="maskedMessageReply()"
                    id="masked_message_reply">{{'PLACEHOLDERS.REPLY' | translate}} </span>
            </div>
            <div *ngIf="canReplyForMaskedMessage !== true">
                <ion-text *ngIf="messagePriority === MESSAGE_PRIORITY.HIGH"
                    class="d-flex priority-text align-items-center" color="danger"><ion-icon id="high-priority-icon"
                        class="chat-action-ion-icon alert-fill danger"></ion-icon>{{ 'PRIORITIES.HIGH' | translate
                    }}</ion-text>
                <ion-text *ngIf="messagePriority === MESSAGE_PRIORITY.LOW"
                    class="d-flex priority-text align-items-center" color="primary"><ion-icon id="low-priority-icon"
                        class="chat-action-ion-icon arrowdown-fill primary"></ion-icon>{{ 'PRIORITIES.LOW' | translate
                    }}</ion-text>
                <div class="typing" *ngIf="typingText">{{typingText}}</div>
                <div class="chat-controls-container">
                    <ion-fab #fabMore slot="fixed" vertical="bottom" horizontal="start">
                        <ion-fab-button size="small" color="medium">
                            <ion-icon name="add"></ion-icon>
                        </ion-fab-button>
                        <ion-fab-list side="top">
                            <ion-fab-button color="medium" (click)="fabMore.close(); gallaryFile.click()">
                                <ion-icon name="images-outline" id="gallary-file-icon"
                                    class="gallary-file-ion-icon"></ion-icon>
                                <input type="file" multiple #gallaryFile class="file-hidden"
                                    (change)="selectedFile($event)" (click)="gallaryFile.value = null"
                                    id="gallary-file" />
                            </ion-fab-button>
                            <ion-fab-button color="medium" (click)="fabMore.close(); cameraFile.click()">
                                <ion-icon name="camera-outline" id="camera-file-icon"
                                    class="camera-file-ion-icon"></ion-icon>
                                <input type="file" #cameraFile class="file-hidden" accept="image/*" capture="camera"
                                    (change)="selectedFile($event)" (click)="cameraFile.value = null"
                                    id="camera-file" />
                            </ion-fab-button>
                            <ion-fab-button *ngIf="sharedService?.userData?.group !== USER_GROUP.PATIENT.toString()"
                                color="medium" (click)="priorityActionSheet(TYPE.PRIORITY)">
                                <ion-icon id="alert-icon" class="chat-action-ion-icon alert-fill light"></ion-icon>
                            </ion-fab-button>
                        </ion-fab-list>
                    </ion-fab>
                    <div class="chat-textarea" id="type-message-textarea">
                        <div contenteditable [contenteditableHtml]="true" class="ion-padding-vertical"
                            id="message-textarea"
                            [ngClass]="{ 'textarea-height': chatWithUserType === constants.virtualUser && !applessMessaging }"
                            style="user-select: text;"
                            attr.placeholder="{{((filesInMemory && filesInMemory.length ? 'PLACEHOLDERS.ADD_COMMENT': chatWithUserType === constants.virtualUser && !applessMessaging ?'PLACEHOLDERS.VIRTUAL_CHAT_DISABLED':'PLACEHOLDERS.TYPE_HERE') ) | translate}}"
                            [(contenteditableModel)]="message"
                            (contenteditableModelChange)="userTyping();onMessageInput();"></div>
                        <ion-icon (click)="presentEmojiPopover($event)" tappable class="emoji-button"
                            [name]="isEmojiPopupOpen? 'close' : 'happy-outline'" id="emoji-popover">
                        </ion-icon>
                    </div>
                    <div class="chat-send-button" tappable (click)="sendMessage()" id="send-message">
                        <ion-icon name="send-sharp" id="send-message-icon"></ion-icon>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-container>
<app-footer backButtonLink="message-center/messages/{{chatPageType}}" [showBackButton]="showBackButton">
    <ion-buttons slot="end" class="footer-buttons" *ngIf="showFooterButtons">
        <div tappable *ngIf="showForwardFeature" class="button-element" (click)="fetchRoomUsers('forward')"
            id="forward-message">
            <ion-icon src="assets/icon/material-svg/share.svg"></ion-icon>
            <label>{{'BUTTONS.FORWARD' | translate}}</label>
        </div>
        <div tappable *ngIf="showInviteFeature" class="button-element" (click)="presentInviteModal()" id="invite-user">
            <ion-icon src="assets/icon/material-svg/account-plus.svg"></ion-icon>
            <label>{{'BUTTONS.INVITE' | translate}}</label>
        </div>
    </ion-buttons>
</app-footer>