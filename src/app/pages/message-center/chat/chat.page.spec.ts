import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { VideoCallService } from 'src/app/services/video-call/video-call.service';
import { Diagnostic } from '@awesome-cordova-plugins/diagnostic/ngx';
import { VideoCallPermissionService } from 'src/app/services/videocall-permission/video-call-permission.service';
import { Apollo } from 'apollo-angular';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, tick, fakeAsync, flush } from '@angular/core/testing';
import { ActionSheetController, AlertController, IonicModule, ModalController, PopoverController } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { ChatPage } from 'src/app/pages/message-center/chat/chat.page';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { CUSTOM_ELEMENTS_SCHEMA, ElementRef } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { RouterTestingModule } from '@angular/router/testing';
import { SharedModule } from 'src/app/shared.module';
import { Clipboard } from '@awesome-cordova-plugins/clipboard/ngx';
import { UnicodeConvertPipe } from 'src/app/pipes/unicodeConvert/unicode-convert.pipe';
import { Constants, MessageCategory, UserGroup } from 'src/app/constants/constants';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { PageRoutes } from 'src/app/constants/page-routes';
import * as moment from 'moment';
import { SendBulkMessageService } from 'src/app/services/send-bulk-message-service/send-bulk-message.service';

describe('ChatPage', () => {
  let component: ChatPage;
  let fixture: ComponentFixture<ChatPage>;
  let sharedService: SharedService;
  let httpService: HttpService;
  let videoCallService: VideoCallService;
  let common: CommonService;
  let modalController: ModalController;
  const { modalSpy, alertSpy, popoverSpy } = TestConstants;
  let actionSheet: ActionSheetController;
  const actionSpy = TestConstants.actionSheetSpy;
  let popoverController: PopoverController;
  let graphqlService: GraphqlService;
  let router: Router;
  let alertController: AlertController;
  let socketService: SocketService;
  let sendBulkMessageService: SendBulkMessageService;
  const response = {
    activity: 1,
    baseId: 0,
    chatroomid: '325814',
    content: [
      {
        id: 4195622,
        patient: '1110840',
        roleId: '2',
        sent: **********,
        sign: 'false',
        tag: 'true',
        tenantid: 558,
        userid: 888525
      }
    ],
    isPdg: 0,
    messageType: '0',
    subject: '',
    success: true,
    title: 'Chat With Tipson i, Dentist',
    messageGroupSites: 'Testtttt',
    patient_data: [{ roleId: 3 }]
  };
  const chatMessages = [
    {
      avatar: 'test',
      sign: 'test',
      checked: true,
      userid: '1',
      message: 'test',
      sent: 'true',
      displayName: 'test',
      id: '1',
      language: 'en-US'
    }
  ];
  const selectMessageData = {
    messageType: 0,
    baseId: 3454015,
    id: 51669,
    userid: 1365534,
    message_sender_assoc_userid: null,
    message_sender_assoc_displayname: null,
    language: '',
    message: 'Messages',
    createdAt: **********,
    sent: **********,
    sender_time: **********,
    info_available: 1,
    fname: 'Vishnu',
    lname: 'EnrolledStaff',
    pgrp: '1',
    tenantid: 1,
    displayName: 'Vishnu EnrolledStaff',
    caregiver_displayname: null,
    caregiver_userid: null,
    avatar: 'https://assets.com/avatars/profile-pic-clinician.png',
    roleId: '1',
    forwardBy: 0,
    sign: 'false',
    tag: 'false',
    tagSign: 'false',
    patient: '',
    tag_ids: '',
    tag_names: '',
    readUsers: [
      {
        userid: 144058,
        lastactivity: **********,
        displayName: 'Frederic Nathaniel',
        avatar: 'https://assets.com/avatars/profile-pic-clinician.png'
      }
    ],
    pfirstname: '',
    plastname: '',
    pdob: '',
    pdisplayname: '',
    pcaregiver_displayname: '',
    ppassword: null,
    msg_flag: 0,
    prev_msg_flag: 0,
    msg_flag_data_id: null,
    flag_modified_on: '0'
  };
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ChatPage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        SharedModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        RouterTestingModule.withRoutes([{ path: 'message-center/messages/active/chat/undefined', component: ChatPage }])
      ],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        VideoCallPermissionService,
        ActionSheetController,
        Idle,
        IdleExpiry,
        Keepalive,
        ModalController,
        PopoverController,
        SocketService,
        Diagnostic,
        Clipboard,
        AlertController,
        UnicodeConvertPipe,
        InAppBrowser,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    socketService = TestBed.inject(SocketService);
    alertController = TestBed.inject(AlertController);
    spyOn(alertController, 'create').and.callFake(() => {
      return alertSpy;
    });
    alertSpy.present.and.stub();
    spyOn(alertController, 'dismiss').and.stub();

    router = TestBed.inject(Router);
    spyOn(router, 'navigate').and.stub();
    spyOn(router, 'navigateByUrl').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onWillDismiss.and.resolveTo({ data: { subject: '', file: [] } });

    actionSheet = TestBed.inject(ActionSheetController);
    spyOn(actionSheet, 'create').and.callFake(() => {
      return actionSpy;
    });
    actionSpy.present.and.stub();
    spyOn(actionSheet, 'dismiss').and.stub();
    actionSpy.onWillDismiss.and.resolveTo({ data: { subject: '', file: [] } });

    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popoverSpy;
    });
    popoverSpy.present.and.stub();
    spyOn(popoverController, 'dismiss').and.stub();
    popoverSpy.onWillDismiss.and.resolveTo({ data: { subject: '', file: [] } });

    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    Object.defineProperty(sharedService, 'configValuesUpdated', { value: of('') });
    common = TestBed.inject(CommonService);
    spyOn(common, 'showMessage').and.stub();
    spyOn(common, 'redirectToPage').and.stub();
    fixture = TestBed.createComponent(ChatPage);
    httpService = TestBed.inject(HttpService);
    videoCallService = TestBed.inject(VideoCallService);
    graphqlService = TestBed.inject(GraphqlService);
    sendBulkMessageService = TestBed.inject(SendBulkMessageService);
    component = fixture.componentInstance;
    component.beforeTranslation = '';
    component.chatMessages = [];
    component.roomId = 1;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('execute maskedMessageReply', () => {
    component.messageListIndex = 1;
    spyOn(httpService, 'doPost').and.returnValue(of({ chatroomId: '1' }));
    Object.defineProperty(sharedService, 'messageList', { value: [] });
    component.maskedMessageReply();
    expect(component.maskedMessageReply).toBeTruthy();
  });

  it('execute sendMessage : attachedFile.length > 0', () => {
    component.attachedFile.length = 1;
    spyOn(component, 'fileUpload').and.returnValue(1);
    component.sendMessage();
    expect(component.sendMessage).toBeTruthy();
  });

  it('execute sendMessage : message', () => {
    component.message = 'test';
    component.selectedMessageData = { selectedTenantId: '43' };
    component.patientSiteId = [3];
    component.sendMessage();
    expect(component.sendMessage).toBeTruthy();
  });

  it('clear filesInMemory and update chatMessages when files are present', () => {
    component.filesInMemory = ['file1', 'file2'];
    spyOn(component, 'fileUpload');
    component.sendMessage();
    expect(component.filesInMemory).toEqual([]);
    expect(component.chatMessages.length).toBe(1);
    expect(component.fileUpload).toHaveBeenCalled();
  });

  it('execute when room IDs match', () => {
    sharedService.roomID = 123;
    component.roomId = 123;
    component.leaveChatRoom();
    expect(component.leaveChatRoom).toBeTruthy();
  });

  it('execute presentSubjectModal', fakeAsync(() => {
    component.groupSubject = 'subject';
    component.roomId = 1;
    component.messageListIndex = 0;
    Object.defineProperty(sharedService, 'messageList', { value: [{ title: '' }] });
    const response = { status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.presentSubjectModal();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.presentSubjectModal).toBeTruthy();
  }));
  it('execute presentSubjectModal: fail to update', fakeAsync(() => {
    component.groupSubject = 'subject';
    component.roomId = 1;
    component.messageListIndex = 0;
    Object.defineProperty(sharedService, 'messageList', { value: [{ title: '' }] });
    const response = { status: 0 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.presentSubjectModal();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.presentSubjectModal).toBeTruthy();
  }));

  it('execute postFileUpload:success', () => {
    component.selectedFileNames = [];
    const response = [
      {
        chatRoomId: 0,
        id: 0,
        msg: "test'1!@call--1653721170.jpg",
        success: true,
        view: 'image'
      }
    ];
    spyOn(component, 'fileUploadToCmis').and.returnValue();
    spyOn(httpService, 'fileUpload').and.returnValue(of(response));
    component.fileUpload();
    expect(component.fileUpload).toBeTruthy();
  });

  it('execute postFileUpload : else case', () => {
    component.selectedFileNames = [];
    const response = [
      {
        chatRoomId: 0,
        id: 0,
        msg: "test'1!@call--1653721170.jpg",
        success: false,
        view: 'image'
      }
    ];
    spyOn(component, 'fileUploadToCmis').and.returnValue();
    spyOn(httpService, 'fileUpload').and.returnValue(of(response));
    component.fileUpload();
    expect(component.fileUpload).toBeTruthy();
  });

  it('execute metaDataSet', () => {
    component.selectedFileNames = [
      [
        {
          details: { success: true, id: 0, msg: '<EMAIL>', view: 'image', chatRoomId: 0 },
          id: 0,
          name: '<EMAIL>',
          format: 'image'
        }
      ]
    ];
    component.attachedFile = [{ name: 'test' }];
    const callback = spyOn(component, 'fileUploadToCmis').and.returnValue();
    component.metaDataSet(1, callback);
    expect(component.metaDataSet).toBeTruthy();
  });

  it('execute setTagInMessage', () => {
    const message = { tag: 'ttt', patient: 'test', pdisplayname: 'TTEEST', tagedItems: [], selectedTags: [], tagSign: 'sffdf' };
    component.attachedFile.length = 1;
    component.setTagInMessage(message, { selectedTags: [] }, '');
    expect(component.setTagInMessage).toBeTruthy();
  });

  it('should handle empty tagedItems correctly', () => {
    const message = { tagedItems: [] };
    const tagData = {
      selectedUser: { id: 1, displayName: 'John Doe' },
      selectedTags: ['tag1', 'tag2']
    };
    const response = { approvalRequired: 'true' };
    component.setTagInMessage(message, tagData, response);

    expect(message.tagedItems).toEqual(['tag1', 'tag2']);
  });

  it('should handle null selectedUser correctly', () => {
    const message = {
      tag: 'true',
      patient: '123',
      pdisplayname: 'Demo',
      tagedItems: [],
      tagSign: 'true'
    };
    const tagData = {
      selectedUser: null,
      selectedTags: ['tag1', 'tag2']
    };
    const response = { approvalRequired: 'false' };

    component.setTagInMessage(message, tagData, response);

    expect(message.tag).toBe('true');
    expect(message.patient).toBeNull();
    expect(message.pdisplayname).toBe('');
    expect(message.tagedItems).toEqual(['tag1', 'tag2']);
    expect(message.tagSign).toBe('true');
  });

  it('execute showChatFilterOption: user is staff', () => {
    sharedService.userData.group = '2';
    expect(component.showChatFilterOption).toBeTrue();
  });

  it('execute loadMoreMessages', () => {
    spyOn(component, 'fetchChatMessages').and.returnValue();
    component.loadMoreMessages();
    expect(component.loadMoreMessages).toBeTruthy();
  });

  it('execute fullImageUrl', () => {
    component.fullImageUrl('');
    expect(component.fullImageUrl).toBeTruthy();
  });

  it('execute getFlagClass', () => {
    component.getFlagClass('');
    expect(component.getFlagClass).toBeTruthy();
  });

  it('execute removeFileFromArray', () => {
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.removeFileFromArray(1);
    expect(component.removeFileFromArray).toBeTruthy();
  });

  it('execute longPressOptions', () => {
    component.longPressOptions(1, 1);
    expect(component.longPressOptions).toBeTruthy();
  });

  it('execute scrollToBottom', () => {
    component.scrollToBottom();
    expect(component.scrollToBottom).toBeTruthy();
  });

  it('execute isMaskedChild', () => {
    component.baseId = '2';
    expect(component.isMaskedChild).toBeTrue();
  });
  it('should execute fetchRoomUsers : invalid room id', () => {
    component.roomId = undefined;
    component.fetchRoomUsers();
    expect(component.fetchRoomUsers).toBeTruthy();
  });
  it('should execute fetchRoomUsers :set clinician name', fakeAsync(() => {
    const selectionOption = 'forward';
    const result = [
      {
        roleId: '2',
        roleName: 'Citus Admin',
        passwordStatus: true,
        displayName: 'test',
        siteId: '0',
        status: '1',
        userId: '3'
      },
      {
        roleId: '3',
        roleName: 'Patient',
        displayName: 'test',
        siteId: '0',
        status: '1',
        userId: '34'
      }
    ];
    const responseData = {
      data: {
        allParticipants: result,
        chatParticipants: result,
        roleParticipants: []
      }
    };
    component.selectedMessageData = { ...selectMessageData, sent: '3424320' };
    component.patientSiteId = [];
    component.roomUsers = <any>result;
    sharedService.userData.userId = '3';
    sharedService.userData.config.enable_multisite = '0';
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doGet').and.returnValue(of(responseData));
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    component.fetchRoomUsers(selectionOption);
    tick(1000);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.fetchRoomUsers).toBeTruthy();
    expect(component.clinicianName).toEqual(result[0].displayName);
  }));
  it('should execute fetchRoomUsers : patientchat selectionOption is forward', fakeAsync(() => {
    const selectionOption = 'forward';
    const result = [
      {
        roleId: '2',
        roleName: 'Citus Admin',
        displayName: 'test',
        siteId: '0',
        status: '1',
        userId: '3'
      },
      {
        roleId: '3',
        roleName: 'Patient',
        displayName: 'test',
        siteId: '0',
        status: '1',
        userId: '34'
      },
      {
        roleId: '3',
        roleName: 'Alternate Contact',
        displayName: 'test',
        siteId: '0',
        status: '1',
        userId: '34',
        caregiver_userid: '32'
      }
    ];
    const responseData = {
      data: {
        allParticipants: result,
        chatParticipants: result,
        roleParticipants: []
      }
    };
    component.selectedMessageData = { ...selectMessageData, sent: '3424320' };
    component.patientSiteId = [];
    component.roomUsers = <any>result;
    sharedService.userData.userId = '3';
    sharedService.userData.config.enable_multisite = '0';
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doGet').and.returnValue(of(responseData));
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    component.fetchRoomUsers(selectionOption);
    tick(1000);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.fetchRoomUsers).toBeTruthy();
  }));

  it('should execute fetchRoomUsers : selectionOption is forward', () => {
    const selectionOption = 'forward';
    const result = [
      {
        roleId: '2',
        roleName: 'Citus Admin',
        siteId: '0',
        status: '1',
        userId: '3'
      }
    ];
    const responseData = {
      data: {
        allParticipants: result,
        chatParticipants: result,
        roleParticipants: []
      }
    };
    component.patientSiteId = [];
    component.roomUsers = <any>result;
    sharedService.userData.userId = '3';
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doGet').and.returnValue(of(responseData));
    component.fetchRoomUsers(selectionOption);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.fetchRoomUsers).toBeTruthy();
  });

  it('should execute fetchRoomUsers : !isMsgGrp & !isMsgGrp', () => {
    const result = [
      {
        roleId: '3',
        roleName: 'Citus Admin',
        siteId: '0',
        status: '1'
      }
    ];
    component.isMsgGrp = false;
    component.isMsgGrp = false;
    component.patientSiteId = [];
    component.roomUsers = <any>result;
    spyOn(httpService, 'doGet').and.returnValue(of(result));
    component.fetchRoomUsers();
    expect(component.fetchRoomUsers).toBeTruthy();
  });

  it('should execute fetchRoomUsers : else case', () => {
    const selectionOption = 'forward';
    const result = [
      {
        roleId: '2',
        roleName: 'Citus Admin',
        siteId: '0',
        status: '1'
      }
    ];
    component.patientSiteId = [];
    component.roomUsers = <any>result;
    spyOn(httpService, 'doGet').and.returnValue(of(result));
    component.fetchRoomUsers(selectionOption);
    expect(component.fetchRoomUsers).toBeTruthy();
  });

  it('should execute fetchRoomUsers : Throw error', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.fetchRoomUsers();
    expect(component.fetchRoomUsers).toBeTruthy();
  });

  it('execute fetchChatMessages', () => {
    const isLoadingMore = true;
    component.toggleTranslationCheck = false;
    component.chatMessages = chatMessages;
    response.isPdg = 1;
    component.toggleTranslate = false;
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchChatMessages(isLoadingMore);
    expect(component.fetchChatMessages).toBeTruthy();
  });

  it('execute fetchChatMessages : isLoadingMore = false', () => {
    component.toggleTranslationCheck = false;
    component.chatMessages = chatMessages;
    response.isPdg = 1;
    component.isPdg = true;
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchChatMessages();
    expect(component.fetchChatMessages).toBeTruthy();
  });

  it('execute fetchChatMessages : isLoadingMore = false & isPdg = 0', () => {
    component.toggleTranslationCheck = true;
    component.chatMessages = chatMessages;
    response.isPdg = 0;
    component.isPdg = true;
    component.patientSiteId = [];
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchChatMessages();
    expect(component.fetchChatMessages).toBeTruthy();
  });

  it('execute fetchChatMessages : isLoadingMore = false & else', () => {
    component.toggleTranslationCheck = true;
    const data = {
      activity: 1,
      baseId: 0,
      chatroomid: '325814',
      content: [
        {
          id: 4195622,
          patient: '1110840',
          roleId: '2',
          sent: **********,
          sign: 'false',
          tag: 'true',
          tenantid: 558,
          userid: 888525
        }
      ],
      messageType: '0',
      subject: '',
      success: true,
      title: 'Chat With Tipson i, Dentist',
      patient_data: [{ roleId: '' }],
      isPdg: 0
    };
    component.chatMessages = chatMessages;
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    spyOn(httpService, 'doGet').and.returnValue(of(data));
    component.fetchChatMessages();
    expect(component.fetchChatMessages).toBeTruthy();
  });

  it('execute fetchChatMessages : throwError', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.fetchChatMessages();
    expect(component.fetchChatMessages).toBeTruthy();
  });

  it('execute cancelMultiTag', () => {
    component.multiTagSelectionIsActive = false;
    component.chatMessages = [{ avatar: 'test', sign: 'test', checked: true, userid: '1', message: 'test', sent: 'true', displayName: 'test' }];
    component.cancelMultiTag();
    expect(component.cancelMultiTag).toBeTruthy();
  });

  it('execute setSignatureValueAfterSearch : chatIndex', () => {
    component.chatMessages = chatMessages;
    const incomingChatData = { id: '1' };
    component.setSignatureValueAfterSearch(incomingChatData);
    expect(component.setSignatureValueAfterSearch).toBeTruthy();
  });

  it('execute saveTranslationLog', () => {
    component.traslatedData = [];
    component.beforeTranslation = JSON.stringify([
      {
        avatar: 'test',
        sign: 'test',
        checked: true,
        userid: 2,
        message: 'test',
        sent: 'true',
        displayName: 'test',
        id: 2
      }
    ]);
    component.chatMessages = chatMessages;
    component.saveTranslationLog('reeee');
    expect(component.saveTranslationLog).toBeTruthy();
  });
  it('execute chatItemClick: Image', () => {
    const ngClickElement = document.createElement('ion-item');
    ngClickElement.setAttribute('ng-click', 'showImage');
    ngClickElement.setAttribute('src', 'abc');
    component.chatItemClick({ target: ngClickElement });
    expect(component.chatItemClick).toBeTruthy();
  });
  it('execute chatItemClick: Image', () => {
    const ngClickElement = document.createElement('ion-item');
    ngClickElement.setAttribute('ng-click', 'showImage');
    ngClickElement.setAttribute('data-viewsrc', 'abc');
    component.chatItemClick({ srcElement: ngClickElement });
    expect(component.chatItemClick).toBeTruthy();
  });
  it('execute chatItemClick: Document showCmisPdfOrDocs', () => {
    const ngClickElement = document.createElement('ion-item');
    ngClickElement.setAttribute('ng-click', 'showCmisPdfOrDocs');
    ngClickElement.setAttribute('src', 'abc');
    component.chatItemClick({ target: ngClickElement });
    expect(component.chatItemClick).toBeTruthy();
  });
  it('execute chatItemClick: Document showPdfOrDocs', () => {
    const ngClickElement = document.createElement('ion-item');
    ngClickElement.setAttribute('ng-click', 'showPdfOrDocs');
    ngClickElement.setAttribute('src', 'abc');
    component.chatItemClick({ currentTarget: ngClickElement });
    expect(component.chatItemClick).toBeTruthy();
  });

  it('execute processFiles: exceed max length', fakeAsync(() => {
    component.attachedFile = { length: 114 };
    component.processFiles({ data: { subject: '', file: [{ size: 20971521 }] } });
  }));
  it('execute chatItemClick: presentPdfFromLink return success', () => {
    const ngClickElement = document.createElement('ion-item');
    ngClickElement.setAttribute('ng-click', 'showPdfOrDocs');
    ngClickElement.setAttribute('src', 'abc');
    spyOn(sharedService, 'presentPdfFromLink').and.resolveTo({ status: true, url: 'test' });
    component.chatItemClick({ currentTarget: ngClickElement });
    expect(component.chatItemClick).toBeTruthy();
  });

  it('execute setDoubleVerificationStatus', () => {
    component.setDoubleVerificationStatus('');
    expect(component.setDoubleVerificationStatus).toBeTruthy();
  });

  it('execute resetMessageReplyTimeoutOnNewmessage', () => {
    component.selectedMessageData = { ...selectMessageData, sent: '3424320' };
    component.resetMessageReplyTimeoutOnNewmessage({ userId: '' });
    expect(component.resetMessageReplyTimeoutOnNewmessage).toBeTruthy();
  });

  it('execute setVideoCallValues', () => {
    component.setVideoCallValues();
    expect(component.setVideoCallValues).toBeTruthy();
  });

  it('execute setVideoConnectData', () => {
    spyOn(sharedService.keepalive, 'stop').and.stub();
    spyOn(sharedService.idle, 'stop').and.stub();
    component.setVideoConnectData({}, () => undefined);
    expect(component.setVideoConnectData).toBeTruthy();
  });

  it('execute setVideoCallParams', () => {
    spyOn(sharedService.keepalive, 'stop').and.stub();
    spyOn(sharedService.idle, 'stop').and.stub();
    spyOn(videoCallService, 'videoRoomCreate').and.returnValue(of({ data: { createVidyoRoom: { roomKey: 231 } } }));
    component.setVideoCallParams();
    expect(component.setVideoCallParams).toBeTruthy();
  });

  it('execute setVideoCallParams: joinchat', () => {
    spyOn(sharedService.keepalive, 'stop').and.stub();
    spyOn(sharedService.idle, 'stop').and.stub();
    component.joinChat = true;
    component.videoCallRoomData = { roomEntity: 12, roomName: '', roomPin: '', displayName: '', host: '', roomKey: '' };
    component.setVideoCallParams();
    expect(component.setVideoCallParams).toBeTruthy();
  });

  it('execute processFiles: exceed max length', fakeAsync(() => {
    component.attachedFile = { length: 114 };
    popoverSpy.onWillDismiss.and.resolveTo({ data: { subject: '', file: [{ size: 20971521 }] } });
    component.processFiles('');
    fixture.detectChanges();
    expect(component.processFiles).toBeTruthy();
  }));

  it('execute processFiles: max file size exceeded', () => {
    component.attachedFile = { length: 1 };
    popoverSpy.onWillDismiss.and.resolveTo({ data: { subject: '', file: [{ size: 20971521 }] } });
    component.processFiles({ subject: '', file: [{ size: 20971521 }] });
    fixture.detectChanges();
    expect(component.processFiles).toBeTruthy();
  });

  it('execute processFiles: invalid data type', () => {
    component.attachedFile = { length: 1 };
    popoverSpy.onWillDismiss.and.resolveTo({ data: { subject: '', file: [{ size: 209710, name: 'file.xml' }] } });
    component.processFiles({ subject: '', file: [{ size: 209710, name: 'file.xml' }] });
    fixture.detectChanges();
    expect(component.processFiles).toBeTruthy();
  });

  it('call statNewChatMessage function', () => {
    component.statNewChatMessage();
    expect(component.statNewChatMessage).toBeDefined();
  });

  it('execute clearTagMessageConfirm', () => {
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.clearTagMessageConfirm();
    expect(component.clearTagMessageConfirm).toBeDefined();
  });

  it('execute infoModal', fakeAsync(() => {
    tick(3000);
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    component.infoModal(selectMessageData);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.infoModal).toBeTruthy();
  }));

  it('execute presentEmojiPopover', fakeAsync(() => {
    let ele: ElementRef;
    component.presentEmojiPopover(ele);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.presentEmojiPopover).toBeTruthy();
  }));

  it('call changeChatFilter function', () => {
    component.changeChatFilter('1');
    expect(component.changeChatFilter).toBeTruthy();
  });
  it('execute presentInviteModal', fakeAsync(() => {
    component.filterOptions = '';
    component.selectedMessageData = { ...selectMessageData, sent: '3424320' };
    modalSpy.onDidDismiss.and.resolveTo({ data: { response: 'fake', invitedUsers: true } });
    component.presentInviteModal();
    tick(1000);
    expect(component.presentInviteModal).toBeDefined();
  }));
  it('execute doTagMessages : approvalRequired = false', () => {
    const tagData = {
      selectedTags: [
        {
          id: 35086,
          name: '2022',
          selected: true,
          meta: '{"outgoingFilingCenter":"","fileSaveFormat":"","summarizeOutcomeMeasure":false,"approvalRequired":false,"patientFacing":false,"enableIntegration":true,"integrationFC":"","triggeron":"approve-tag","fileSaveFormatIntegration":"","nursingAgencyUserTagSelected":[],"messageCategoryCode":null,"messageTypeID":null,"externalIntegrationSettings":{"messageTagCategoryId":"","messageTagCategoryName":"","messageTagTypeId":"","messageTagTypeName":""}}'
        }
      ],
      selectedUser: { id: '1357616', displayName: '(Kishan. KJ Prod' }
    };
    const message = { tag: 'ttt', patient: 'test', pdisplayname: 'TTEEST', tagedItems: [], tagSign: 'sffdf' };
    component.chatMessages = chatMessages;
    const response = {
      success: true,
      approvalRequired: false
    };
    component.multiTagSelectionIsActive = true;
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doTagMessages(message, 0, tagData);
    expect(component.doTagMessages).toBeTruthy();
  });

  it('execute doTagMessages', () => {
    const tagData = {
      selectedTags: [
        {
          id: 35086,
          name: '2022',
          selected: true,
          meta: '{"outgoingFilingCenter":"","fileSaveFormat":"","summarizeOutcomeMeasure":false,"approvalRequired":true,"patientFacing":false,"enableIntegration":true,"integrationFC":"","triggeron":"approve-tag","fileSaveFormatIntegration":"","nursingAgencyUserTagSelected":[],"messageCategoryCode":null,"messageTypeID":null,"externalIntegrationSettings":{"messageTagCategoryId":"","messageTagCategoryName":"","messageTagTypeId":"","messageTagTypeName":""}}'
        }
      ],
      selectedUser: { id: '1357616', displayName: '(Kishan. KJ Prod' }
    };
    const message = { tag: 'ttt', patient: 'test', pdisplayname: 'TTEEST', tagedItems: [], tagSign: 'sffdf' };
    component.chatMessages = chatMessages;
    const response = {
      success: true,
      approvalRequired: true
    };
    component.multiTagSelectionIsActive = true;
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doTagMessages(message, 0, tagData);
    expect(component.doTagMessages).toBeTruthy();
  });

  it('execute doTagMessages : response =  []', () => {
    const tagData = {
      selectedTags: [
        {
          id: 35086,
          meta: '{"outgoingFilingCenter":"","fileSaveFormat":"","summarizeOutcomeMeasure":false,"approvalRequired":false,"patientFacing":true,"enableIntegration":true,"integrationFC":"Send to EHR","triggeron":"add-tag","fileSaveFormatIntegration":"{MRN}-{UniqueNO}","nursingAgencyUserTagSelected":[],"messageCategoryCode":null,"messageTypeID":null,"externalIntegrationSettings":{"messageTagCategoryId":"","messageTagCategoryName":"","messageTagTypeId":"","messageTagTypeName":""}}',
          name: '2022',
          selected: true
        }
      ],
      selectedUser: { id: '1357616', displayName: '(Kishan. KJ Prod' }
    };
    const message = { tag: 'ttt', patient: 'test', pdisplayname: 'TTEEST', tagedItems: [], tagSign: 'sffdf' };
    component.chatMessages = chatMessages;
    component.multiTagSelectionIsActive = true;
    spyOn(httpService, 'doPost').and.returnValue(of([]));
    component.doTagMessages(message, 0, tagData);
    expect(component.doTagMessages).toBeTruthy();
  });

  it('execute goToVideoChat with joinChat', () => {
    component.joinChat = true;
    component.goToVideoChat();
    expect(component.goToVideoChat).toBeDefined();
  });

  it('execute goToVideoChat with initiates Chat', () => {
    component.joinChat = false;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.goToVideoChat();
    expect(component.goToVideoChat).toBeDefined();
  });

  it('execute goToVideoChat with initiates Chat : chatWithUserType = Enrolled', () => {
    component.chatWithUserType = Constants.enrolledUser;
    component.joinChat = false;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.goToVideoChat();
    expect(component.goToVideoChat).toBeDefined();
  });

  it('execute connectVideo if joinChat and enableApplessVideoChat are both false', async () => {
    spyOn(videoCallService, 'iosAudioPreLoad').and.returnValue(Promise.resolve());
    spyOn(component, 'connectVideo');
    spyOn(component.sharedService, 'isEnableConfig').and.returnValue(false);
    component.joinChat = false;
    await component.goToVideoChat();
    expect(videoCallService.iosAudioPreLoad).toHaveBeenCalled();
    expect(component.sharedService.isEnableConfig).toHaveBeenCalledWith('enable_appless_video_chat');
    expect(component.connectVideo).toHaveBeenCalled();
  });

  it('should set isAppLessWorkFlow correctly when joinChat is false and enableApplessVideoChat is true', async () => {
    spyOn(videoCallService, 'iosAudioPreLoad').and.returnValue(Promise.resolve());
    spyOn(component.sharedService, 'isEnableConfig').and.returnValue(true);
    spyOn(common, 'showAlert').and.returnValue(Promise.resolve(true));
    spyOn(component, 'connectVideo');
    component.joinChat = false;
    component.chatWithUserType = Constants.enrolledUser;
    await component.goToVideoChat();
    expect(videoCallService.iosAudioPreLoad).toHaveBeenCalled();
    expect(component.sharedService.isEnableConfig).toHaveBeenCalledWith('enable_appless_video_chat');
    expect(common.showAlert).toHaveBeenCalled();
    expect(videoCallService.isAppLessWorkFlow).toBe(false);
  });

  it('should set isAppLessWorkFlow to false when user chooses not to use app-less video chat', async () => {
    spyOn(videoCallService, 'iosAudioPreLoad').and.returnValue(Promise.resolve());
    spyOn(component.sharedService, 'isEnableConfig').and.returnValue(true);
    spyOn(common, 'showAlert').and.resolveTo(false);
    spyOn(component, 'connectVideo');
    component.joinChat = false;
    component.chatWithUserType = 'Constants.someOtherUserType';
    await component.goToVideoChat();
    expect(videoCallService.iosAudioPreLoad).toHaveBeenCalled();
    expect(component.sharedService.isEnableConfig).toHaveBeenCalledWith('enable_appless_video_chat');
    expect(common.showAlert).toHaveBeenCalled();
    expect(videoCallService.isAppLessWorkFlow).toBe(false);
  });

  it('should set isAppLessWorkFlow to true when user chooses use app-less video chat', async () => {
    spyOn(videoCallService, 'iosAudioPreLoad').and.returnValue(Promise.resolve());
    spyOn(component.sharedService, 'isEnableConfig').and.returnValue(true);
    spyOn(common, 'showAlert').and.resolveTo('Appless');
    spyOn(component, 'connectVideo');
    component.joinChat = false;
    component.chatWithUserType = 'Constants.someOtherUserType';
    await component.goToVideoChat();
    expect(videoCallService.iosAudioPreLoad).toHaveBeenCalled();
    expect(component.sharedService.isEnableConfig).toHaveBeenCalledWith('enable_appless_video_chat');
    expect(common.showAlert).toHaveBeenCalled();
    expect(videoCallService.isAppLessWorkFlow).toBe(false);
  });

  it('should call updateConfigPermissions and setMessageReplyTimeout when message.userId is not administratorId', () => {
    const message = { userId: 'someUserId' };
    const updateConfigPermissionsSpy = spyOn(component, 'updateConfigPermissions');
    const setMessageReplyTimeoutSpy = spyOn(component, 'setMessageReplyTimeout');
    component.resetMessageReplyTimeoutOnNewmessage(message);
    expect(updateConfigPermissionsSpy).toHaveBeenCalled();
    expect(setMessageReplyTimeoutSpy).toHaveBeenCalled();
  });

  it('execute failedMessageResend function', () => {
    component.filesInMemoryOffline.length = 1;
    component.failedMessageResend();
    expect(component.failedMessageResend).toBeDefined();
  });

  it('should call translation if message has userId and different language', () => {
    const message = {
      userId: 1,
      language: 'fr-FR',
      data: 'Message data'
    };
    spyOn(component, 'translatedMessageList');

    component.messageTranslate(message);

    expect(component.translatedMessageList).toBeDefined();
  });

  it('should call translation if message has userId and no language', () => {
    const message = {
      userId: 1,
      data: 'Message data'
    };
    spyOn(component, 'translatedMessageList');

    component.messageTranslate(message);

    expect(component.translatedMessageList).toBeDefined();
  });

  it('should call translation if chatMessages have userId and different language', () => {
    const message = undefined;
    spyOn(component, 'translatedMessageList');
    const messages: any = [{ userid: 1, message: 'Message 1' }];
    component.chatMessages = messages;
    component.messageTranslate(message);
    expect(component.translatedMessageList).toBeDefined();
  });

  it('execute setVideoCallValues function', () => {
    component.roomId = 1;
    videoCallService.joinChat = true;
    videoCallService.sharedService.videoCall = true;
    videoCallService.participant = true;
    expect(component.setVideoCallValues).toBeDefined();
  });

  it('execute initializeVideo function', () => {
    expect(component.initializeVideo).toBeDefined();
  });

  it('execute sendUrlToAppLessVideo function', () => {
    const sendApplessData = of({
      data: {
        sendApplessData: {
          userid: 1738191,
          username: null,
          chatroomId: 415811,
          videoId: 798,
          __typename: 'ApplessVideo'
        }
      }
    });

    spyOn(graphqlService, 'sendAppLessVideoData').and.returnValue(sendApplessData);
    component.sendUrlToAppLessVideo(videoCallService.videoConnectOption);
    expect(component.sendUrlToAppLessVideo).toBeDefined();
  });

  it('execute presentUserPopover function', fakeAsync(() => {
    tick(3000);
    popoverSpy.present();
    let ele: ElementRef<HTMLElement>;
    component.presentUserPopover(ele);
    expect(component.presentUserPopover).toBeDefined();
  }));

  it('execute presentReadPopover', fakeAsync(() => {
    const ev = {};
    const message = { id: '1' };
    component.presentReadPopover(ev, message);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.presentReadPopover).toBeTruthy();
  }));

  it('execute removeSign', () => {
    const message = [];
    Object.defineProperty(component, 'chatMessages', { value: [{ sign: '' }] });
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    component.removeSign(message, 0);
    expect(component.removeSign).toBeDefined();
  });

  it('should call getLastMessage', () => {
    component.getLastMessage();
    expect(component.getLastMessage).toBeDefined();
  });

  it('should call checkTimeOutMessageIfUserNotAvailable', () => {
    component.checkTimeOutMessageIfUserNotAvailable();
    expect(component.checkTimeOutMessageIfUserNotAvailable).toBeDefined();
  });

  it('should call presentForwardModal', fakeAsync(() => {
    tick(3000);
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    component.presentForwardModal('1');
    expect(component.presentForwardModal).toBeDefined();
  }));

  it('should call presentForwardBehaviourModal', fakeAsync(() => {
    tick(3000);
    component.forwardData = { rerouteBehaviour: 'go' };
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    component.presentForwardBehaviourModal();
    expect(component.presentForwardBehaviourModal).toBeDefined();
  }));

  it('execute textFromHtml', () => {
    component.textFromHtml('test');
    expect(component.textFromHtml).toBeDefined();
  });

  it('execute copyToClipBoard', fakeAsync(() => {
    spyOn(component, 'textFromHtml');
    component.copyToClipBoard('test');
    expect(component.copyToClipBoard).toBeDefined();
  }));

  it('should call userTyping', () => {
    component.userTyping();
    expect(component.userTyping).toBeTruthy();
  });

  it('should call isSameUser', () => {
    component.isSameUser({ userid: '3' });
    expect(component.isSameUser).toBeTruthy();
  });

  it('should call checkTimeOutMessageIfUserNotAvailable', fakeAsync(() => {
    tick(35632);
    component.messageReplyTimeout = 1800;
    sharedService.userData.group = '3';
    component.doNotReplyStatus = true;
    component.selectedMessageData = { baseId: false };
    component.isArchivedMessage = false;
    component.chatMessages = response.content as any;
    component.title = 'Chat With abc';
    spyOn(component, 'updateConfigPermissions').and.stub();
    component.checkTimeOutMessageIfUserNotAvailable();
    expect(component.checkTimeOutMessageIfUserNotAvailable).toBeTruthy();
    flush();
  }));

  it('should call checkTimeOutMessageIfUserNotAvailable', fakeAsync(() => {
    tick(35632);
    component.messageReplyTimeout = 1800;
    sharedService.userData.group = '3';
    component.doNotReplyStatus = true;
    component.selectedMessageData = { baseId: false };
    component.isArchivedMessage = true;
    component.chatMessages = response.content as any;
    component.title = 'Chat With abc';
    spyOn(component, 'updateConfigPermissions').and.stub();
    component.checkTimeOutMessageIfUserNotAvailable();
    expect(component.checkTimeOutMessageIfUserNotAvailable).toBeTruthy();
    flush();
  }));

  it('should execute fetchRoomUsers : selectionOption is forward', fakeAsync(() => {
    const selectionOption = 'forward';
    const result = [
      {
        roleId: '3',
        roleName: 'Citus Admin',
        displayName: 'test',
        siteId: '0',
        status: '1',
        messageType: '0',
        baseId: '0'
      }
    ];
    const responseData = {
      data: {
        allParticipants: result,
        chatParticipants: result,
        roleParticipants: []
      }
    };
    component.selectedMessageData = result[0] as any;
    component.patientSiteId = [];
    component.isPdg = false;
    component.isMsgGrp = false;
    component.isArchivedMessage = false;
    component.roomUsers = <any>result;
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doGet').and.returnValue(of(responseData));
    component.fetchRoomUsers(selectionOption);
    expect(component.fetchRoomUsers).toBeTruthy();
    flush();
  }));

  it('execute doForward', () => {
    component.roomId = 1;
    component.messageListIndex = 0;
    Object.defineProperty(sharedService, 'messageList', { value: [{}] });
    component.forwardData = { chatroomId: undefined, rerouteBehaviour: 'remove_forwarder' };
    const result = { status: 1 };
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doPost').and.returnValue(of(result));
    component.doForward();
    expect(component.doForward).toBeTruthy();
  });

  it('execute doForward', () => {
    component.roomId = 1;
    component.messageListIndex = 0;
    Object.defineProperty(sharedService, 'messageList', { value: [{}] });
    component.forwardData = { chatroomId: undefined, rerouteBehaviour: 'remove_forwarder' };
    const result = { status: 0 };
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doPost').and.returnValue(of(result));
    component.doForward();
    expect(component.doForward).toBeTruthy();
  });

  it('execute presentActionSheetForFlag', () => {
    component.presentActionSheetForFlag({}, 0, { close: () => 1 });
    expect(component.presentActionSheetForFlag).toBeTruthy();
  });

  it('execute enableTranslation', () => {
    component.toggleTranslationCheck = true;
    component.enableTranslation();
    expect(component.enableTranslation).toBeTruthy();
  });

  it('execute enableTranslation : Auto translate', () => {
    component.toggleTranslationCheck = false;
    component.toggleTranslate = true;
    component.chatMessages = chatMessages;
    component.chatMessages[0].message = '';
    component.enableTranslation();
    expect(component.enableTranslation).toBeTruthy();
  });

  it('execute translatedMessageList', () => {
    const messages = [
      {
        userid: 2,
        message: 'haai'
      }
    ];
    component.beforeTranslation = JSON.stringify([
      {
        avatar: 'test',
        sign: 'test',
        checked: true,
        userid: 2,
        message: 'test',
        sent: 'true',
        displayName: 'test',
        id: 2
      }
    ]);
    Object.defineProperty(component, 'chatMessages', { value: messages });
    component.translatedMessageList(messages);
    expect(component.translatedMessageList).toBeTruthy();
  });

  it('execute getTotalCheckedCount', () => {
    const messages = [
      {
        userid: 2,
        message: 'haai',
        checked: true
      }
    ];
    Object.defineProperty(component, 'chatMessages', { value: messages });
    component.getTotalCheckedCount();
    expect(component.getTotalCheckedCount).toBeTruthy();
  });

  it('execute getTotalCheckedCount: selected tag count', () => {
    component.getTotalCheckedCount(1);
    expect(component.getTotalCheckedCount).toBeTruthy();
  });

  it('execute doSignWithImage', () => {
    const message = {};
    const imageData = '';
    spyOn(httpService, 'doPost').and.returnValue(of({ success: true, content: [] }));
    Object.defineProperty(component, 'chatMessages', { value: [{ sign: [] }] });
    component.doSignWithImage(message, 0, imageData);
    expect(component.doSignWithImage).toBeTruthy();
  });

  it('execute presentTagModal: default', () => {
    const message = {};
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    component.presentTagModal(message, 0);
    expect(component.presentTagModal).toBeTruthy();
  });

  it('execute presentTagModal: selectMultiple', () => {
    const message = { tagedItems: [{ id: '3' }] };
    modalSpy.onDidDismiss.and.resolveTo({ data: { selectMultiple: [] } });
    Object.defineProperty(component, 'chatRoomPatients', { value: [{ userId: 1, displayName: 'ds' }] });
    component.multiTagSelectionIsActive = false;
    component.presentTagModal(message, 0);
    expect(component.presentTagModal).toBeTruthy();
  });

  it('execute isMessageSending: false', () => {
    const isMessageSending = component.isMessageSending('1');
    expect(isMessageSending).toBeFalse();
  });

  it('execute isMessageSending: true for guid format id', () => {
    const isMessageSending = component.isMessageSending('sdjfh-dsjhfsd');
    expect(isMessageSending).toBeTrue();
  });

  it('execute presentSignatureModal', fakeAsync(() => {
    const message = {};
    component.isiPad = false;
    modalSpy.onDidDismiss.and.resolveTo({ data: {} });
    spyOn(sharedService.platform, 'is').and.returnValue(true);
    component.presentSignatureModal(message, 0);
    tick(2000);
    expect(component.presentSignatureModal).toBeTruthy();
  }));

  it('execute updateConfigPermissions: Do not reply', () => {
    sharedService.userData.config.message_reply_timeout = '120';
    component.selectedMessageData = { ...selectMessageData, sent: '3424320' };
    sharedService.userData.group = '3';
    sharedService.userData.config.restrict_to_branch_hour = '';
    component.updateConfigPermissions();
    expect(component.updateConfigPermissions).toBeTruthy();
  });

  it('execute updateConfigPermissions: Chat on restrict branch hour', () => {
    sharedService.userData.config.message_reply_timeout = '120';
    component.selectedMessageData = { ...selectMessageData, sent: '3424320' };
    sharedService.userData.group = '3';
    sharedService.userData.config.restrict_to_branch_hour = '1';
    component.updateConfigPermissions();
    expect(component.updateConfigPermissions).toBeTruthy();
  });

  it('execute updateConfigPermissions: double verification', () => {
    sharedService.userData.config.message_reply_timeout = '10';
    component.selectedMessageData = { ...selectMessageData, sent: '1832984300', messageType: null };
    sharedService.userData.group = '3';
    sharedService.userData.siteConfigs.working_hour = '1';
    sharedService.userData.config.enable_double_verification = '1';
    sharedService.userData.config.restrict_to_branch_hour = '';
    component.updateConfigPermissions();
    expect(component.updateConfigPermissions).toBeTruthy();
  });

  it('messageTranslate function should defined', () => {
    const res = {
      data: {
        translations: 'translateText'
      }
    };
    component.chatMessages = [];
    component.chatMessages = chatMessages;
    chatMessages[0].language = 'it-ch';
    spyOn(httpService, 'doPost').and.returnValue(of(res));
    component.messageTranslate();
    expect(component.messageTranslate).toBeTruthy();
  });

  it('execute messageTranslate: throwError', () => {
    spyOn(httpService, 'doPost').and.returnValue(throwError(''));
    component.chatMessages = [];
    component.chatMessages = chatMessages;
    component.chatMessages[0].message = '';
    component.messageTranslate();
    expect(component.messageTranslate).toBeDefined();
  });
  it('presentActionSheet function should defined', () => {
    const param = {
      sign: 'false',
      userid: 1
    };
    component.currentUser = 1;
    component.enableSign = true;
    Object.defineProperty(sharedService, 'platformValue', { value: of('mobile') });
    component.presentActionSheet(param, 1);
    expect(component.presentActionSheet).toBeTruthy();
  });
  it('presentActionSheet function check tagSign', () => {
    const param = {
      sign: 'true',
      userid: 1,
      tagSign: 'false'
    };
    component.enableTagging = true;
    component.presentActionSheet(param, 1);
    expect(component.presentActionSheet).toBeTruthy();
  });
  it('initSocketEvents function', () => {
    component.roomId = 32;
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ args: { chatroomId: '43' } }]));
    component.initSocketEvents();
    expect(component.initSocketEvents).toBeTruthy();
  });

  it('execute switchApplessMode function', fakeAsync(() => {
    component.roomId = 32;
    component.selectedMessageData = { createdBy: '3' };
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doPost').and.returnValue(of({ chatroomId: '1' }));
    component.switchApplessMode();
    expect(component.switchApplessMode).toBeTruthy();
  }));
  it('should return true if the deleted time is within 30 minutes', () => {
    const deletedTime = moment().subtract(20, 'minutes').format(Constants.dateFormat.mmddyyhma);
    expect(component.isUndoAvailable(deletedTime)).toBeTrue();
  });

  it('should return true if the deleted time is less than 30 minutes ago', () => {
    const deletedTime = moment().subtract(29, 'minutes').format(Constants.dateFormat.mmddyyhma);
    expect(component.isUndoAvailable(deletedTime)).toBeTrue();
  });

  it('should delete or undo message', () => {
    spyOn(component, 'fetchUpdatedChatMessages');
    spyOn(httpService, 'doPut').and.returnValue(of({ status: true, message: 'Success', messageId: 1 }));
    component.deleteOrUndoMessage({ id: 1 }, 0, 'delete');
    expect(httpService.doPut).toHaveBeenCalled();
    expect(component.fetchUpdatedChatMessages).toHaveBeenCalledWith(1);
  });

  it('should got toelse if  fetchUpdatedChatMessages call not success', () => {
    spyOn(component, 'fetchUpdatedChatMessages');
    spyOn(httpService, 'doPut').and.returnValue(of({ status: false, message: 'Success', messageId: 1 }));
    component.deleteOrUndoMessage({ id: 1 }, 0, 'delete');
    expect(httpService.doPut).toHaveBeenCalled();
  });

  it('should get deleted date time', () => {
    const dateTime = '2023-10-10T10:10:10Z';
    const result = component.getDeletedDateTime(dateTime);
    expect(result).toBe(moment(dateTime).format(Constants.dateFormat.mmddyyhma));
  });

  it('should call deleteUndoMessageConfirm ', fakeAsync(() => {
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(component, 'deleteOrUndoMessage');
    component.deleteUndoMessageConfirm({ message: 1 }, 0, 'delete');
    tick(1000);
    expect(component.deleteOrUndoMessage).toHaveBeenCalled();
  }));

  it('should not update chatMessages if response content is not present', () => {
    const messageID = 1;
    const res = {
      content: []
    };
    spyOn(component.messageCenterService, 'fetchChatMessages').and.returnValue(of(res));
    component.chatMessages = [
      {
        id: '1',
        message: 'Old message',
        userid: '',
        sent: '',
        displayName: '',
        avatar: '',
        sign: ''
      }
    ];
    component.fetchUpdatedChatMessages(messageID);
    expect(component.chatMessages[0].message).toBe('Old message');
  });

  it('should not update chatMessages if message is not found in chatMessages', () => {
    const messageID = 1;
    const res = {
      content: [{ id: 2, message: 'Updated message' }]
    };
    spyOn(component.messageCenterService, 'fetchChatMessages').and.returnValue(of(res));
    component.chatMessages = [
      {
        id: '1',
        message: 'Old message',
        userid: '',
        sent: '',
        displayName: '',
        avatar: '',
        sign: ''
      }
    ];
    component.fetchUpdatedChatMessages(messageID);
    expect(component.chatMessages[0].message).toBe('Old message');
  });
  it('execute switchApplessMode function: No Privilege', fakeAsync(() => {
    component.roomId = 32;
    component.selectedMessageData = { createdBy: '4' };
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doPost').and.returnValue(of({ chatroomId: '1' }));
    component.switchApplessMode();
    expect(component.switchApplessMode).toBeTruthy();
  }));

  it('execute switchApplessMode function: Unexpected error', fakeAsync(() => {
    component.roomId = 32;
    component.selectedMessageData = { createdBy: '3' };
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doPost').and.returnValue(throwError({ data: { errors: [{ message: 'Unexpected Error' }] } }));
    component.switchApplessMode();
    expect(component.switchApplessMode).toBeTruthy();
  }));

  it('showMessageFailedPopover function', () => {
    component.showMessageFailedPopover();
    expect(component.showMessageFailedPopover).toBeTruthy();
  });

  it('execute initSocketEvents', () => {
    component.roomId = 1;
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ chatroomId: '1' }]));
    component.initSocketEvents();
    expect(component.initSocketEvents).toBeTruthy();
  });

  it('execute ngOnInit socket offline', () => {
    component.roomId = 1;
    socketService.socketStatusUpdated.next(null);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('should call replyMessage and update the message property', () => {
    const mockMessage = {
      id: 1,
      displayName: 'John Doe',
      sent: Date.now(),
      message: 'Message Data!'
    };
    const spy = spyOn(component, 'replyMessage').and.callThrough();
    component.replyMessage(mockMessage);

    expect(spy).toHaveBeenCalled();
    expect(component.message).toContain('Message Data!');
  });

  it('should create the correct DOM structure when replyMessage is called', () => {
    const mockMessage = {
      id: 1,
      displayName: 'John Doe',
      sent: Date.now(),
      message: 'Message Data!'
    };

    component.replyMessage(mockMessage);

    expect(component.replyMessage).toBeTruthy();
  });

  it('execute ngOnInit socket online', () => {
    component.roomId = 1;
    socketService.socketStatusUpdated.next(null);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('execute ngOnInit app minimized', () => {
    component.roomId = 1;
    sharedService.appMinimized.next(true);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('execute ngOnInit app resume', () => {
    component.roomId = 1;
    sharedService.appMinimized.next(false);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  describe('priorityActionSheet', () => {
    it('should call priorityActionSheet with type = priority and isFilter = false', () => {
      component.priorityActionSheet('priority', false);
      expect(component.priorityActionSheet).toBeDefined();
      expect(component.messagePriority).toBe(2);
    });

    it('should call priorityActionSheet with type = priority and isFilter = true', () => {
      component.priorityActionSheet('priority', false);
      expect(component.priorityActionSheet).toBeDefined();
      expect(component.priorityFilterValue).toBe(0);
    });

    it('should call priorityActionSheet with type = flag and isFilter = false', () => {
      component.priorityActionSheet('flag', false);
      expect(component.priorityActionSheet).toBeDefined();
      expect(component.messagePriority).toBe(2);
    });

    it('should call priorityActionSheet with type = flag and isFilter = true', () => {
      component.priorityActionSheet('flag', true);
      expect(component.priorityActionSheet).toBeDefined();
      expect(component.messagePriority).toBe(2);
    });
  });
  describe('selectedFile', () => {
    it('should call selectedFile with max file upload size', () => {
      const mockData = {
        target: {
          files: [
            {
              name: 'download.svg',
              lastModifiedDate: 'Wed Mar 23 2022 16:39:31 GMT+0530 (India Standard Time) {}',
              size: 209715201,
              type: 'image/svg'
            }
          ]
        }
      };
      component.selectedFile(mockData);
      expect(common.showMessage).toHaveBeenCalledWith('ERROR_MESSAGES.FILE_UPLOADSIZE');
    });

    it('should call selectedFile with invalid file', () => {
      const mockData = {
        target: {
          files: [
            {
              name: 'download.svg',
              lastModifiedDate: 'Wed Mar 23 2022 16:39:31 GMT+0530 (India Standard Time) {}',
              size: 215201,
              type: 'image/svg'
            }
          ]
        }
      };
      component.selectedFile(mockData);
      expect(common.showMessage).toHaveBeenCalledWith('ERROR_MESSAGES.FILE_TYPE_NOT_ALLOWED');
    });

    it('should call selectedFile with max file length', () => {
      const mockData = {
        target: {
          files: []
        }
      };

      for (let i = 0; i <= 11; i++) {
        mockData.target.files.push({
          name: 'download.png',
          lastModifiedDate: 'Wed Mar 23 2022 16:39:31 GMT+0530 (India Standard Time) {}',
          size: 215201,
          type: 'image/png'
        });
      }
      component.selectedFile(mockData);
      expect(common.showMessage).toHaveBeenCalledWith('ERROR_MESSAGES.MAX_FILE_LENGTH');
    });
  });

  it('should return true if messageType is broadcast', () => {
    component.messageType = Number(Constants.messageListTypes.broadcast);
    expect(component.isBroadcast).toBeTrue();
  });

  it('should return false if messageType is not broadcast', () => {
    component.messageType = Number(Constants.messageListTypes.general);
    expect(component.isBroadcast).toBeFalse();
  });

  it('should fetch chat messages successfully', () => {
    const response = {
      success: true,
      content: [
        {
          id: 101,
          message: 'Test message',
          userid: 1,
          sent: **********,
          displayName: 'User 1'
        }
      ],
      messageType: '0',
      subject: 'Test Subject',
      title: 'Test Title',
      createdBy: '1',
      baseId: '0',
      messageGroupId: '0'
    };
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchChatMessages(false);
    expect(component.chatMessages.length).toBeGreaterThan(1);
    expect(component.chatMessages[0].message).toBe('Test message');
  });

  it('should handle error while fetching chat messages', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(() => new Error('Error')));
    component.fetchChatMessages();
    expect(router.navigate).toHaveBeenCalledWith([component.isArchivedMessage ? PageRoutes.archivedMessage : PageRoutes.activeMessages]);
  });

  it('should show download icon for messages with images', () => {
    const message = {
      message: '<img src="image1.jpg"><br><img src="image2.jpg">'
    };
    const result = component.showDownloadIcon(message);
    expect(result.message).toContain('<ion-icon name="cloud-download"');
  });

  it('should get message history and present info modal', () => {
    const message = { id: 1 };
    const deliveredUsers = [];
    const readUsers = [];
    const res = {
      data: {
        getDeleteUndoHistoryByMessageId: {
          deleteUndoHistory: [{ action: 'delete', timestamp: '2023-10-01T00:00:00Z' }]
        }
      }
    };

    const apolloResponse = {
      data: res.data,
      loading: false,
      networkStatus: 7
    };
    spyOn(graphqlService, 'getMessageDeleteUndoHistory').and.returnValue(of(apolloResponse));
    spyOn(component, 'presentInfoModal');

    component.getMessageHistory(message, deliveredUsers, readUsers);

    expect(graphqlService.getMessageDeleteUndoHistory).toHaveBeenCalledWith({ messageId: message.id });
    expect(component.presentInfoModal).toHaveBeenCalledWith(
      { ...message, deleteUndoHistory: res.data.getDeleteUndoHistoryByMessageId.deleteUndoHistory },
      deliveredUsers,
      readUsers
    );
  });

  it('should not show download icon for messages without images', () => {
    const message = {
      message: 'This is a text message'
    };
    const result = component.showDownloadIcon(message);
    expect(result.message).not.toContain('<ion-icon name="cloud-download"></ion-icon>');
  });

  it('initSocketEvents function', () => {
    component.roomId = 32;
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ args: { chatroomId: '43' } }]));
    component.initSocketEvents();
    expect(component.initSocketEvents).toBeTruthy();
  });

  it('should initialize appMinimizedSubscription in ngOnInit', () => {
    component.ngOnInit();
    expect(component.appMinimizedSubscription).toBeDefined();
  });

  it('should set appMinimized when appMinimizedSubscription emits a value', () => {
    component.ngOnInit();
    sharedService.appMinimized.next(true);
    expect(component.appMinimized).toBeTrue();

    sharedService.appMinimized.next(false);
    expect(component.appMinimized).toBeFalse();
  });

  it('should unsubscribe from appMinimizedSubscription in ngOnDestroy', () => {
    component.ngOnInit();
    spyOn(component.appMinimizedSubscription, 'unsubscribe');

    component.ngOnDestroy();
    expect(component.appMinimizedSubscription.unsubscribe).toHaveBeenCalled();
  });

  it('should call leaveChatRoom when app is minimized', () => {
    spyOn(component, 'leaveChatRoom');

    component.ngOnInit();
    sharedService.appMinimized.next(true);

    expect(component.leaveChatRoom).toHaveBeenCalled();
  });
  it('should add download icon to messages with images', () => {
    const message = {
      message: '<img src="test.jpg" data-src="test.jpg" data-mediatype="image">',
      data: '<img src="test.jpg" data-src="test.jpg" data-mediatype="image">'
    };
    const result = component.showDownloadIcon(message);
    expect(result.message).toContain('show-download-options');
    expect(result.data).toContain('show-download-options');
  });

  it('should initialize component properties correctly', () => {
    expect(component.currentUser).toBe(3);
    expect(component.imageUrlPrefix).toContain('cometchat/writable/filetransfer/uploads');
    expect(component.filterOptions).toBe('');
    expect(component.patientSiteId).toEqual([]);
    expect(component.sharedService.disableSideMenu).toBeFalse();
  });

  it('should call metaDataSet, uploadFileToCmis, and emitEvent', () => {
    component.selectedFileNames = [{ chatRoomId: 'chatRoomId', fileName: 'fileName' }];
    const filesAttached = [{ name: 'file1' }];
    const uniqueId = 'uniqueId';
    const mockMetaData = { key: 'value' };
    const mockFileData = {
      data: {
        uploadFileToCmis: {
          cmisFileData: JSON.stringify({
            file1: JSON.stringify({ status: Constants.apiStatusCode, results: { organizationId: 'org1' } })
          })
        }
      }
    };

    spyOn(component, 'metaDataSet').and.callFake((chatRoomId, callback) => callback(mockMetaData));
    const uploadFileToCmisSpy = spyOn(graphqlService, 'uploadFileToCmis').and.returnValue(of(mockFileData));
    spyOn(sendBulkMessageService, 'getFileUrl').and.returnValue({ fileUrl: 'fileUrl' });
    spyOn(sendBulkMessageService, 'fileFormatTypeTofileTag').and.callFake((file, fileUrl, fileName, fileType, fileType2, callback) => {
      callback({ downloadIcon: 'downloadIcon', message: 'message' });
    });
    spyOn(component, 'sentPushNotification');

    component.fileUploadToCmis(filesAttached, uniqueId);

    expect(component.metaDataSet).toHaveBeenCalled();
    expect(graphqlService.uploadFileToCmis).toHaveBeenCalledWith(Constants.fileUniqueName, JSON.stringify(mockMetaData));
    expect(component.sentPushNotification).toHaveBeenCalled();
    // Manually check the arguments for uploadFileToCmis
    const uploadFileToCmisArgs = uploadFileToCmisSpy.calls.mostRecent().args;
    expect(uploadFileToCmisArgs[0]).toBe(Constants.fileUniqueName);
    expect(uploadFileToCmisArgs[1]).toBe(JSON.stringify(mockMetaData));
  });
  it('should upload files to CMIS and handle the response correctly', () => {
    component.selectedFileNames = [{ chatRoomId: 'chatRoomId', fileName: 'fileName' }];
    const filesAttached = [{ name: 'file1.txt', type: 'text/plain' }];
    const uniqueId = 'unique-id';
    const mockMetaData = { key: 'value' };
    const mockResponse = {
      data: {
        uploadFileToCmis: {
          cmisFileData: JSON.stringify({
            '0': JSON.stringify({
              status: 200,
              results: {
                organizationId: 'org-id',
                attributes: {
                  data: [{ fileType: 'text', displayName: 'file1.txt' }]
                }
              }
            })
          })
        }
      }
    };
    component.roomId = 123;
    spyOn(component, 'metaDataSet').and.callFake((chatRoomId, callback) => callback(mockMetaData));
    spyOn(graphqlService, 'uploadFileToCmis').and.returnValue(of(mockResponse));
    spyOn(sendBulkMessageService, 'getFileUrl').and.returnValue({ fileUrl: 'fileUrl' });
    spyOn(sendBulkMessageService, 'fileFormatTypeTofileTag').and.callFake((file, fileUrl, fileName, fileType, fileType2, callback) => {
      callback({ downloadIcon: 'downloadIcon', message: 'message' });
    });

    spyOn(component, 'sentPushNotification');

    component.fileUploadToCmis(filesAttached, uniqueId);

    expect(component.metaDataSet).toHaveBeenCalled();
    expect(graphqlService.uploadFileToCmis).toHaveBeenCalledWith(Constants.fileUniqueName, JSON.stringify(mockMetaData));
    expect(component.sentPushNotification).toHaveBeenCalled();
    expect(sendBulkMessageService.getFileUrl).toHaveBeenCalled();
    expect(sendBulkMessageService.fileFormatTypeTofileTag).toHaveBeenCalled();
  });
  it('should upload files to CMIS and handle the response correctly with having messageLocal data', () => {
    component.selectedFileNames = [{ chatRoomId: 'chatRoomId', fileName: 'fileName' }];
    component.messageLocal = 'messageLocal';
    const filesAttached = [{ name: 'file1.txt', type: 'text/plain' }];
    const uniqueId = 'unique-id';
    const mockMetaData = { key: 'value' };
    const mockResponse = {
      data: {
        uploadFileToCmis: {
          cmisFileData: JSON.stringify({
            '0': JSON.stringify({
              status: 200,
              results: {
                organizationId: 'org-id',
                attributes: {
                  data: [{ fileType: 'text', displayName: 'file1.txt' }]
                }
              }
            })
          })
        }
      }
    };
    component.roomId = 123;
    spyOn(component, 'metaDataSet').and.callFake((chatRoomId, callback) => callback(mockMetaData));
    spyOn(graphqlService, 'uploadFileToCmis').and.returnValue(of(mockResponse));
    spyOn(sendBulkMessageService, 'getFileUrl').and.returnValue({ fileUrl: 'fileUrl' });
    spyOn(sendBulkMessageService, 'fileFormatTypeTofileTag').and.callFake((file, fileUrl, fileName, fileType, fileType2, callback) => {
      callback({ downloadIcon: 'downloadIcon', message: 'message' });
    });

    spyOn(component, 'sentPushNotification');

    component.fileUploadToCmis(filesAttached, uniqueId);

    expect(component.metaDataSet).toHaveBeenCalled();
    expect(graphqlService.uploadFileToCmis).toHaveBeenCalledWith(Constants.fileUniqueName, JSON.stringify(mockMetaData));
    expect(component.sentPushNotification).toHaveBeenCalled();
    expect(sendBulkMessageService.getFileUrl).toHaveBeenCalled();
    expect(sendBulkMessageService.fileFormatTypeTofileTag).toHaveBeenCalled();
  });
  it('changeChatFilter function', () => {
    component.filteredTags = [{ id: '1', name: 'tag1' }];
    component.priorityFilterValue = 0;
    component.flagFilterValue = 0;
    component.showMentionMessages = false;
    component.changeChatFilter('0');
    expect(component.changeChatFilter).toBeTruthy();
  });
  const  messageListMockData = [
    {
      loading: false,
      chatAvatar: 'test',
      chatHeading: 'test',
      chatParticipantCount: 2,
      chatParticipants: [],
      chatSubHeading: 'test',
      chatSubject: 'test',
      chatroomFlag: 1,
      chatroomId: 1,
      deliveryTime: '100000',
      hasUnreadMessages: false,
      initiatorName: 'User1',
      isAcExist: false,
      isPatientExist: false,
      isPatientInitiatedChat: false,
      isPdg: false,
      isSelfArchived: false,
      isSelfMessage: false,
      maskedSubCount: 0,
      maskedUnreadCount: 0,
      mentionedUsers: 0,
      message: 'test',
      messageCategory: MessageCategory.GENERAL,
      messageFlag: 0,
      messageForwarded: 'Forwarded',
      messageMentionRead: false,
      messageMentionUnread: false,
      messageOrder: '1000000',
      messagePriorityRead: 0,
      messagePriorityUnread: 0,
      messageTagList: [],
      messageType: 1,
      messageUnreadCount: 0,
      messagesCount: 1,
      pinnedStatus: false,
      priorityId: 1,
      repliedTo: '',
      unreadCount: 0,
      message_group_id: '1',
      createdby: '1',
      baseId: '0',
      invited_status: ''
    }
  ];
  it('should call doForwardMessage, fetchRoomUsers, and sentPushNotification when success is true', () => {
    component.roomId = 1;
    component.messageListIndex = 0;
    component.selectedMessageData = { selectedTenantId: '1' };
    component.forwardData = {
      chatroomId: undefined,
      rerouteBehaviour: Constants.messageForwardingBehaviourOptions.keepForwarder,
      forwardUser: 'User'
    };
    spyOn(component.messageCenterService, 'doForwardMessage').and.callFake((data, callback) => callback({ success: true }));
    spyOn(component, 'fetchRoomUsers');
    spyOn(common, 'getTranslateData').and.returnValue('Forwarded to');
    spyOn(component.sharedService, 'sentPushNotification');
    component.sharedService.messageList = messageListMockData;
    component.doForward();

    expect(component.messageCenterService.doForwardMessage).toHaveBeenCalledWith(
      { forwardData: { ...component.forwardData, chatroomId: component.roomId } },
      jasmine.any(Function)
    );
    expect(component.fetchRoomUsers).toHaveBeenCalled();
    expect(component.sharedService.messageList[component.messageListIndex].messageForwarded).toBe('Forwarded to User');
    expect(component.sharedService.sentPushNotification).toHaveBeenCalled();
  });

  it('should call doForwardMessage and remove message from messageList when success is true and rerouteBehaviour is removeForwarder', () => {
    component.roomId = 1;
    component.messageListIndex = 0;
    component.sharedService.messageList = messageListMockData;
    component.selectedMessageData = { selectedTenantId: '1' };
    component.forwardData = { chatroomId: undefined, rerouteBehaviour: Constants.messageForwardingBehaviourOptions.removeForwarder };
    spyOn(component.messageCenterService, 'doForwardMessage').and.callFake((data, callback) => callback({ success: true }));
    spyOn(component, 'fetchRoomUsers');
    spyOn(component.sharedService.messageList, 'splice');
    component.doForward();

    expect(component.messageCenterService.doForwardMessage).toHaveBeenCalledWith(
      { forwardData: { ...component.forwardData, chatroomId: component.roomId } },
      jasmine.any(Function)
    );
    expect(component.fetchRoomUsers).toHaveBeenCalled();
    expect(component.sharedService.messageList.splice).toHaveBeenCalledWith(component.messageListIndex, 1);
  });

  it('should not call fetchRoomUsers or sentPushNotification when success is false', () => {
    component.roomId = 1;
    component.messageListIndex = 0;
    component.forwardData = {
      chatroomId: undefined,
      rerouteBehaviour: Constants.messageForwardingBehaviourOptions.keepForwarder,
      forwardUser: 'User'
    };
    spyOn(component.messageCenterService, 'doForwardMessage').and.callFake((data, callback) => callback({ success: false }));
    spyOn(component, 'fetchRoomUsers');
    spyOn(component.sharedService, 'sentPushNotification');

    component.doForward();

    expect(component.messageCenterService.doForwardMessage).toHaveBeenCalledWith(
      { forwardData: { ...component.forwardData, chatroomId: component.roomId } },
      jasmine.any(Function)
    );
    expect(component.fetchRoomUsers).not.toHaveBeenCalled();
    expect(component.sharedService.sentPushNotification).not.toHaveBeenCalled();
  });

  describe('isVirtualPatient', () => {
    it('should return true if user is a virtual patient', () => {
      component.sharedService.userData.group = UserGroup.PATIENT.toString();
      component.sharedService.userData.isVirtual = true;
      expect(component.isVirtualPatient).toBeTrue();
    });

    it('should return false if user is not a virtual patient', () => {
      component.sharedService.userData.group = UserGroup.PATIENT.toString();
      component.sharedService.userData.isVirtual = false;
      expect(component.isVirtualPatient).toBeFalse();
    });

    it('should return false if user group is not patient', () => {
      component.sharedService.userData.group = UserGroup.PARTNER.toString();
      component.sharedService.userData.isVirtual = true;
      expect(component.isVirtualPatient).toBeFalse();
    });
  });
  describe('handleAppLessInappMode', () => {
    it('should set isVirtualUserExist to true if virtual user exists ', () => {
      const data = {
        allParticipants: [
          { userId: '1', status: '1', is_enrolled: 0 },
          { userId: '2', status: '1', is_enrolled: 0 },
          { userId: '3', status: '1', is_enrolled: 1 }
        ]
      };
      component.currentUser = 3;
      component.selectedMessageData = { createdBy: '3', appLessMode: false };

      component.handleAppLessAndInAppMode(data);

      expect(component.isVirtualUserExist).toBeTrue();
    });

    it('should set isVirtualUserExist to false if participants are enrolled', () => {
      const data = {
        allParticipants: [
          { userId: '1', status: '1', is_enrolled: 1 },
          { userId: '2', status: '1', is_enrolled: 1 },
          { userId: '3', status: '1', is_enrolled: 1 }
        ]
      };
      component.currentUser = 3;
      component.selectedMessageData = { createdBy: '2', appLessMode: false };

      component.handleAppLessAndInAppMode(data);

      expect(component.isVirtualUserExist).toBeFalse();
    });
  });
});
