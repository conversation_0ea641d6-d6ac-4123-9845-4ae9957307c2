<div class="read-popover">
  <div class="read-head">Read by {{ users?.length }} user(s)</div>
  <ul class="popover-list">
    <li *ngFor="let user of users" class="user-list-section">
      <div class="avatar">
        <img
          appAvatar
          #avatarDirective="avatarDirective"
          [src]="avatarDirective.avatarSrc"
          (error)="avatarDirective.onImageError($event)"
          [avatar]="user?.avatar"
          [userType]="user?.userType"
          [hasFullAvatarUrl]="false"
          [thumbnail]="true"
          alt="avatar"
        />
      </div>
      <div class="name">
        {{ user.displayName }}&nbsp;
        <time>{{ user.lastactivity * 1000 | shortDateTime }}</time>
      </div>
    </li>
  </ul>
</div>
