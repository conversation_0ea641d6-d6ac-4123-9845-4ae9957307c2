import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { ChatPageRoutingModule } from './chat-routing.module';
import { ChatPage } from './chat.page';
import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { InviteComponentModule } from '../invite/invite.module';
import { ForwardComponentModule } from '../forward/forward.module';
import { ForwardBehaviourComponentModule } from '../forward-behaviour/forward-behaviour.module';
import { SignatureComponentModule } from '../../../components/signature/signature.module';
import { ChatRoomUsersComponent } from './chatroom-users/chatroom-users.component';
import { TagComponentModule } from '../tag/tag.module';
import { EmojiPopoverComponent } from './emoji-popover/emoji-popover.component';
import { ReadPopoverComponent } from './read-popover/read-popover.component';
import { SubjectPopoverComponent } from './subject-popover/subject-popover.component';
import { AdvancedViewerComponentModule } from 'src/app/components/advanced-viewer/advanced-viewer.module';
import { MsgInfoModalComponent } from './msg-info-modal/msg-info-modal.component';
import { SkeletonLoaderModule } from 'src/app/components/skeleton-loader/skeleton-loader.module';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ChatPageRoutingModule,
    SharedModule,
    TranslateModule,
    InviteComponentModule,
    ForwardComponentModule,
    ForwardBehaviourComponentModule,
    SignatureComponentModule,
    TagComponentModule,
    HeaderPlainModule,
    AdvancedViewerComponentModule,
    SkeletonLoaderModule
  ],
  declarations: [ChatPage, ChatRoomUsersComponent, EmojiPopoverComponent, ReadPopoverComponent, SubjectPopoverComponent, MsgInfoModalComponent]
})
export class ChatPageModule {}
