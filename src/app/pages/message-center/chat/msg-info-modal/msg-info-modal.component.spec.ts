import { SafePipe } from 'src/app/pipes/safe.pipe';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';
import { MsgInfoModalComponent } from 'src/app/pages/message-center/chat/msg-info-modal/msg-info-modal.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { UnicodeConvertPipe } from 'src/app/pipes/unicodeConvert/unicode-convert.pipe';

describe('MsgInfoModalComponent', () => {
  let component: MsgInfoModalComponent;
  let fixture: ComponentFixture<MsgInfoModalComponent>;
  let modalController: ModalController;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [MsgInfoModalComponent, SafePipe],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        SharedService,
        ModalController,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        {
          provide: UnicodeConvertPipe,
          useValue: {
            transform: () => {
              return '';
            }
          }
        }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    fixture = TestBed.createComponent(MsgInfoModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('execute dismiss', () => {
    spyOn(modalController, 'dismiss').and.stub();
    component.dismiss();
    expect(component.dismiss).toBeTruthy();
  });

  it('execute convertTime', () => {
    component.convertTime('**********');
    expect(component.convertTime).toBeTruthy();
  });
});
