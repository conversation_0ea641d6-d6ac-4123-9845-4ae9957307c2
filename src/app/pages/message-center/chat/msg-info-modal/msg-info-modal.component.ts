import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import * as moment from 'moment';
import { Constants } from 'src/app/constants/constants';
import { MessageDeliveredUsers } from 'src/app/interfaces/common-interface';
import { convertUTCToTimeZoneDateTimeSplit, isPresent } from 'src/app/utils/utils';

@Component({
  selector: 'app-msg-info-modal',
  templateUrl: './msg-info-modal.component.html',
  styleUrls: ['./msg-info-modal.component.scss']
})
export class MsgInfoModalComponent implements OnInit {
  message: any;
  isSentChat: boolean;
  chatroomId: string;
  deliveredUsers: MessageDeliveredUsers[];
  readUsers: MessageDeliveredUsers[];
  messageClass: string;
  constructor(private modalController: ModalController) {}
  ngOnInit(): void {
    this.messageClass = this.isSentChat ? Constants.messageSentChat : Constants.messageReceivedChat;
  }
  convertTime(time: string): string {
    const unixTime = moment.unix(Number(time));
    const format =
      unixTime.diff(moment().format(Constants.dateFormat.mmddyy), 'days') === 0 ? Constants.dateFormat.h2ma : Constants.dateFormat.mmddh2ma;
    return unixTime.format(format);
  }
  dismiss(): void {
    this.modalController.dismiss({});
  }

  getDeletedDateTime(dateTime) {
    if (isPresent(dateTime)) {
      const deletedDate = convertUTCToTimeZoneDateTimeSplit(dateTime, '', moment.tz.guess(), Constants.dateFormat.mmddyyhma);
      return deletedDate;
    }
    return '';
  }
}
