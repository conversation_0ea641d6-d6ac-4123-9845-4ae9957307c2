ion-header {
    color: black;
    ion-toolbar {
        --background: var(--ion-color-skin-main);
        color: white;
        .ion-toolbar-header {
            display: contents;
            text-align: center;
            ion-icon {
                padding: 0 9px;
                cursor: pointer;
                float: right;
            }
        }
    }
}
.message-section {
    width: 100%;
    display: flex;
    color: #58595b;

    .msg-info {
        float: left;
        font-size: 13px;
        height: 100%;

        .chat-signature {
            background: var(--ion-color-skin-secondary-bright) url(/assets/icon/chat/chat-sign-doc-icon.png) no-repeat 7px center;
            margin-top: 2px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            left: 0px;
            cursor: pointer;
            background-size: 28px auto;
        }
    }


    .msg-middle {
        flex: 1;
        width: 70%;

        .msg-content {
            float: left;
            width: 100%;
            border-bottom-right-radius: 0px;
            position: relative;
            border-radius: 5px;
            color: #ffffff;
            padding: 10px;
        }

        .chat-arrow:before {
            content: " ";
            position: absolute;
            width: 0;
            height: 0;
            border: 22px solid;
        }
    }

}

.message-section.message-sent-chat {
    .msg-middle {
        padding: 10%;

        .msg-content {
            background: var(--ion-color-chat-bg-sent);
        }

        .chat-arrow:before {
            right: -18px;
            bottom: 0px;
            border-color: transparent transparent var(--ion-color-chat-bg-sent) transparent;
        }
    }

    .msg-info {
        margin: auto;
        width: 60px;
    }

}
.message-section.message-received-chat {
    .msg-middle {
        padding: 10%;

        .msg-content {
            background: var(--ion-color-chat-bg);
        }

        .chat-arrow:before {
            left: -20px;
            top: 0px;
            border-color: var(--ion-color-chat-bg) transparent transparent transparent;
        }
    }

    .msg-end {
        font-size: 13px;
    }
}

.avatar {
    border-radius: 50%;
    border: 1px solid #e6e6e6;
    width: 50px;
    background: #e6e5eb;
    height: 50px;
    margin: 4px 8px 4px 0px;
}
.time {
    font-weight: 200;
    color: #58595b;
    font-size: 11px;
    width: 100%;
}
.checkmark {
    color: var(--ion-color-skin-secondary-bright);
}
.sub-title{
    color:var(--ion-color-skin-main);
}
.display-name {
    color: #58595b;
    font-size: 14px;
}
.history-icon {
    display: flex;
    align-items: center;
    ion-icon {
        margin-right: 5px;
    }
}

ion-item:last-child {
    --inner-border-width: 0;
  }