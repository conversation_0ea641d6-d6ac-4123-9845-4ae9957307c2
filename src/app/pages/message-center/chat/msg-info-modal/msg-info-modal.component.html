<ion-header>
  <ion-toolbar>
    <ion-grid>
      <ion-row class="ion-align-items-center">
        <ion-col class="ion-toolbar-header">
          <ion-title>{{ 'LABELS.MESSAGE_INFO' | translate }} </ion-title>
        </ion-col>
        <ion-col class="ion-toolbar-header">
          <ion-icon name="close" (click)="dismiss()"></ion-icon>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-toolbar>
</ion-header>

<ion-content *ngIf="message">
  <div class="message-section" [ngClass]="messageClass">
    <div class="msg-middle">
      <div class="msg-content chat-arrow">
        <p class="msg-cont-text">
          <span class="common-message-parse hide-download-message-info" [innerHtml]="message?.message | unicodeConvert | safe: 'html'"></span>
        </p>
      </div>
    </div>
  </div>
  <ion-card *ngIf="isSentChat && readUsers">
    <ion-card-header>
      <ion-card-subtitle class="sub-title">
        <ion-icon class="checkmark" name="checkmark-done-sharp"></ion-icon>
        {{ 'LABELS.READ_BY' | translate: { readBy: readUsers.length } }}
      </ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-item *ngFor="let user of readUsers">
        <img
          class="avatar"
          appAvatar
          #avatarDirective="avatarDirective"
          [src]="avatarDirective.avatarSrc"
          (error)="avatarDirective.onImageError($event)"
          [avatar]="user?.avatar"
          [userType]="user?.userType"
          [hasFullAvatarUrl]="false"
          [thumbnail]="true"
          alt="avatar"
        />
        <ion-row>
          <ion-label class="display-name">{{ user?.displayName }}</ion-label>
          <span class="time">{{ convertTime(user?.readTime) }}</span>
        </ion-row>
      </ion-item>
    </ion-card-content>
  </ion-card>
  <ion-card *ngIf="isSentChat && deliveredUsers">
    <ion-card-header>
      <ion-card-subtitle class="sub-title">
        <ion-icon class="checkmark" name="checkmark-sharp"></ion-icon>
        {{ 'LABELS.DELIVERED_TO' | translate: { deliveredTo: deliveredUsers.length } }}
      </ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-item *ngFor="let user of deliveredUsers">
        <img
          class="avatar"
          appAvatar
          #avatarDirective="avatarDirective"
          [src]="avatarDirective.avatarSrc"
          (error)="avatarDirective.onImageError($event)"
          [avatar]="user?.avatar"
          [userType]="user?.userType"
          [hasFullAvatarUrl]="false"
          [thumbnail]="true"
          alt="avatar"
        />
        <ion-row>
          <ion-label class="display-name">{{ user?.displayName }}</ion-label>
          <span class="time">{{ convertTime(user?.deliveryTime) }}</span>
        </ion-row>
      </ion-item>
    </ion-card-content>
  </ion-card>
  <ion-card *ngIf="!isSentChat">
    <ion-card-header>
      <ion-card-subtitle class="sub-title">{{ 'LABELS.SENT_BY' | translate }}</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-item>
        <img
          class="avatar"
          appAvatar
          #avatarDirective="avatarDirective"
          [src]="avatarDirective.avatarSrc"
          (error)="avatarDirective.onImageError($event)"
          [avatar]="message?.avatar"
          alt="avatar"
        />
        <ion-row>
          <ion-label class="display-name">{{ message?.displayName }}</ion-label>
          <span class="time">{{ convertTime(message?.sent) }}</span>
        </ion-row>
      </ion-item>
    </ion-card-content>
  </ion-card>
  <ion-card *ngIf="message?.deleteUndoHistory?.length > 0">
    <ion-card-header>
      <ion-card-subtitle class="sub-title history-icon">
        <ion-icon name="list-outline"></ion-icon>
        <span>{{ 'LABELS.MESSAGE_HISTORY' | translate }}</span>
      </ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ng-container *ngIf="message.deleteUndoHistory.length > 1; else singleDeleteBlock">
          <div *ngFor="let action of message.deleteUndoHistory; let i = index" lines="none">
            <span class="display-name">
              {{
                'LABELS.MESSAGE_DELETE_HISTORY'
                  | translate
                    : {
                        index: message.deleteUndoHistory.length - i,
                        message: action.actionMessage
                          ? action.actionMessage
                          : action.actionType === 1
                            ? ('MESSAGES.MESSAGE_RESTORED_ON' | translate)
                            : ('MESSAGES.MESSAGE_DELETED_ON' | translate),
                        deletedOn: getDeletedDateTime(action.actionTime)
                      }
              }}
            </span>
          </div>
        </ng-container>
        <ng-template #singleDeleteBlock>
          <ion-item lines="none">
            <span class="display-name">
              {{ 'LABELS.MESSAGE_WAS_DELETED' | translate: { message: message.message, deletedOn: getDeletedDateTime(message.messageDeletedTime) } }}
            </span>
          </ion-item>
        </ng-template>
      </ion-list>
    </ion-card-content>
  </ion-card>
</ion-content>
