.chat-sub-header {
    border-bottom: 1px solid #e6e6e6;
    padding: 18px 5px 10px;
    background: #fff;

    .left-sec {
        float: left;
        padding-top: 3px;
        margin-left: 5px;

        .user-select {
            margin-left: 3px;
        }

        .left-label {
            color: #858586;
            line-height: 14px;
            font-size: 13px;
        }

        select {
            font-size: 14px;
            background-color: #fff;
            color: #000;
        }
        .filter-icon {
            font-size: 32px;
            color: var(--ion-color-small-button);
        }
    }

    .middle-sec {
        position: absolute;
        text-align: center;
        width: 100%;

        ion-icon {
            font-size: 20px;
        }
    }

    .right-sec {
        float: right;
        display: flex;

        .video-join {
            font-size: 13px;
            width: 50px;
            background: var(--ion-color-skin-secondary-bright);
            color: #ffffff;
            border-radius: 30px;
            height: 23px;
            position: relative;
            right: 5px;
        }

        .count {
            border-radius: 50%;
            background-color: var(--ion-color-count-dark-bg);
            text-align: center;
            line-height: 20px;
            position: absolute;
            right: 0;
            font-size: 12px;
            min-width: 20px;
            color: var(--ion-color-count-dark-color);
            margin: -6px 8px;
        }

        img {
            min-width: 30px;
            width: 28px;
            height: 28px;
        }
    }

    ion-icon {
        font-size: 26px;
        color: var(--ion-color-button);
        margin-right: 16px;
    }

    .translate-selected {
        opacity: 0.4;
    }
}

ion-content {
    border-bottom: 1px solid #e6e6e6;
}

.chat-sub-header-subject {
    border-bottom: 0px solid #e6e6e6;
    padding: 28px 5px 0px;
    background: #fff;

    .middle-sec-subject {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #404441;
        margin: 5px auto;
        margin-left: auto;
        flex-wrap: nowrap;
        margin-bottom: 0px;
        font-weight: 650;

        .subject-text {
            color: var(--ion-color-skin-main);
            font-weight: 400;
            padding-right: 10px;
            width: 90%;
            max-width: fit-content;
            text-transform: capitalize;
        }
    }

    ion-icon {
        font-size: 18px;
        color: var(--ion-color-skin-main);
        margin-top: -2px;
        margin-left: 1px;
    }
}

.blue-button {
    button {
        background: #bccdde;
    }
}

.blue-button-dark {
    button {
        pointer-events: none;
        cursor: pointer;
        background: #4a95af;
    }
}

.list {
    padding-top: 15px;
    padding-bottom: 30px;
}

.hidden-textarea {
    cursor: pointer;
    text-align: center;
    margin: 15px;
    font-size: 15px;
    color: #58595b;
}

.above-footer-container {
    .chat-information {
        padding: 10px;
        background: #ffffff;
        border-top: 1px solid #e6e6e6;
        font-size: 15px;
        text-align: center;
        color: #58595b;

        ion-button {
            margin: 12px;
        }
    }

    .chat-normal-group {
        .attachments-preview {
            border-top: #c7c7c7 solid 1px;
            background: #ffffff;
            border-radius: 10px 10px 0 0;
            text-align: center;
            width: 100%;
            max-height: 274px;
            overflow-y: scroll;

            .file-section {
                max-width: 100px;
                display: inline-block;
                margin: 8px;
                vertical-align: middle;
                position: relative;
                padding: 5px;
                max-height: 120px;

                img {
                    vertical-align: middle;
                    box-shadow:
                        0 4px 8px 0 rgb(0 0 0 / 20%),
                        0 6px 20px 0 rgb(0 0 0 / 19%);
                    width: 90px;
                    height: 90px;
                    object-fit: contain;
                }

                .remove-file {
                    border-radius: 50%;
                    background-color: red;
                    text-align: center;
                    top: -4px;
                    height: 20px;
                    line-height: 20px;
                    position: absolute;
                    cursor: pointer;
                    right: -6px;
                    font-size: 12px;
                    min-width: 20px;
                    color: #ffffff;
                }

                .file-name {
                    display: inline-block;
                    width: 80px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: var(--ion-color-black);
                }
            }
        }
    }
}

.disable-virtual {
    pointer-events: none;
    opacity: 0.7;
    cursor: not-allowed;
}

.message-section {
    width: 100%;
    padding: 5px 0px;
    display: flex;
    color: #58595b;

    .msg-info {
        float: left;
        font-size: 13px;
        height: 100%;

        .chat-signature {
            background: var(--ion-color-skin-secondary-bright) url(/assets/icon/chat/chat-sign-doc-icon.png) no-repeat
                7px center;
            margin-top: 2px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            left: 0px;
            cursor: pointer;
            background-size: 28px auto;
        }
    }

    .msg-middle {
        display: flex;
        flex: 1;
        width: 70%;

        .msg-content {
            float: left;
            width: 100%;
            border-bottom-right-radius: 0px;
            position: relative;
            border-radius: 5px;
            color: #ffffff;
            padding: 10px;

            .msg-sign {
                display: block;
                border-top: 1px dashed rgba(0, 0, 0, 0.2);

                img {
                    width: 100px;
                    position: relative;
                    top: 5px;
                }

                .remove-chat-sign {
                    position: absolute;
                    bottom: 9px;
                    right: 10px;
                    color: #ffffff;
                    width: 28px;
                    height: 28px;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    background: var(--ion-color-skin-main);
                }
            }

            .read-check {
                float: right;
                font-size: 12px;
                color: #86d0a6;
                right: 10px;
                cursor: pointer;
            }

            .unread-span {
                float: right;
                font-size: 12px;
                color: black;
                right: 10px;
            }

            .unsend-span {
                float: right;
                font-size: 12px;
                right: 10px;
                color: var(--ion-color-socket-bg-disconnect);

                ion-icon {
                    stroke: var(--ion-color-socket-bg-disconnect);
                    stroke-width: 3px;
                }
            }

            .msg-info-icon {
                cursor: pointer;
                float: right;
                padding: 0px 10px;

                ion-icon {
                    background: var(--ion-color-skin-secondary-bright);
                    border-radius: 50%;
                }
            }

            .msg-tag-wrap {
                padding: 10px 0px 5px 10px;

                .msg-tag-element {
                    position: relative;
                    background: rgba(255, 255, 255, 0.7);
                    display: inline-block;
                    margin-right: 20px;
                    border-radius: 0px 5px 5px 0px;
                    color: #336797;
                    padding: 5px 6px 5px 12px;
                    z-index: 100;

                    .tag {
                        max-width: 100px;
                        display: block;
                        overflow: hidden;
                        margin: 0px;
                        float: left;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        font-size: 12px;
                    }

                    &:before {
                        position: absolute;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background: var(--ion-color-skin-main);
                        top: 9px;
                        left: 0px;
                        z-index: 9999;
                        content: " ";
                    }

                    &:after {
                        right: 100%;
                        top: 0;
                        border: solid transparent;
                        content: " ";
                        height: 0;
                        width: 0;
                        position: absolute;
                        pointer-events: none;
                        border-right-color: rgba(255, 255, 255, 0.7);
                        border-width: 12px;
                        margin-top: 0;
                    }
                }
            }
        }

        .chat-arrow:before {
            content: " ";
            position: absolute;
            width: 0;
            height: 0;
            border: 22px solid;
        }
    }

    .avatar {
        float: left;
        width: 40px;
        display: block;
        margin-left: 15px;
        height: 100%;

        img {
            position: absolute;
            bottom: 0;
        }
    }

    .flag-container {
        position: absolute;
        bottom: 44px;
        right: 21px;
    }

    .msg-checkbox {
        position: absolute;
        bottom: 0;
    }

    .avatar-img {
        width: 40px;
        height: 40px;
        border-radius: 100%;
        -webkit-border-radius: 100%;
        -moz-border-radius: 100%;
        -ms-border-radius: 100%;
        -webkit-touch-callout: none;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        object-fit: cover;
        background: #e6e5eb;
        border: 1px solid #e6e6e6;
    }
}

.message-sent-chat .msg-middle:has(.priority-msg) {
    padding-left: 5% !important;
}

.message-received-chat .msg-middle:has(.priority-msg) {
    padding-right: 5% !important;
}

.message-section.message-sent-chat {
    .msg-middle {
        padding-left: 10%;
        padding-right: 5px;

        .msg-content {
            background: var(--ion-color-chat-bg-sent);
        }

        .chat-arrow:before {
            right: -18px;
            bottom: 0px;
            border-color: transparent transparent var(--ion-color-chat-bg-sent) transparent;
        }
    }

    .msg-info {
        margin: auto;
        width: 60px;
    }
}

.message-section.message-received-chat {
    .msg-middle {
        padding-right: 10%;
        padding-left: 22px;

        .msg-content {
            background: var(--ion-color-chat-bg);
        }

        .chat-arrow:before {
            left: -20px;
            top: 0px;
            border-color: var(--ion-color-chat-bg) transparent transparent transparent;
        }
    }

    .msg-end {
        font-size: 13px;
    }
}

.message-section.message-activity {
    .msg-middle {
        padding-right: 5%;
        padding-left: 62px;

        .msg-content {
            background: #bbdde8;
        }
    }

    .msg-end {
        font-size: 13px;
    }
}

.multi-select-actions {
    padding: 5px;

    .multi-tag-button {
        padding: 12px;
        text-align: center;
        width: 152px;
        height: 43px;
        border-radius: 10%;
        color: #fff;
        font-size: 17px;
        font-weight: 600;

        &.cancel-button {
            background-color: #79949cba;
            float: left;
        }

        &.tag-button {
            background-color: #11c1f3;
            float: right;
        }
    }
}

.typing {
    color: #fff;
    background: rgba(20, 99, 113, 0.5);
    text-align: center;
    font-size: 13px;
    line-height: 22px;
}

.chat-controls-container {
    display: flex;
    border-top: 1px solid #e6e6e6;
    background: #fff;

    .chat-textarea {
        width: 100%;
        position: relative;
        color: #000;

        ion-textarea {
            --padding-end: 35px;
            max-height: 75px;
            overflow-y: auto;
        }

        .emoji-button {
            position: absolute;
            font-size: 25px;
            right: 10px;
            top: 11px;
            color: #adadad;
            z-index: 3;
        }
    }

    .chat-send-button {
        font-size: 22px;
        background: var(--ion-color-footer-btn);
        border-radius: 50%;
        height: 36px;
        width: 36px;
        padding: 7px 9px;
        color: #fff;
        margin: 5px;
        margin-right: 7px;
    }

    .choose-attachments-button {
        background: #848e98;
        border-radius: 50%;
        height: 34px;
        width: 34px;
        flex: none;
        margin: 5px;
    }

    ion-fab {
        position: relative;
        bottom: 3px;
        left: 0;
        margin-right: 15px;
        width: 34px;
        height: 34px;

        ion-fab-button {
            height: 34px;
            width: 34px;
        }

        ion-fab-list {
            left: -2px;
            margin-bottom: 40px;
        }
    }
}
  .status-button-container {
    text-align: right;
  }
  
.skeleton-loader {
    .left-sk {
        width: 80%;
        height: 40px;
    }

    .right-sk {
        width: 80%;
        float: right;
        height: 40px;
    }
}

.icon-language {
    font-size: 22px !important;
}

.message-content-parent {
    position: relative;
    width: 100%;

    ion-spinner {
        position: absolute;
        margin: auto;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        transform: scale(3);
        color: white;
    }
}

.image-uploading {
    background: #000;
    color: #fff;
    border-radius: 2px;
    text-align: center;
}

.overlay {
    position: absolute;
    height: 100%;
    width: 100%;
    display: block;
    z-index: 999;
    background: rgba(0, 0, 0, 0.5);
    color: #f1f1f1;
    width: 100%;
    transition: 0.5s ease;
    opacity: 1;
    font-size: 20px;
    text-align: center;
}

.checkbox-container {
    ion-checkbox {
        --checkbox-background-checked: var(--ion-color-blumine);
    }
}

.chat-button {
    ion-button {
        --background: var(--ion-color-de-york);
    }
    .ion-activated {
        --background-activated: var(--ion-color-de-york);
    }
}

app-footer {
    .footer-buttons {
        margin-bottom: 10px;

        .button-element {
            margin-right: 25px;

            ion-icon {
                display: block;
                color: var(--ion-color-skin-secondary-bright);
                font-size: 24px;
                margin: auto;
                margin-bottom: -4px;
            }

            label {
                color: var(--ion-color-skin-secondary-bright);
                font-size: 12px;
            }
        }
    }
}

.double-verification {
    .cnf-double-verificaion {
        margin: 8px auto;
        background: var(--ion-color-skin-secondary-bright);
        color: #ffffff;
        border-radius: 30px;
        height: 30px;
        line-height: 30px;
    }
}

ion-fab-list ion-icon {
    color: white;
    font-size: x-large;
    margin: 0.4rem;
}

.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.priority-msg-icon {
    position: absolute;
    margin: 0;
    margin-left: -1.5rem;
}
.mail-inbox-head {
    ion-icon {
        color: var(--ion-color-small-button) !important;
    }

    ion-icon.bg-default {
        background-color: var(--ion-color-small-button) !important;
    }

    .ion-flag-green {
        color: var(--ion-color-small-button) !important;
        font-size: 24px;
    }
    .ion-flag-blue {
        color: blue !important;
        font-size: 24px;
    }
    .ion-flag-orange {
        color: orange !important;
        font-size: 24px;
    }
    .ion-flag-red {
        color: red !important;
        font-size: 24px;
    }
}

.file-hidden {
    display: none;
}

.alert-fill.light {
    background-color: white;
}

.textarea-height {
    min-height: 6rem;
}
.chat-textarea .ion-padding-vertical {
    min-height: 25px !important;
    max-height: 75px !important;
    overflow-y: auto !important;
    padding-right: 35px !important;
    overflow-wrap: anywhere;
}

.mention-list {
    max-height: 15rem;
    overflow-y: scroll;
}

ion-item-option::part(native) {
    min-width: 80px;
}

.common-swipe-buttons .reply {
    --background: #369db3;
}

.avatar-shadow {
    box-shadow:
        0 0 3px rgba(0, 0, 0, 0.5),
        0 2px 3px rgba(0, 0, 0, 0.5);
}

.users-list-toolbar {
    --background: var(--ion-color-skin-main);
}

[contenteditable][placeholder]:empty:before {
    content: attr(placeholder);
    position: absolute;
    color: gray;
    background-color: transparent;
}

#message-textarea {
    min-height: 50px;
    user-select: text;
    -webkit-user-select: text;
    pointer-events: all !important;
    -webkit-user-modify: read-write !important;
}

:host ::ng-deep .callout {
    width: 90%;
    padding: 5px;
    border: 1px solid #849ba5;
    background: #bbddeb;
    border-left-width: 5px;
    border-radius: 3px;
    min-height: 3rem;
    max-width: 80%;
    margin-bottom: 7px;
    position: relative;
    color: #000;
    box-shadow:
        0 0 2px rgba(0, 0, 0, 0.12),
        0 1px 2px rgba(0, 0, 0, 0.14);
    small {
        margin-top: 0;
        margin-bottom: 5px;
        display: inline-block;
    }
    .callout-message {
        font-size: 14px;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        white-space: normal;
        overflow: hidden;
        max-height: 50px;
    }

    .callout-time {
        margin-left: 1rem;
        display: none;
    }
    p:last-child {
        margin-bottom: 0;
    }
    code {
        border-radius: 3px;
    }
    & + .bs-callout {
        margin-top: -5px;
    }
    .mention {
        color: inherit !important;
        font-weight: normal !important;
    }
    .close {
        position: absolute;
        top: 2px;
        right: 2px;
    }
}

:host ::ng-deep .mention {
    color: #262667;
    background: none !important;
    border: none;
    padding: 0 !important;
    &.active {
        color: #fb434a;
        font-weight: bold;
    }
}
.undo-cont-text {
    font-style: italic;
}
.msg-cont-text .undo-delete {
    float: right;
    font-size: 1.5em;
}

.ooo-message{
    background-color: #f8d7da;
}

.ooo-message a {
    text-decoration: underline;
}

.ooo-message-grey {
    background: #ededed;
}

.flip-180 {
    transform: scaleX(-1);
}
.disabled-view {
    opacity: .4;
}

