import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, PopoverController } from '@ionic/angular';
import { EmojiPopoverComponent } from './emoji-popover.component';

describe('EmojiPopoverComponent', () => {
  let component: EmojiPopoverComponent;
  let fixture: ComponentFixture<EmojiPopoverComponent>;
  let popoverController: PopoverController;
  const popupSpy = jasmine.createSpyObj('Popup', ['present', 'dismiss', 'onWillDismiss', 'close']);

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [EmojiPopoverComponent],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateTestingModule],
      providers: [
        PopoverController,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popupSpy;
    });
    popupSpy.present.and.stub();
    spyOn(popoverController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(EmojiPopoverComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('execute searchEmoji', () => {
    const text = { value: 'smile' };
    component.searchEmoji(text);
    expect(component.searchEmoji).toBeTruthy();
  });

  it('execute searchEmoji: empty text', () => {
    const text = { value: '' };
    component.searchEmoji(text);
    expect(component.searchEmoji).toBeTruthy();
  });

  it('execute chooseItem', () => {
    const emoji = {};
    component.chooseItem(emoji);
    expect(component.chooseItem).toBeTruthy();
  });

  it('execute goToEmoji', () => {
    const emoji = {
      name: 'Smileys & Emotion',
      image: 'assets/images/emojis-filter/smileys.png'
    };
    component.goToEmoji(emoji);
    expect(component.selectedFilterValue).toBe(emoji.name);
  });
});
