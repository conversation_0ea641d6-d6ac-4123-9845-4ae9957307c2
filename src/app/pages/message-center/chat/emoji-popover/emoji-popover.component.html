<div class="emoji-popover">
  <div class="intercom-composer-popover intercom-composer-emoji-popover">
    <div class="intercom-emoji-picker">
      <div class="intercom-composer-popover-header">
        <!-- TODO! CHP-3596 -->
        <ion-input type="search" #search placeholder="Search" (ionChange)="searchEmoji(search)" autocapitalize="on">
        </ion-input>
      </div>

      <div class="intercom-composer-popover-body-container">
        <div class="intercom-composer-popover-body">
          <div class="intercom-emoji-picker-groups">
            <div class="intercom-emoji-picker-group" *ngIf="recentEmojis.length>0 && !searchTerm">
              <div class="intercom-emoji-picker-group-title">Recently used</div>
              <span class="intercom-emoji-picker-emoji" [title]="emoji.name" (click)="chooseItem(emoji)"
                *ngFor="let emoji of recentEmojis">{{emoji.emoji}}</span>
            </div>
            <div class="intercom-emoji-picker-group" *ngFor="let group of emojiGroupstoShowFiltered">
              <div class="intercom-emoji-picker-group-title" [id]="group">{{group}}</div>
              <span class="intercom-emoji-picker-emoji" [title]="emoji.name" (click)="chooseItem(emoji)"
                *ngFor="let emoji of emojiJsonFiltered[group]">{{emoji.emoji}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="filterData">
        <span *ngFor="let item of emojiFilter" (click)="goToEmoji(item)"
          [class.selectedSpan]="item?.name === selectedFilterValue">
          <img [src]="item?.image" [alt]="item?.name">
        </span>
      </div>
    </div>
  </div>
</div>