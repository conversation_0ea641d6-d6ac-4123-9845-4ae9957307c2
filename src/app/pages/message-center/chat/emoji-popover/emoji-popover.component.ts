import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Constants } from 'src/app/constants/constants';
import { Component } from '@angular/core';
import { emojiJson } from 'src/app/constants/emojis';
import { EmojiResponse, MessageList } from 'src/app/interfaces/emoji-filter';

@Component({
  selector: 'app-emoji-popover',
  templateUrl: './emoji-popover.component.html',
  styleUrls: ['./emoji-popover.component.scss']
})
export class EmojiPopoverComponent {
  users = [];
  searchTerm = '';
  emojiGroupstoShow = [
    'Smileys & Emotion',
    'People & Body',
    'Animals & Nature',
    'Food & Drink',
    'Travel & Places',
    'Activities',
    'Objects',
    'Symbols',
    'Flags'
  ];
  emojiFilter;
  emojiGroupstoShowFiltered = [];
  emojiJsonFiltered = {};
  recentEmojis = [];
  emojiRecentMaxLength = 20;
  constructor(private sharedService: SharedService) {
    this.emojiFilter = Constants.emojiFilter;
    this.emojiGroupstoShowFiltered = this.emojiGroupstoShow;
    this.emojiJsonFiltered = emojiJson;
    this.recentEmojis = JSON.parse(localStorage.getItem(Constants.storageKeys.recentEmojis)) || [];
  }

  searchEmoji(text: any): void {
    this.emojiJsonFiltered = Object.assign({}, emojiJson);
    this.emojiGroupstoShowFiltered = Object.assign([], this.emojiGroupstoShow);
    this.searchTerm = text.value;
    if (!this.searchTerm) {
      return;
    }
    this.emojiGroupstoShow.forEach((element: string, index: number) => {
      this.emojiJsonFiltered[element] = emojiJson[element].filter((form: MessageList) => {
        const valueKey = form.name;
        if (valueKey && this.searchTerm) {
          if (valueKey.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1) {
            return true;
          }
          return false;
        }
      });
      if (this.emojiJsonFiltered[element].length < 1) {
        this.emojiGroupstoShowFiltered.splice(index, 1);
      }
    });
  }
  selectedFilterValue: string;
  chooseItem(emoji: MessageList): void {
    this.sharedService.emojiValueGet.next(emoji);
    const index = this.recentEmojis.findIndex((x) => x.emoji === emoji.emoji);
    if (index !== -1) {
      this.recentEmojis.splice(index, 1);
    }
    this.recentEmojis.unshift(emoji);
    this.recentEmojis.length =
      this.recentEmojis.length < this.emojiRecentMaxLength ? this.recentEmojis.length : this.emojiRecentMaxLength;
    localStorage.setItem(Constants.storageKeys.recentEmojis, JSON.stringify(this.recentEmojis));
  }

  goToEmoji(item: EmojiResponse): void {
    document.getElementById(item.name).scrollIntoView();
    this.selectedFilterValue = item.name;
  }
}
