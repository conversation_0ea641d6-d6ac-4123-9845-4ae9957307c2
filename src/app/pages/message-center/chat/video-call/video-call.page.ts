import { CommonService } from 'src/app/services/common-service/common.service';
import { Subscription } from 'rxjs';
import { VideoCall } from 'src/app/constants/video-call';
import { Component, OnInit } from '@angular/core';
import { isBlank } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';
import VidyoPlatform from 'src/app/vidyo-platform-plugin';
import { VideoCallService } from '../../../../services/video-call/video-call.service';

@Component({
  selector: 'app-video-call',
  templateUrl: './video-call.page.html',
  styleUrls: ['./video-call.page.scss']
})
export class VideoCallPage implements OnInit {
  videoChanged = null;
  videoCallEndSubs = null;
  vidyoClientSubscribe = null;
  connectionMessage: string;
  private userLeftSubs: Subscription;
  microphonePrivacy = false;
  cameraPrivacy = false;
  constructor(
    public videoCallService: VideoCallService,
    private common: CommonService
  ) {
    this.connectionMessage = 'VIDEO_MESSAGES.CONNECTING';
  }

  ngOnInit(): void {
    const getHTMLElement = document.getElementsByTagName('html');
    if (getHTMLElement.length > 0) {
      getHTMLElement[0].classList.add('ios-video-call-set-background');
    }
    if (isBlank(this.videoChanged) || (this.videoChanged && this.videoChanged.closed)) {
      this.videoChanged = this.videoCallService.videoChanged.subscribe((eventData) => {
        this.videoCallService.playAudioForCall.emit({
          playSound: false,
          action: ''
        });
        if (eventData && eventData[0] === VideoCall.noRemoteParticipant && !this.videoCallService.participant) {
          this.showVideoAlert(this.common.getTranslateData('VIDEO_MESSAGES.ALL_USER_LEFT'));
        }
      });
    }
    if (isBlank(this.videoCallEndSubs) || (this.videoCallEndSubs && this.videoCallEndSubs.closed)) {
      this.videoCallEndSubs = this.videoCallService.videoCallEnd.subscribe((eventData) => {
        this.showVideoAlert(eventData.data.title);
      });
    }

    if (isBlank(this.vidyoClientSubscribe) || (this.vidyoClientSubscribe && this.vidyoClientSubscribe.closed)) {
      this.vidyoClientSubscribe = this.videoCallService.vidyoClient.subscribe((data) => {
        if (data && data.assignConnectionData) {
          this.connectionMessage = 'VIDEO_MESSAGES.WAITING';
        }
        if (data && data.playAudio) {
          if (!this.videoCallService.joinChat) {
            this.videoCallService.playAudioForCall.emit({
              playSound: true,
              action: VideoCall.forCall
            });
          }
        }
      });
    }
    this.videoCallService.waitingMessage.subscribe();
    this.userLeftSubs = this.videoCallService.userLeft.subscribe((data) => {
      if (data[0].participents === Constants.noParticipents) {
        this.showVideoAlert(this.common.getTranslateData('VIDEO_MESSAGES.ALL_USER_LEFT'));
      }
    });
  }

  ionViewWillEnter(): void {
    this.videoCallService.sharedService.resetSessionTimeout(false);
    this.videoCallService.sharedService.isIOSVideoCallConnected = true;
    VidyoPlatform.openConference(this.videoCallService.connectOptions);
  }

  toggleMute(): void {
    this.microphonePrivacy = !this.microphonePrivacy;
    this.videoCallService.toggleMute(this.microphonePrivacy);
  }

  toggleCamera(): void {
    this.cameraPrivacy = !this.cameraPrivacy;
    this.videoCallService.changeCameraPrivacy(this.cameraPrivacy);
  }

  disconnect(): void {
    if (this.videoCallService.sharedService.userData?.appLessSession) {
      this.common.showAlert({ message: this.common.getTranslateData('VIDEO_MESSAGES.CANCEL_MESSAGE_APPLESS') }).then((response) => {
        if (response) {
          this.endVideoCall();
          this.unSubscribeVideoEvents();
          this.videoCallService.disconnectApplessClient();
          const data = {
            action: VideoCall.sendAppLessAction.participantJoin,
            userid: this.videoCallService.sharedService.userData.userId,
            token: this.videoCallService.sharedService.userData.authenticationToken
          };
          this.videoCallService.sharedService.updateToAppLessVideo(data).subscribe();
        }
      });
    } else {
      this.endVideoCall();
      this.videoCallService.disconnectVideo();
      this.unSubscribeVideoEvents();
    }
  }

  toggleCameraView(): void {
    this.videoCallService.cycleCamera();
  }

  showVideoAlert(title: string): void {
    this.videoCallService.waitingAlertMsg = true;
    const buttons = [
      {
        text: this.common.getTranslateData('BUTTONS.WAIT'),
        confirm: true,
        class: 'wait-btn'
      },
      {
        text: this.common.getTranslateData('BUTTONS.CANCEL'),
        confirm: false,
        class: 'cancel-btn'
      }
    ];
    const header = title;
    const alertData = {
      message: this.common.getTranslateData('VIDEO_MESSAGES.CANCEL_MESSAGE'),
      header,
      buttons,
      cssClass: VideoCall.videoClass.commonSweetAlert,
      alertId: VideoCall.alertMsgId,
      backDrop: false
    };
    if (this.videoCallService.sharedService.platform.is('ios') && this.videoCallService.sharedService.platform.is('capacitor')) {
      this.common.showAlert(alertData).then((confirmation) => {
        this.common.closeAllAlert();
        if (!confirmation) {
          this.videoCallService.initiatorEnd = true;
          this.videoCallService.joinChat = false;
          this.videoCallService.playAudio(false);
          this.videoCallService.disconnectVideo();
          this.endVideoCall();
        } else {
          this.videoCallService.waitingAlertMsg = false;
          this.videoCallService.playAudioForCall.emit({
            playSound: false,
            action: VideoCall.forCall
          });
        }
      });
    }
  }

  endVideoCall(): void {
    if (!this.videoCallService.participant) {
      this.videoCallService.deleteRoom();
      this.videoCallService.callTrackActivityEndChat();
      if (this.videoCallService.isAppLessWorkFlow) {
        const data = {
          action: VideoCall.sendAppLessAction.initiatorLeft,
          chatroom: this.videoCallService.currentChatroomId,
          status: VideoCall.leftStatus
        };
        this.videoCallService.sharedService.updateToAppLessVideo(data).subscribe();
        this.videoCallService.isAppLessWorkFlow = false;
      }
    }
  }

  ionViewDidLeave(): void {
    this.videoCallService.sharedService.isIOSVideoCallConnected = false;
    this.userLeftSubs.unsubscribe();
    const getHTMLElement = document.getElementsByTagName('html');
    if (getHTMLElement.length > 0) {
      getHTMLElement[0].classList.remove('ios-video-call-set-background');
    }
  }

  unSubscribeVideoEvents(): void {
    this.videoCallService.callInitiatedRoomId = '';
    if (this.vidyoClientSubscribe) {
      this.vidyoClientSubscribe.unsubscribe();
    }
    if (this.videoCallEndSubs) {
      this.videoCallEndSubs.unsubscribe();
    }
    if (this.videoChanged) {
      this.videoChanged.unsubscribe();
    }
  }
}
