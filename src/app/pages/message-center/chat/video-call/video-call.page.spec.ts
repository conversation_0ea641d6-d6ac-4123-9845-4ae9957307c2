import { Apollo } from 'apollo-angular';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AlertController, IonicModule } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';
import { VideoCallPage } from './video-call.page';

describe('VideoCallPage', () => {
  let component: VideoCallPage;
  let fixture: ComponentFixture<VideoCallPage>;

  let alertController: AlertController;
  const { alertSpy } = TestConstants;
  beforeEach(() => {

    TestBed.configureTestingModule({
      declarations: [VideoCallPage],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), HttpClientModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        Apollo,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(VideoCallPage);
    component = fixture.componentInstance;

    alertController = TestBed.inject(AlertController);
    spyOn(alertController, 'create').and.callFake(() => {
      return alertSpy;
    });
    alertSpy.present.and.stub();
    spyOn(alertController, 'dismiss').and.stub();

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should be call toggleMute', () => {
    component.toggleMute();
    expect(component.toggleMute).toBeDefined();
  });

  it('should be call toggleCamera', () => {
    component.toggleCamera();
    expect(component.toggleCamera).toBeDefined();
  });

  it('should be call toggleCameraView', () => {
    component.toggleCameraView();
    expect(component.toggleCameraView).toBeDefined();
  });

  it('should be call ionViewDidLeave', () => {
    component.ionViewDidLeave();
    expect(component.ionViewDidLeave).toBeDefined();
  });

  it('should be call showVideoAlert', () => {
    component.showVideoAlert('All users left this conference˝');
    expect(component.showVideoAlert).toBeDefined();
  });

  it('should call unSubscribeVideoEvents', () => {
    component.unSubscribeVideoEvents();
    expect(component.unSubscribeVideoEvents).toBeDefined();
  });

  it('should call endVideoCall', () => {
    component.endVideoCall();
    expect(component.endVideoCall).toBeDefined();
  });
});
