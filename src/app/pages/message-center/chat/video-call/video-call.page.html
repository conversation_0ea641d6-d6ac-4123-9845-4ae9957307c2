<ion-content>
  <ion-row id="waiting-message" class="show-message">
    <ion-col size="12" class="ion-text-center">{{connectionMessage | translate}}</ion-col>
  </ion-row>
  <ion-row class="setBottomButton">
    <ion-col size="2" class="ion-text-center ion-justify-content-center display-flex" (click)="toggleCameraView()">
      <ion-fab-button color="blumine">
        <ion-icon name="camera-reverse"></ion-icon>
      </ion-fab-button>
    </ion-col>
    <ion-col size="2" class="ion-text-center ion-justify-content-center display-flex" (click)="toggleCamera()">
      <ion-fab-button color="blumine">
        <ion-icon [name]="cameraPrivacy ? 'videocam-outline' : 'videocam'"></ion-icon>
      </ion-fab-button>
    </ion-col>
    <ion-col size="2" class="ion-text-center ion-justify-content-center display-flex" (click)="toggleMute()">
      <ion-fab-button color="blumine">
        <ion-icon [name]="microphonePrivacy ? 'mic-off' : 'mic'">
        </ion-icon>
      </ion-fab-button>
    </ion-col>
    <ion-col size="2" class="ion-text-center ion-justify-content-center display-flex" (click)="disconnect()">
      <ion-fab-button color="danger">
        <ion-icon name="call">
        </ion-icon>
      </ion-fab-button>
    </ion-col>
    <ion-col size="12" class="ion-text-center" *ngIf="videoCallService?.iosVideoCallStatus">
      <ion-label>
        <ion-text color="white">
          <h1>{{ videoCallService?.iosVideoCallStatus }}</h1>
        </ion-text>
      </ion-label>
    </ion-col>
  </ion-row>
</ion-content>