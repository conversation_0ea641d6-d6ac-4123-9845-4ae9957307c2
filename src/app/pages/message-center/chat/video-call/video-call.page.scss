ion-content {
    --background: none;
    width: 100%;
    height: 100%;
    .setBottomButton {
        position: absolute;
        bottom: 50px;
        width: 100%;
    }
    .display-flex {
        display: flex;
    }
    .show-message {
        position: absolute;
        width: 100%;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        background: #1c1c1c7d;
        color: #ccc;
        font-size: 16px;
        z-index: 999;
    }
    .hide-overlay {
        display: none;
    }
}
ion-app.md [padding],
ion-app.md [padding] .scroll-content {
    background: none;
}
ion-app.ios [padding],
ion-app.ios [padding] .scroll-content {
    background: none;
}
.setBottomButton{
    --ion-color-danger: #eb445a !important;
}
