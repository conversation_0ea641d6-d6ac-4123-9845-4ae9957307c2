import { Component } from '@angular/core';
import { Nav<PERSON>ara<PERSON>, ModalController } from '@ionic/angular';

@Component({
  selector: 'app-subject-popover',
  templateUrl: './subject-popover.component.html',
  styleUrls: ['./subject-popover.component.scss']
})
export class SubjectPopoverComponent {
  subject = '';
  constructor(private readonly navParams: NavParams, private readonly modalController: ModalController) {
    this.subject = this.navParams.get('subject');
  }

  done(): void {
    this.modalController.dismiss({ subject: this.subject });
  }

  cancel(): void {
    this.modalController.dismiss();
  }
}
