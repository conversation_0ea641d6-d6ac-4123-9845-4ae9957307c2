import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';

import { SubjectPopoverComponent } from './subject-popover.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';

describe('SubjectPopoverComponent', () => {
  let component: SubjectPopoverComponent;
  let fixture: ComponentFixture<SubjectPopoverComponent>;
  let modalController: ModalController;
  const modalSpy = TestConstants.modalSpy;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SubjectPopoverComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot()],
      providers: [Nav<PERSON>ara<PERSON>, ModalController],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();

    fixture = TestBed.createComponent(SubjectPopoverComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute done', () => {
    component.done();
    expect(component.done).toBeTruthy();
  });
  it('execute cancel', () => {
    component.cancel();
    expect(component.cancel).toBeTruthy();
  });
});
