<ion-header>
  <ion-toolbar mode="ios" color="blumine">
    <ion-buttons slot="start">
      <ion-button (click)="cancel()" color="de-york" class="ion-text-capitalize">
        {{'BUTTONS.CANCEL' | translate}}
      </ion-button>
    </ion-buttons>
    <ion-title>{{'LABELS.UPDATE_SUBJECT'| translate}}</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="done()" color="de-york" class="ion-text-capitalize">
        {{'BUTTONS.UPDATE' | translate}}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content>
  <div class="messageUpdateDiv ion-padding ion-margin">
    <textarea [(ngModel)]="subject" autocapitalize="on"></textarea>
  </div>
</ion-content>