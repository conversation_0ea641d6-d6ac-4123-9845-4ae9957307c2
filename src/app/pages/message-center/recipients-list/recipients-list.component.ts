import { Component, OnInit } from '@angular/core';
import { APIs } from 'src/app/constants/apis';
import { Constants } from 'src/app/constants/constants';
import { ModalController, NavParams } from '@ionic/angular';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { UntypedFormGroup } from '@angular/forms';
import { isBlank } from '../../../utils/utils';
import { CommonService } from 'src/app/services/common-service/common.service';
import { forkJoin } from 'rxjs';
import { MaskedRecipientListExtraParam } from 'src/app/interfaces/messages';
import { Config } from 'src/app/constants/config';

@Component({
  selector: 'app-recipients-list',
  templateUrl: './recipients-list.component.html',
  styleUrls: ['./recipients-list.component.scss']
})
export class RecipientsListComponent implements OnInit {
  selectRecipientForm: UntypedFormGroup;
  recipientList = [];
  isBroadcast: boolean;
  list = [];
  listFiltered = [];
  userTypes = Constants.userTypes;
  selectedRecipientIds = [];
  errorMessage = '';
  search = '';
  maskedReqBody = {
    siteIds: [0]
  };

  constructor(
    private readonly navParams: NavParams,
    private readonly httpService: HttpService,
    private readonly sharedService: SharedService,
    private readonly modalController: ModalController,
    private readonly common: CommonService
  ) {
    this.isBroadcast = this.navParams.get('isBroadcast');
    this.selectedRecipientIds = this.navParams.get('choosedRecipientIds');
  }

  ngOnInit(): void {
    if (!isBlank(this.selectedRecipientIds)) {
      this.list = this.listFiltered = this.selectedRecipientIds;
    }
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.filterRoleList();
    });
  }
  ionViewWillEnter() {
    if (this.isBroadcast) {
      this.fetchTags();
    } else {
      this.fetchMaskedRecipients();
    }
  }
  /**
   * filterRoleList function used to hide
   *   Caregiver tag for Alternate Contact workflow
   *   Alternate Contact tag for Caregiver workflow
   */
  filterRoleList(): void {
    if (this.isBroadcast) {
      const isAlternateFlow =
        this.sharedService.getConfigValue(Config.defaultPatientsWorkflow) === Constants.workflowAlternateContact;
      const caregiverLabel = Constants.userCaregiver.toUpperCase();
      const alternateContactLabel = Constants.alternateContact;
      this.listFiltered = this.list = this.list.map((item) => ({
        ...item,
        hidden:
          (item.tag_name === alternateContactLabel && !isAlternateFlow) ||
          (item.tag_name.toUpperCase() === caregiverLabel && isAlternateFlow)
      }));
    }
  }
  fetchTags(): void {
    const userTagsResult = [];
    const userRolesArray = [];
    const getTags = this.httpService.doGet({ endpoint: APIs.getActiveTags, loader: true });
    const getUserRoles = this.httpService.doGet({ endpoint: APIs.getActiveUserRoles, loader: true });
    const isAlternateFlow =
      this.sharedService.getConfigValue(Config.defaultPatientsWorkflow) === Constants.workflowAlternateContact;
    const caregiverLabel = Constants.userCaregiver.toUpperCase();
    const alternateContactLabel = Constants.alternateContact;
    forkJoin([getTags, getUserRoles]).subscribe(
      (result: any) => {
        //TODO: Get User Tag Response Data
        if (result[0].length > 0) {
          result[0].map((item: any, index: number) => {
            userTagsResult[index] = {
              ...item,
              checked: false,
              hidden: false
            };
          });
        }
        //TODO: Get User Role Response Data
        if (result[1].length > 0) {
          result[1].map((item: any, index: number) => {
            userRolesArray[index] = {
              ...item,
              id: item.id,
              tag_name: item.name,
              role_name: item.role_name,
              checked: false,
              hidden:
                (item.name === alternateContactLabel && !isAlternateFlow) ||
                (item.name.toUpperCase() === caregiverLabel && isAlternateFlow)
            };
          });
        }
        this.list = this.listFiltered = userTagsResult.concat(userRolesArray);
        if (!isBlank(this.selectedRecipientIds)) {
          this.list = this.listFiltered = this.selectedRecipientIds;
        }
        if (isBlank(this.listFiltered)) {
          this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
        this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
      }
    );
  }

  fetchMaskedRecipients(): void {
    const url = APIs.getMaskedRecipients;
    const userData = this.sharedService.userData;
    let extraParams: MaskedRecipientListExtraParam = {
      status: Constants.notRejected,
      siteIds: this.maskedReqBody && this.maskedReqBody.siteIds ? this.maskedReqBody.siteIds.toString() : '0',
      recipientRoles: userData.config.masked_discussion_recipient_roles
    };
    if (userData.accessSecurityEnabled) {
      extraParams = {
        ...extraParams,
        accessSecurityEnabled: userData.accessSecurityEnabled,
        accessSecurityEsiValue: userData.accessSecurityEsiValue,
        accessSecurityIdentifierType: userData.accessSecurityIdentifierType,
        accessSecurityType: userData.accessSecurityType
      };
    }
    this.httpService.doGet({ endpoint: url, extraParams }).subscribe(
      (response) => {
        if (response.length) {
          this.listFiltered = response;
          this.listFiltered.map((item) => ({ ...item, checked: false }));
          const groups = new Set(this.listFiltered.map((item) => item.role));
          const listFilteredWithGroup = [];
          groups.forEach((g) =>
            listFilteredWithGroup.push({
              name: g,
              expanded: false,
              checked: false,
              partial: false,
              values: this.listFiltered.filter((i) => i.role === g)
            })
          );
          this.list = this.listFiltered = listFilteredWithGroup;
          if (!isBlank(this.selectedRecipientIds)) {
            this.list = this.listFiltered = this.selectedRecipientIds;
          }
          this.filterList(this.search);
        } else {
          this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
        this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
      }
    );
  }

  expand(i: number): void {
    this.listFiltered[i].expanded = !this.listFiltered[i].expanded;
  }

  cancel(): void {
    this.modalController.dismiss({
      dismissed: true
    });
  }

  filterList(searchTerm): boolean {
    this.listFiltered = JSON.parse(JSON.stringify(this.list));
    this.search = searchTerm;
    if (!searchTerm) {
      this.list = this.listFiltered;
      return;
    }
    if (this.isBroadcast) {
      this.listFiltered = this.list.filter((form: any) => {
        const valueKey = form.tag_name;
        if (valueKey && searchTerm) {
          if (valueKey.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1) {
            return true;
          }
          return false;
        }
      });
    } else {
      this.listFiltered.map((item) => (item.expanded = true));
      this.list.forEach((element: any, index: number) => {
        this.listFiltered[index].values = element.values.filter((form: any) => {
          const valueKey = form.displayname;
          if (valueKey && searchTerm) {
            if (valueKey.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1) {
              return true;
            }
            return false;
          }
        });
      });
    }
  }

  submitRecipients(): void {
    this.modalController.dismiss({ data: this.list });
    this.cancel();
  }

  clearAll(): void {
    if (this.isBroadcast) {
      this.listFiltered.map((item) => (item.checked = false));
    } else {
      this.listFiltered.map(
        (item) => ((item.checked = false), (item.partial = false), item.values.map((users) => (users.checked = false)))
      );
    }
  }

  get isDisabled(): boolean {
    if (this.isBroadcast) {
      return this.listFiltered.map((item) => item.checked).indexOf(true) === -1;
    } else {
      let check = true;
      this.listFiltered.map(({ partial, checked }: { checked: boolean; partial?: boolean }) => {
        if (partial || checked) {
          check = false;
          return;
        }
      });
      return check;
    }
  }
  /* groupSelect function renamed to checkUser to check/uncheck user individually as well as group */
  checkUser(groupIndex: number, userIndex?: number): void {
    if (userIndex >= 0) {
      let values = this.listFiltered[groupIndex].values;
      let checkedLength = values.filter((user) => user.checked).length;
      this.listFiltered[groupIndex].checked = checkedLength === values.length ? true : false;
      this.listFiltered[groupIndex].partial = checkedLength > 0 ? true : false;
    } else {
      this.listFiltered[groupIndex].values.map((item) => {
        item.checked = this.listFiltered[groupIndex].checked;
      });
      this.listFiltered[groupIndex].partial = this.listFiltered[groupIndex].values[0].checked;
    }
  }

  filterSitesData(data: []): void {
    this.maskedReqBody.siteIds = data;
    this.fetchMaskedRecipients();
  }
  initialSiteData(data: []): void {
    this.maskedReqBody.siteIds = data;
  }

  broadCastSelect(i: number) {
    this.listFiltered[i].checked = !this.listFiltered[i].checked;
  }
}
