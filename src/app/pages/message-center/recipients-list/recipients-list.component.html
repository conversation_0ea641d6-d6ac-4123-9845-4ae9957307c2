<app-header-plain headerTitle="TITLES.CHOOSE_RECIPIENTS" (close)="cancel()"></app-header-plain>
<app-sites (filterSites)="filterSitesData($event)" *ngIf="!isBroadcast" (onLoadSites)="initialSiteData($event)"></app-sites>
<ion-searchbar id="ion-search" (ionInput)="filterList($event.srcElement.value)"></ion-searchbar>
<ion-content>
    <div class="common-list" *ngIf="isBroadcast && listFiltered?.length > 0">
        <ion-item lines="none" *ngFor="let listRow of listFiltered; let i = index" [id]="'ion-item-'+i"
            [hidden]="listRow?.hidden">
            <ion-label>{{listRow?.tag_name || '-'}}</ion-label>
            <ion-checkbox [checked]="listRow?.checked" class="common-checkbox" slot="end" mode='ios' id="user-{{i}}"
                [(ngModel)]="listRow.checked">
            </ion-checkbox>
        </ion-item>
    </div>
    <div class="common-list" *ngIf="!isBroadcast && listFiltered?.length > 0">
        <div *ngFor="let group of listFiltered; let i = index">
            <ion-item lines="none" *ngIf="group.values.length" [id]="'ion-item-'+i">
                <ion-icon [name]="group.expanded?'remove-circle-outline':'add-circle-outline'" slot="start"
                    (click)="expand(i)" tappable></ion-icon>
                <ion-label>{{group?.name}}</ion-label>
                <ion-checkbox class="common-checkbox" [(ngModel)]="group.checked" slot="end"
                    (ngModelChange)="checkUser(i)" mode='ios' id="group-{{i}}">
                </ion-checkbox>
            </ion-item>
            <div *ngIf="group.expanded && group.values.length" class="child-list">
                <ion-item lines="none" *ngFor="let users of group.values; let id = index">
                    {{users?.displayname}}
                    <ion-checkbox class="common-checkbox" slot="end" mode='ios' [(ngModel)]="users.checked"
                        (ngModelChange)="checkUser(i,id)" id="user-{{id}}">
                    </ion-checkbox>
                </ion-item>
            </div>
        </div>
    </div>
    <div class="common-no-items">{{errorMessage}}</div>
</ion-content>
<ion-footer>
    <!-- TODO! CHP-3595 -->
    <ion-row>
        <ion-col size="6">
            <ion-button class="ion-text-capitalize" (click)="clearAll()" id="clear-all" expand="block" color="de-york"
                [disabled]="isDisabled">
                {{'BUTTONS.CLEAR_ALL' | translate}}
            </ion-button>
        </ion-col>
        <ion-col size="6">
            <ion-button class="ion-text-capitalize" (click)="submitRecipients()" id="submit" expand="block"
                color="de-york">
                {{'BUTTONS.DONE' | translate}}
            </ion-button>
        </ion-col>
    </ion-row>
</ion-footer>