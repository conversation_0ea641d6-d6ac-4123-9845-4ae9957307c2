import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';
import { RecipientsListComponent } from './recipients-list.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { TestConstants } from 'src/app/constants/test-constants';

describe('RecipientsListComponent', () => {
  let component: RecipientsListComponent;
  let fixture: ComponentFixture<RecipientsListComponent>;
  let modalController: ModalController;
  const modalSpy = jasmine.createSpyObj('Modal', ['present', 'onDidDismiss', 'onWillDismiss', 'dismiss']);

  const mockFilterData = [
    {
      id: 1,
      tag_name: 'Test Tag',
      role_name: 'Test Role',
      checked: false,
      values: [{ checked: false }]
    }
  ];
  let sharedService: SharedService;
  let httpService: HttpService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [RecipientsListComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      providers: [
        NavParams,
        NgxPermissionsService,
        NgxPermissionsStore,
        ModalController,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(RecipientsListComponent);
    httpService = TestBed.inject(HttpService);
    sharedService = TestBed.inject(SharedService);
    Object.defineProperty(sharedService, 'userData', { value: TestConstants.userData });
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('clearAll function should be called,set checked as false and isBroadcast is true', () => {
    component.listFiltered = mockFilterData;
    component.isBroadcast = true;
    component.clearAll();
    expect(component.listFiltered[0].checked).toEqual(false);
    expect(component.clearAll).toBeDefined();
  });

  it('clearAll function should be called,set checked as false and isBroadcast is false', () => {
    component.listFiltered = mockFilterData;
    component.isBroadcast = false;
    component.clearAll();
    expect(component.listFiltered[0].checked).toEqual(false);
    expect(component.clearAll).toBeDefined();
  });

  it('filterList execute: with search key for masked message recipients', () => {
    component.list = [{ values: [{ displayname: 'John Antony' }] }];
    component.filterList('john');
    expect(component.filterList).toBeTruthy();
  });
  it('filterList execute: with unmatched search key for masked message recipients', () => {
    component.list = [{ values: [{ displayname: 'John Antony' }] }];
    component.filterList('leo');
    expect(component.filterList).toBeTruthy();
  });
  it('filterList execute: without search key for broadcast message recipients', () => {
    component.isBroadcast = true;
    component.list = [{ tag_name: 'John Antony' }];
    component.filterList('jo');
    expect(component.filterList).toBeTruthy();
  });
  it('filterList execute: with search key for broadcast message recipients', () => {
    component.isBroadcast = true;
    component.list = [{ tag_name: 'John Antony' }];
    component.filterList(null);
    expect(component.filterList).toBeTruthy();
  });
  it('filterList execute: with wnmatched search key for broadcast message recipients', () => {
    component.isBroadcast = true;
    component.list = [{ tag_name: 'John Antony' }];
    component.filterList('leo');
    expect(component.filterList).toBeTruthy();
  });
  it('isDisabled execute: broadcast message recipients', () => {
    component.isBroadcast = true;
    component.listFiltered = [{ name: 'a', checked: false, partial: false }];
    expect(component.isDisabled).toBeTrue();
  });
  it('isDisabled execute: masked message recipients', () => {
    component.listFiltered = [
      { name: 'a', checked: false, partial: false },
      { name: 'b', checked: true, partial: false }
    ];
    expect(component.isDisabled).toBeFalse();
  });
  it('checkUser execute: masked message recipients for individual selection', () => {
    component.listFiltered = [
      {
        name: 'group 1',
        checked: false,
        partial: false,
        values: [
          { name: 'a', checked: true },
          { name: 'b', checked: true }
        ]
      }
    ];
    component.checkUser(0, 0);
    expect(component.checkUser).toBeDefined();
  });
  it('checkUser execute: masked message recipients for individual selection with no checked user', () => {
    component.listFiltered = [
      {
        name: 'group 1',
        checked: false,
        partial: false,
        values: [
          { name: 'a', checked: false },
          { name: 'b', checked: false }
        ]
      }
    ];
    let response = [...component.listFiltered];
    response[0].values[0].checked = true;
    response[0].partial = true;
    component.checkUser(0, 0);
    expect(component.checkUser).toBeDefined();
    expect(component.listFiltered[0].checked).toBeFalse();
    expect(component.listFiltered[0].partial).toBeTrue();
  });
  it('checkUser execute: masked message recipients for group selection', () => {
    component.listFiltered = [
      {
        name: 'group 1',
        checked: false,
        partial: false,
        values: [{ name: 'a', checked: false }]
      }
    ];
    let response = [...component.listFiltered];
    response[0].values[0].checked = true;
    response[0].checked = true;
    component.checkUser(0);
    expect(component.checkUser).toBeDefined();
  });
  it('expand execute: masked message recipients', () => {
    component.listFiltered = [{ expanded: false }];
    component.expand(0);
    expect(component.expand).toBeDefined();
    expect(component.listFiltered[0].expanded).toBeTrue();
  });
  it('fetchMaskedRecipients execute: masked message recipients', () => {
    let response = [
      { name: 'a', role: 'group 1' },
      { name: 'b', role: 'group 1' }
    ];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    let list = [
      {
        name: 'group 1',
        checked: false,
        partial: false,
        values: [
          { name: 'a', checked: false },
          { name: 'b', checked: false }
        ]
      }
    ];
    component.selectedRecipientIds = list;
    component.fetchMaskedRecipients();
    expect(component.fetchMaskedRecipients).toBeDefined();
    expect(component.listFiltered).toEqual(list);
  });
  it('fetchMaskedRecipients execute: masked message recipients with no data', () => {
    let response = [];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchMaskedRecipients();
    expect(component.fetchMaskedRecipients).toBeDefined();
    expect(component.errorMessage).toEqual('MESSAGES.NO_ITEM_FOUND');
  });
  it('fetchMaskedRecipients execute: masked message recipients throws error', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.fetchMaskedRecipients();
    expect(component.fetchMaskedRecipients).toBeDefined();
    expect(component.errorMessage).toEqual('MESSAGES.NO_ITEM_FOUND');
  });
  it('broadCastSelect execute: broadcast message recipients selection', () => {
    component.listFiltered = [{ checked: true }];
    component.broadCastSelect(0);
    expect(component.broadCastSelect).toBeDefined();
    expect(component.listFiltered[0].checked).toBeFalse();
  });
  it('filterSitesData execute', () => {
    component.filterSitesData([]);
    expect(component.filterSitesData).toBeDefined();
  });
  it('submitRecipients execute', () => {
    component.submitRecipients();
    expect(component.submitRecipients).toBeDefined();
  });
  it('fetchTags execute', () => {
    component.selectedRecipientIds = [1, 2, 3];
    const response = [{ id: 1, name: 'test', role_name: 'test role' }];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchTags();
    expect(component.fetchTags).toBeDefined();
  });
  it('fetchTags execute : response = []', () => {
    spyOn(httpService, 'doGet').and.returnValue(of([]));
    component.fetchTags();
    expect(component.fetchTags).toBeDefined();
  });
  it('fetchTags execute : throws error', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.fetchTags();
    expect(component.fetchTags).toBeDefined();
  });
  it('execute ngOnInit', () => {
    component.selectedRecipientIds = [1, 2, 3];
    component.isBroadcast = true;
    component.ngOnInit();
    expect(component.ngOnInit).toBeDefined();
  });
  describe('ionViewWillEnter', () => {
    it('execute ionViewWillEnter with call fetchTags', () => {
      component.isBroadcast = true;
      spyOn(component, 'fetchTags').and.stub();
      component.ionViewWillEnter();
      expect(component.fetchTags).toHaveBeenCalled();
    });
    it('execute ionViewWillEnter with call fetchMaskedRecipients', () => {
      component.isBroadcast = false;
      spyOn(component, 'fetchMaskedRecipients').and.stub();
      component.ionViewWillEnter();
      expect(component.fetchMaskedRecipients).toHaveBeenCalled();
    });
  });
  it('execute filterRoleList', () => {
    component.isBroadcast = true;
    component.filterRoleList();
    expect(component.filterRoleList).toBeDefined();
  });
  it('execute ngOnInit to call configValuesUpdated', () => {
    component.ngOnInit();
    sharedService.configValuesUpdated.next('');
    expect(component.ngOnInit).toBeDefined();
  });
  it('execute initialSiteData', () => {
    component.initialSiteData([]);
    expect(component.maskedReqBody.siteIds).toEqual([]);
  });
});
