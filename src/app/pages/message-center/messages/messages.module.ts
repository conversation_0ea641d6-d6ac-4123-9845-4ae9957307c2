import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { MessagesPageRoutingModule } from './messages-routing.module';
import { MessagesPage } from './messages.page';
import { SharedModule } from '../../../shared.module';
import { ParseMessageToHtmlDirective } from '../../../directives/parse-message/parse-message-to-html.directive';
import { SearchBarModule } from 'src/app/components/search-bar/search-bar.module';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';
import { ForwardComponentModule } from '../forward/forward.module';
import { ForwardBehaviourComponentModule } from '../forward-behaviour/forward-behaviour.module';
import { SitesModule } from 'src/app/components/sites/sites.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        MessagesPageRoutingModule,
        SharedModule,
        SearchBarModule,
        ForwardComponentModule,
        ForwardBehaviourComponentModule,
        ActionButtonModule,
        SitesModule
    ],
    declarations: [MessagesPage, ParseMessageToHtmlDirective],
    exports: [ParseMessageToHtmlDirective],
})
export class MessagesPageModule { }
