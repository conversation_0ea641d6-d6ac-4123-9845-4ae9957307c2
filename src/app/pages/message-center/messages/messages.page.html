<app-header [headerTitle]="titleWithCount"></app-header>
<div class="message-listing-page" *ngIf="!isUserGroupPatient; else siteHeaderPadding">
  <app-sites (filterSites)="filterSitesData($event)" (onLoadSites)="initialSiteData($event)" [siteLabel]="siteLabel"></app-sites>
</div>
<ng-template #siteHeaderPadding>
  <div class="site-header-padding"></div>
</ng-template>
<app-search-bar
  *ngIf="!longPressTriggered"
  (seachAction)="searchMessage($event)"
  (closeSearch)="closeSearch()"
  (filterAction)="filterData($event)"
  (closeDateFilter)="loadFilterData($event)"
  [selectedDateOptions]="messageReqBody.selectedDateOptions"
  [isDateFilter]="true"
  [dateRange]="messageReqBody.dateRange"
  [monthTypes]="monthTypes"
  [searchText]="messageReqBody.searchKeyword"
  [filteredTags]="messageReqBody.filterTags"
  [messagePriority]="messageReqBody.priorityValue"
  [showMentionMessages]="messageReqBody.mentionUsers"
  [showUnreadMessages]="messageReqBody.unread"
  [flagFilter]="messageReqBody.flagValue"
  [chatThreadTypeSelected]="messageReqBody.chatThreadTypes"
>
</app-search-bar>
<div class="multi-select-head" *ngIf="longPressTriggered">
  <ion-grid>
    <ion-row class="ion-align-items-center">
      <ion-col size="2">
        <ion-checkbox #checkAll class="common-checkbox" mode="ios" (ionChange)="selectAllCheckboxes(checkAll)"> </ion-checkbox>
      </ion-col>
      <ion-col class="sub-title">
        <div (click)="cancelMultiSelect()" id="cancel">{{ 'BUTTONS.CANCEL' | translate }}</div>
      </ion-col>
      <ion-col size="2" class="ion-text-end">
        <ion-icon id="Unpin" tappable (click)="multiplePinOrUnpin()" [name]="isPinnedSelected ? 'push-pin-slash' : 'push-pin'"></ion-icon>
      </ion-col>
      <ion-col size="2" class="ion-text-end">
        <ion-icon id="delete" tappable (click)="multipleArchive()" name="trash-outline"></ion-icon>
      </ion-col>
    </ion-row>
  </ion-grid>
</div>
<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-list>
    <app-skeleton-loader *ngIf="!messageList || showLoaderOnStickyFiltersExist" [count]="messageList ? 1 : 5" [avatar]="true"></app-skeleton-loader>
    <div class="list">
      <div
        *ngFor="let message of messageList; trackByIdentifier: 'chatroomId'; let i = index"
        [ngClass]="i === pinnedIndex && i > 0 && !isArchived ? 'pinned-border' : ''"
      >
        <ion-item-sliding #swipeList>
          <ion-item
            lines="none"
            class="message-display"
            [ngClass]="{ unread: message.hasUnreadMessages }"
            type="item-text-wrap"
            (click)="gotoChatPage(message, i, $event)"
            dirLongPress
            (longPressed)="longPressOptions(i)"
            *ngIf="!message.loading"
            id="goto-chat-page-{{ message.chatroomId }}"
          >
            <ion-checkbox *ngIf="longPressTriggered" class="common-checkbox msg-checkbox" slot="start" [(ngModel)]="message.checked" mode="ios">
            </ion-checkbox>
            <div class="row inbox-message-details">
              <div class="avatar">
                <div class="avatar-container">
                  <img
                    draggable="false"
                    appAvatar
                    #avatarDirective="avatarDirective"
                    [src]="avatarDirective.avatarSrc"
                    (error)="avatarDirective.onImageError($event)"
                    [avatar]="message.chatAvatar"
                    [messageCategory]="message.messageCategory"
                    alt=""
                    outOfOfficeStatus
                    [oooInfo]="message.oooInfo"
                    [customClass]="'chat-list-red-badge'"
                  />
                  <div class="unread-count" *ngIf="message.hasUnreadMessages && (message.maskedUnreadCount > 0 || message.unreadCount > 0)">
                    <span>{{ message.maskedSubCount > 0 ? message.maskedUnreadCount : message.unreadCount }}</span>
                  </div>
                  <ion-icon
                    id="expand-button"
                    *ngIf="message.maskedSubCount > 0"
                    class="masked-caret"
                    [name]="message.childExpanded ? 'chevron-up-outline' : 'chevron-down-outline'"
                  ></ion-icon>
                </div>
              </div>
              <div class="chat-details-middle wrap-ellipsis">
                <div class="inbox-message-from wrap-ellipsis">
                  <div class="from-name wrap-ellipsis">
                    <div class="inbox-per-name wrap-ellipsis" *ngIf="message.chatHeading">{{ message.chatHeading }}</div>
                  </div>
                </div>
                <div class="inbox-mes-name wrap-ellipsis" *ngIf="message.chatSubHeading">{{ message.chatSubHeading }}</div>
                <div
                  class="inbox-mes-name wrap-ellipsis"
                  *ngIf="message?.patientInfo?.patientDisplayName && isAlternateContact && +message.messageType !== +messageTypes.broadcast"
                >
                  {{ patientLabel }} : {{ message.patientInfo.patientDisplayName }}
                </div>
                <div class="forward-content wrap-ellipsis" *ngIf="message.messageForwarded">({{ message.messageForwarded }})</div>
                <div
                  class="inbox-mes-name wrap-ellipsis"
                  *ngIf="message.chatSubject && (+message.messageType === +messageTypes.messageGroup || +message.messageType === +messageTypes.pdg)"
                >
                  {{ 'LABELS.SUBJECT' | translate }}: {{ message.chatSubject }}
                </div>
                <div class="inbox-message-content wrap-ellipsis flex-align">
                  <span *ngIf="message.isSelfMessage && +message.messageType !== +messageTypes.pdg">{{ meLabel }}:</span>
                  <span
                    [dirParseMessageToHtml]="message.message | unicodeConvert"
                    class="wrap-ellipsis flex-align bottom-content-margin gap-1"
                    [ngClass]="{ 'f-italic': +message.messageStatus === 0 }"
                    *ngIf="+message.messageType !== +messageTypes.pdg"
                  ></span>
                  <span
                    class="common-message-parse wrap-ellipsis flex-align bottom-content-margin gap-1"
                    *ngIf="message.siteName && +message.messageType === +messageTypes.pdg"
                  >
                    {{ siteLabelPdg | translate }} : {{ message.siteName }}
                  </span>
                </div>
                <app-tags
                  [tags]="message.messageTagList"
                  [chatRoomId]="message.chatroomId"
                  (removeMessageTags)="removeMessageTags($event, message.chatroomId)"
                  [maxAllowedTags]="1"
                  *ngIf="!isPatient && !isPartner && !message?.loading"
                  (emitTagOpenStatus)="viewTagClickedHandle($event)"
                ></app-tags>
              </div>
              <div class="date-block">
                <div class="message-date">
                  {{
                    (+message.messageStatus === 1
                      ? (message.childExpanded ? message.deliveryTime : message.messageOrder) * 1000
                      : formatMessageDeletedTime(message.messageDeletedTime)
                    ) | shortDateTime
                  }}
                </div>
                <div style="text-align: end" *ngIf="message?.pinnedStatus && !isArchived"><ion-icon name="push-pin"></ion-icon></div>
                <div class="flag-container">
                  <ng-container *ngTemplateOutlet="showMessagePriority; context: { message: message }"></ng-container>
                  <ng-container *ngTemplateOutlet="showMessageMention; context: { message: message }"></ng-container>
                  <ng-container *ngIf="enableFlagging && !isArchived">
                    <div class="common-flag-icon" *ngIf="message.chatroomFlag > 0" [ngClass]="sharedService.getFlagType(message.chatroomFlag)">T</div>
                    <div class="common-flag-icon" *ngIf="message.messageFlag > 0" [ngClass]="sharedService.getFlagType(message.messageFlag)">M</div>
                  </ng-container>
                </div>
              </div>
            </div>
          </ion-item>
          <ion-item-options side="end" class="common-swipe-buttons">
            <ion-item-option
              class="more"
              color="light"
              *ngIf="!isArchived && (enableFlagging || showForwardButton(message) || (!this.isPatient && !this.isPartner))"
              (click)="openActionSheet(message, i, swipeList)"
              id="more"
            >
              <ion-icon name="ellipsis-horizontal-outline" slot="top"></ion-icon>
              <ion-label>{{ 'BUTTONS.MORE' | translate }}</ion-label>
            </ion-item-option>
            <ion-item-option class="archive" *ngIf="!isArchived" color="danger" (click)="archiveChat(message, i, swipeList)" id="archieve-chat">
              <ion-icon name="trash" slot="top"></ion-icon>
              <ion-label>{{ 'BUTTONS.ARCHIVE' | translate }}</ion-label>
            </ion-item-option>
            <ion-item-option
              class="archive"
              *ngIf="isArchived && message?.isSelfArchived"
              (click)="restoreChat(message, i, swipeList)"
              id="restore-chat"
            >
              <ion-icon name="refresh" slot="top"></ion-icon>
              <ion-label>{{ 'BUTTONS.RESTORE' | translate }}</ion-label>
            </ion-item-option>
          </ion-item-options>
        </ion-item-sliding>
        <div *ngIf="message?.loadingChild || message?.loading" [ngClass]="{ 'sub-list-loader': message?.loadingChild }">
          <app-skeleton-loader [count]="1" [avatar]="true"></app-skeleton-loader>
        </div>
        <div *ngIf="message.childExpanded && !message?.loadingChild" class="sub-message-section">
          <ion-item
            lines="none"
            *ngFor="let message of message.maskedReplyMessages; let childIndex = index"
            class="message-display"
            [ngClass]="{ unread: message.unreadCount > 0 }"
            (click)="gotoChatPage(message, i, $event, childIndex)"
          >
            <div class="row inbox-message-details">
              <div class="avatar">
                <div class="avatar-container">
                  <img
                    draggable="false"
                    appAvatar
                    #avatarDirective="avatarDirective"
                    [src]="avatarDirective.avatarSrc"
                    (error)="avatarDirective.onImageError($event)"
                    [avatar]="message.chatAvatar"
                    [messageCategory]="message.messageCategory"
                    alt=""
                    outOfOfficeStatus
                    [oooInfo]="message.oooInfo"
                    [customClass]="'chat-list-red-badge'"
                  />
                  <div class="unread-count" *ngIf="message.unreadCount > 0">
                    <span>{{ message.unreadCount }}</span>
                  </div>
                </div>
              </div>
              <div class="chat-details-middle">
                <div class="inbox-message-from">
                  <div class="from-name" id="user-from-{{ message.fromUserId }}">
                    <div class="inbox-per-name">{{ message.chatHeading }}</div>
                  </div>
                </div>
                <div class="inbox-mes-name" *ngIf="message.chatSubHeading">{{ message.chatSubHeading }}</div>
                <div class="forward-content" *ngIf="message.messageForwarded">({{ message.messageForwarded }})</div>
                <div class="inbox-message-content wrap-ellipsis flex-align">
                  <span *ngIf="message.isSelfMessage">{{ meLabel }}:</span>
                  <span
                    [dirParseMessageToHtml]="message.message | unicodeConvert"
                    [ngClass]="{ 'f-italic': +message.messageStatus === 0 }"
                    class="common-message-parse wrap-ellipsis flex-align bottom-content-margin gap-1"
                  ></span>
                </div>
              </div>
              <div class="date-block">
                <div class="message-date">
                  {{
                    (+message.messageStatus === 1
                      ? (message.childExpanded ? message.deliveryTime : message.sent) * 1000
                      : formatMessageDeletedTime(message.messageDeletedTime)
                    ) | shortDateTime
                  }}
                </div>
                <div class="flag-container">
                  <ng-container *ngTemplateOutlet="showMessagePriority; context: { message: message }"></ng-container>
                  <ng-container *ngTemplateOutlet="showMessageMention; context: { message: message }"></ng-container>
                  <ng-container *ngIf="enableFlagging">
                    <div class="common-flag-icon" *ngIf="message.chatroomFlag > 0" [ngClass]="sharedService.getFlagType(message.chatroomFlag)">T</div>
                    <div class="common-flag-icon" *ngIf="message.messageFlag > 0" [ngClass]="sharedService.getFlagType(message.messageFlag)">M</div>
                  </ng-container>
                </div>
              </div>
            </div>
          </ion-item>
        </div>
      </div>
    </div>
  </ion-list>
  <ng-template #showMessagePriority let-message="message">
    <ng-container
      *ngIf="
        (message.maskedUnreadCount > 0 || message.unreadCount > 0) &&
          message.messagePriorityUnread !== MESSAGE_PRIORITY.NORMAL &&
          message.messagePriorityUnread !== '0';
        else readPriorityMsg
      "
    >
      <ng-container
        *ngTemplateOutlet="
          msgPriorityIcon;
          context: {
            priority: message.messagePriorityUnread,
            class: message.messagePriorityUnread === MESSAGE_PRIORITY.HIGH ? 'alert-fill danger' : 'arrowdown-fill primary'
          }
        "
      ></ng-container>
    </ng-container>
    <ng-template #readPriorityMsg>
      <ng-container
        *ngTemplateOutlet="
          msgPriorityIcon;
          context: {
            priority: message.messagePriorityRead,
            class: message.messagePriorityRead === MESSAGE_PRIORITY.HIGH ? 'alert-outline danger' : 'arrowdown-outline primary'
          }
        "
      ></ng-container>
    </ng-template>
    <ng-template #msgPriorityIcon let-priority="priority" let-class="class">
      <small class="priority-status">
        <ion-icon [ngClass]="class" *ngIf="priority === MESSAGE_PRIORITY.HIGH"></ion-icon>
        <ion-icon [ngClass]="class" *ngIf="priority === MESSAGE_PRIORITY.LOW"></ion-icon>
      </small>
    </ng-template>
  </ng-template>
  <ng-template #showMessageMention let-message="message">
    <ng-container *ngIf="message.messageMentionUnread; else readMentionMsg">
      <small class=""> <ion-icon class="at-fill danger" *ngIf="message.messageMentionUnread"></ion-icon></small>
    </ng-container>
    <ng-template #readMentionMsg>
      <small class=""><ion-icon class="at-outline danger" *ngIf="message.messageMentionRead"></ion-icon></small>
    </ng-template>
  </ng-template>
  <div class="no-message" *ngIf="messageList && messageList.length === 0">
    <img src="assets/icon/chat/no-message.png" *ngIf="!isArchived" alt="" />
    <div class="no-msg-archived" *ngIf="isArchived">{{ 'LABELS.NO_MESSAGES' | translate }}</div>
    <div class="head-text" *ngIf="!isArchived">
      {{
        messageReqBody?.searchKeyword
          ? ('LABELS.SORRY_COULDNT_FIND_RESULT' | translate: { searchString: messageReqBody?.searchKeyword })
          : ('LABELS.YOUR_INBOX_EMPTY' | translate)
      }}
    </div>
    <div class="sub-text" *ngIf="!isArchived">{{ 'LABELS.START_NEW_CHAT_CLICK_BELOW' | translate }}</div>
  </div>
  <div class="common-load-more-small" *ngIf="showLoadMoreOptions">
    <ion-infinite-scroll threshold="100px" (ionInfinite)="loadMoreMessages()">
      <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{ 'BUTTONS.LOAD_MORE' | translate }}"></ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </div>
</ion-content>
<app-action-button [button]="button" *ngIf="!isArchived || isPatient"></app-action-button>
<app-footer [showBackButton]="!longPressTriggered" backButtonLink="home">
  <ion-buttons slot="start" class="footer-buttons" *ngIf="longPressTriggered">
    <div tappable class="button-element" (click)="markAsRead()" id="read">
      <div class="read-label">{{ (isSelectedAnyMessage ? 'BUTTONS.READ' : 'BUTTONS.READ_ALL') | translate }}</div>
    </div>
  </ion-buttons>
</app-footer>
