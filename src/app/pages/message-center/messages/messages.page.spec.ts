import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { IonicModule, NavController, ModalController, IonItemSliding, ActionSheetController } from '@ionic/angular';
import { MessagesPage } from 'src/app/pages/message-center/messages/messages.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Constants, msgHistoryDateTimeFormat, MessageCategory } from 'src/app/constants/constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import * as moment from 'moment';

describe('MessagesPage', () => {
  let component: MessagesPage;
  let fixture: ComponentFixture<MessagesPage>;
  let httpService: HttpService;
  let sharedService: SharedService;
  let navCtrl: NavController;
  let slide: IonItemSliding;
  let commonService: CommonService;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  const actionSpy = TestConstants.actionSheetSpy;
  let actionSheetController: ActionSheetController;
  let persistentService: PersistentService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [MessagesPage],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        CommonService,
        PersistentService,
        SQLite,
        NativeStorage,
        Idle,
        IdleExpiry,
        Keepalive,
        ModalController,
        ActionSheetController,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    actionSheetController = TestBed.inject(ActionSheetController);
    spyOn(actionSheetController, 'create').and.callFake(() => {
      return actionSpy;
    });
    actionSpy.present.and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    persistentService = TestBed.inject(PersistentService);
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: { selectedData: [{ id: 1, selected: true }] } });
    commonService = TestBed.inject(CommonService);
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    spyOn(commonService, 'showToast').and.stub();
    fixture = TestBed.createComponent(MessagesPage);
    httpService = TestBed.inject(HttpService);
    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    Object.defineProperty(sharedService, 'configValuesUpdated', { value: of({}) });
    component = fixture.componentInstance;
    fixture.detectChanges();
    navCtrl = TestBed.inject(NavController);
    component.messageList = [
      { chatroomid: 1, checked: true, childExpanded: true, unreadCount: 2, markAsRead: true },
      {
        chatroomid: 2,
        checked: false,
        childExpanded: false,
        unreadCount: 2,
        maskedReplyMessages: [{ unreadCount: 0 }],
        markAsRead: true
      },
      { chatroomid: 3, checked: true, childExpanded: false, unreadCount: 2, markAsRead: false },
      { chatroomid: 4, checked: false, childExpanded: false, unreadCount: 2, markAsRead: true }
    ];
    sharedService.messageList = component.messageList;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute loadAllMessages', () => {
    const response = { message: [] };
    spyOn(sharedService, 'fetchAllMessages').and.returnValue(of(response));
    component.loadAllMessages();
    expect(component.loadAllMessages).toBeTruthy();
  });
  it('execute closeSearch', () => {
    const response = { message: [] };
    spyOn(sharedService, 'fetchAllMessages').and.returnValue(of(response));
    component.closeSearch();
    expect(component.closeSearch).toBeTruthy();
  });
  it('execute searchMessage', () => {
    const response = { message: [] };
    spyOn(sharedService, 'fetchAllMessages').and.returnValue(of(response));
    spyOn(sharedService, 'trackActivity').and.callThrough();
    component.searchMessage({ text: 'hi' });
    expect(component.searchMessage).toBeTruthy();
  });
  it('execute markAsRead', () => {
    component.messageList = [
      { id: 1, checked: true },
      { id: 2, checked: false }
    ];
    const response = { counts: { messages: 2 } };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.markAsRead();
    expect(component.markAsRead).toBeTruthy();
  });
  it('execute markAsRead : read all', () => {
    component.messageList = [
      { id: 1, checked: false },
      { id: 2, checked: false }
    ];
    const response = { counts: { messages: 2 } };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.markAsRead();
    expect(component.markAsRead).toBeTruthy();
  });
  it('execute selectAllCheckboxes', () => {
    component.selectAllCheckboxes({ checked: true });
    expect(component.selectAllCheckboxes).toBeTruthy();
  });

  it('should set showLoadMoreOptions to true when results length is >= to messageLoadLimit', () => {
    const results = [1, 2, 3];
    component.messageList = [1, 2, 3, 4, 5];
    component.showLoadMore(results);

    expect(component.showLoadMore).toBeTruthy();
  });
  it('execute gotoChatPage : long press', () => {
    component.longPressTriggered = true;
    component.gotoChatPage('', 1, null);
    expect(component.gotoChatPage).toBeTruthy();
  });
  it('execute gotoChatPage : message navigate', () => {
    component.longPressTriggered = false;
    sharedService.messageList = component.messageList;
    sharedService.messageCount = 2;
    spyOn(navCtrl, 'navigateForward').and.stub();
    component.gotoChatPage(
      {
        messageType: Constants.messageListTypes.masked,
        lastMaskedMessageId: 1,
        childExpanded: true,
        middleContent: []
      },
      1,
      { target: { id: '' } }
    );
    expect(component.gotoChatPage).toBeTruthy();
    expect(sharedService.messageCount).toEqual(0);
  });
  it('execute gotoChatPage : masked message navigate', () => {
    component.longPressTriggered = false;
    sharedService.messageList = component.messageList;
    sharedService.messageCount = 2;
    spyOn(navCtrl, 'navigateForward').and.stub();
    component.gotoChatPage(
      {
        messageType: Constants.messageListTypes.masked,
        lastMaskedMessageId: 1,
        childExpanded: true,
        middleContent: [],
        baseId: 1
      },
      1,
      { target: { id: '' } },
      0
    );
    expect(component.gotoChatPage).toBeTruthy();
  });
  it('execute getMaskedReplyMessages', () => {
    const response = { message: [] };
    sharedService.messageList = component.messageList;
    spyOn(sharedService, 'fetchReplyMessagesofMasked').and.returnValue(of(response));
    component.getMaskedReplyMessages({ chatroomid: 1 }, 0);
    expect(component.getMaskedReplyMessages).toBeTruthy();
  });
  it('should call cancelMultiSelect', () => {
    component.cancelMultiSelect();
    component.longPressTriggered = false;
    expect(component.cancelMultiSelect).toBeDefined();
  });

  it('should call presentForwardModal', () => {
    const message = { chatroomid: 1, checked: true, childExpanded: true, unreadCount: 2 };
    component.presentForwardModal(message as any, 1, slide);
    component.fetchRoomUsers(message as any, 1);
    expect(component.cancelMultiSelect).toBeDefined();
  });

  it('should call longPressOptions', () => {
    component.longPressOptions(3);
    expect(component.longPressOptions).toBeDefined();
  });

  it('should call multipleArchive', () => {
    component.multipleArchive();
    expect(component.multipleArchive).toBeDefined();
  });

  it('should call isSelectedAnyMessage', () => {
    expect(component.isSelectedAnyMessage).toBeDefined();
  });

  it('should update pin status successfully', () => {
    const response = { status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.updatePinStatus('pin', [1, 2, 3]);
    expect(component.updatePinStatus).toBeTruthy();
  });

  it('should handle error during pin status update', () => {
    const response = { status: false };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.updatePinStatus('pin', [1, 2, 3]);
    expect(component.updatePinStatus).toBeTruthy();
  });

  it('should handle multiple pin or unpin', () => {
    const sampleMessageList = [
      { checked: true, pinnedStatus: true, chatroomId: 1 },
      { checked: true, pinnedStatus: false, chatroomId: 2 },
      { checked: false, pinnedStatus: false, chatroomId: 3 }
    ];
    component.messageList = sampleMessageList;
    spyOn(component, 'updatePinStatus');
    component.multiplePinOrUnpin();

    expect(component.updatePinStatus).toHaveBeenCalledWith('unpin', [1, 2]);
  });

  it('should determine if pinned message is selected', () => {
    const sampleMessageList = [
      { checked: true, pinnedStatus: true },
      { checked: true, pinnedStatus: false },
      { checked: false, pinnedStatus: false }
    ];
    component.messageList = sampleMessageList;
    const { isPinnedSelected } = component;
    expect(isPinnedSelected).toBe(true);
  });

  it('should open action sheet with correct buttons', async () => {
    const message = { pinnedStatus: true };
    const i = 0;
    const event = { close: jasmine.createSpy('close') };
    component.openActionSheet(message as any, i, event);
    expect(component.openActionSheet).toBeDefined();
  });

  it('execute archiveChat: Normal alert', () => {
    const payload = {
      userid: '3',
      baseId: 0,
      messageType: '',
      message_group_id: '0',
      category: 'chat-messages'
    };
    const event = {
      close: () => {
        return;
      }
    };
    component.archiveChat(payload as any, 0, event);
    expect(component.archiveChat).toBeTruthy();
  });

  it('execute archiveChat: alert for room includes patient', () => {
    component.archiveChatMessagesForAllUser = true;
    sharedService.userData.group = '4';
    const payload = {
      userid: '3',
      patient_userid: '12',
      baseId: 0,
      messageType: '0',
      message_group_id: '0',
      chatroomid: '3'
    };
    const event = {
      close: () => {
        return;
      }
    };
    const response = { staffCount: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.archiveChat(payload as any, 0, event);
    expect(component.archiveChat).toBeTruthy();
  });

  it('execute archiveChat: alert for room includes patient, more than 1 staffs', () => {
    component.archiveChatMessagesForAllUser = true;
    sharedService.userData.group = '4';
    const payload = {
      userid: '3',
      patient_userid: '12',
      baseId: 0,
      messageType: '0',
      message_group_id: '0',
      chatroomid: '3'
    };
    const event = {
      close: () => {
        return;
      }
    };
    spyOn(commonService, 'showCustomAlert').and.resolveTo(true);
    const response = { staffCount: 2 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.archiveChat(payload as any, 0, event);
    expect(component.archiveChat).toBeTruthy();
  });

  it('should archive chat with normal message type and privilege', () => {
    const message = {
      chatHeading: 'heading',
      chatSubHeading: 'SUb Heading',
      messageType: Constants.normalMessageType,
      chatroomId: 'someRoomId',
      chatWith: 'user1',
      messageCategory: 'general',
      chatWithRole: 'Staff'
    };
    const index = 0;
    const event = { close: jasmine.createSpy('close') };
    spyOn(component, 'getStaffUsersCount').and.returnValue({
      subscribe: (callback: (response: any) => void) => {
        callback({ success: true, data: { accessibleUserCount: 2 } });
      }
    });
    spyOn(component, 'showArchiveAlertWithPatient');
    spyOn(component, 'showNormalAlert');
    component.archiveChatMessagesForAllUser = true;
    component.archiveChat(message as any, index, event);
    expect(event.close).toHaveBeenCalled();
    expect(component.getStaffUsersCount).toHaveBeenCalledWith(message.chatroomId);
    expect(component.showArchiveAlertWithPatient).toHaveBeenCalledWith(message.chatHeading, message.chatSubHeading, 2, index, message);
    expect(component.showNormalAlert).not.toHaveBeenCalled();
  });

  it('execute archivedMaskedMessage', () => {
    sharedService.userData.group = '4';
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const payload = {
      userid: '3',
      createdby: '3',
      invited_status: '',
      chatroomid: '3',
      subList: [],
      message: { subList }
    };
    const response = { status: 1 };
    sharedService.messageList = component.messageList;
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.archivedMaskedMessage(payload as any, 0);
    expect(component.archivedMaskedMessage).toBeTruthy();
  });

  it('execute archivedMaskedMessage: failure', () => {
    sharedService.userData.group = '4';
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const payload = {
      userid: '3',
      createdby: '3',
      invited_status: '',
      chatroomid: '3',
      subList: [],
      message: { subList }
    };
    const response = { status: 0 };
    sharedService.messageList = component.messageList;
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.archivedMaskedMessage(payload as any, 0);
    expect(component.archivedMaskedMessage).toBeTruthy();
  });

  it('execute multipleArchive', () => {
    const payload = {
      chatroomid: 2,
      checked: true,
      childExpanded: false,
      unreadCount: 2,
      maskedReplyMessages: [{ unreadCount: 0 }],
      markAsRead: true,
      category: 'chat-messages'
    };
    component.messageList.push(payload as any);
    sharedService.messageList = component.messageList;
    component.multipleArchive();
    expect(component.multipleArchive).toBeTruthy();
  });

  it('execute multipleArchive: Masked', () => {
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const message = {
      userid: '3',
      createdby: '3',
      chatroomid: 2,
      checked: true,
      childExpanded: false,
      messageType: '2',
      unreadCount: 2,
      maskedReplyMessages: [{ unreadCount: 0 }],
      markAsRead: true,
      category: 'chat-messages',
      subList,
      invited_status: '1'
    };
    component.messageList.push(message);
    sharedService.messageList = component.messageList;
    component.multipleArchive();
    expect(component.multipleArchive).toBeTruthy();
  });

  it('execute multipleArchive: Masked, invited status 0', () => {
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const message = {
      userid: '3',
      createdby: '3',
      chatroomid: 2,
      checked: true,
      childExpanded: false,
      messageType: '2',
      unreadCount: 2,
      maskedReplyMessages: [{ unreadCount: 0 }],
      markAsRead: true,
      category: 'chat-messages',
      subList,
      invited_status: '0'
    };
    component.messageList.push(message);
    sharedService.messageList = component.messageList;
    component.multipleArchive();
    expect(component.multipleArchive).toBeTruthy();
  });

  it('execute multipleArchive: category is inventory-counts', () => {
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const message = {
      userid: '3',
      createdby: '3',
      chatroomid: 2,
      checked: true,
      childExpanded: false,
      messageType: '2',
      unreadCount: 2,
      maskedReplyMessages: [{ unreadCount: 0 }],
      markAsRead: true,
      category: 'inventory-counts',
      subList,
      invited_status: '0'
    };
    component.messageList.push(message);
    sharedService.messageList = component.messageList;
    component.multipleArchive();
    expect(component.multipleArchive).toBeTruthy();
  });

  it('execute multipleArchive: category is signature-documents', () => {
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const message = {
      userid: '3',
      createdby: '3',
      chatroomid: 2,
      checked: true,
      childExpanded: false,
      messageType: '2',
      unreadCount: 2,
      maskedReplyMessages: [{ unreadCount: 0 }],
      markAsRead: true,
      category: 'signature-documents',
      subList,
      invited_status: '0'
    };
    component.messageList.push(message);
    sharedService.messageList = component.messageList;
    component.multipleArchive();
    expect(component.multipleArchive).toBeTruthy();
  });

  it('execute multipleArchive: category is structured-forms', () => {
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const message = {
      userid: '3',
      createdby: '3',
      chatroomid: 2,
      checked: true,
      childExpanded: false,
      messageType: '2',
      unreadCount: 2,
      maskedReplyMessages: [{ unreadCount: 0 }],
      markAsRead: true,
      category: 'structured-forms',
      subList,
      invited_status: '0'
    };
    component.messageList.push(message);
    sharedService.messageList = component.messageList;
    component.multipleArchive();
    expect(component.multipleArchive).toBeTruthy();
  });

  it('execute showCommonErrorMessage: default', () => {
    const payload = {};
    component.showCommonErrorMessage(payload as any);
    expect(component.showCommonErrorMessage).toBeTruthy();
  });

  it('execute showCommonErrorMessage', () => {
    const payload = { error: 'Test error' };
    component.showCommonErrorMessage(payload as any);
    expect(component.showCommonErrorMessage).toBeTruthy();
  });

  it('execute filterData', () => {
    const payload = { filter: { tags: '' } };
    component.messageReqBody.flagValue = 2;
    spyOn(sharedService, 'fetchAllMessages').and.returnValue(of({ message: component.messageList }));
    component.filterData(payload as any);
    expect(component.filterData).toBeTruthy();
  });

  it('execute filterSitesData', () => {
    const payload = [1] as any;
    component.messageReqBody.flagValue = 2;
    spyOn(sharedService, 'fetchAllMessages').and.returnValue(of({ message: component.messageList }));
    component.filterSitesData(payload as any);
    expect(component.filterSitesData).toBeTruthy();
  });

  it('execute restoreMaskedMessages', () => {
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const message = {
      userid: '3',
      createdby: '3',
      chatroomid: 2,
      checked: true,
      childExpanded: false,
      messageType: '2',
      unreadCount: 2,
      maskedReplyMessages: [{ unreadCount: 0 }],
      markAsRead: true,
      category: 'structured-forms',
      subList,
      invited_status: '0'
    };
    const response = { message: component.messageList, status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.restoreMaskedMessages(message as any, 0);
    expect(component.restoreMaskedMessages).toBeTruthy();
  });

  it('execute restoreMaskedMessages: failure', () => {
    const subList = [{ chatroomid: '1', baseId: '1' }];
    const message = {
      userid: '3',
      createdby: '3',
      chatroomid: 2,
      checked: true,
      childExpanded: false,
      messageType: '2',
      unreadCount: 2,
      maskedReplyMessages: [{ unreadCount: 0 }],
      markAsRead: true,
      category: 'structured-forms',
      subList,
      invited_status: '0'
    };
    const response = { status: 0 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.restoreMaskedMessages(message as any, 0);
    expect(component.restoreMaskedMessages).toBeTruthy();
  });

  it('execute restoreChatroomMessages: failure', () => {
    const message = {
      chatroomid: 2
    };
    const response = { status: 0 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.restoreChatroomMessages(message as any, 0);
    expect(component.restoreChatroomMessages).toBeTruthy();
  });

  it('execute restoreChatroomMessages', () => {
    const message = {
      chatroomid: 2
    };
    const response = { message: component.messageList, status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.restoreChatroomMessages(message as any, 0);
    expect(component.restoreChatroomMessages).toBeTruthy();
  });

  it('execute restoreChat', () => {
    const message = {
      chatroomid: 2,
      category: 'chat-messages'
    };
    const event = {
      close: () => {
        return;
      }
    };
    component.restoreChat(message as any, 0, event);
    expect(component.restoreChat).toBeTruthy();
  });

  it('execute restoreChat: masked', () => {
    const message = {
      chatroomid: 2,
      category: 'chat-messages',
      messageType: 2
    };
    const event = {
      close: () => {
        return;
      }
    };
    component.restoreChat(message as any, 0, event);
    expect(component.restoreChat).toBeTruthy();
  });

  it('execute doArchive', () => {
    const message = {
      chatroomid: 2,
      category: 'chat-messages',
      messageType: 2
    };
    const event = {
      close: () => {
        return;
      }
    };
    sharedService.messageList = component.messageList;
    const response = { message: component.messageList, status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doArchive(message as any, 0);
    expect(component.doArchive).toBeTruthy();
  });

  it('execute fetchRoomUsers', () => {
    const message = {
      chatroomid: 2,
      category: 'chat-messages',
      messageType: 2
    };
    sharedService.messageList = component.messageList;
    const response = [{ roleId: '3' }];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchRoomUsers(message as any, 0);
    expect(component.fetchRoomUsers).toBeTruthy();
  });

  it('execute fetchRoomUsers: throw error', () => {
    const message = {
      chatroomid: 2,
      category: 'chat-messages',
      messageType: 2
    };
    sharedService.messageList = component.messageList;
    const response = '';
    spyOn(httpService, 'doGet').and.returnValue(throwError(response));
    component.fetchRoomUsers(message as any, 0);
    expect(component.fetchRoomUsers).toBeTruthy();
  });

  it('execute doForward: keepForwarder', () => {
    component.forwardData = {
      rerouteBehaviour: 'keepForwarder'
    };
    const response = { status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doForward(0);
    expect(component.doForward).toBeTruthy();
  });

  it('execute doForward: load all', () => {
    component.forwardData = {
      rerouteBehaviour: ''
    };
    const response = { status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doForward(0);
    expect(component.doForward).toBeTruthy();
  });

  it('execute doForward: Message already exist forward', () => {
    component.forwardData = {
      rerouteBehaviour: ''
    };
    const response = { status: 0 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doForward(0);
    expect(component.doForward).toBeTruthy();
  });

  it('execute doRefresh', () => {
    const event = {
      close: () => {
        return;
      }
    };
    const response = { message: [] };
    spyOn(sharedService, 'fetchAllMessages').and.returnValue(of(response));
    component.doRefresh(event);
    expect(component.doRefresh).toBeTruthy();
  });

  it('execute ionViewWillEnter', () => {
    component.ionViewWillEnter();
    expect(component.ionViewWillEnter).toBeTruthy();
  });

  it('execute loadMoreMessages', () => {
    const response = { message: [] };
    sharedService.messageList = [] as any;
    spyOn(sharedService, 'fetchAllMessages').and.returnValue(of(response));
    component.loadMoreMessages();
    expect(component.loadMoreMessages).toBeTruthy();
  });

  it('execute presentForwardBehaviourModal', fakeAsync(() => {
    component.forwardData = {
      rerouteBehaviour: ''
    };
    const response = { data: { forwardBehaviour: '' } };
    modalSpy.onDidDismiss.and.resolveTo(response);
    component.presentForwardBehaviourModal(0);
    expect(component.presentForwardBehaviourModal).toBeTruthy();
  }));

  it('execute presentActionSheet', fakeAsync(() => {
    const payload = {};
    const event = {
      close: () => {
        return;
      }
    };
    component.presentActionSheet(payload as any, 0, event);
    expect(component.presentActionSheet).toBeTruthy();
  }));

  it('execute deleteMessage', () => {
    const payload = {
      chatroomid: '1',
      messageType: '1',
      patient_userid: ''
    };
    const response = { status: 1 };
    spyOn(httpService, 'doPut').and.returnValue(of(response));
    component.deleteMessage(payload as any, 0, '');
    expect(component.deleteMessage).toBeTruthy();
  });

  it('execute deleteMessage: failure', () => {
    const payload = {
      chatroomid: '1',
      messageType: '1',
      patient_userid: ''
    };
    const response = { status: 0 };
    spyOn(httpService, 'doPut').and.returnValue(of(response));
    component.deleteMessage(payload as any, 0, '');
    expect(component.deleteMessage).toBeTruthy();
  });

  it('execute deleteSignatureDocument', () => {
    const payload = {
      id: '1'
    };
    const response = { status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.deleteSignatureDocument(payload as any, 0);
    expect(component.deleteSignatureDocument).toBeTruthy();
  });

  it('execute deleteSignatureDocument: failure', () => {
    const payload = {
      id: '1'
    };
    const response = { status: 0 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.deleteSignatureDocument(payload as any, 0);
    expect(component.deleteSignatureDocument).toBeTruthy();
  });

  it('execute deleteInventoryCount', () => {
    const payload = {
      id: '1'
    };
    const response = { status: 1 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.deleteInventoryCount(payload as any, 0);
    expect(component.deleteInventoryCount).toBeTruthy();
  });

  it('execute deleteInventoryCount: failure', () => {
    const payload = {
      id: '1'
    };
    const response = { status: 0 };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.deleteInventoryCount(payload as any, 0);
    expect(component.deleteInventoryCount).toBeTruthy();
  });

  it('execute showArchiveAlertWithPatient: All staffs', () => {
    const payload = {
      chatroomid: '1',
      messageType: '1',
      patient_userid: ''
    };
    spyOn(commonService, 'showCustomAlert').and.resolveTo('all-user');
    spyOn(httpService, 'doPut').and.returnValue(of({}));
    spyOn(component, 'deleteMessage').and.callThrough();
    component.showArchiveAlertWithPatient('Test', '', 1, 0, payload);
    expect(component.showArchiveAlertWithPatient).toBeTruthy();
  });

  it('execute showArchiveAlertWithPatient: for-me', () => {
    const payload = {
      chatroomid: '1',
      messageType: '1',
      patient_userid: ''
    };
    spyOn(commonService, 'showCustomAlert').and.resolveTo('for-me');
    spyOn(httpService, 'doPut').and.returnValue(of({}));
    spyOn(component, 'deleteMessage').and.stub();
    component.showArchiveAlertWithPatient('Test', '', 1, 0, payload);
    expect(component.showArchiveAlertWithPatient).toBeTruthy();
  });

  it('execute showArchiveAlertWithPatient: cancel', () => {
    spyOn(commonService, 'showCustomAlert').and.resolveTo('cancel');
    component.showArchiveAlertWithPatient('Test', '', 1, 0);
    expect(component.showArchiveAlertWithPatient).toBeTruthy();
  });

  it('execute showNormalAlert: Masked', () => {
    const payload = {
      category: 'chat-messages',
      messageType: 2
    };
    component.showNormalAlert(payload as any, 0);
    expect(component.showNormalAlert).toBeTruthy();
  });

  it('execute showNormalAlert: signature-documents', () => {
    const payload = {
      category: 'signature-documents',
      messageType: 2
    };
    component.showNormalAlert(payload as any, 0);
    expect(component.showNormalAlert).toBeTruthy();
  });

  it('execute showNormalAlert: inventory-counts', () => {
    const payload = {
      category: 'inventory-counts',
      messageType: 2
    };
    component.showNormalAlert(payload as any, 0);
    expect(component.showNormalAlert).toBeTruthy();
  });

  it('execute getStaffUsersCount', () => {
    component.getStaffUsersCount(0);
    expect(component.getStaffUsersCount).toBeTruthy();
  });

  it('execute archiveAll', () => {
    const response = {
      status: 1,
      data: [{ name: 'test' }]
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.archiveAll(response);
    expect(component.archiveAll).toBeTruthy();
  });

  it('should remove message tags', () => {
    component.messageList = [
      { chatroomid: 1, loading: true },
      { chatroomid: 2, loading: false }
    ];
    const tagIdToRemove = 2;
    const chatroomId = 2;
    component.removeMessageTags(tagIdToRemove, chatroomId);
    expect(component.removeMessageTags).toBeDefined();
  });

  it('should update messageList and sharedService.messageList on callback', () => {
    const message = { /* your message object */ };
    const i = 0;
    const event = { close: jasmine.createSpy('close') };

    component.presentActionSheet(message as any, i, event);
  });
  it('should call doTagThread and handle success response', () => {
    const mockThread = { chatroomid: 1, loading: false };
    const mockTagIds = [1, 2, 3];
    spyOn(httpService, 'doPost').and.returnValue(of({ success: true }));
    spyOn(component, 'doRefresh');
    component.doTagThread(mockThread, mockTagIds);
    expect(mockThread.loading).toBe(false);
  });

  it('should call doTagThread and handle error response', () => {
    const mockThread = { chatroomid: 1, loading: false };
    const mockTagIds = [1, 2, 3];
    const errorMessage = 'An error occurred';
    spyOn(httpService, 'doPost').and.returnValue(of({ success: false, error: errorMessage }));
    component.doTagThread(mockThread, mockTagIds);
    expect(mockThread.loading).toBe(false);
  });

  it('execute ngOnInit', () => {
    sharedService.messageListUpdated.next('update');
    component.isArchived = false;
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('execute archiveAll : status !== 1', () => {
    const response = {};
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.archiveAll(response);
    expect(component.archiveAll).toBeTruthy();
  });

  it('execute viewTagClickedHandle', () => {
    component.viewTagClickedHandle(true);
    expect(component.viewTagClickedHandle).toBeTruthy();
  });

  it('execute manageTags: existing tags found', () => {
    sharedService.userData.group = '3';
    const response = [{ userType: 'patient-facing', id: 1 }];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.manageTags({ threadTagList: [{ id: 1 }] }, 1, { close: () => undefined });
    expect(component.manageTags).toBeTruthy();
  });

  it('execute manageTags', () => {
    sharedService.userData.group = '3';
    const response = [{ userType: 'patient-facing', id: 2 }];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.manageTags({}, 1, { close: () => undefined });
    expect(component.manageTags).toBeTruthy();
  });

  it('execute manageTags: throw error', () => {
    const response = [];
    spyOn(httpService, 'doGet').and.returnValue(throwError(response));
    component.manageTags({}, 1, { close: () => undefined });
    expect(component.manageTags).toBeTruthy();
  });

  it('execute doTagThread : status success', () => {
    const response = { success: true };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doTagThread({ loading: false }, [1]);
    expect(component.doTagThread).toBeTruthy();
  });

  it('execute doTagThread : status error', () => {
    const response = { error: 'message' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doTagThread({ loading: false }, [1]);
    expect(component.doTagThread).toBeTruthy();
  });

  it('execute removeMessageTags', () => {
    component.messageList = [{ chatroomid: 21 }];
    component.removeMessageTags(1, 21);
    expect(component.removeMessageTags).toBeTruthy();
  });

  it('should load filter data', () => {
    spyOn(component, 'loadAllMessages');
    spyOn(persistentService, 'setPersistentData');
    const value = { text: 1, dates: { from: '2021-01-01', to: '2021-01-31' } };
    component.loadFilterData(value);
    expect(component.loadAllMessages).toHaveBeenCalled();
    expect(persistentService.setPersistentData).toHaveBeenCalled();
  });

  it('should return true if filter is applied', () => {
    component.messageReqBody.selectedDateOptions = 1;
    expect(component.isFilterApplied).toBeTrue();
  });
  it('should get stored data', () => {
    spyOn(persistentService, 'getPersistentData').and.returnValue('');
    component.getStoredData();
    expect(persistentService.getPersistentData).toHaveBeenCalled();
  });

  it('should clear stored data', () => {
    spyOn(persistentService, 'removePersistentData');
    component.clearStoredData();
    expect(persistentService.removePersistentData).toHaveBeenCalled();
  });

  it('should show filter notification', () => {
    component.messageReqBody.selectedDateOptions = 1;
    component.showFilterNotification();
    expect(component.showNotify).toBeTrue();
  });

  it('should set filter keys from stored filter keys', () => {
    spyOn(persistentService, 'getPersistentData').and.returnValue({});
    component.setFilterKeysFromStoredFilterKeys();
    expect(persistentService.getPersistentData).toHaveBeenCalled();
  });
  describe('updateMessagesOnEvent', () => {
    beforeEach(() => {
      sharedService.messageUnreadCount = 2;
    });
    it('should set loader when event fetching is happening', () => {
      component.messageList = [{ chatroomId: 435, loading: false }];
      component.updateMessagesOnEvents({ chatRoomId: 435 });
      expect(component.messageList[0].loading).toBe(true);
    });
    it('should increment count for already existing chatroom', () => {
      component.messageList = [
        { chatroomId: 437, loading: false, unreadCount: 2, pinnedStatus: true },
        { chatroomId: 436, loading: false, unreadCount: 2, pinnedStatus: false },
        { chatroomId: 435, loading: false, unreadCount: 2, pinnedStatus: true }
      ];
      component.updateMessagesOnEvents({ message: { chatroomId: 437, unreadCount: 3 }, incrementCount: 1 });
      expect(sharedService.messageUnreadCount).toBe(3);
      expect(component.messageList[0].unreadCount).toBe(3);
    });
    it('should insert message for new chatroom', () => {
      component.messageList = [
        { chatroomId: 437, loading: false, unreadCount: 2, pinnedStatus: false },
        { chatroomId: 436, loading: false, unreadCount: 2, pinnedStatus: false },
        { chatroomId: 435, loading: false, unreadCount: 2, pinnedStatus: false }
      ];
      component.updateMessagesOnEvents({ message: { chatroomId: 438, unreadCount: 3 }, incrementCount: 3 });
      expect(sharedService.messageUnreadCount).toBe(5);
      expect(component.messageList[0].unreadCount).toBe(3);
      expect(component.messageList[0].chatroomId).toBe(438);
    });
  });

  it('should format message deleted time correctly', () => {
    const dateTimeUTC = '2023-10-10T10:10:10Z';
    const formattedTime = component.formatMessageDeletedTime(dateTimeUTC);
    expect(formattedTime).toBe(moment(dateTimeUTC).format(msgHistoryDateTimeFormat));
  });

  it('should return empty string if dateTimeUTC is empty', () => {
    const formattedTime = component.formatMessageDeletedTime('');
    expect(formattedTime).toBe('');
  });
});
