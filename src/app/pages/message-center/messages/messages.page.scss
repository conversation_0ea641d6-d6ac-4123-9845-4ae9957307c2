ion-content {

    ion-list {
        padding-top: 0px;
    }
}

.site-filter-top {
    margin-top: 16px;
    color: #58595B;
    line-height: 16px;
} 
.sub-list-loader {
    margin-left: 54px;
}

.multi-select-head {
    background: #ffffff;
    border: none;
    height: 80px;
    position: relative;
    border-bottom: 1px solid #e6e6e6;
    padding-top: 28px;

    .sub-title {
        color: var(--ion-color-skin-secondary-bright);
        font-size: 18px;
        line-height: 28px;
        text-align: center;
    }

    ion-icon {
        color: #369ab1;
        font-size: 27px;
    }
}

.date-block {
    width: 50px;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: space-around;
    margin-left: auto;
    text-align: center;
}

.message-display {
    padding-bottom: 0;
    width: 100%;
    border-bottom: 1px solid #e6e6e6;
    --background: #fff;

    &.unread {

        border-left: 5px solid #64c28d;
        --background: #ecf8f1;

        .inbox-message-details {

            .inbox-per-name {
                color: #369ab1;
            }

            .unread-count {
                position: absolute;
                left: 5px;
                top: 9px;
                background: var(--ion-color-count-dark-bg) none repeat scroll 0 0;
                border-radius: 50%;
                color: var(--ion-color-count-dark-color);
                display: block;
                font-size: 12px;
                line-height: 16px;
                min-height: 20px;
                padding: 2px;
                min-width: 20px;
                text-align: center;
            }
        }

    }

    .msg-checkbox {
        margin-right: 10px;
    }

    .inbox-message-details {
        display: flex;
        width: 100%;
        padding: 10px 22px 3px 10px;
        justify-content: flex-start;
        flex-direction: row;
        flex-wrap: nowrap;
        text-transform: capitalize;

        .masked-caret {
            font-size: 25px;
            position: absolute;
            right: 96px;
            bottom: 18px;
            background: #fafafa;
            color: #607d8b;
            border-radius: 18px;
            padding: 4px;
        }

        .chat-details-middle {
            text-align: left;
            padding-left: 10px;
            width: 80%;
        }

        .inbox-per-name {
            font-weight: 600;
            font-size: 17px;
        }

        .inbox-mes-name {
            font-weight: 600;
            color: #404040;
            font-size: 14px;
            font-family: 'Open Sans', sans-serif;
            line-height: 16px;
        }

        .forward-content {
            color: var(--ion-color-skin-secondary-bright);
            font-size: 13px;
        }

        .message-date {
            font-size: 11px;
            color: #58595b;
            font-family: 'Open Sans', sans-serif;
            font-weight: 300;
            width: 50px;
        }

        .flag-container {
            width: 50px;
            display: flex;
            flex-direction: row;
            justify-content: center;
        }

        .inbox-message-content {
            font-size: 12px;
            font-family: 'Open Sans', sans-serif;
            line-height: 16px;
            color: #58595b;
            max-height: 18px;
            overflow: hidden;


        }

        .avatar {
            width: 50px;
            height: 50px;
            .avatar-container {
                width: 50px;
                height: 50px;
            }
            img {
                width: 50px;
                height: 50px;
                border-radius: 100%;
                -webkit-border-radius: 100%;
                -moz-border-radius: 100%;
                -ms-border-radius: 100%;
                -webkit-touch-callout: none;
                user-select: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                object-fit: cover;
                background: #e6e5eb;
                border: 1px solid #e6e6e6 !important;
           }
        }
    }
}

.sub-message-section {
    .message-display {
        --background: #fafafa;
    }

    .inbox-message-details {
        padding-left: 54px;

        .unread-count {
            left: 55px !important;
        }

        .avatar img {
            width: 45px;
            height: 45px;
        }
    }

    .chat-details-middle {
        .inbox-message-from {
            .inbox-per-name {
                font-size: 15px;
            }

            .inbox-mes-name {
                font-size: 12px;
            }
        }

        .inbox-message-content {
            font-size: 11px;
        }
    }
}
.flex-align {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
}
.bottom-content-margin {
    margin-left: 3px;
}
.message-display:hover {
    --background: #f2f2f2;
    background: #f2f2f2;
    cursor: pointer;
}

.no-message {
    text-align: center;

    img {
        margin: auto;
        display: block;
        padding-top: 75px;
    }

    .head-text {
        color: #369ab1;
        font-size: 21px;
        margin: 25px 0;
        line-height: 25px;
    }

    .no-msg-archived {
        color: var(--ion-color-skin-secondary-bright);
        font-size: 18px;
        margin: 25px 0;
        line-height: 25px;
    }

    .sub-text {
        color: #595959;
        font-size: 18px;
        line-height: 25px;
    }
}

app-footer {
    .footer-buttons {
        margin-bottom: 10px;

        .button-element {
            margin-left: 25px;

            .read-label {
                color: var(--ion-color-skin-secondary-bright);
                font-size: 12px;
            }
        }

    }
}

::ng-deep .message-listing-page .site-header{
    padding-top: 23px;
}

ion-item-option::part(native) {
    min-width: 80px;
}

.alert-outline.danger, .alert-fill.danger {
    background-color: #eb445a;
}

.arrowdown-outline.primary, .arrowdown-fill.primary {
    background-color: #3880ff;
}

ion-item-option::part(native) {
    min-width: 80px;
}

.gap-1 {
    gap: 2px;
}

:host ::ng-deep .callout-content {
    display: none;
}


:host ::ng-deep .mention {
    background: none!important;
    border: none;
    padding: 0!important;
    color: inherit;
    font-weight: inherit;
}

.at-fill, .at-outline {
    margin-right: 7px;
}

.pinned-border {
    border-top: 5px solid #dbd6d6;
}
