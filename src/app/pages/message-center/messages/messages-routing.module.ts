import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MessagesPage } from './messages.page';
import { NgxPermissionsGuard } from 'ngx-permissions';
import { permissionWithParamsType } from '../../../constants/permissions';
import { PageRoutes } from '../../../constants/page-routes';

const routes: Routes = [
    {
        path: '',
        redirectTo: 'active'
    },
    {
        path: ':type',
        component: MessagesPage,
        canActivate: [NgxPermissionsGuard],
        data: {
            permissions: {
                only: permissionWithParamsType,
                redirectTo: PageRoutes.accessDenied
            }
        }
    },
    {
        path: ':type/chat',
        loadChildren: () => import('../chat/chat.module').then((m) => m.ChatPageModule)
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class MessagesPageRoutingModule { }
