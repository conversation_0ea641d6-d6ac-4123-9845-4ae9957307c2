import { Component, OnInit, ViewChild } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Nav<PERSON>ontroller, ModalController, IonInfiniteScroll, ActionSheetController } from '@ionic/angular';
import { CommonService } from 'src/app/services/common-service/common.service';
import { ActivatedRoute } from '@angular/router';
import { Constants, MessageCategory, MessagePriority, msgHistoryDateTimeFormat, TagType, UserGroup } from 'src/app/constants/constants';
import { ForwardComponent } from 'src/app/pages/message-center/forward/forward.component';
import { APIs } from 'src/app/constants/apis';
import { HttpService } from 'src/app/services/http-service/http.service';
import { ForwardBehaviourComponent } from 'src/app/pages/message-center/forward-behaviour/forward-behaviour.component';
import { Config } from 'src/app/constants/config';
import { Activity } from 'src/app/constants/activity';
import { arraysMatch, deepCopyJSON, isBlank, isPresent, subtractCount } from 'src/app/utils/utils';
import { MessageInboxPayload, MessageTagList, MessagesResponse } from 'src/app/interfaces/messages';
import { UnicodeConvertPipe } from 'src/app/pipes/unicodeConvert/unicode-convert.pipe';
import { Permissions } from 'src/app/constants/permissions';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { AdvancedSelectComponent } from 'src/app/components/advanced-select/advanced-select.component';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Socket } from 'src/app/constants/socket';
import { Subscription } from 'rxjs';
import { MessageCenterService } from 'src/app/services/message-center/message-center.service';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import * as moment from 'moment';

enum ArchiveType {
  ALL_USER = 'all-user',
  FOR_ME = 'for-me',
  CANCEL = 'cancel'
}

@Component({
  selector: 'app-messages',
  templateUrl: './messages.page.html',
  styleUrls: ['./messages.page.scss'],
  providers: [UnicodeConvertPipe]
})
export class MessagesPage implements OnInit {
  @ViewChild(IonInfiniteScroll) infiniteScroll: IonInfiniteScroll;
  subscription: Subscription;
  messageList: any;
  pageCount = Constants.defaultPageCount;
  button = Constants.startNewChatButton;
  currentUser: string;
  isArchived: boolean;
  isPatient: boolean;
  isPartner: boolean;
  monthTypes = Constants.monthTypes.messages;
  pinnedIndex: number;
  forwardData: any;
  enableFlagging = false;
  messageReqBody: MessageInboxPayload = {
    archived: false,
    viewInventory: true,
    showChatHistory: true,
    pageCount: Constants.defaultPageCount,
    searchKeyword: '',
    flagValue: 0,
    priorityValue: 0,
    siteIds: [],
    isShowLoader: true,
    unread: false,
    filterTags: [],
    mentionUsers: false,
    selectedDateOptions: Constants.filterSelectedOptions.lastMonth,
    dateRange: null,
    chatThreadTypes: []
  };
  archivedMessageFilterKey = {
    filterTags: [],
    priorityValue: 0,
    mentionUsers: false,
    unread: false,
    flagValue: 0,
    chatThreadTypes: []
  };
  title: string;
  broadcastLabel: string;
  meLabel: string;
  longPressTriggered = false;
  showTagsTriggered = false;
  patientLabel: string;
  caregiverLabel: string;
  isUserGroupPatient: boolean;
  showLoadMoreOptions = true;
  archiveChatMessagesForAllUser: boolean;
  isAlternateContact = false;
  allThreadTags;
  MESSAGE_PRIORITY = MessagePriority;
  messageTypes = Constants.messageListTypes;
  showLoaderOnStickyFiltersExist = false;
  siteLabel = '';
  siteLabelPdg = '';
  searchText = '';
  showNotify: boolean;
  constructor(
    public sharedService: SharedService,
    private readonly navCtrl: NavController,
    private readonly common: CommonService,
    private readonly route: ActivatedRoute,
    private readonly modalController: ModalController,
    private readonly httpService: HttpService,
    private readonly socketService: SocketService,
    private readonly messageCenterService: MessageCenterService,
    private readonly permissionService: PermissionService,
    private readonly actionSheetCtrl: ActionSheetController,
    private readonly persistentService: PersistentService
  ) {
    this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITES');
    this.siteLabelPdg = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITE');
    this.route.paramMap.subscribe((paramMap) => {
      this.isArchived = paramMap.get('type') === 'archived';
      this.messageReqBody.archived = this.isArchived;
      this.isUserGroupPatient = this.sharedService.loggedUserIsPatient();
      if (!this.isArchived && isPresent(this.sharedService.messageList) && !this.isFilterApplied) {
        this.processFetchedMessages(this.sharedService.messageList);
      }
      if (this.isArchived) {
        this.title = this.common.getTranslateData('TITLES.ARCHIVED_MESSAGES');
      } else {
        this.title = this.common.getTranslateData('TITLES.MESSAGES');
      }
      this.getStoredData();
      this.showFilterNotification();
    });
    this.isAlternateContact = this.sharedService.userData?.isAlternateContact;
    this.broadcastLabel = this.common.getTranslateData('LABELS.BROADCAST_MESSAGE');
    this.meLabel = this.common.getTranslateData('LABELS.ME');
    this.patientLabel = this.common.getTranslateData('GENERAL.PATIENT');
    this.caregiverLabel = this.common.getTranslateData('GENERAL.CAREGIVER');
    this.isPatient = this.sharedService.loggedUserIsPatient();
    this.currentUser = this.sharedService.userData?.userId;
    this.isPartner = this.sharedService.loggedUserIsPartner();
  }
  loadFilterData(value: { text: number; dates?: { from: string; to: string } }): void {
    this.messageList = undefined;
    this.showLoadMoreOptions = false;
    this.messageReqBody.selectedDateOptions = isPresent(value.text) ? value.text : 0;
    if (value.text === Constants.filterSelectedOptions.custom && isPresent(value?.dates?.from) && isPresent(value?.dates?.to)) {
      this.messageReqBody.dateRange = value.dates;
    }
    if (!this.isArchived) {
      this.persistentService.setPersistentData(Constants.storageKeys.dateRangeFilterActiveMessages, value.dates);
      this.persistentService.setPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsActiveMessages, value.text);
    } else {
      this.persistentService.setPersistentData(Constants.storageKeys.dateRangeFilterArchivedMessages, value.dates);
      this.persistentService.setPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsArchivedMessages, value.text);
    }
    this.messageReqBody.pageCount = Constants.defaultPageCount;
    this.loadAllMessages();
  }
  get isFilterApplied(): boolean {
    return !(
      !this.isArchived &&
      this.messageReqBody.selectedDateOptions === Constants.filterSelectedOptions.lastMonth &&
      this.messageReqBody.flagValue === 0 &&
      this.messageReqBody.priorityValue === 0 &&
      !this.messageReqBody.unread &&
      isBlank(this.messageReqBody.filterTags) &&
      !this.messageReqBody.mentionUsers &&
      (isBlank(this.messageReqBody.siteIds) || arraysMatch(this.messageReqBody.siteIds, this.sharedService.selectedSites)) &&
      isBlank(this.messageReqBody.searchKeyword)
    );
  }
  storeSingleMessageToShared(message: MessagesResponse): void {
    const index = this.sharedService.messageList.findIndex((x) => x.chatroomId === message.chatroomId);
    if (index !== -1) {
      this.sharedService.messageList[index] = message;
    }
  }
  storeSingleMessage(message: MessagesResponse, i?: number): void {
    if (message.chatroomId === this.messageList[i]) {
      this.messageList[i] = message;
    } else {
      const index = this.messageList.findIndex((x) => x.chatroomId === message.chatroomId);
      if (index !== -1) {
        this.messageList[index] = message;
      }
    }
    this.storeSingleMessageToShared(message);
  }
  storeActiveMessagesAsShared(list: MessagesResponse[]): void {
    if (!this.isFilterApplied) {
      this.sharedService.messageList = list;
    }
  }

  ngOnInit(): void {
    this.updateConfigPermissions();
    this.isUserGroupPatient = this.sharedService.loggedUserIsPatient();
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.updateConfigPermissions();
    });
    if (!this.sharedService.messageListUpdated.observed) {
      this.sharedService.messageListUpdated.subscribe((value: any) => {
        this.updateMessagesOnEvents(value);
      });
    }
  }
  updateMessagesOnEvents(value: any): void {
    if (value?.chatRoomId) {
      const index = this.messageList.findIndex((x) => +x.chatroomId === +value.chatRoomId);
      if (index !== -1) {
        this.messageList[index].loading = true;
      }
    } else if (value.message || value.maskedParent) {
      const messageThread = value?.maskedParent || value?.message;
      const index = this.messageList.findIndex((x) => +x.chatroomId === +messageThread.chatroomId);
      let { incrementCount } = value;
      if (index === -1 && !value?.removeThread) {
        if (!this.messageList) {
          this.messageList = [];
        }
        this.messageList.unshift(messageThread);
        incrementCount = messageThread?.maskedSubCount > 0 ? messageThread?.maskedUnreadCount : messageThread.unreadCount;
      } else if (value?.removeThread) {
        this.messageList.splice(index, 1);
      } else {
        Object.assign(this.messageList[index], messageThread);
      }
      if (isPresent(incrementCount)) {
        this.sharedService.messageUnreadCount += incrementCount;
        if (this.sharedService.messageUnreadCount < 0) {
          this.sharedService.messageUnreadCount = 0;
        }
      }
      this.messageList.sort((a, b) => {
        const sentComparison = Number(b.messageOrder) - Number(a.messageOrder);
        if (a.pinnedStatus && !b.pinnedStatus) {
          return -1;
        }
        if (!a.pinnedStatus && b.pinnedStatus) {
          return 1;
        }
        return sentComparison;
      });
    }
  }

  ionViewWillEnter(): void {
    this.showLoadMoreOptions = false;
    this.messageReqBody.pageCount = Constants.defaultPageCount;
    this.loadAllMessages();
  }

  updateConfigPermissions(): void {
    this.enableFlagging = this.sharedService.isEnableConfig(Config.enableMessageFlagging);
    this.archiveChatMessagesForAllUser = this.permissionService.userHasPermission(Permissions.archiveChatMessagesForAllUser);
  }

  searchMessage(searchData: { text: string }): void {
    if (searchData && searchData.text) {
      this.messageReqBody.pageCount = Constants.defaultPageCount;
      this.messageReqBody.searchKeyword = searchData.text;
      if (!this.isArchived) {
        this.persistentService.setPersistentData(Constants.storageKeys.searchTextActiveMessages, searchData.text);
      } else {
        this.persistentService.setPersistentData(Constants.storageKeys.searchTextArchivedMessages, searchData.text);
      }
      this.messageReqBody.isShowLoader = true;
      this.showLoadMoreOptions = false;
      this.messageList = undefined;
      this.fetchMessages([]);
      this.sharedService.trackActivity({
        type: Activity.messaging,
        name: Activity.searchMessage,
        des: {
          data: {
            displayName: this.sharedService.userData.displayName,
            keyword: searchData.text
          },
          desConstant: Activity.searchMessageDes
        }
      });
    }
  }

  closeSearch(): void {
    if (isBlank(this.messageReqBody.searchKeyword)) {
      return;
    }
    this.messageReqBody.searchKeyword = '';
    this.clearStoredData();
    this.messageReqBody.pageCount = Constants.defaultPageCount;
    this.messageReqBody.isShowLoader = true;
    this.messageList = undefined;
    this.fetchMessages([]);
  }

  loadMoreMessages(): void {
    this.messageReqBody.pageCount += 1;
    this.messageReqBody.isShowLoader = false;
    this.fetchMessages(this.messageList);
  }
  fetchMessages(messageList = []): Promise<{ message: MessagesResponse[] }> {
    if (this.messageReqBody.pageCount === Constants.defaultPageCount) {
      this.infiniteScroll?.complete();
    }
    return new Promise((resolve) => {
      if (this.subscription) {
        this.subscription.unsubscribe();
      }
      this.common.notifySearchFilterApplied(this.showNotify);
      this.subscription = this.sharedService.fetchAllMessages(this.messageReqBody).subscribe({
        next: (result: { message: MessagesResponse[]; totalUnreadMessagesCount: number }) => {
          this.showNotify = false;
          this.showLoaderOnStickyFiltersExist = false;
          resolve(result);
          this.sharedService.messageUnreadCount = result.totalUnreadMessagesCount;
          this.processFetchedMessages(result.message, messageList);
        },
        error: () => {
          this.showLoaderOnStickyFiltersExist = false;
          this.processFetchedMessages([], messageList);
        }
      });
    });
  }
  /**
   * processFetchedMessages
   * @param message  MessagesResponse[]
   * @param messageList  MessagesResponse[]
   */
  processFetchedMessages(message: MessagesResponse[], messageList: MessagesResponse[] = []): void {
    this.messageList = [...(messageList || []), ...message];
    this.storeActiveMessagesAsShared(this.messageList);
    this.pinnedIndex = this.messageList.findIndex((x) => !x.pinnedStatus);
    this.showLoadMore(message);
    this.infiniteScroll?.complete();
  }
  loadAllMessages(): void {
    this.messageReqBody.isShowLoader = true;
    this.getStoredData();
    this.fetchMessages([]);
    this.sharedService.getMessageFormCounts(Constants.countTypes.messages);
  }
  gotoChatPage(message: any, i: number, event: any, childIndex?: number): void {
    const { unreadCount } = this.messageList[i];
    if (this.longPressTriggered || this.showTagsTriggered) {
      return;
    }
    if (event.target.id === 'expand-button' && message.messageType === +Constants.messageListTypes.masked) {
      this.messageList[i] = { ...message, childExpanded: !message?.childExpanded };
      this.storeSingleMessageToShared(this.messageList[i]);
      if (!message.maskedReplyMessages) {
        this.getMaskedReplyMessages(message, i);
      }
      return;
    }
    if (+message.maskedSubCount !== 0 && childIndex !== undefined) {
      this.messageList[i].maskedUnreadCount = subtractCount(
        this.messageList[i]?.maskedUnreadCount,
        this.messageList[i].maskedReplyMessages[childIndex]?.unreadCount
      );
      this.sharedService.messageCount = subtractCount(
        this.sharedService?.messageCount,
        this.messageList[i].maskedReplyMessages[childIndex]?.unreadCount
      );
      this.messageList[i].maskedReplyMessages[childIndex].unreadCount = 0;
      this.messageList[i].childExpanded = false;
      this.messageList[i].hasUnreadMessages = this.messageList[i].maskedUnreadCount !== 0;
      this.storeSingleMessageToShared(this.messageList[i]);
    } else {
      this.sharedService.messageCount = subtractCount(this.sharedService.messageCount, this.messageList[i].unreadCount);
      this.messageList[i].unreadCount = 0;
      this.messageList[i].hasUnreadMessages =
        +message.messageType === +Constants.messageListTypes.masked && +message.maskedSubCount !== 0 ? this.messageList[i].hasUnreadMessages : false;
      this.storeSingleMessageToShared(this.messageList[i]);
    }
    const setURL = message.chatroomId;
    // store chatRoomID in shared service because some time not pass chatRoomID in joinChat to fixed send message issue
    this.sharedService.roomID = message.chatroomId;
    if (this.sharedService.userData.userId !== message.userid && message.grp === Constants.patientGroupId.toString() && unreadCount != 0) {
      this.sharedService.confirmReviewPushNotification(message);
    }
    this.navCtrl.navigateForward(['chat', setURL], {
      relativeTo: this.route,
      queryParams: { createdBy: message.createdby, displayName: message.chatWithName }
    });

    this.sharedService.trackActivity({
      type: Activity.messaging,
      name: Activity.startChatSession,
      des: {
        data: {
          activityContent: `${message.displayName} (${message.chatWithUserId})`,
          user: message.chatWith,
          userId: message.chatWithUserId,
          chatroomId: message.chatroomId,
          details: JSON.stringify(message)
        },
        desConstant: Activity.startChatSessionDes
      },
      linkageId: message.chatroomId
    });
  }

  getMaskedReplyMessages(message: any, i: number): void {
    this.messageList[i].loadingChild = true;
    this.sharedService.fetchReplyMessagesofMasked(message.chatroomId, this.isArchived).subscribe({
      next: (response) => {
        const updatedMessage = { ...message };
        updatedMessage.loadingChild = false;
        updatedMessage.unreadCount = 0;
        updatedMessage.maskedReplyMessages = response.message;
        updatedMessage.childExpanded = true;
        this.storeSingleMessage(updatedMessage, i);
      },
      error: () => {
        const updatedMessage = { ...message };
        updatedMessage.loadingChild = false;
        updatedMessage.childExpanded = false;
        this.storeSingleMessage(updatedMessage, i);
      }
    });
  }

  cancelMultiSelect(): void {
    this.longPressTriggered = false;
    this.messageList.map((ele) => (ele.checked = false));
  }

  fetchRoomUsers(message, index): void {
    let siteId = 0;
    let isDisabledSite = false;
    if (message && message.patientInfo && message.patientInfo.patientSiteId) {
      siteId = +message.patientInfo.patientSiteId;
      isDisabledSite = true;
    }
    this.showForwardModal(message, index, { siteId, isDisabledSite });
  }

  presentForwardModal(message: any, index: number, swipeList: any) {
    swipeList?.close();
    this.fetchRoomUsers(message, index);
  }

  async showForwardModal(message, index, siteParams) {
    const modal = await this.modalController.create({
      component: ForwardComponent,
      componentProps: { selectedMessageData: message, passSiteParams: siteParams }
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.forwardData = {};
        this.forwardData.toId = data.toId;
        this.forwardData.chatroomId = message.chatroomId;
        this.forwardData.message = message.message;
        if (this.sharedService.getConfigValue(Config.messageForwardingBehaviour) === Constants.messageForwardingBehaviourOptions.userPreference) {
          this.presentForwardBehaviourModal(index);
        } else {
          this.forwardData.rerouteBehaviour = this.sharedService.getConfigValue(Config.messageForwardingBehaviour);
          this.doForward(index);
        }
      }
    });
    await modal.present();
  }

  async presentForwardBehaviourModal(index: number): Promise<void> {
    const modal = await this.modalController.create({
      component: ForwardBehaviourComponent
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.forwardData.rerouteBehaviour = data.forwardBehaviour;
        this.doForward(index);
      }
    });
    return modal.present();
  }

  doForward(index: number): void {
    this.messageCenterService.doForwardMessage({ forwardData: this.forwardData }, ({ success, invitedUserInfo }) => {
      if (success) {
        if (this.forwardData.rerouteBehaviour === Constants.messageForwardingBehaviourOptions.keepForwarder) {
          const forwardedLabel = this.common.getTranslateData('LABELS.FORWARDED_TO');
          this.messageList[index].messageForwarded = `${forwardedLabel} ${invitedUserInfo.displayName}`;
        } else {
          this.messageList.splice(index, 1);
        }
        this.storeActiveMessagesAsShared(this.messageList);
        const userDetails = {
          fromName: this.messageList[index].fromName,
          userid: this.messageList[index].userid
        };
        const deepLinking: any = {
          roomId: this.messageList[index].chatroomId,
          messageType: this.messageList[index].messageType,
          baseId: this.messageList[index].baseId,
          messageGroupId: this.messageList[index].message_group_id,
          createdby: this.messageList[index].createdby,
          chatroomid: this.messageList[index].chatroomId
        };
        const pushParams = {
          chatRoomOrToId: this.forwardData.toId,
          userId: Constants.configFalse,
          pushMessage: this.common.getTranslateData('MESSAGES.CHAT_FORWARD_NOTIFICATION'),
          privilegeKey: '',
          showDoubleVerificationStatus: Number(Constants.configFalse)
        };
        const notificationDataRecipient = {
          sourceId: Constants.sourceId.other.toString(),
          sourceCategoryId: Constants.sourceCategoryId.userForwardNotificationForRecipient
        };
        this.sharedService.inviteAndForwardPushNotification(deepLinking, userDetails, pushParams, notificationDataRecipient);
      }
    });
  }

  doRefresh(event: any, thread?: any): void {
    this.messageReqBody.pageCount = Constants.defaultPageCount;
    this.messageReqBody.flagValue = 0;
    this.messageReqBody.isShowLoader = false;
    if(thread) thread.loading = true;
    this.fetchMessages([]).then(() => {
      if (event) {
        event.target.complete();
      }
      if (thread) thread.loading = false;
    });
  }

  showLoadMore(results: any) {
    this.showLoadMoreOptions = isPresent(results) && results.length >= Constants.messageListLoadLimit;
  }
  showForwardButton(message: MessagesResponse) {
    return (
      this.permissionService?.userHasPermission(Permissions.messageForwarding) &&
      this.isGeneralMessageOrGroup(message.messageCategory) &&
      !this.sharedService.loggedUserIsPatient()
    );
  }
  isGeneralMessageOrGroup(messageCategory: MessageCategory): boolean {
    return [MessageCategory.GENERAL, MessageCategory.PDG, MessageCategory.MESSAGE_GROUP].includes(messageCategory);
  }

  longPressOptions(i: number): void {
    if (!this.isArchived) {
      this.longPressTriggered = true;
      this.messageList[i].checked = true;
    }
  }

  archiveChat(message: MessagesResponse, index: number, event: any): void {
    event.close();
    // Check message type and Archive For All Staffs role privilege.
    if (this.isGeneralMessageOrGroup(message.messageCategory) && this.archiveChatMessagesForAllUser) {
      let userCount = 0;
      this.getStaffUsersCount(message.chatroomId).subscribe((response: any) => {
        if (response.success && response.data && response.data.accessibleUserCount) {
          userCount = Number(response.data.accessibleUserCount);
          if (userCount > 1) {
            this.showArchiveAlertWithPatient(message.chatHeading, message.chatSubHeading, userCount, index, message);
          } else {
            this.showNormalAlert(message, index);
          }
        }
      });
    } else {
      this.showNormalAlert(message, index);
    }
  }

  showArchiveAlertWithPatient(chatWith: string, chatWithRole: string, staffCount: number, index: number, selectedMessageList?: any): void {
    const buttons = [
      {
        text: 'BUTTONS.ARCHIVE_FOR_ALL_USER',
        role: 'all-user',
        cssClass: 'warn-btn alert-ok full-width',
        handler: ArchiveType.ALL_USER
      },
      {
        text: 'BUTTONS.ARCHIVE_FOR_ME',
        role: 'me',
        cssClass: 'warn-btn alert-ok full-width',
        handler: ArchiveType.FOR_ME
      },
      {
        text: 'BUTTONS.CANCEL',
        role: 'cancel',
        cssClass: 'warn-btn alert-cencel full-width',
        handler: ArchiveType.CANCEL
      }
    ];
    const alertData = {
      message: this.common.getTranslateDataWithParam('MESSAGES.GOING_TO_ARCHIVE_THIS_MESSAGE_WITH_PATIENT', {
        chatWith,
        chatWithRole,
        staffCount
      }),
      header: 'MESSAGES.ARE_YOU_SURE',
      buttons
    };
    this.common.showCustomAlert(alertData).then((confirmation) => {
      switch (confirmation) {
        case ArchiveType.ALL_USER:
          this.deleteMessage(selectedMessageList, index, ArchiveType.ALL_USER);
          break;
        case ArchiveType.FOR_ME:
          this.deleteMessage(selectedMessageList, index, ArchiveType.FOR_ME);
          break;
        case ArchiveType.CANCEL:
          break;
        default:
          break;
      }
    });
  }

  showNormalAlert(messageData: MessagesResponse, index: number): void {
    const alertData = {
      message: 'MESSAGES.GOING_TO_ARCHIVE_THIS_MESSAGE',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        // Perform action based on category
        if (messageData.messageType !== +Constants.messageListTypes.masked) {
          this.deleteMessage(messageData, index, '');
        } else if (messageData.messageType === +Constants.messageListTypes.masked) {
          this.archivedMaskedMessage(messageData, index);
        }
      }
    });
  }

  /**
   * Delete Message
   * @param data selected message data
   * @param index selected index
   */
  deleteMessage(data: any, index: number, selectedVal: string) {
    const { chatroomId } = data;
    const type = selectedVal === '' || selectedVal === 'for-me' ? 1 : 2;
    const params = {
      chatroomId: Number(chatroomId),
      type
    };
    this.httpService.doPut({ endpoint: APIs.deleteChatroomMessage, payload: params }).subscribe((response) => {
      if (response?.status) {
        this.common.showToast({
          message: this.common.getTranslateData('MESSAGES.MESSAGE_ARCHIVED_SUCCESS')
        });
        this.sharedService.messageCount = subtractCount(this.sharedService.messageCount, this.messageList[index].unreadCount);
        this.messageList.splice(index, 1);
        this.storeActiveMessagesAsShared(this.messageList);
        this.pinnedIndex = this.messageList.findIndex((x) => !x.pinnedStatus);
      } else {
        this.common.showToast({ message: this.common.getTranslateData('MESSAGES.MESSAGE_ARCHIVED_FAILED'), color: 'danger' });
      }
    });
  }

  /**
   * Archived Mask Message
   * @param data selected message data
   * @param index selected index
   */
  archivedMaskedMessage(data: any, index: number) {
    const params = {
      baseId: data.baseChatroomId || Constants.configFalse,
      initiatedBaseId: Constants.configFalse,
      userId: data.userid,
      createdby: data.createdby,
      chatroomid: data.chatroomId,
      subList: '', // Need to handle from API CHP-10103
      invitedStatus: data.invited_status,
      displayname: this.sharedService.userData.displayName
    };
    this.httpService.doPost({ endpoint: APIs.archiveMaskedMessage, payload: params }).subscribe((response) => {
      if (response?.status) {
        this.common.showToast({
          message: this.common.getTranslateData('MESSAGES.MESSAGE_ARCHIVED_SUCCESS')
        });
        this.archiveOrRestorePollingToServer(data.chatroomId);
        this.sharedService.messageCount = subtractCount(this.sharedService.messageCount, this.messageList[index].unreadCount);
        this.messageList.splice(index, 1);
        this.storeActiveMessagesAsShared(this.messageList);
        this.pinnedIndex = this.messageList.findIndex((x) => !x.pinnedStatus);
      } else {
        this.showCommonErrorMessage(response);
      }
    });
  }

  /**
   * Delete Signature Documents
   * @param data selected message data
   * @param index selected index
   */

  deleteSignatureDocument(data: any, index: number) {
    let params = {
      docId: data.id,
      deletedBy: data.to == this.sharedService.userData.userId ? 'to' : 'from'
    };
    this.httpService
      .doPost({ endpoint: APIs.deleteSignatureDoc, payload: params, parseToString: true, contentType: 'form' })
      .subscribe((response) => {
        if (response?.status) {
          this.common.showToast({
            message: this.common.getTranslateData('MESSAGES.MESSAGE_ARCHIVED_SUCCESS')
          });

          this.sharedService.messageCount = subtractCount(this.sharedService.messageCount, this.messageList[index].unreadCount);
          this.messageList.splice(index, 1);
          this.storeActiveMessagesAsShared(this.messageList);
        } else {
          this.showCommonErrorMessage(response);
        }
      });
  }
  /**
   * Delete Inventory Count
   * @param data selected message data
   * @param index selected index
   */
  deleteInventoryCount(data: any, index: number) {
    const params = { id: data.id, level: 0, userId: this.sharedService.userData.userId, status: 1 };
    this.httpService.doPost({ endpoint: APIs.deleteInventory, payload: params, extraParams: { level: params.level } }).subscribe((response) => {
      if (response?.status) {
        this.common.showToast({
          message: this.common.getTranslateData('MESSAGES.MESSAGE_ARCHIVED_SUCCESS')
        });
        this.sharedService.messageCount = subtractCount(this.sharedService.messageCount, this.messageList[index].unreadCount);
        this.messageList.splice(index, 1);
        this.storeActiveMessagesAsShared(this.messageList);
      } else {
        this.showCommonErrorMessage(response);
      }
    });
  }

  doArchive(payload: any, index: number): void {
    this.httpService.doPost({ endpoint: APIs.archiveMessages, payload }).subscribe(() => {
      this.sharedService.messageCount = subtractCount(this.sharedService.messageCount, this.messageList[index].unreadCount);
      this.messageList.splice(index, 1);
      this.storeActiveMessagesAsShared(this.messageList);
      this.pinnedIndex = this.messageList.findIndex((x) => !x.pinnedStatus);
    });
  }

  archiveAll(payload: any): void {
    this.httpService.doPost({ endpoint: APIs.archiveInboxItems, payload, loader: true }).subscribe((response: any) => {
      if (response.status === 1) {
        this.longPressTriggered = false;
        this.common.showMessage(this.common.getTranslateData('MESSAGES.MESSAGE_ARCHIVED_SUCCESS'));
        this.messageReqBody.pageCount = 0;
        this.loadAllMessages();
      } else {
        this.common.showMessage(this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'));
      }
    });
  }

  getStaffUsersCount(chatRoomId: any): any {
    const url = APIs.getChatRoomStaffCount;
    const params = {
      chatRoomId
    };

    return this.httpService.doGet({
      endpoint: url,
      extraParams: params
    });
  }

  restoreChat(message: MessagesResponse, index: number, event: any): void {
    event.close();
    const alertData = {
      message: 'MESSAGES.GOING_TO_RESTORE_THIS_MESSAGE',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        if (message.messageType !== +Constants.messageListTypes.masked) {
          this.restoreChatroomMessages(message, index);
        } else if (message.messageType === +Constants.messageListTypes.masked) {
          this.restoreMaskedMessages(message, index);
        }
      }
    });
  }

  /**
   * Restore Chatroom Message
   * @param message Selected message data
   * @param index Selected message index
   */
  restoreChatroomMessages(message: any, index: number) {
    const body = {
      chatroomId: message.chatroomId
    };
    this.httpService
      .doPost({
        endpoint: APIs.restoreChatroomMessage,
        payload: body,
        contentType: 'form',
        parseToString: true
      })
      .subscribe((response: any) => {
        if (response?.status) {
          this.common.showToast({ message: this.common.getTranslateData('MESSAGES.MESSAGE_RESTORED_SUCCESS') });
          this.archiveOrRestorePollingToServer(message.chatroomId);
          this.messageList.splice(index, 1);
          this.sharedService.getMessageFormCounts(Constants.countTypes.messages);
        } else {
          this.showCommonErrorMessage(response);
        }
      });
  }

  /**
   * Restore Masked Message
   * @param message Selected message data
   * @param index Selected message index
   */
  restoreMaskedMessages(message: MessagesResponse, index: number) {
    const params = {
      baseId: message.baseChatroomId || +Constants.configFalse, // message?.maskedChatRoomBaseId || Constants.configFalse,
      initiatedBaseId: Constants.configFalse, // message?.initiatedBaseId || Constants.configFalse,
      userId: this.sharedService.userData?.userId,
      createdby: message?.createdby,
      chatroomId: message?.chatroomId,
      subList: '', // Need to handle from API CHP-10103
      invitedStatus: message?.invited_status,
      displayname: this.sharedService.userData?.displayName
    };

    this.httpService
      .doPost({
        endpoint: APIs.restoreMaskedMessage,
        payload: params
      })
      .subscribe((response) => {
        if (response?.status) {
          this.common.showToast({ message: this.common.getTranslateData('MESSAGES.MESSAGE_RESTORED_SUCCESS') });
          this.messageList.splice(index, 1);
          this.archiveOrRestorePollingToServer(message?.chatroomId.toString());
          this.sharedService.getMessageFormCounts(Constants.countTypes.messages);
        } else {
          this.showCommonErrorMessage(response);
        }
      });
  }

  async openActionSheet(message: MessagesResponse, i: number, event: any) {
    const buttons = [];
    buttons.push({
      text: this.common.getTranslateData(`${message?.pinnedStatus ? 'BUTTONS.UNPIN' : 'BUTTONS.PIN'}`),
      icon: message?.pinnedStatus ? 'push-pin-slash' : 'push-pin',
      data: {
        action: message.pinnedStatus ? 'unpin' : 'pin'
      }
    });

    if (this.enableFlagging && !message.isPatientInitiatedChat) {
      buttons.push({
        text: this.common.getTranslateData('BUTTONS.FLAG'),
        icon: 'flag',
        data: {
          action: 'flag'
        }
      });
    }
    if (this.showForwardButton(message)) {
      buttons.push({
        text: this.common.getTranslateData('BUTTONS.FORWARD'),
        icon: 'arrow-redo-outline',
        data: {
          action: 'forward'
        }
      });
    }
    if (!this.isPatient && !this.isPartner) {
      buttons.push({
        text: this.common.getTranslateData('BUTTONS.TAGS'),
        icon: 'pricetags-outline',
        data: {
          action: 'tags'
        }
      });
    }

    buttons.push({
      text: this.common.getTranslateData('BUTTONS.CANCEL'),
      role: 'cancel',
      icon: 'close',
      data: {
        action: 'cancel'
      }
    });

    const actionSheet = await this.actionSheetCtrl.create({
      mode: 'ios',
      buttons
    });

    actionSheet.onDidDismiss().then(({ data }) => {
      event.close();
      if (!data) return false;
      if (data.action === 'tags') {
        this.manageTags(message, i, event);
      } else if (data.action === 'flag') {
        this.presentActionSheet(message, i, event);
      } else if (data.action === 'forward') {
        this.presentForwardModal(message, i, event);
      } else if (data.action === 'pin' || data.action === 'unpin') {
        this.updatePinStatus(data.action, [Number(message.chatroomId)]);
      }
    });

    await actionSheet.present();
  }

  /**
   * archiveOrRestorePollingToServer Function
   * @param chatRoomId string room id of chat
   * @param additionalParams Record<string, any> json for additions params to be passed to userMessageToServer
   */
  archiveOrRestorePollingToServer(chatRoomId: string, additionalParams: Record<string, any> = {}): void {
    const message = !this.isArchived
      ? this.common.getTranslateDataWithParam('MESSAGES.ARCHIVED_NO_LONGER_PART_OF_CHAT_MESSAGE', {
          displayName: this.sharedService.userData?.displayName
        })
      : this.common.getTranslateDataWithParam('MESSAGES.RESTORED_CHAT_MESSAGE', {
          displayName: this.sharedService.userData?.displayName
        });
    this.socketService.emitEvent(Socket.userMessagetoServer, {
      ...additionalParams,
      data: message,
      notification: true,
      chatRoomId,
      insert: false,
      tenantId: this.sharedService.userData?.tenantId,
      tenantName: this.sharedService.userData?.tenantName
    });
  }

  presentActionSheet(message: any, i: number, event: any): void {
    event.close();
    this.sharedService.presentActionSheet({ message, index: i, flagApiType: 'thread' }, (flagType: number) => {
      this.messageList[i].chatroomFlag = flagType;
      this.storeActiveMessagesAsShared(this.messageList);
    });
  }

  multipleArchive(): void {
    const getAllSelectedMessage = this.messageList.filter((item: any) => item.checked);
    if (getAllSelectedMessage.length > 0) {
      getAllSelectedMessage.map((item: any) => {
        item.markAsRead = true;
      });
      let payload = {
        userId: this.sharedService?.userData?.userId,
        displayName: this.sharedService?.userData?.displayName,
        chatroomIdsArchive: [],
        maskedMessagesArchive: [],
        maskedMessagesArchivedRoomIds: [],
        inventoryIdsArchive: [],
        signatureDocIdsArchive: [],
        allIds: [],
        message: []
      };
      getAllSelectedMessage.map((item: any, index: number) => {
        if (item.markAsRead === true) {
          if (item.messageType !== +Constants.messageListTypes.masked) {
            payload.chatroomIdsArchive.push(item.chatroomId);
          } else {
            const maskedArchiveData = {
              baseId: item.baseChatroomId || Constants.configFalse,
              initiatedBaseId: item.initiatedBaseId || Constants.configFalse,
              userId: this.sharedService?.userData?.userId,
              createdby: item.createdby,
              chatroomId: item.chatroomId,
              subList: '', // Need to handle from API CHP-10103
              invitedStatus: item.invited_status,
              displayname: this.sharedService?.userData?.displayName
            };
            payload.maskedMessagesArchive.push(maskedArchiveData);
            if (maskedArchiveData.invitedStatus == '0') {
              payload.maskedMessagesArchivedRoomIds.push(maskedArchiveData.chatroomId);
              if (maskedArchiveData?.subList != '') {
                payload.maskedMessagesArchivedRoomIds = payload.maskedMessagesArchivedRoomIds.concat(
                  payload.maskedMessagesArchivedRoomIds,
                  maskedArchiveData?.subList.split(',')
                );
              }
            } else {
              payload.maskedMessagesArchivedRoomIds.push(maskedArchiveData.chatroomId);
              if (maskedArchiveData.userId == maskedArchiveData.createdby) {
                payload.maskedMessagesArchivedRoomIds.push(maskedArchiveData.initiatedBaseId);
              }
            }
          }
          payload.allIds.push(item.id);
          if (index === getAllSelectedMessage.length - 1) {
            payload.chatroomIdsArchive = [...payload.chatroomIdsArchive, ...payload.maskedMessagesArchivedRoomIds];
            this.archiveAll(payload);
          }
        }
      });
    }
  }

  /**
   * Update Pin Chat Thread Status
   * @param actionType Selected action for pin/unpin
   * @param chatRoomIds Selected chat threads for pin/unpin
   */
  updatePinStatus(actionType: string, chatRoomIds: number[]) {
    const params = {
      actionType,
      chatRoomIds
    };
    this.httpService.doPost({ endpoint: APIs.updatePinStatus, payload: params }).subscribe((response) => {
      if (response?.status) {
        this.longPressTriggered = false;
        if (actionType === 'pin') {
          this.common.showMessage(this.common.getTranslateData('MESSAGES.MESSAGE_PIN_SUCCESS'));
        } else {
          this.common.showMessage(this.common.getTranslateData('MESSAGES.MESSAGE_UNPIN_SUCCESS'));
        }
        this.messageReqBody.pageCount = 0;
        this.loadAllMessages();
      } else {
        this.common.showToast({ message: this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'), color: 'danger' });
      }
    });
  }

  multiplePinOrUnpin(): void {
    const getAllSelectedMessage = this.messageList.filter((item) => item?.checked);
    if (getAllSelectedMessage.length > 0) {
      const hasPinnedMessage = getAllSelectedMessage.some((item) => item?.pinnedStatus);
      const actionType = hasPinnedMessage ? 'unpin' : 'pin';
      const chatRoomIds = getAllSelectedMessage.map((item) => Number(item?.chatroomId));
      this.updatePinStatus(actionType, chatRoomIds);
    }
  }

  get isPinnedSelected(): boolean {
    const getAllSelectedMessage = this.messageList.filter((item: any) => item.checked);
    if (getAllSelectedMessage.length > 0) {
      return getAllSelectedMessage.some((item: any) => item.pinnedStatus);
    }
    return false;
  }

  selectAllCheckboxes({ checked }: { checked: boolean }): void {
    this.messageList.map((ele) => (ele.checked = checked));
  }

  get titleWithCount(): string {
    const countSection =
      this.sharedService.messageUnreadCount && this.sharedService.messageUnreadCount > 0 && !this.isArchived
        ? ` (${this.sharedService.messageUnreadCount})`
        : '';
    return `${this.title}${countSection}`;
  }

  markAsRead(): void {
    const chatroomIds = this.messageList.reduce((acc, ele) => {
      if (this.isSelectedAnyMessage ? ele.checked && ele.hasUnreadMessages : ele.hasUnreadMessages) {
        acc.push(ele.chatroomId);
      }
      return acc;
    }, []);

    const payload = {
      chatroomIds
    };
    if (chatroomIds.length === 0) {
      this.common.showToast({ message: this.common.getTranslateData('MESSAGES.NO_UNREAD_MESSAGES_SELECTED'), color: 'warning' });
      return;
    }
    this.httpService
      .doPost({
        endpoint: APIs.updateReadStatusMessage,
        payload,
        contentType: 'form'
      })
      .subscribe((response) => {
        if (response?.status) {
          this.sharedService.messageCount = response?.counts?.messages ?? this.sharedService.messageCount;
          if (isPresent(response?.chatroomIds)) {
            response?.chatroomIds?.split(',')?.forEach((chatroomId) => {
              const chatRoomId = +chatroomId;
              if (chatRoomId > 0) {
                const message = this.messageList.find((ele) => +ele?.chatroomId === chatRoomId);
                if (message) {
                  const unreadCount =
                    message.messageType === +Constants.messageListTypes.masked
                      ? parseInt(message.maskedUnreadCount, 10) || 0
                      : parseInt(message.unreadCount, 10) || parseInt(message.unread, 10) || 0;
                  this.sharedService.messageUnreadCount -= unreadCount;
                  message.hasUnreadMessages = false;
                  message.unreadCount = 0;
                  message.maskedUnreadCount = 0;
                  message.maskedReplyMessages = undefined;
                  message.childExpanded = false;
                }
              }
            });
          }
          this.sharedService.messageUnreadCount = Math.max(0, this.sharedService.messageUnreadCount);
          this.cancelMultiSelect();
          this.storeActiveMessagesAsShared(this.messageList);
        } else {
          this.common.showToast({ message: this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'), color: 'danger' });
        }
      });
  }

  get isSelectedAnyMessage(): number {
    return this.messageList.filter((ele) => ele.checked).length;
  }

  filterData(data: any): void {
    const filter = data?.filter;
    if (
      filter.flagValue !== this.messageReqBody.flagValue ||
      filter.priorityValue !== this.messageReqBody.priorityValue ||
      !arraysMatch(filter.tags, this.messageReqBody.filterTags) ||
      filter.mentionUsers !== this.messageReqBody.mentionUsers ||
      filter.unread !== this.messageReqBody.unread
    ) {
      this.infiniteScroll?.complete();
      this.messageReqBody.flagValue = filter.flagValue;
      this.messageReqBody.filterTags = filter.tags ? filter.tags.split(',').map(Number) : [];
      this.messageReqBody.priorityValue = filter.priorityValue;
      this.messageReqBody.isShowLoader = true;
      this.showLoadMoreOptions = false;
      this.messageReqBody.mentionUsers = filter.mentionUsers;
      this.messageReqBody.pageCount = Constants.defaultPageCount;
      this.messageReqBody.unread = filter.unread;
      this.messageList = undefined;
      this.messageReqBody.chatThreadTypes = filter.chatThreadTypes;
      if (!this.isArchived) {
        this.persistentService.setPersistentData(Constants.storageKeys.activeMessageFilterKeys, this.messageReqBody);
      }
      this.fetchMessages([]);
    }
  }

  filterSitesData(data: number[]): void {
    if (!arraysMatch(this.messageReqBody.siteIds, data)) {
      this.messageReqBody.pageCount = Constants.defaultPageCount;
      this.messageReqBody.siteIds = deepCopyJSON(data);
      this.messageReqBody.isShowLoader = true;
      this.messageList = undefined;
      this.fetchMessages([]);
    }
  }

  showCommonErrorMessage(response: any) {
    if (response?.error) {
      this.common.showToast({ message: response?.error, color: 'danger' });
    } else {
      this.common.showToast({
        message: this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
        color: 'danger'
      });
    }
  }

  manageTags(message: any, i: number, event: any) {
    event.close();
    // Fetch Thread Tags
    const url = APIs.getAlltags;
    const params = {
      tag_type: 1,
      patientTenantId: this.sharedService.userData.tenantId,
      integrationEnabled: false
    };
    this.httpService.doGet({ endpoint: url, extraParams: params }).subscribe(async (response) => {
      this.allThreadTags = response;
      this.allThreadTags = this.allThreadTags.filter((availableTag) => {
        return (
          (Number(this.sharedService.userData.group) === UserGroup.PATIENT &&
            (availableTag.userType === Constants.tagUserTypes.patientFacing ||
              availableTag.userType === Constants.tagUserTypes.staffPatientFacing)) ||
          (Number(this.sharedService.userData.group) !== UserGroup.PATIENT &&
            (availableTag.userType === Constants.tagUserTypes.staffFacing ||
              availableTag.userType === Constants.tagUserTypes.staffPatientFacing ||
              isBlank(availableTag.userType)))
        );
      });

      // Open Tag manager if threads available
      if (this.allThreadTags.length) {
        const modal: any = await this.modalController.create({
          component: AdvancedSelectComponent,
          componentProps: {
            options: this.allThreadTags,
            showSearch: true,
            buttons: { selectAll: false, clearAll: true, done: true },
            headerTitle: this.common.getTranslateData('TITLES.MANAGE_TAGS'),
            infoMsg: this.common.getTranslateData('MESSAGES.SHOW_ONLY_INTEGRATION_DISABLED_TAGS')
          },
          cssClass: 'common-advanced-select',
          animated: true
        });
        modal.onDidDismiss().then(({ data }) => {
          if (data) {
            const selectedTags = this.allThreadTags.filter((tag) => {
              return data.selectedData.find((row) => row.selected && tag.id === row.id);
            });
            if (selectedTags.length === 0) return;
            const existingTags = selectedTags.filter((a: any) => {
              const indexFound =
                (isPresent(message?.messageTagList) &&
                  message?.messageTagList?.findIndex(
                    (b: MessageTagList) => b.id === a.id && a.type === TagType.THREAD && b.type === TagType.THREAD
                  )) ||
                -1;
              return indexFound > -1;
            });
            if (existingTags.length > 0) {
              this.common.showMessage(
                this.common.getTranslateDataWithParam('MESSAGES.TAGS_ALREADY_EXISTS', {
                  tags: existingTags.map((tag) => tag.name).join(', ')
                })
              );
            } else {
              this.doTagThread(message, selectedTags);
            }
          }
        });
        return modal.present();
      }
    });
  }

  viewTagClickedHandle(value: boolean) {
    this.showTagsTriggered = value;
  }

  doTagThread(thread: any, tags: any): void {
    const tagIds = tags.map((tag) => Number(tag.id));
    const payload = {
      single: '0',
      multiple: tagIds.join(';'),
      chatroomIds: [thread.chatroomId],
      id: [],
      timezone: this.sharedService.getConfigValue(Config.tenantTimezoneName),
      config_identity_value: this.sharedService.getConfigValue(Config.esiCodeForPatientIdentity),
      config_staff_identity: this.sharedService.getConfigValue(Config.esiCodeForStaffIdentity),
      config_staff_name: this.sharedService.getConfigValue(Config.esiCodeForStaffName),
      enable: this.sharedService.getConfigValue(Config.enableProgressNoteIntegration)
    };
    this.httpService
      .doPost({
        endpoint: APIs.chatSignatureAction,
        payload,
        extraParams: { action: 'addchattags' }
      })
      .subscribe((response) => {
        thread.loading = false;
        if (response.success) {
          this.common.showMessage(this.common.getTranslateData('MESSAGES.THREAD_TAGGED_SUCCESSFULLY'));
          thread.messageTagList = (thread.messageTagList || [])?.concat(tags.map((tag) => ({ ...tag, type: TagType.THREAD })));
          this.storeActiveMessagesAsShared(this.messageList);
        } else {
          this.common.showMessage(response.error);
        }
        },
        (error) => {
          const errorMessage =
            error && error.data && error.data.errors && error.data.errors.length && error.data.errors[0].message
              ? error.data.errors[0].message
              : this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
          this.common.showToast({ message: errorMessage, color: 'danger' });
        }
      );
  }

  removeMessageTags(tagId: number, chatroomId: any) {
    const index = this.messageList?.findIndex((chatroom) => Number(chatroom.chatroomId) === Number(chatroomId));
    if (index > -1) {
      const thread = this.messageList[index];
      thread.messageTagList = thread.messageTagList?.filter((tag) => Number(tag.id) !== Number(tagId) && tag.type === TagType.THREAD);
      this.storeActiveMessagesAsShared(this.messageList);
    }
  }
  initialSiteData(data: any): void {
    this.messageReqBody.siteIds = data;
  }

  getStoredData(): void {
    this.showLoaderOnStickyFiltersExist = true;
    if (!isBlank(this.isArchived)) {
      if (!this.isArchived) {
        this.messageReqBody.searchKeyword = this.persistentService.getPersistentData(Constants.storageKeys.searchTextActiveMessages) || '';
        this.messageReqBody.dateRange = this.persistentService.getPersistentData(Constants.storageKeys.dateRangeFilterActiveMessages) || {
          from: '',
          to: ''
        };
        this.messageReqBody.selectedDateOptions = !isBlank(
          this.persistentService.getPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsActiveMessages))
          ? this.persistentService.getPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsActiveMessages)
          : Constants.filterSelectedOptions.lastMonth;
      } else {
        this.messageReqBody.searchKeyword = this.persistentService.getPersistentData(Constants.storageKeys.searchTextArchivedMessages) || '';
        this.messageReqBody.dateRange = this.persistentService.getPersistentData(Constants.storageKeys.dateRangeFilterArchivedMessages) || {
          from: '',
          to: ''
        };
        this.messageReqBody.selectedDateOptions = !isBlank(
          this.persistentService.getPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsArchivedMessages))
          ? this.persistentService.getPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsArchivedMessages)
          : Constants.filterSelectedOptions.lastMonth;
      }
      this.setFilterKeysFromStoredFilterKeys();
    }
  }
  /**
   * Clear the stored data based on inbox/archived
   */
  clearStoredData(): void {
    const key = !this.isArchived ? Constants.storageKeys.searchTextActiveMessages : Constants.storageKeys.searchTextArchivedMessages;
    this.persistentService.removePersistentData(key);
  }

  showFilterNotification(): void {
    if (
      this.messageReqBody &&
      ((!this.isArchived &&
        (this.messageReqBody.selectedDateOptions !== Constants.filterSelectedOptions.lastMonth ||
          this.messageReqBody.flagValue !== 0 ||
          this.messageReqBody.priorityValue !== 0 ||
          this.messageReqBody.unread ||
          !isBlank(this.messageReqBody.filterTags) ||
          this.messageReqBody.mentionUsers ||
          !isBlank(this.messageReqBody.searchKeyword) ||
          !isBlank(this.messageReqBody.chatThreadTypes))) ||
        (this.isArchived &&
          (this.messageReqBody.selectedDateOptions !== Constants.filterSelectedOptions.lastMonth || !isBlank(this.messageReqBody.searchKeyword))))
    ) {
      this.showNotify = true;
    }
  }
  setFilterKeysFromStoredFilterKeys(): void {
    const filterKeys = this.isArchived
      ? this.archivedMessageFilterKey
      : this.persistentService.getPersistentData(Constants.storageKeys.activeMessageFilterKeys);
    if (filterKeys) {
      this.messageReqBody.filterTags = filterKeys.filterTags;
      this.messageReqBody.priorityValue = filterKeys.priorityValue;
      this.messageReqBody.mentionUsers = filterKeys.mentionUsers;
      this.messageReqBody.unread = filterKeys.unread;
      this.messageReqBody.flagValue = filterKeys.flagValue;
      this.messageReqBody.chatThreadTypes = filterKeys.chatThreadTypes;
    }
  }

  /** This method will format the UTC date and time to the format assigned to msgHistoryDateTimeFormat
   * @param dateTimeUTC string
   * @returns string
   */
  formatMessageDeletedTime(dateTimeUTC: string): string {
    return dateTimeUTC ? moment(dateTimeUTC).format(msgHistoryDateTimeFormat) : '';
  }
}
