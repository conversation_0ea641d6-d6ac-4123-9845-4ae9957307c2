import { Component, ViewChild } from '@angular/core';
import { APIs } from 'src/app/constants/apis';
import { Constants } from 'src/app/constants/constants';
import { IonInfiniteScroll, ModalController, NavParams } from '@ionic/angular';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, UntypedFormControl } from '@angular/forms';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Permissions } from 'src/app/constants/permissions';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Subject } from 'rxjs';
import { Config } from 'src/app/constants/config';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Socket } from 'src/app/constants/socket';
import { atLeastOneCheckboxCheckedValidator, deepCopyJSON, isBlank, isPresent } from 'src/app/utils/utils';
import { Activity } from 'src/app/constants/activity';
import { getValueFromLocalStorage } from 'src/app/utils/storage-utils';

@Component({
  selector: 'app-invite',
  templateUrl: './invite.component.html',
  styleUrls: ['./invite.component.scss']
})
export class InviteComponent {
  @ViewChild(IonInfiniteScroll) infiniteScroll: IonInfiniteScroll;
  usersList = [];
  userTypes = Constants.userTypes;
  listForm: UntypedFormGroup;
  activeTab: string;
  searchText = '';
  pageCount = Constants.defaultPageCount;
  backupGroupsList;
  chatRoomId: number;
  constants = Constants;
  noData: boolean;
  showPatientsTab = true;
  showAssociateContactsTab = false;
  errorMessage = this.commonService.getTranslateData('MESSAGES.NO_USERS_AVAILABLE');
  eventsSubject: Subject<void> = new Subject<void>();
  tabActive = true;
  inviteReqBody = {
    siteIds: [0]
  };
  isSiteFilterDisabled = false;
  tempPatientSiteId: any = [0];
  messageGroupId: number;
  isLoadMore = false;
  showLoadMore = false;
  loadLimit = 20;
  patientId = 0;
  roomUsers: any = [];
  staffRolesSubscription: any;
  constructor(
    private readonly httpService: HttpService,
    private readonly sharedService: SharedService,
    private readonly commonService: CommonService,
    private readonly permissionService: PermissionService,
    private readonly socketService: SocketService,
    private readonly modalController: ModalController,
    private readonly graphqlService: GraphqlService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly navParams: NavParams
  ) {
    this.isSiteFilterDisabled = this.sharedService.disableSiteFilter;
    this.tempPatientSiteId = this.sharedService.tempPatientSiteId;
    this.inviteReqBody.siteIds = this.tempPatientSiteId;
    if (!this.permissionService.userHasPermission(Permissions.invitePatientsToChatroom)) {
      this.showPatientsTab = false;
    }
    const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    if (!this.isSiteFilterDisabled && isBlank(this.inviteReqBody.siteIds) && isPresent(localSelectedSites)) {
      this.tempPatientSiteId = JSON.parse(localSelectedSites);
      this.inviteReqBody.siteIds = JSON.parse(localSelectedSites);
    }
    this.listForm = this.formBuilder.group({
      list: new UntypedFormArray([])
    });
    this.chatRoomId = this.navParams.get('chatRoomId');
    this.patientId = this.navParams.get('patientId');
    this.roomUsers = this.navParams.get('roomUsers');
    if (Number(this.patientId) > 0 && this.sharedService.getConfigValue(Config.defaultPatientsWorkflow) === Constants.workflowAlternateContact) {
      this.getAssociateContacts();
    }
    this.activeTab = Constants.userTypes.staff;
    this.fetchUsers();
  }

  getAssociateContacts(callback?: (arr: any[]) => void): void {
    const body = {
      tenantId: this.sharedService?.userData?.tenantId,
      patientId: this.patientId,
      isActive: 1,
      chatRoomInvite: true
    };
    this.httpService.doGet({ endpoint: APIs.getAlternateContacts, extraParams: body, loader: false }).subscribe(
      (response) => {
        const usersList = response?.data?.length
          ? response.data
              .filter((user) => !this.roomUsers.some((roomUser) => roomUser.userId === user.userId))
              .map((associateContactUser) => ({
                ...associateContactUser,
                displayname: `${associateContactUser.firstName} ${associateContactUser.lastName}`,
                displayText: this.commonService.getAssociateContactDisplayname(associateContactUser, false)
              }))
          : [];
        this.showAssociateContactsTab = !isBlank(usersList);
        callback(usersList);
      },
      () => {
        callback([]);
      }
    );
  }

  fetchUsers(): void {
    if (this.activeTab === Constants.userTypes.groups) {
      const url = APIs.getUserRolesWithTenants;
      const params = {
        optionShow: 'staffroles'
      };
      this.httpService.doGet({ endpoint: url, extraParams: params, loader: false }).subscribe({
        next: (response) => {
          this.backupGroupsList = response.map((r) => ({
            ...r,
            displayname: r.RoleName,
            isGrouped: true,
            expanded: false
          }));
          this.usersList = deepCopyJSON(this.backupGroupsList);
          this.createForm(response);
          this.noData = isBlank(response);
        },
        error: (error) => {
          this.sharedService.errorHandler(error);
          this.noData = true;
        }
      });
    } else if (this.activeTab === Constants.userTypes.associateContacts) {
      this.getAssociateContacts((usersList) => {
        this.usersList = usersList;
        this.createForm(this.usersList);
      });
    } else {
      let siteIds = this.inviteReqBody.siteIds.toString();
      if (!this.inviteReqBody.siteIds.length) {
        siteIds = Constants.defaultSiteId;
      }
      this.sharedService
        .fetchUsers(
          {
            excludeRoleId: this.activeTab === Constants.userTypes.staff,
            excludeLogginedUser: this.sharedService.userData.userId,
            pageCount: this.pageCount,
            searchKeyword: this.searchText,
            siteIds
          },
          false
        )
        .subscribe({
          next: (response) => {
            if (this.infiniteScroll) {
              this.infiniteScroll?.complete();
            }
            if (!this.isLoadMore || this.pageCount === 0) {
              this.usersList = [];
            }
            this.usersList = [
              ...this.usersList,
              ...response.map((r) => ({
                ...r,
                displayText: this.commonService.getPatientDisplayName(r, Constants.patientValue)
              }))
            ];
            this.createForm(this.usersList);
            this.noData = isBlank(response);
            this.showLoadMore = response.length === this.loadLimit;
          },
          error: () => {
            this.noData = true;
          }
        });
    }
  }

  cancel(): void {
    this.modalController.dismiss({
      dismissed: true
    });
  }

  segmentChanged(event: any): void {
    this.activeTab = event.detail.value;
    this.usersList = [];
    this.backupGroupsList = [];
    this.noData = false;
    this.listForm.reset();
    this.showLoadMore = false;
    this.searchText = '';
    this.pageCount = Constants.defaultPageCount;
    this.fetchUsers();
  }

  expand(i: number): void {
    this.usersList[i].expanded = !this.usersList[i].expanded;
    if (this.usersList[i].expanded && !this.usersList[i].staffList) {
      if (this.staffRolesSubscription) {
        this.staffRolesSubscription.unsubscribe();
      }
      this.staffRolesSubscription = this.graphqlService
        .getStaffsByRoles({ roleId: +this.usersList[i].RoleID, siteIds: this.inviteReqBody.siteIds.toString() })
        ?.valueChanges?.subscribe(
          ({ data }) => {
            this.usersList[i].staffList = data.getSessionTenant.roleBasedStaffs || [];
          },
          () => {
            this.usersList[i].staffList = [];
          }
        );
    }
  }

  createForm(userList: any[]): void {
    const controls = userList.map((c, index) => {
      const checkedStatus = this.listForm.value.list[index] || false;
      return new UntypedFormControl(checkedStatus);
    });
    this.listForm = this.formBuilder.group({
      list: new UntypedFormArray(controls, atLeastOneCheckboxCheckedValidator())
    });
  }
  done(patientIndex?: any): void {
    if (this.activeTab !== Constants.userTypes.patients && this.filteredList.length < 1) {
      return;
    }
    const alertData = {
      message:
        this.activeTab === this.userTypes.groups ? 'MESSAGES.GOING_TO_INVITE_SELECTED_ROLES_IN_CHAT' : 'MESSAGES.GOING_TO_INVITE_SELECTED_IN_CHAT',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.commonService.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        if (this.activeTab === Constants.userTypes.groups) {
          const selectedStaffRoles = this.listForm.value.list.map((v, i: number) => (v ? this.usersList[i].RoleID : null)).filter((v) => v !== null);
          this.inviteUsers(selectedStaffRoles, true);
        } else {
          this.inviteUsers(this.activeTab === Constants.userTypes.patients ? [this.usersList[patientIndex]] : this.filteredList);
        }
      }
    });
  }

  get filteredList(): any[] {
    return this.listForm.value.list.reduce((result: any[], v: unknown, i: number) => {
      if (v) {
        const user = this.usersList[i];
        if (Number(user.userId) !== user.patientId) {
          result.push({
            id: user.userId,
            name: user.displayname,
            associatedId: user.patientId
          });
        } else {
          result.push({
            id: user.userId,
            name: user.displayname
          });
        }
      }
      return result;
    }, []);
  }

  inviteUsers(selectedUsers: any[], hasRoles = false): void {
    const url = APIs.inviteUserToChatRoom;
    const params: {
      chatroomId: number;
      invitedById: string;
      invitedBy: string;
      msgGrpId: number;
      addUserToGroup: string;
      inviteRoles?: string;
      multipleInvite?: string;
      userName?: string;
      userId?: string;
    } = {
      chatroomId: this.chatRoomId,
      invitedById: this.sharedService.userData.userId,
      invitedBy: this.sharedService.userData.displayName,
      msgGrpId: this.messageGroupId,
      addUserToGroup: this.sharedService.getConfigValue(Config.addUserToGroupOnInviteToChatSession)
    };
    if (this.activeTab !== this.constants.userTypes.patients) {
      if (hasRoles) {
        params.inviteRoles = JSON.stringify(selectedUsers);
      } else {
        params.multipleInvite = JSON.stringify(selectedUsers);
      }
    } else {
      params.userName = selectedUsers[0].displayname;
      params.userId = selectedUsers[0].userId;
    }
    this.httpService
      .doPost({
        endpoint: url,
        payload: params,
        contentType: 'form',
        parseToString: true
      })
      .subscribe(({ data }) => {
        let activityData;
        const { invitedRoles, inviteStatus, invitedUsersInfo, invitedMessage, failedUsersInfo, failedRoles, inviteFailedStatus, error } = data;
        if (inviteStatus === Constants.statusSuccess) {
          let inviteList = '';
          if (this.activeTab === this.userTypes.patients) {
            inviteList = params.userName;
          } else {
            inviteList =
              this.activeTab === this.userTypes.groups
                ? invitedRoles?.map((row) => row.roleName).join(',')
                : invitedUsersInfo?.map((row) => row.displayName).join(',');
          }
          const succesMessage = this.commonService.getTranslateDataWithParam('MESSAGES.MESSAGE_INVITE_SUCCESS', {
            inviteList
          });
          this.commonService.showToast({ message: succesMessage });
          activityData = {
            type: Activity.messaging,
            name: Activity.inviteToChatSession,
            des: {
              data: {
                invitedBy: params.invitedBy,
                invited: hasRoles ? params.inviteRoles : params.multipleInvite,
                chatroomId: this.chatRoomId,
                message: succesMessage
              },
              desConstant: hasRoles ? Activity.inviteRolesToChatSessionDes : Activity.inviteUsersToChatSessionDes
            },
            linkageId: this.chatRoomId
          };
          this.sharedService.trackActivity(activityData);
        }
        if (inviteFailedStatus === Constants.statusSuccess) {
          let failList = '';
          if (this.activeTab === this.userTypes.patients) {
            failList = params.userName;
          } else {
            failList =
              this.activeTab === this.userTypes.groups
                ? failedRoles?.map((row) => row.roleName).join(',')
                : failedUsersInfo?.map((row) => row.displayName).join(',');
          }
          const failMessage = this.commonService.getTranslateDataWithParam('MESSAGES.MESSAGE_INVITE_ALREADY_ADDED', {
            failList
          });
          this.commonService.showToast({ message: failMessage, color: 'danger' });
          activityData = {
            type: Activity.messaging,
            name: Activity.inviteToChatSession,
            des: {
              data: { chatroomId: this.chatRoomId, message: failMessage },
              desConstant: Activity.inviteToChatSessionFailedDes
            },
            linkageId: this.chatRoomId
          };
          this.sharedService.trackActivity(activityData);
        }
        if (error && error.message) {
          this.commonService.showToast({ message: error.message, color: 'danger' });
        }
        this.modalController.dismiss({
          response: invitedMessage,
          invitedUsers: this.activeTab !== this.constants.userTypes.patients ? invitedUsersInfo : selectedUsers,
          invitedRoles,
          usersList: this.usersList
        });
      });
  }

  clearAll(): void {
    this.listForm.reset();
  }

  searchOperations(searchAction: any): void {
    this.searchText = searchAction.value;
    this.noData = false;
    this.showLoadMore = false;
    this.pageCount = Constants.defaultPageCount;
    this.usersList = [];
    this.fetchUsers();
    this.sharedService.trackActivity({
      type: Activity.messaging,
      name: Activity.searchUser,
      des: {
        data: {
          displayName: this.sharedService.userData.displayName,
          keyword: this.searchText
        },
        desConstant: Activity.inviteSearchUserDes
      }
    });
  }

  filterList(event: any): void {
    this.usersList = deepCopyJSON(this.backupGroupsList);
    const searchTerm = event.srcElement.value;
    if (!searchTerm) {
      this.noData = false;
      return;
    }
    this.usersList = this.usersList.filter((form: any) => {
      if (form.displayname && searchTerm) {
        if (form.displayname.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1) {
          return true;
        }
      }
      return false;
    });
    this.noData = isBlank(this.usersList);
  }
  filterSitesData(data: []): void {
    this.inviteReqBody.siteIds = data;
    if (this.activeTab !== Constants.userTypes.groups) {
      this.usersList = [];
      this.pageCount = Constants.defaultPageCount;
      this.fetchUsers();
    }
  }

  loadData() {
    this.pageCount += 1;
    this.isLoadMore = true;
    this.fetchUsers();
  }
}
