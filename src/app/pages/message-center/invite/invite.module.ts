import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { InviteComponent } from './invite.component';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';
import { SitesModule } from 'src/app/components/sites/sites.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        ReactiveFormsModule,
        SharedModule,
        TranslateModule,
        HeaderPlainModule,
        SearchBarRecipientsModule,
        SitesModule
    ],
    declarations: [InviteComponent],
    exports: [InviteComponent]
})
export class InviteComponentModule { }
