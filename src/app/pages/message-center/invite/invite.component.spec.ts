import { ReactiveFormsModule } from '@angular/forms';
import { Apollo } from 'apollo-angular';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { of, throwError } from 'rxjs';
import { HttpService } from 'src/app/services/http-service/http.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants } from 'src/app/constants/constants';
import { InviteComponent } from './invite.component';

describe('InviteComponent', () => {
  let component: InviteComponent;
  let fixture: ComponentFixture<InviteComponent>;
  let sharedService: SharedService;
  let httpService: HttpService;
  let modalController: ModalController;
  let graphqlService: GraphqlService;
  let commonService: CommonService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [InviteComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule
      ],
      providers: [
        Apollo,
        NavParams,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        SharedService,
        ModalController,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    httpService = TestBed.inject(HttpService);
    modalController = TestBed.inject(ModalController);
    graphqlService = TestBed.inject(GraphqlService);
    commonService = TestBed.inject(CommonService);
    spyOn(modalController, 'dismiss').and.stub();
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    fixture = TestBed.createComponent(InviteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('execute fetchUsers: staffs', () => {
    component.activeTab = 'staff';
    const response = [{}];
    spyOn(sharedService, 'fetchUsers').and.returnValue(of(response));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });

  it('execute fetchUsers: groups', () => {
    component.activeTab = 'groups';
    const response = [
      {
        RoleName: ''
      }
    ];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });

  it('execute fetchUsers: groups -Error', () => {
    component.activeTab = 'groups';
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });

  it('execute cancel', () => {
    component.cancel();
    expect(component.cancel).toBeTruthy();
  });

  it('execute segmentChanged', () => {
    const event = { detail: { value: '' } };
    component.segmentChanged(event);
    expect(component.segmentChanged).toBeTruthy();
  });

  it('execute expand', () => {
    component.usersList = [{ expanded: true, staffList: [] }];
    const response = { valueChanges: of({ data: { getSessionTenant: { roleBasedStaffs: [] } } }) };
    spyOn(graphqlService, 'getStaffsByRoles').and.returnValue(response);
    component.expand(0);
    expect(component.expand).toBeTruthy();
  });

  it('execute expand-error from graphql', () => {
    component.usersList = [{ expanded: true, staffList: [] }];
    const response = { valueChanges: throwError('') };
    spyOn(graphqlService, 'getStaffsByRoles').and.returnValue(response);
    component.expand(0);
    expect(component.expand).toBeTruthy();
  });

  it('execute done: No data', () => {
    Object.defineProperty(component, 'filteredList', { value: [] });
    component.done();
    expect(component.done).toBeTruthy();
  });

  it('execute done: groups', () => {
    const data = [{}];
    component.activeTab = 'groups';
    component.usersList = [{ RoleID: '1' }];
    Object.defineProperty(component, 'listForm', { value: { value: { list: [{}] } } });
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    Object.defineProperty(component, 'filteredList', { value: data });
    component.done();
    expect(component.done).toBeTruthy();
  });

  it('execute done: staffs', () => {
    const data = [{}];
    component.activeTab = 'staffs';
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    Object.defineProperty(component, 'filteredList', { value: data });
    component.done();
    expect(component.done).toBeTruthy();
  });

  it('execute filteredList', () => {
    const data = { value: { list: [{}] } };
    component.usersList = [{ userId: '1', displayname: '' }];
    Object.defineProperty(component, 'listForm', { value: data });
    const response = component.filteredList;
    expect(component.filteredList).toBeTruthy();
  });

  it('execute inviteUsers', () => {
    const selectedUsers: any = [];
    const response = {
      invite_failed_status: '',
      invited_users: [{ name: '' }]
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.inviteUsers(selectedUsers);
    expect(component.inviteUsers).toBeTruthy();
  });

  it('execute inviteUsers: fail', () => {
    const selectedUsers: any = [];
    const response = {
      invite_failed_status: 1,
      invite_failed_users: [{ name: '' }]
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.inviteUsers(selectedUsers);
    expect(component.inviteUsers).toBeTruthy();
  });

  it('execute inviteUsers : when activeTab = patients', () => {
    const selectedUsers: any = [{ name: 'test', id: '1' }];
    const response = {
      invite_failed_status: '',
      invited_users: [{ name: '' }]
    };
    component.activeTab = Constants.userTypes.patients;
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.inviteUsers(selectedUsers);
    expect(component.inviteUsers).toBeTruthy();
  });

  it('execute clearAll', () => {
    Object.defineProperty(component, 'listForm', {
      value: {
        reset: () => {
          return;
        }
      }
    });
    component.clearAll();
    expect(component.clearAll).toBeTruthy();
  });

  it('execute searchOperations', () => {
    const searchAction = { value: '' };
    component.searchOperations(searchAction);
    expect(component.searchOperations).toBeTruthy();
  });

  it('execute filterList: no search text', () => {
    component.backupGroupsList = [];
    const event = { srcElement: { value: '' } };
    component.filterList(event);
    expect(component.filterList).toBeTruthy();
  });

  it('execute filterList', () => {
    component.backupGroupsList = [{ displayname: 'test' }];
    const event = { srcElement: { value: 't' } };
    component.filterList(event);
    expect(component.filterList).toBeTruthy();
  });

  it('execute filterList: search does not match', () => {
    component.backupGroupsList = [{ displayname: 'test' }];
    const event = { srcElement: { value: 'testing' } };
    component.filterList(event);
    expect(component.filterList).toBeTruthy();
  });

  it('execute filterSitesData', () => {
    component.inviteReqBody = { siteIds: [] };
    const data: any = [];
    component.activeTab = 'staff';
    component.filterSitesData(data);
    expect(component.filterSitesData).toBeTruthy();
  });

  it('execute loadData', fakeAsync(() => {
    component.loadData();

    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.loadData).toBeTruthy();
  }));

  it('execute fetchUsers: associateContacts', () => {
    component.activeTab = 'associateContacts';
    component.roomUsers = [{ userId: 2, firstName: 'firstName value', lastName: 'lastName value' }];
    const response = { data: [{ contactId: '2598488', roleId: '3', userId: '2' }] };
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });

  it('execute fetchUsers: associateContacts -Error', () => {
    component.activeTab = 'associateContacts';
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });

  it('execute loadData', () => {
    component.filterSitesData([]);
    expect(component.filterSitesData).toBeTruthy();
  });
});
