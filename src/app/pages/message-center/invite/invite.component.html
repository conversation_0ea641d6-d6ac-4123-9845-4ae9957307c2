<app-header-plain headerTitle="TITLES.INVITE" (close)="cancel()"></app-header-plain>
<div>
  <app-sites (filterSites)="filterSitesData($event)" [disableFilter]="isSiteFilterDisabled" [selectedSiteIds]="tempPatientSiteId"></app-sites>
</div>
<app-search-bar-recipients
  *ngIf="activeTab !== userTypes.groups && activeTab !== userTypes.associateContacts"
  [events]="eventsSubject.asObservable()"
  [tabActive]="tabActive"
  (searchAction)="searchOperations($event)"
>
</app-search-bar-recipients>
<ion-searchbar *ngIf="activeTab === userTypes.groups" (ionInput)="filterList($event)"></ion-searchbar>
<div class="common-tab-segment">
  <ion-segment [value]="activeTab" (ionChange)="segmentChanged($event)" id="user-tab" mode="ios">
    <ion-segment-button [value]="userTypes.groups">
      <ion-label>{{ 'GENERAL.STAFF_ROLES' | translate }}</ion-label>
    </ion-segment-button>
    <ion-segment-button [value]="userTypes.staff">
      <ion-label>{{ 'GENERAL.STAFF' | translate }}</ion-label>
    </ion-segment-button>
    <ion-segment-button [value]="userTypes.patients" *ngIf="showPatientsTab">
      <ion-label>{{ 'GENERAL.PATIENT' | translate }}</ion-label>
    </ion-segment-button>
    <ion-segment-button [value]="userTypes.associateContacts" *ngIf="showAssociateContactsTab">
      <ion-label>{{ 'GENERAL.ASSOCIATE_CONTACTS' | translate }}</ion-label>
    </ion-segment-button>
  </ion-segment>
</div>
<ion-content>
  <form class="common-list" [formGroup]="listForm" id="list-form">
    <div formArrayName="list" *ngFor="let user of usersList; let i = index" [ngClass]="{ 'parent-list': user.isGrouped }">
      <ion-item
        lines="none"
        (click)="activeTab === constants.userTypes.patients ? done(i) : ''"
        *ngIf="activeTab !== constants.userTypes.associateContacts"
      >
        <ion-icon
          *ngIf="user.isGrouped"
          [name]="user.expanded ? 'remove-circle-outline' : 'add-circle-outline'"
          slot="start"
          (click)="expand(i)"
          tappable
          id="expand"
        >
        </ion-icon>
        <span class="common-capitalize-text" *ngIf="user.roleId !== constants.patientGroupId">
          {{ user.caregiver_displayname ? user.caregiver_displayname + ' (' + user.displayname + ')' : user.displayname }}
        </span>
        <span class="common-capitalize-text" *ngIf="user.roleId === constants.patientGroupId">
          {{ user.displayText }}
        </span>
        <span class="common-na-space common-capitalize-text" *ngIf="user.naTags && user.naTags !== ''"> &nbsp;({{ user.naTagNames }})</span>
        <ion-checkbox
          *ngIf="activeTab !== constants.userTypes.patients"
          class="common-checkbox"
          slot="end"
          mode="ios"
          [formControlName]="i"
          id="user-{{ i }}"
        >
        </ion-checkbox>
      </ion-item>
      <ion-item lines="none" *ngIf="activeTab === constants.userTypes.associateContacts">
        <span
          class="common-capitalize-text"
          *ngIf="user.roleName === constants.roleName.patient || user.roleName === constants.roleName.alternateContact"
        >
          {{ user.displayText }}
        </span>
        <ion-checkbox
          *ngIf="activeTab !== constants.userTypes.patients"
          class="common-checkbox"
          slot="end"
          mode="ios"
          [formControlName]="i"
          id="user-{{ i }}"
        >
        </ion-checkbox>
      </ion-item>
      <div *ngIf="user.expanded" class="child-list">
        <app-skeleton-loader *ngIf="!user.staffList" [count]="2" [skeletonWidths]="[80]"></app-skeleton-loader>
        <ion-item lines="none" class="common-capitalize-text" *ngFor="let staff of user.staffList">
          {{ staff.displayName }}
        </ion-item>
        <ion-item lines="none" *ngIf="user.staffList && user.staffList.length < 1">
          {{ 'GENERAL.NO_USERS_FOUND' | translate }}
        </ion-item>
        <ion-item lines="none" *ngIf="user.staffList === null">
          <ion-skeleton-text animated style="width: 100%"></ion-skeleton-text>
        </ion-item>
      </div>
    </div>
    <div class="common-no-items" *ngIf="noData && activeTab !== constants.userTypes.groups">{{ errorMessage }}</div>
  </form>
  <app-skeleton-loader *ngIf="usersList.length === 0 && !noData" [count]="5" [skeletonWidths]="[80]"></app-skeleton-loader>
  <ion-infinite-scroll *ngIf="showLoadMore" threshold="100px" (ionInfinite)="loadData()" id="load-data">
    <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{ 'BUTTONS.LOAD_MORE' | translate }}"> </ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
<ion-footer>
  <ion-row *ngIf="activeTab !== constants.userTypes.patients">
    <ion-col size="6">
      <ion-button (click)="clearAll()" expand="block" color="de-york" class="ion-text-capitalize" [disabled]="!listForm.valid">
        {{ 'BUTTONS.CLEAR_ALL' | translate }}
      </ion-button>
    </ion-col>
    <ion-col size="6">
      <ion-button (click)="done()" expand="block" color="de-york" class="ion-text-capitalize">
        {{ 'BUTTONS.DONE' | translate }}
      </ion-button>
    </ion-col>
  </ion-row>
</ion-footer>
