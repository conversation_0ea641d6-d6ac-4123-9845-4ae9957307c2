ion-content {
    border-bottom: 1px solid var(--ion-color-border);
}

.blue-button {
    button {
        background: var(--ion-color-blue-button-light);
    }
}

.blue-button-dark {
    button {
        pointer-events: none;
        cursor: pointer;
        background: var(--ion-color-blue-button-dark);
    }
}

.list {
    padding-top: 15px;
    padding-bottom: 30px;
}

.hidden-textarea {
    cursor: pointer;
    text-align: center;
    margin: 15px;
    font-size: 15px;
    color: var(--ion-color-text-dark);
}


.message-section {
    width: 100%;
    padding: 5px 0px;
    display: flex;
    color: var(--ion-color-text-dark);
    flex-wrap: wrap;

    .msg-info {
        float: left;
        font-size: 13px;
        height: 100%;

        .chat-signature {
            background: var(--ion-color-skin-secondary-bright) url(/assets/icon/chat/chat-sign-doc-icon.png) no-repeat 7px center;
            margin-top: 2px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            left: 0px;
            cursor: pointer;
            background-size: 28px auto;
        }
    }

    .msg-middle {
        flex: 1;
        width: 70%;

        .msg-content {
            float: left;
            width: 100%;
            border-bottom-right-radius: 0px;
            position: relative;
            border-radius: 5px;
            color: #ffffff;
            padding: 10px;


            .read-check {
                float: right;
                font-size: 12px;
                color: var(--ion-color-read-check);
                right: 10px;
                cursor: pointer;
            }

            .unread-span {
                float: right;
                font-size: 12px;
                color: black;
                right: 10px;
            }

            .unsend-span {
                float: right;
                font-size: 12px;
                right: 10px;
                color: var(--ion-color-socket-bg-disconnect);

                ion-icon {
                    stroke: var(--ion-color-socket-bg-disconnect);
                    stroke-width: 3px;
                }
            }

            .msg-info-icon {
                cursor: pointer;
                float: right;
                padding: 0px 10px;

                ion-icon {
                    background: var(--ion-color-skin-secondary-bright);
                    border-radius: 50%;
                }
            }

        }

        .chat-arrow:before {
            content: " ";
            position: absolute;
            width: 0;
            height: 0;
            border: 22px solid;
        }
    }

    .avatar {
        float: left;
        width: 40px;
        display: block;
        margin-left: 15px;
        height: 100%;

        img {
            position: absolute;
            bottom: 0;
        }
    }

    .flag-container {
        position: absolute;
        bottom: 44px;
        right: 21px;
    }

    .msg-checkbox {
        position: absolute;
        bottom: 0;
    }

    .avatar-img {
        width: 40px;
        height: 40px;
        border-radius: 100%;
        -webkit-border-radius: 100%;
        -moz-border-radius: 100%;
        -ms-border-radius: 100%;
        -webkit-touch-callout: none;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        object-fit: cover;
        background: var(--ion-color-avatar-bg);
        border: 1px solid var(--ion-color-border);
    }
}

.message-sent-chat {
    flex-direction: row-reverse;

    .msg-middle {
        .msg-content {
            background: var(--ion-color-chat-bg-sent);
            float: right;
            margin-right: 22px;
            width: 94%;
        }

        .chat-arrow:before {
            right: -18px;
            bottom: 0px;
            border-color: transparent transparent var(--ion-color-chat-bg-sent) transparent;
        }
    }

    .msg-info {
        display: flex;
        align-items: flex-end;
    }
}

.message-received-chat {
    flex-direction: row;

    .msg-middle {
        padding-left: 22px;

        .msg-content {
            float: right;
            background: var(--ion-color-chat-bg);
        }

        .chat-arrow:before {
            left: -20px;
            top: 0px;
            border-color: var(--ion-color-chat-bg) transparent transparent transparent;
        }
    }

}

.common-load-more-small {
    margin: 10px;
}

.msg-end {
    font-size: 13px;
    margin-right: 5%;
}

.common-message-parse {
    word-break: break-word;
}

.typing {
    color: #fff;
    background: var(--ion-color-typing-background);
    text-align: center;
    font-size: 13px;
    line-height: 22px;
    display: flex;
    justify-content: center;
}

.chat-controls-container {
    border-top: 1px solid var(--ion-color-chat-border-top);
    background: #fff;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: flex-end;

    .chat-textarea {
        width: 100%;
        position: relative;
        color: #000;

        ion-textarea {
            --padding-end: 35px;
            max-height: 75px;
            overflow-y: auto;
        }
    }

    .chat-send-button-modal {
        --background: var(--ion-color-footer-btn);
        --border-radius: 50%;
        font-size: 10px;
        height: 40px;
        width: 40px;
    }
}

ion-footer {
    bottom: 0;
    border-bottom-width: 0;
    background-position: top;
    padding-bottom: env(safe-area-inset-bottom);
}