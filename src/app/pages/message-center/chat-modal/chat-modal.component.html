<app-header-plain [headerTitle]="headerTitle" (close)="goBack()" type="rightCloseIcon" customClass="wrap-ellipsis">
</app-header-plain>
<ion-content>
    <div *ngIf="isLoadingMore">
        <app-skeleton-loader [type]="1"></app-skeleton-loader>
    </div>
    <ion-list>
        <ion-row class="common-load-more-small blue-button-dark" *ngIf="chatMessages?.length===0">
            <button id="load-more-messages">{{'BUTTONS.NO_MESSAGE_AVAILABLE' | translate}}</button>
        </ion-row>
        <ion-row class=" common-load-more-small blue-button" *ngIf="showLoadMore && !isLoadingMore">
            <button id="load-more-messages" (click)="loadMoreMessages()">{{'BUTTONS.LOAD_EARLIER_MESSAGES' |
                translate}}</button>
        </ion-row>
        <div class="list" #chatMessageRef>
            <ion-item-group lines="none" *ngFor="let message of chatMessages; let i = index; trackByIdentifier: 'id'" [ngSwitch]="+message.userid"
                id="chat-message-{{i}}">
                <ion-item lines="none" *ngSwitchCase="+currentUser" class="message-section message-sent-chat">
                    <ion-col size="auto" class="msg-end">
                        <time>{{message.sent * 1000|shortDateTime}}</time>
                    </ion-col>
                    <ion-col class="msg-middle">
                        <ion-col class="msg-content chat-arrow">
                            <div class="notranslate">
                                <span>{{'LABELS.ME'| translate}}:</span>
                                <span class="read-check"
                                    *ngIf="!isMessageSending(message.id)&&lastUserActivity>=message.sent" id="read">
                                    <ion-icon name="checkmark-sharp"></ion-icon>
                                    {{'BUTTONS.READ'| translate}}
                                </span>
                                <span class="unread-span"
                                    *ngIf="!isMessageSending(message.id)&&message.sent>lastUserActivity">
                                    {{'LABELS.SENT'| translate}}
                                </span>
                                <span class="unsend-span" [ngClass]="{'hide':!isMessageSending(message.id)}"
                                    (click)="showMessageFailedPopover()">
                                    <ion-icon src="/assets/icon/chat/icons8-clock.svg"></ion-icon>
                                    {{'LABELS.SENDING'| translate}}
                                </span>
                            </div>
                            <div class="message-content-parent">
                                <p class="msg-cont-text" id="msg-cont-text">
                                    <ng-container *ngIf="message.messageStatus === 1 || !message.hasOwnProperty('messageStatus'); else messageHistory">
                                        <span class="common-message-parse" [innerHtml]="message.message | unicodeConvert | autolinker | safe:'html'"></span>
                                    </ng-container>
                                    <ng-template #messageHistory>
                                        <ng-container *ngIf="message.deleteUndoHistory.length > 1; else singleDeleteBlock">
                                            <span class="undo-cont-text">
                                                <ng-container *ngFor="let action of message.deleteUndoHistory; let i = index;">
                                                    <span class="undo-cont-text">
                                                        {{'LABELS.MESSAGE_WAS_DELETED_OR_UNDO' | translate: {index: message.deleteUndoHistory.length - i, action: (action.actionType === 0 ? 'deleted' : 'restored'), deletedOn: getDeletedDateTime(action.actionTime)} }}
                                                    </span><br><br>
                                                </ng-container>
                                            </span>
                                        </ng-container>
                                        <ng-template #singleDeleteBlock>
                                            <span class="undo-cont-text">
                                                {{'LABELS.MESSAGE_WAS_DELETED' | translate: {message: message.message, deletedOn: getDeletedDateTime(message.messageDeletedTime)} }}
                                            </span>
                                        </ng-template>
                                    </ng-template>
                                </p>
                            </div>
                        </ion-col>
                    </ion-col>
                    <ion-col size="auto" class="msg-info">
                        <img class="avatar-img" outOfOfficeStatus [oooInfo]="getOooInfo(message.userid)" 
                        [customClass]="'schdule-center-avatar-position'" [src]="message.avatar" alt="avatar">
                    </ion-col>
                </ion-item>
                <ion-item lines="none" *ngSwitchDefault class="message-section message-received-chat">
                    <ion-col size="auto" class="msg-info">
                        <img class="avatar-img" [src]="message.avatar" alt="avatar">
                    </ion-col>
                    <ion-col class="msg-middle">
                        <div class="msg-content chat-arrow">
                            <div class="notranslate" *ngIf="message?.displayName">
                                <span>
                                    {{message?.displayName}}:
                                </span>
                            </div>
                            <div class="message-content-parent">
                                <p class="msg-cont-text" id="msg-cont-text">
                                    <ng-container *ngIf="message.messageStatus === 1 || !message.hasOwnProperty('messageStatus'); else messageHistory">
                                        <span class="common-message-parse" [innerHtml]="message.message | unicodeConvert | autolinker | safe:'html'"></span>
                                    </ng-container>
                                    <ng-template #messageHistory>
                                        <ng-container *ngIf="message.deleteUndoHistory.length > 1; else singleDeleteBlock">
                                            <span class="undo-cont-text">
                                                <ng-container *ngFor="let action of message.deleteUndoHistory; let i = index;">
                                                    <span class="undo-cont-text">
                                                        {{'LABELS.MESSAGE_WAS_DELETED_OR_UNDO' | translate: {index: message.deleteUndoHistory.length - i, action: (action.actionType === 0 ? 'deleted' : 'restored'), deletedOn: getDeletedDateTime(action.actionTime)} }}
                                                    </span><br><br>
                                                </ng-container>
                                            </span>
                                        </ng-container>
                                        <ng-template #singleDeleteBlock>
                                            <span class="undo-cont-text">
                                                {{'LABELS.MESSAGE_WAS_DELETED' | translate: {message: message.message, deletedOn: getDeletedDateTime(message.messageDeletedTime)} }}
                                            </span>
                                        </ng-template>
                                    </ng-template>
                                </p>
                            </div>
                        </div>
                    </ion-col>
                    <ion-col size="auto" class="msg-end">
                        <time>{{message.sent * 1000|shortDateTime}}</time>
                    </ion-col>
                </ion-item>
            </ion-item-group>
            <div id="content-bottom"></div>
        </div>
    </ion-list>
</ion-content>
<ion-footer>
    <ion-row class="typing" *ngIf="typingText">{{typingText}}</ion-row>
    <ion-row class="chat-controls-container">
        <ion-col class="chat-textarea" id="type-message-textarea">
            <ion-textarea rows="1" [(ngModel)]="message" autoGrow="true"
                placeholder="{{'PLACEHOLDERS.TYPE_HERE' | translate}}" (keyup)="userTyping()" id="type-message" rows="1"
                autocapitalize="on">
            </ion-textarea>
        </ion-col>
        <ion-col size="auto">
            <ion-button class="chat-send-button-modal" shape="circle" (click)="sendMessage()" id="send-message">
                <ion-icon slot="icon-only" name="send-sharp" id="send-message-icon"></ion-icon>
            </ion-button>
        </ion-col>
    </ion-row>
</ion-footer>
