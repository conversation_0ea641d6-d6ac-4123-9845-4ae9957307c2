import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Config } from 'src/app/constants/config';
import { Constants, MessagePriority } from 'src/app/constants/constants';
import { Socket } from 'src/app/constants/socket';
import { ChatMessageResponse, MessageContent } from 'src/app/interfaces/messages';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { MessageCenterService } from 'src/app/services/message-center/message-center.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { convertUTCToTimeZoneDateTimeSplit, isPresent, subtractCount } from 'src/app/utils/utils';
import { Subscription } from 'rxjs';
import * as moment from 'moment';

@Component({
  selector: 'app-chat-modal',
  templateUrl: './chat-modal.component.html',
  styleUrls: ['./chat-modal.component.scss']
})
export class ChatModalComponent implements OnInit {
  failedMessageResendCompletedSubscription: Subscription;
  appMinimizedSubscription: Subscription;
  headerTitle = '';
  chatroomId: number;
  chatMessages: MessageContent[] = [];
  selectedMessageData: ChatMessageResponse;
  modalId = '';
  message = '';
  showLoadMore = false;
  isLoadingMore = false;
  currentUser: string;
  typingText: string;
  messageListIndex: number;
  pageNumber = Constants.defaultPageCount;
  messageFilter = Constants.messageFilterOptions[1].value;
  lastUserActivity: number;
  appMinimized = false;
  constructor(
    private modalController: ModalController,
    private sharedService: SharedService,
    private messageCenterService: MessageCenterService,
    private socketService: SocketService,
    private commonService: CommonService
  ) {
    this.currentUser = this.sharedService.userData.userId;
    const defaultCategory = this.sharedService.getConfigValue(Config.defaultCategoryOfMessageDisplay);
    if (isPresent(defaultCategory) && defaultCategory != Constants.undefined) {
      this.messageFilter = defaultCategory;
    }
  }

  ngOnInit(): void {
    if (isPresent(this.chatroomId)) {
      this.messageListIndex = this.sharedService.getChatroomIndex(this.chatroomId);
      this.getChatDetails();
      this.initSocketEvents();

      this.appMinimizedSubscription = this.sharedService.appMinimized.subscribe((appMinimized: boolean) => {
        this.appMinimized = appMinimized;
        if (appMinimized) {
          this.leaveChatRoom();
        } else {
          this.getChatDetails(false, false);
          this.initSocketEvents();
        }
      });
      this.failedMessageResendCompletedSubscription = this.messageCenterService.FailedMessageResendCompleted.subscribe(
        (resendFailed) => {
          if (resendFailed) {
            this.getChatDetails(false, false);
            this.initSocketEvents();
          }
        }
      );
    } else {
      this.goBack();
    }
  }

  leaveChatRoom(): void {
    this.socketService.emitEvent(Socket.leaveChatRoom, this.chatroomId);
    this.socketService.subscribeEvent(Socket.userLeave);
  }
  ionViewDidLeave(): void {
    this.leaveChatRoom();
  }
  initSocketEvents(): void {
    this.socketService.emitEvent(
      Socket.joinToChatroom,
      {
        user: this.sharedService.userData?.userId,
        room: this.chatroomId && this.chatroomId.toString(),
        name: this.sharedService.userData?.displayName,
        avatar: this.sharedService.userData?.profileImageThumbUrl,
        citusRole: this.sharedService.userData?.group,
        patientReminderCheckingType: this.sharedService?.getConfigValue(Config.patientReminderCheckingType),
        sites: this.sharedService.userData.mySites,
        tenantId: this.sharedService.userData.tenantId,
        tenantKey: this.sharedService.userData.tenantKey,
        tenantName: this.sharedService.userData.tenantName,
        eventSource: JSON.stringify({
          environment: this.sharedService.platformValue,
          device: this.commonService.getDeviceType(),
          component: Constants.eventSourceComponent.scheduleCenterChat
        })
      },
      (data) => {
        if (data === Constants.trueAsString) {
          // do nothing
        }
      }
    );
    this.socketService.subscribeEvent(Socket.lastUserActivity).subscribe((data) => {
      this.lastUserActivity = data[0];
    });
    this.socketService.subscribeEvent(Socket.userTyping).subscribe((data: any) => {
      this.typingText = data[0];
    });
    this.socketService.subscribeEvent(Socket.userMessage).subscribe((data: any) => {
      const message = data[0];
      const displayName = data[1];
      if (this.chatroomId === Number(data[0]?.chatroomId)) {
        if (
          this.messageListIndex === -1 &&
          this.messageCenterService.isFirstChat(this.chatMessages) &&
          Number(message.userId) !== Constants.administratorId
        ) {
          /**
           * Considering this as a new chat message
           */
          this.sharedService.addNewChatFromAPI(this.chatroomId.toString());
        }
        const index = this.chatMessages ? this.chatMessages.findIndex((item) => item?.uniqueId === message?.uniqueId) : -1;
        if (this.messageCenterService.showIncomingMessage(message, this.messageFilter) && index === -1) {
          this.chatMessages.push({
            message: message.data,
            avatar: message.avatar,
            uniqueId: message.id,
            id: message.id,
            sent: message.time,
            sign: Constants.falseAsString,
            userid: message.userId,
            readUsers: [],
            displayName
          });
        }
        this.commonService.scrollToView();
        if (this.sharedService.messageList && this.messageListIndex > -1) {
          this.messageCenterService.updateChatMessageListDetails(this.sharedService.messageList[this.messageListIndex], message);
        }
      }
    });
    this.socketService.subscribeEvent(Socket.updateUserMessage).subscribe((data: any) => {
      const chat = data[0];
      if (this.chatroomId === Number(chat.chatroomId)) {
        const unique = this.messageCenterService.fetchMessageUniqueId(chat);
        const index = this.messageCenterService.getMessageChatIndex(this.chatMessages, unique);
        this.messageCenterService.removeFailedMessage(this.chatroomId, unique);
        if (index > -1 && chat.status !== Constants.duplicate) {
          this.chatMessages[index].id = chat.id;
          this.chatMessages[index].sent = chat.sent;
          if (this.chatMessages[index].userid !== this.sharedService.userData.userId) {
            if (!this.appMinimized) {
              this.messageCenterService.updateDeliveryOrReadStatus(this.chatroomId).subscribe();
            }
          }
        }
      }
    });
    this.socketService.subscribeEvent(Socket.updateChatMessageDeleteStatus).subscribe((data: any) => {
      const chat = data[0];
      if (this.chatroomId === Number(chat.chatroomId)) {
        const params = {
          room: chat.chatroomId,
          id: chat.messageId,
          showAllMessageDeleteUndoHistory: true
        };
        this.messageCenterService.fetchChatMessages(params, true).subscribe((response: ChatMessageResponse) => {
          if (isPresent(response.content)) {
            const updatedMessage = response.content.find((message) => message.id === chat.messageId);
            const index = this.chatMessages.findIndex((message) => message.id === updatedMessage.id);
            if (index > -1) {
              this.chatMessages[index] = { ...this.chatMessages[index], ...updatedMessage };
            }
          }
        });
      }
    });
  }
  /**
   * isMessageSending used to identify whether it is failed message or not.
   * If id does not exists or it is in guid format, then it is a failed message.
   * @param id string | number
   * @returns
   */
  isMessageSending(id: string | number): boolean {
    return !id || id.toString().split('-').length > 1;
  }
  loadMoreMessages(): void {
    this.pageNumber += 1;
    this.getChatDetails(true, false);
  }
  userTyping(): void {
    this.messageCenterService.userTyping();
  }
  sendMessage(): void {
    if (this.message.trim()) {
      this.chatMessages = this.messageCenterService.sendMessage(
        this.message,
        this.chatroomId,
        MessagePriority.NORMAL,
        this.selectedMessageData,
        this.chatMessages
      );
      this.commonService.scrollToView();
      this.message = '';
    }
  }
  showMessageFailedPopover(): void {
    this.commonService.showCustomAlert({
      message: this.commonService.getTranslateData('MESSAGES.SENDING_DISCONNECTED_MESSAGE'),
      cssClass: 'common-alert'
    });
  }
  getChatDetails(isLoadMore?: boolean, showLoader = true): void {
    if (isLoadMore) {
      this.isLoadingMore = true;
    }
    let showChatHistory = false;
    if (this.sharedService.isEnableConfig(Config.showChatHistoryToNewParticipant)) {
      showChatHistory = true;
    }

    this.messageCenterService
      .fetchChatMessages(
        {
          room: this.chatroomId.toString(),
          page: this.pageNumber,
          last: isLoadMore && showChatHistory ? Number(this.chatMessages[0].id) : 0,
          messageFilter: this.messageFilter,
          userId: this.sharedService.userData.userId,
          flagVlaue: Constants.defaultFlagValue,
          action: Constants.fetchMessageAction,
          showAllMessageDeleteUndoHistory: true
        },
        showLoader
      )
      .subscribe(
        (response: ChatMessageResponse) => {
          this.messageCenterService.updateDeliveryOrReadStatus(this.chatroomId).subscribe();
          this.selectedMessageData = response;
          this.headerTitle = response.title;
          this.chatMessages = isLoadMore ? [...response.content.reverse(), ...this.chatMessages] : response.content.reverse();
          this.showLoadMore = response.content.length >= Constants.messageLoadLimit && showChatHistory;
          this.isLoadingMore = false;
          if (!isLoadMore) {
            this.commonService.scrollToView();
            this.chatMessages = [...this.chatMessages, ...this.messageCenterService.getFailedMessages(this.chatroomId)];
          } else {
            this.commonService.scrollToView(`chat-message-${response.content.length}`);
          }
          if (this.sharedService.messageList && this.messageListIndex > -1) {
            const unreadCount = +this.sharedService.messageList[this.messageListIndex]?.unreadCount || 0;
            this.sharedService.messageCount = subtractCount(this.sharedService.messageCount, unreadCount);
            if (this.sharedService.messageList[this.messageListIndex]?.unreadCount) {
              this.sharedService.messageList[this.messageListIndex].unreadCount = 0;
            }
          }
        },
        () => {
          this.goBack();
        }
      );
  }
  getDeletedDateTime(dateTime) {
    if (isPresent(dateTime)) {
      const deletedDate = convertUTCToTimeZoneDateTimeSplit(dateTime, '', moment.tz.guess(), Constants.dateFormat.mmddyyhma);
      return deletedDate;
    }
    return '';
  }

  goBack(): void {
    this.leaveChatRoom();
    this.appMinimizedSubscription.unsubscribe();
    this.failedMessageResendCompletedSubscription.unsubscribe();
    this.modalController.dismiss(false, '', this.modalId);
  }
  getOooInfo(userId): any {
    return +userId === +this.sharedService.userData.userId ? this.sharedService.userData.oooInfo : null;
  }
}
