import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChatModalComponent } from 'src/app/pages/message-center/chat-modal/chat-modal.component';
import { SharedModule } from 'src/app/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { SkeletonLoaderModule } from 'src/app/components/skeleton-loader/skeleton-loader.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    SharedModule,
    TranslateModule,
    HeaderPlainModule,
    SkeletonLoaderModule
  ],
  declarations: [ChatModalComponent],
  exports: [ChatModalComponent]
})
export class ChatModalModule {}
