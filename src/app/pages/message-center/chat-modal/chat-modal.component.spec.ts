import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { IonicModule, ModalController } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { of } from 'rxjs';
import { TestConstants } from 'src/app/constants/test-constants';
import { MessageCenterService } from 'src/app/services/message-center/message-center.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';

import { ChatModalComponent } from './chat-modal.component';
import * as moment from 'moment';
import { Constants } from 'src/app/constants/constants';
import { Socket } from 'src/app/constants/socket';

describe('ChatModalComponent', () => {
  let component: ChatModalComponent;
  let fixture: ComponentFixture<ChatModalComponent>;
  let sharedService: SharedService;
  let modalController: ModalController;
  let messageCenterService: MessageCenterService;
  let socketService: SocketService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ChatModalComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      providers: [
        SharedService,
        SocketService,
        MessageCenterService,
        NgxPermissionsService,
        NgxPermissionsStore,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        Idle,
        IdleExpiry,
        Keepalive,
        ModalController
      ]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    modalController = TestBed.inject(ModalController);
    messageCenterService = TestBed.inject(MessageCenterService);
    socketService = TestBed.inject(SocketService);
    spyOn(modalController, 'dismiss').and.stub();
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    fixture = TestBed.createComponent(ChatModalComponent);
    component = fixture.componentInstance;
    component.chatroomId = 1;
    component.selectedMessageData = {
      success: true,
      activity: 1,
      content: [],
      chatroomid: '43',
      messageType: '',
      title: '',
      subject: '',
      createdBy: '',
      baseId: '',
      messageGroupId: '32',
      displayName: 'gfdgd',
      appLessMode: true
    };
    sharedService.userData.userId = '1';
    sharedService.userData.oooInfo = 'Out of office';
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute ionViewDidLeave', () => {
    component.ionViewDidLeave();
    expect(component.ionViewDidLeave).toBeTruthy();
  });

  it('execute loadMoreMessages', () => {
    component.loadMoreMessages();
    expect(component.loadMoreMessages).toBeTruthy();
  });

  it('execute userTyping', () => {
    component.userTyping();
    expect(component.userTyping).toBeTruthy();
  });

  it('execute sendMessage', () => {
    component.message = 'ds';
    component.sendMessage();
    expect(component.sendMessage).toBeTruthy();
  });

  it('execute showMessageFailedPopover', () => {
    component.showMessageFailedPopover();
    expect(component.showMessageFailedPopover).toBeTruthy();
  });

  it('execute goBack', () => {
    component.goBack();
    expect(component.goBack).toBeTruthy();
  });

  it('execute getChatDetails', () => {
    spyOn(messageCenterService, 'fetchChatMessages').and.returnValue(of(component.selectedMessageData));
    component.getChatDetails();
    expect(component.getChatDetails).toBeTruthy();
  });

  it('should call getChatDetails and reset unreadCount when messageListIndex is valid', () => {
    spyOn(messageCenterService, 'fetchChatMessages').and.returnValue(of(component.selectedMessageData));
    const messageList: any[] = [
      {
        chatroomid: 1,
        checked: true,
        childExpanded: true,
        unreadCount: 2,
        markAsRead: true,
        chatAvatar: '',
        chatHeading: '',
        chatParticipantCount: 0,
        chatParticipants: []
      }
    ];
    sharedService.messageList = messageList;
    component.messageListIndex = 0;
    sharedService.messageCount = 10;
    component.getChatDetails();
    expect(component.getChatDetails).toBeTruthy();
    expect(sharedService.messageCount).toBe(8);
    expect(sharedService.messageList[0].unreadCount).toBe(0);
  });

  it('execute initSocketEvents', () => {
    component.chatroomId = 1;
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ chatroomId: '1' }]));
    component.initSocketEvents();
    expect(component.initSocketEvents).toBeTruthy();
  });

  it('should update chat message delete status', () => {
    const chat = { chatroomId: 1, messageId: '123' };
    const updatedMessage = { id: '123', message: 'Updated message' };
    const response = { content: [updatedMessage] };

    spyOn(socketService, 'subscribeEvent').and.callFake((event) => {
      if (event === Socket.updateChatMessageDeleteStatus) {
        return of([chat]);
      }
      return of([]);
    });
    spyOn(messageCenterService, 'fetchChatMessages').and.returnValue(of(response));
    component.chatMessages = [
      {
        id: '123',
        message: 'Old message',
        userid: '',
        sent: '',
        displayName: '',
        avatar: '',
        sign: ''
      }
    ];

    component.initSocketEvents();

    expect(component.chatMessages[0].message).toBe('Updated message');
  });

  it('execute ngOnInit socket offline', () => {
    component.chatroomId = 1;
    socketService.status = false;
    socketService.socketStatusUpdated.next(null);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('execute ngOnInit socket online', () => {
    component.chatroomId = 1;
    socketService.status = true;
    socketService.socketStatusUpdated.next(null);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('execute ngOnInit app minimized', () => {
    component.chatroomId = 1;
    sharedService.appMinimized.next(true);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('execute ngOnInit app resume', () => {
    component.chatroomId = 1;
    sharedService.appMinimized.next(false);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('should get deleted date time', () => {
    const dateTime = '2023-10-10T10:10:10Z';
    const result = component.getDeletedDateTime(dateTime);
    expect(result).toBe(moment(dateTime).format(Constants.dateFormat.mmddyyhma));
  });

  it('execute no chat room id', () => {
    component.chatroomId = undefined;
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('should return oooInfo if userId matches', () => {
    const result = component.getOooInfo(1);
    expect(result).toBe('Out of office');
  });

  it('should return null if userId does not match', () => {
    const result = component.getOooInfo(456);
    expect(result).toBeNull();
  });

});
