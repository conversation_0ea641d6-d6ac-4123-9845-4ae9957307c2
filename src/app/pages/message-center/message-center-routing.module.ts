import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from 'src/app/services/auth-guard/auth.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'messages',
    pathMatch: 'full'
  },
  {
    path: 'messages',
    loadChildren: () => import('./messages/messages.module').then((m) => m.MessagesPageModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'send-bulk-messages',
    loadChildren: () =>
      import('./send-bulk-messages/send-bulk-messages.module').then((m) => m.SendBulkMessagesPageModule),
    canActivate: [AuthGuard]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MessageCenterRoutingModule {}
