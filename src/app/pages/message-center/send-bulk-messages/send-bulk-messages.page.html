<app-header [headerTitle]="title"></app-header>
<div *ngIf="isBroadcast" class="bulk-site">
    <app-sites (filterSites)="filterSitesData($event)" (onLoadSites)="initialSiteData($event)"></app-sites>
</div>
<ion-content class="sendBulkMessagePage">
    <div class="common-box-container">
        <form name="messageSendForm" id="message-send-form" [formGroup]="messageSendForm" class="common-start-padding">
            <div class="common-choose-recipients" tappable (click)="presentRecipientsModal()" id="choose-recipient">
                <div class="display-text-wrap">
                    <label class="display-text-label">{{chooseRecipients}}</label>
                </div>
            </div>

            <div *ngIf="!isBroadcast" class="common-input-row">
                <!-- TODO! CHP-3596 -->
                <ion-input type="text" id="subject" placeholder="{{ 'LABELS.MESSAGE_SUBJECT' | translate }}"
                    formControlName="subject" name="subject" class="ion-input-style set-border-bg-color"
                    autocapitalize="on"></ion-input>

            </div>
            <div class="common-input-row">
                <!-- TODO! CHP-3596 -->
                <ion-textarea id="message" formControlName="message" name="message"
                    placeholder="{{ 'LABELS.MESSAGE' | translate }}" class="ion-input-style set-border-bg-color"
                    rows="6" autocapitalize="on">
                </ion-textarea>
            </div>

            <div class="common-file-browse">
                <img src="../../../assets/images/attachment.png" alt="attach" />
                <span class="browse-span">{{ 'LABELS.ATTACH_FILE' | translate}}</span>
                <input class="selectfile" #fileInput type="file" (click)="fileInput.value = null" name="attachFile"
                    (change)="onSelectFile($event)" id="attach-file" multiple />
            </div>
            <div class="common-file-browse sel-priority" (click)="presentActionSheetPriority()">
                    <ion-icon class="{{sharedService.getPriority(priorityFilterValue)}}" name="{{sharedService.getPriority(priorityFilterValue)}}"></ion-icon>
                    <span >{{ 'LABELS.SELECT_PRIORITY' | translate}}</span>
            </div>

            <div class="common-uploads" *ngIf="file">
                <div class="upload-item-top">
                    <div class="upload-item filename" *ngFor="let f of file; let i = index;">
                        <span> {{f.e.name}} </span>
                        <span class="file-close close" (click)="removeFile(i)" id="remove-file-{{i}}">x</span>
                    </div>
                </div>
            </div>
            <div class="button-padding" *ngIf="file.length==0"></div>
            <!-- TODO! CHP-3595 -->
            <ion-button (click)="sendMessage()" expand="block" color="de-york" id="send" class="ion-text-capitalize">
                {{ 'BUTTONS.SEND' | translate }}
            </ion-button>
        </form>
    </div>
</ion-content>
<app-footer></app-footer>