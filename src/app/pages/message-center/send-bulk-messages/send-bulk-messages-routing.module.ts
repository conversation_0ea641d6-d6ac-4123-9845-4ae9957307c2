import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { SendBulkMessagesPage } from './send-bulk-messages.page';
import { permissionWithParamsType } from '../../../constants/permissions';
import { NgxPermissionsGuard } from 'ngx-permissions';
import { PageRoutes } from '../../../constants/page-routes';


const routes: Routes = [
    { path: '',
    redirectTo: 'broadcast',
    data: {
        permissions: {
            only: permissionWithParamsType,
            redirectTo: PageRoutes.accessDenied
        }
    } },
    {
        path: ':type',
        component: SendBulkMessagesPage,
        canActivate: [NgxPermissionsGuard],
        data: {
            permissions: {
                only: permissionWithParamsType,
                redirectTo: PageRoutes.accessDenied
            }
        }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class SendBulkMessagesPageRoutingModule { }
