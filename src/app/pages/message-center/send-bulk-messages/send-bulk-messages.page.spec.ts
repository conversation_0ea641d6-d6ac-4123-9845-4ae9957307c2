import { Apollo } from 'apollo-angular';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ComponentFixture, fakeAsync, flush, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { SendBulkMessagesPage } from './send-bulk-messages.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonMessageComponent } from 'src/app/components/common-message/common-message.component';
import { CommonService } from 'src/app/services/common-service/common.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';

describe('SendBulkMessagesPage', () => {
  let component: SendBulkMessagesPage;
  let fixture: ComponentFixture<SendBulkMessagesPage>;
  let httpService: HttpService;
  let sharedService: SharedService;
  let common: CommonService;
  let graphqlService: GraphqlService;
  const mockData = {
    target: {
      files: [
        {
          name: 'download.jpeg',
          lastModifiedDate: 'Wed Mar 23 2022 16:39:31 GMT+0530 (India Standard Time) {}',
          size: 209715201,
          type: 'image/jpeg'
        },
        {
          name: 'download.jpeg',
          lastModifiedDate: 'Wed Mar 23 2022 16:39:31 GMT+0530 (India Standard Time) {}',
          size: 7954,
          type: 'image/jpeg'
        },
        {
          name: 'download.j',
          lastModifiedDate: 'Wed Mar 23 2022 16:39:31 GMT+0530 (India Standard Time) {}',
          size: 21,
          type: 'image/jpeg'
        }
      ]
    }
  };
  const mockRecipients = [
    {
      checked: true,
      id: 4282,
      is_deleted: 0,
      tag_name: 'New',
      tag_type: 2,
      tenant_id: 1,
      ticked: false,
      userTagId: 10262,
      values: [{ checked: true }]
    }
  ];
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SendBulkMessagesPage],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule,
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        SharedService,
        CommonMessageComponent,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    common = TestBed.inject(CommonService);
    spyOn(common, 'showMessage').and.stub();
    fixture = TestBed.createComponent(SendBulkMessagesPage);
    httpService = TestBed.inject(HttpService);
    graphqlService = TestBed.inject(GraphqlService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('onSelectFile function should be called,when file length < maxFileLength', () => {
    component.allowedFileTypes = ['png', 'jpg', 'jpeg', 'jpe', 'pdf'];
    component.file.length = 0;
    component.sizeErrorMessage = 'File size should be less than 20 MB';
    component.onSelectFile(mockData);
    expect(component.onSelectFile).toBeDefined();
  });

  it('onSelectFile function should be called, when file length > maxFileLength', () => {
    component.allowedFileTypes = ['png', 'jpg', 'jpeg', 'jpe', 'pdf'];
    component.file.length = 20;
    component.sizeErrorMessage = 'File size should be less than 20 MB';
    component.onSelectFile(mockData);
    expect(component.onSelectFile).toBeDefined();
  });

  it('Define removeFile click', () => {
    component.removeFile(0);
    expect(component.removeFile).toBeTruthy();
  });

  it('filterSitesData function should be defined', () => {
    component.filterSitesData([]);
    expect(component.filterSitesData).toBeTruthy();
  });

  it('execute postFileUpload', () => {
    component.imgdata = [];
    component.file = [];
    component.selectedFileNames = [];
    const response = [
      {
        chatRoomId: 0,
        id: 0,
        msg: "test'1!@call--1653721170.jpg",
        success: true,
        view: 'image'
      }
    ];
    spyOn(component, 'fileUploadToCmis').and.returnValue(1);
    spyOn(httpService, 'fileUpload').and.returnValue(of(response));
    component.postFileUpload([], '');
    expect(component.postFileUpload).toBeTruthy();
  });

  it('execute postFileUpload success false', () => {
    component.imgdata = [];
    component.file = [];
    component.selectedFileNames = [];
    const response = [
      {
        chatRoomId: 0,
        id: 0,
        msg: "test'1!@call--1653721170.jpg",
        success: false,
        view: 'image'
      }
    ];

    spyOn(component, 'fileUploadToCmis').and.returnValue(1);
    spyOn(httpService, 'fileUpload').and.returnValue(of(response));
    component.postFileUpload([], '');
    expect(sharedService.isLoading).toBeFalse();
  });

  it('execute sendBulkMessage : Boadcast', () => {
    component.selectedRecipients = component.recipientId;
    const data = [
      {
        chatroom_id: 326710,
        message_id: 4205063,
        userid: '29',
        configurationNotEnabled: 1
      }
    ];
    const messageType = 'broadcast';
    component.selectedRolesPatient = [{ id: 1 }];
    component.file = [
      { id: 1, checked: false },
      { id: 2, checked: false }
    ];
    spyOn(httpService, 'doPost').and.returnValue(of(data));
    spyOn(sharedService, 'trackActivity').and.callThrough();
    component.userDetails = sharedService.userData;
    spyOn(sharedService, 'addNewChatFromAPI').and.returnValue();
    component.sendBulkMessage('', {}, messageType);
    expect(component.sendBulkMessage).toBeTruthy();
  });

  it('execute sendBulkMessage : throw error', () => {
    const messageType = 'broadcast';
    component.selectedRolesPatient = [{ id: 1 }];
    spyOn(httpService, 'doPost').and.returnValue(throwError(''));
    component.sendBulkMessage('', {}, messageType);
    expect(component.sendBulkMessage).toBeTruthy();
  });

  it('execute sendBulkMessage : Masked', () => {
    const data = [
      {
        chatroom_id: 326710,
        message_id: 4205063,
        userid: '29',
        configurationNotEnabled: 1
      }
    ];
    const messageType = 'masked';
    component.selectedRolesPatient = [];
    component.file = [
      { id: 1, checked: false },
      { id: 2, checked: false }
    ];
    component.selectedRecipients = [];
    spyOn(httpService, 'doPost').and.returnValue(of(data));
    spyOn(sharedService, 'trackActivity').and.callThrough();
    component.userDetails = sharedService.userData;
    spyOn(sharedService, 'addNewChatFromAPI').and.returnValue();
    component.sendBulkMessage('', {}, messageType);
    expect(component.sendBulkMessage).toBeTruthy();
  });

  it('execute sendMessage : Broadcast', () => {
    component.recipientId = mockRecipients;
    component.selectedRecipients = component.recipientId;
    component.isBroadcast = true;
    component.file.length = 1;
    spyOn(component, 'fileUpload').and.returnValue(1);
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(component, 'validateMessageForm').and.returnValue(true);
    component.sendMessage();
    expect(component.sendMessage).toBeTruthy();
  });

  it('execute sendMessage : Broadcast & file is empty', () => {
    component.recipientId = mockRecipients;
    component.selectedRecipients = component.recipientId;
    component.isBroadcast = true;
    spyOn(component, 'fileUpload').and.returnValue(1);
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(component, 'validateMessageForm').and.returnValue(true);
    component.sendMessage();
    expect(component.sendMessage).toBeTruthy();
  });

  it('execute sendMessage : Masked & file is empty', () => {
    component.recipientId = mockRecipients;
    component.selectedRecipients = component.recipientId;
    component.isBroadcast = false;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.selectedRolesPatient = [1112850, 1112852, 797571, 761013, 1100097, 1098764];
    spyOn(component, 'validateMessageForm').and.returnValue(true);
    component.sendMessage();
    expect(component.sendMessage).toBeTruthy();
  });

  it('execute sendMessage : Masked', () => {
    component.file.length = 1;
    component.recipientId = mockRecipients;
    component.selectedRecipients = component.recipientId;
    component.isBroadcast = false;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.selectedRolesPatient = [1112850, 1112852, 797571, 761013, 1100097, 1098764];
    spyOn(component, 'fileUpload').and.returnValue(1);
    spyOn(component, 'validateMessageForm').and.returnValue(true);
    component.sendMessage();
    expect(component.sendMessage).toBeTruthy();
  });

  it('execute fileUpload : broadcast', () => {
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.selectedRolesPatient = [{ id: 1 }];
    component.fileUpload(true);
    expect(component.fileUpload).toBeTruthy();
  });

  it('execute fileUpload : masked', () => {
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.selectedRolesPatient = [{ id: 1 }];
    component.fileUpload(false);
    expect(component.fileUpload).toBeTruthy();
  });

  it('execute maskedMessageConfirmation', () => {
    component.file.length = 1;
    const response = true;
    spyOn(common, 'showAlert').and.resolveTo(response);
    spyOn(component, 'postFileUpload').and.returnValue(1);
    component.maskedMessageConfirmation('', '');
    expect(component.maskedMessageConfirmation).toBeTruthy();
  });

  it('execute maskedMessageConfirmation : file is empty', () => {
    const response = true;
    spyOn(common, 'showAlert').and.resolveTo(response);
    component.maskedMessageConfirmation('', '');
    expect(component.maskedMessageConfirmation).toBeTruthy();
  });

  it('execute pushNotification', () => {
    const users = [{ chatroom_id: '1' }];
    component.pushNotification(users, '', '');
    expect(component.pushNotification).toBeTruthy();
  });

  it('execute metaDataSet', fakeAsync(() => {
    const uploadFile = [
      [
        {
          details: { success: true, id: 0, msg: '<EMAIL>', view: 'image', chatRoomId: 0 },
          id: 0,
          name: '<EMAIL>',
          format: 'image'
        }
      ]
    ];
    const file = [{ e: {} }];
    const callback = spyOn(component, 'fileUploadToCmis').and.returnValue(1);
    component.metaDataSet(uploadFile, file, callback);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.metaDataSet).toBeTruthy();
  }));

  it('execute pushMessage', () => {
    component.pushMessage([]);
    expect(component.pushMessage).toBeTruthy();
  });

  it('execute validateMessageForm : recipientId.length is 0', () => {
    component.validateMessageForm();
    expect(component.validateMessageForm).toBeDefined();
  });

  it('execute validateMessageForm : Broadcast is false & messageSendForm.value.subject is empty', () => {
    component.recipientId.length = 1;
    component.isBroadcast = false;
    component.messageSendForm.value.subject = '';
    component.validateMessageForm();
    expect(component.validateMessageForm).toBeDefined();
  });

  it('execute validateMessageForm : messageSendForm.value.message is empty', () => {
    component.recipientId.length = 1;
    component.isBroadcast = true;
    component.messageSendForm.value.subject = 'testSub';
    component.messageSendForm.value.message = '';
    component.validateMessageForm();
    expect(component.validateMessageForm).toBeDefined();
  });

  it('execute validateMessageForm : return true', () => {
    component.recipientId.length = 1;
    component.isBroadcast = true;
    component.messageSendForm.value.subject = 'test sub';
    component.messageSendForm.value.message = 'test message';
    component.validateMessageForm();
    expect(component.validateMessageForm).toBeDefined();
  });

  it('execute setPushMessage', () => {
    const users = [{ chatroom_id: 326922, message_id: 4209101, userid: '100249' }];
    const messageFormData = { message: 'test message', subject: 'test sub' };
    const messageType = 'masked';
    component.setPushMessage(users, messageFormData, messageType);
    expect(component.setPushMessage).toBeTruthy();
  });

  it('execute setPushMessage : messageFormData.message is empty', () => {
    const users = [{ chatroom_id: 326922, message_id: 4209101, userid: '100249' }];
    const messageFormData = { message: '', subject: 'test sub' };
    const messageType = 'masked';
    component.setPushMessage(users, messageFormData, messageType);
    expect(component.setPushMessage).toBeTruthy();
  });
  it('execute getFileUrl', () => {
    const event = {
      accountId: 2,
      attachment: [
        {
          contentName: 'download%40call--**********.jpeg',
          attributes: { fileshareId: '2724c050-5828-4c65-aef5-2616508c68ce', mimeType: 'image/jpeg', size: '0' },
          description: ''
        }
      ],
      attributes: { data: Array(1) },
      contentId: 648587,
      contentName: 'callbell',
      contentType: 'comment',
      createdBy: 'Callbell',
      description: '',
      modifiedBy: 'Callbell',
      organizationId: 2,
      parentId: '12',
      parentType: 'content'
    };
    component.getFileUrl('', event);
    expect(component.getFileUrl).toBeTruthy();
  });
  it('execute fileUploadToCmis', fakeAsync(() => {
    component.selectedRolesPatient = [{ id: 1 }];
    const response = {
      data: {
        uploadFileToCmis: {
          cmisFileData:
            '{"0":"{\\"status\\":200,\\"statusMessage\\":\\"OK\\",\\"results\\":{\\"contentId\\":648604,\\"createdBy\\":\\"Callbell\\",\\"parentId\\":\\"12\\",\\"parentType\\":\\"content\\",\\"contentType\\":\\"comment\\",\\"contentName\\":\\"callbell\\",\\"attributes\\":{\\"data\\":[{\\"userId\\":\\"12\\",\\"actualUserId\\":\\"1007\\",\\"owner\\":\\"12\\",\\"userType\\":\\"owner\\",\\"objectType\\":\\"file\\",\\"fileType\\":\\"image\\",\\"displayName\\":\\"download.jpeg\\",\\"parentFolderId\\":\\"nil\\",\\"isDeleted\\":false,\\"createdOn\\":*************,\\"modifiedOn\\":*************,\\"chatRoomId\\":0,\\"chatType\\":\\"broadcast_message\\",\\"sourceType\\":\\"attachment\\",\\"fileOriginalName\\":\\"<EMAIL>\\",\\"broadcastMessage\\":\\"\\",\\"attributeUniqueId\\":\\"89bd5a2a-e7ee-4786-acc2-9ce9d474ae7b\\"}]},\\"modifiedBy\\":\\"Callbell\\",\\"attachment\\":[{\\"contentName\\":\\"download%40call--**********.jpeg\\",\\"attributes\\":{\\"fileshareId\\":\\"06b0e0ee-166e-458e-b665-93daa7a7d2c5\\",\\"mimeType\\":\\"image/jpeg\\",\\"size\\":\\"0\\"},\\"description\\":\\"\\"}],\\"accountId\\":2,\\"organizationId\\":2,\\"description\\":\\"\\"}}"}',
          __typename: 'FileUploadResponse'
        }
      }
    };
    const files = [{ e: File }];
    const messageFormData = { message: 'd', subject: 'd' };
    const payload = {
      maskedMessage: {},
      cmisFileUploadData: {},
      createdBy: '1007',
      platform: 'android',
      tenantId: '1'
    };
    const selectedFileNames = [
      [
        {
          details: {
            success: true,
            id: 0,
            msg: '<EMAIL>',
            view: 'image',
            chatRoomId: 0
          },
          id: 0,
          name: '<EMAIL>',
          format: 'image'
        }
      ]
    ];
    component.cmisFileUploadData = [
      {
        result: {
          accountId: '',
          attachment: [
            {
              contentName: 'download%40call--**********.jpeg',
              attributes: { fileshareId: '98c47172-fce8-40f8-846a-793f3e50eb29', mimeType: 'image/jpeg' },
              description: ''
            }
          ],
          attributes: {
            mimeType: '',
            data: [
              {
                actualUserId: '1007',
                attributeUniqueId: 'b5b7bcf3-088d-4f99-ad71-4525c8b8b784',
                broadcastMessage: '',
                chatRoomId: 0,
                chatType: 'broadcast_message',
                createdOn: *************,
                displayName: 'download.jpeg',
                fileOriginalName: '<EMAIL>',
                fileType: 'image',
                isDeleted: false,
                modifiedOn: *************,
                objectType: 'file',
                owner: '12',
                parentFolderId: 'nil',
                sourceType: 'attachment',
                userId: '12',
                userType: 'owner'
              }
            ]
          },
          contentId: 648614,
          contentName: 'callbell',
          contentType: 'comment',
          createdBy: 'Callbell',
          description: '',
          modifiedBy: 'Callbell',
          organizationId: 2,
          parentId: '12',
          parentType: 'content'
        }
      }
    ];
    spyOn(component, 'getFileUrl').and.returnValue('');
    spyOn(graphqlService, 'uploadFileToCmis').and.returnValue(of(response));
    component.fileUploadToCmis(selectedFileNames, files, messageFormData, payload);
    expect(component.fileUploadToCmis).toBeTruthy();
    flush();
  }));

  it('execute postFileUpload throwError', () => {
    spyOn(httpService, 'fileUpload').and.returnValue(throwError(''));
    component.postFileUpload([], '');
    expect(component.postFileUpload).toBeTruthy();
  });
  it('execute initialSite with no Data ', () => {
    component.initialSiteData([]);
    expect(component.initialSiteData).toBeTruthy();
  });
});
