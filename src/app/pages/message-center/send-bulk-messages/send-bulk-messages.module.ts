import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SendBulkMessagesPageRoutingModule } from './send-bulk-messages-routing.module';
import { SendBulkMessagesPage } from './send-bulk-messages.page';
import { SharedModule } from 'src/app/shared.module';
import { RecipientsListComponentModule } from '../recipients-list/recipients-list.module';
import { SitesModule } from 'src/app/components/sites/sites.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    SendBulkMessagesPageRoutingModule,
    SharedModule,
    RecipientsListComponentModule,
    SitesModule
  ],
  declarations: [SendBulkMessagesPage]
})
export class SendBulkMessagesPageModule { }
