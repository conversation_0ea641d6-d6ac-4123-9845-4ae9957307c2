import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Constants } from 'src/app/constants/constants';
import { AlternateContactsEntity, UserEntity } from 'src/app/interfaces/common-interface';
import { CommonService } from 'src/app/services/common-service/common.service';
import { UserService } from 'src/app/services/user-service/user.service';

@Component({
  selector: 'app-choose-contacts',
  templateUrl: './choose-contacts.component.html',
  styleUrls: ['./choose-contacts.component.scss']
})
export class ChooseContactsComponent implements OnInit {
  userEntity: UserEntity;
  alternateContacts = [];
  enrolledUser = ` (${Constants.enrolledPatient})`;
  virtualUser = ` (${Constants.virtual})`;
  constructor(
    private modalController: ModalController,
    private commonService: CommonService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.alternateContacts = this.userEntity?.alternateContacts;
  }
  typeOf(value): string {
    return typeof value;
  }
  /**
   * isContactDisabled returns false if current tab is not groups and user is either enrolled or having contact method
   * @param user
   * @returns boolean
   */
  isContactDisabled(user: UserEntity | AlternateContactsEntity): boolean {
    return !this.userService.isUserContactable(user);
  }
  checkContactDisabled(user: UserEntity | AlternateContactsEntity): boolean {
    const isDisabled = this.isContactDisabled(user);
    if (isDisabled) {
      this.commonService.showToast({
        message: 'ERROR_MESSAGES.USER_CANT_BE_SELECTED',
        color: 'dark'
      });
    }
    return isDisabled;
  }

  goBack() {
    this.modalController.dismiss();
  }
  choosePatient(): void {
    if (!this.checkContactDisabled(this.userEntity)) {
      this.modalController.dismiss({
        patientSelected: true,
        selectedUser: this.userEntity
      });
    }
  }
  chooseAlternateUser(user: AlternateContactsEntity): void {
    if (!this.checkContactDisabled(user)) {
      const selectedUser = {
        ...user,
        createdWidth: [
          {
            userId: user.contactId,
            associatedId: user.patientId
          }
        ]
      };
      this.modalController.dismiss({
        patientSelected: false,
        selectedUser
      });
    }
  }
}
