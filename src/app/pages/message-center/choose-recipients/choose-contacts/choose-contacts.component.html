<app-header-plain headerTitle="TITLES.CHOOSE_CONTACTS" (close)="goBack()"></app-header-plain>
<ion-content>
  <div class="common-list">
    <ion-item
      tappable
      lines="none"
      id="choose-user"
      class="users-list bold"
      *ngIf="userEntity"
      (click)="choosePatient()"
      [ngClass]="{ 'disabled-user': isContactDisabled(userEntity) }"
    >
      <span>{{ userEntity?.displayname }}</span>
      <span *ngIf="typeOf(userEntity?.passwordStatus) === 'boolean'">{{ userEntity.passwordStatus ? enrolledUser : virtualUser }}</span>
    </ion-item>
    <ion-item
      tappable
      lines="none"
      *ngFor="let user of alternateContacts"
      (click)="chooseAlternateUser(user)"
      id="choose-user"
      class="users-list recipient-child"
      [ngClass]="{ 'disabled-user': isContactDisabled(user) }"
    >
      <span>{{ user?.displayName }}</span>
      <span *ngIf="user.relation" class="space"> ({{ user?.relation }})</span>
    </ion-item>
    <ion-item class="common-list" *ngIf="!alternateContacts?.length">
      <ion-label>{{ 'MESSAGES.NO_DATA_FOUND' | translate }}</ion-label>
    </ion-item>
  </div>
</ion-content>
