import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChooseContactsComponent } from './choose-contacts.component';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';

@NgModule({
  declarations: [ChooseContactsComponent],
  imports: [CommonModule, FormsModule, IonicModule, SharedModule, TranslateModule, HeaderPlainModule],
  exports: [ChooseContactsComponent]
})
export class ChooseContactsModule {}
