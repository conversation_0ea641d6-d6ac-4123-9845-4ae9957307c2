import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { IonicModule, ModalController } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TestConstants } from 'src/app/constants/test-constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { UserEntity } from 'src/app/interfaces/common-interface';
import { ChooseContactsComponent } from 'src/app/pages/message-center/choose-recipients/choose-contacts/choose-contacts.component';
import { UserService } from 'src/app/services/user-service/user.service';

describe('ChooseContactsComponent', () => {
  let component: ChooseContactsComponent;
  let fixture: ComponentFixture<ChooseContactsComponent>;
  let modalController: ModalController;
  let commonService: CommonService;
  let userService: UserService;
  const { modalSpy } = TestConstants;
  const userMockData: UserEntity = {
    tenantId: '96541',
    verificationStatus: null,
    contactCount: '1',
    tenantName: 'Cfdgdfghfd',
    siteId: '96541',
    siteName: 'Cgfdfd',
    languages: '',
    password: false,
    dualRoles: '[{"cRoleId": 3, "roleName": "Patient", "tenantUsersRoleId": 4353}]',
    last_login: true,
    userid: '43543543',
    tenantid: '91',
    userId: '43543543',
    name: '<EMAIL>',
    displayname: 'aaa aaaaa',
    email: '<EMAIL>',
    mobile: '',
    countryCode: '+1',
    firstname: 'aaa',
    lastname: 'aaaaa',
    status: 7,
    roleId: '3',
    userJobType: null,
    enable_email_notifications: '0',
    enable_sms_notifications: '0',
    dob: '2000-11-12',
    organization: '',
    shipping_address: null,
    role: 'Patient',
    tenantRoleId: '1425',
    naTags: null,
    naTagNames: null,
    cmisid: '2068146',
    signedFolderId: null,
    IdentityValue: '222231122',
    passwordStatus: false,
    isCaregiverPatient: false,
    mobVerificationStatus: true,
    emailVerificationStatus: true,
    alternateContacts: [
      {
        contactId: '45436435',
        roleId: '3',
        status: '1',
        displayName: 'aaa cccccc',
        firstName: 'aaa',
        lastName: 'cccccc',
        relation: 'servant',
        email: '<EMAIL>',
        alternateUsername: '<EMAIL>',
        ESIValue: '43543',
        patientId: '4354335',
        mobile: '',
        countryCode: '+1',
        password: true,
        created_at: '2022-11-02 09:10:11',
        modified_at: '2023-04-12 09:31:49',
        cmisid: '54325',
        patientFirstName: 'aaa',
        patientLastName: 'aaaaa',
        patientStatus: '1',
        patientDob: '2000-11-12',
        patientPassword: null,
        patientDisplayName: 'aaa aaaaa',
        tenantId: '456546',
        tenantName: 'fghfghfghf',
        tenantRoleId: '54654',
        userId: '54654654',
        isVirtual: false,
        passwordStatus: false,
        identity_value: '',
        roleName: 'Alternate Contact',
        patientDobFormatted: '1/2/2000'
      }
    ]
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ChooseContactsComponent],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        CommonService,
        UserService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    commonService = TestBed.inject(CommonService);
    userService = TestBed.inject(UserService);
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    spyOn(commonService, 'showToast').and.stub();
    fixture = TestBed.createComponent(ChooseContactsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute typeOf', () => {
    const checkvalue = true;
    const checkResult = component.typeOf(checkvalue);
    expect(checkResult).toEqual('boolean');
  });
  it('execute goBack', () => {
    component.goBack();
    expect(component.goBack).toBeTruthy();
  });
  it('execute goBack', () => {
    component.goBack();
    expect(component.goBack).toBeTruthy();
  });
  it('execute choosePatient: Contact enabled patient', () => {
    component.userEntity = userMockData;
    component.choosePatient();
    expect(component.choosePatient).toBeTruthy();
  });
  it('execute chooseAlternateUser: Contact enabled alternate user', () => {
    component.chooseAlternateUser(userMockData.alternateContacts[0]);
    expect(component.chooseAlternateUser).toBeTruthy();
  });
});
