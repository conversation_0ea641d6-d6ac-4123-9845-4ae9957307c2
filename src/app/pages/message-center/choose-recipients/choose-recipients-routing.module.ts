import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { ChooseRecipientsPage } from './choose-recipients.page';

const routes: Routes = [
  {
    path: '',
    component: ChooseRecipientsPage
  },
  {
    path: 'choose-contacts',
    loadChildren: () => import('./choose-contacts/choose-contacts.module').then((m) => m.ChooseContactsModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ChooseRecipientsPageRoutingModule {}
