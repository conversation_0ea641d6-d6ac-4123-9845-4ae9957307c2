import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';
import { SitesModule } from 'src/app/components/sites/sites.module';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { AccordionChipsComponent } from 'src/app/components/accordion-chips/accordion-chips.component';
import { ChooseContactsModule } from './choose-contacts/choose-contacts.module';
import { ChooseRecipientsPage } from './choose-recipients.page';
import { SelectTopicComponent } from './select-topic/select-topic.component';
import { ChooseRecipientsPageRoutingModule } from './choose-recipients-routing.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ChooseRecipientsPageRoutingModule,
    SharedModule,
    HeaderPlainModule,
    SearchBarRecipientsModule,
    SitesModule,
    ChooseContactsModule,
    ScrollingModule,
    AccordionChipsComponent
  ],
  declarations: [ChooseRecipientsPage, SelectTopicComponent]
})
export class ChooseRecipientsPageModule {}
