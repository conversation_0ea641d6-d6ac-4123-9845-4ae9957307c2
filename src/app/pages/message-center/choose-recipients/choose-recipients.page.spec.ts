import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { IonicModule, IonInfiniteScroll, ModalController, NavParams } from '@ionic/angular';
import { ChooseRecipientsPage } from 'src/app/pages/message-center/choose-recipients/choose-recipients.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedModule } from 'src/app/shared.module';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { BrowserDynamicTestingModule } from '@angular/platform-browser-dynamic/testing';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { CommonService } from 'src/app/services/common-service/common.service';
import { OrderByPipe } from 'src/app/pipes/order-by/order-by.pipe';
import { TestConstants } from 'src/app/constants/test-constants';
import { DoOperation } from 'src/app/interfaces/messages';
import { ChatWithTypes, Constants } from 'src/app/constants/constants';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Config } from 'src/app/constants/config';

describe('ChooseRecipientsPage', () => {
  const { modalSpy } = TestConstants;
  let component: ChooseRecipientsPage;
  let fixture: ComponentFixture<ChooseRecipientsPage>;
  let sharedService: SharedService;
  let modalController: ModalController;
  let httpService: HttpService;
  let commonService: CommonService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ChooseRecipientsPage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        SharedModule,
        BrowserDynamicTestingModule,
        TranslateModule.forRoot({}),
        RouterModule.forRoot([])
      ],
      providers: [
        OrderByPipe,
        NavParams,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        SharedService,
        IonInfiniteScroll,
        ModalController,
        InAppBrowser,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    httpService = TestBed.inject(HttpService);
    commonService = TestBed.inject(CommonService);
    sharedService = TestBed.inject(SharedService);
    spyOn(sharedService, 'scheduleSelectionFilter').and.stub();
    spyOn(sharedService, 'trackActivity').and.stub();
    Object.defineProperty(sharedService, 'userData', { value: TestConstants.userData });
    Object.defineProperty(sharedService, 'configValuesUpdated', { value: of({}) });
    fixture = TestBed.createComponent(ChooseRecipientsPage);
    component = fixture.componentInstance;
    spyOn(commonService, 'redirectToPage').and.stub();
    fixture.detectChanges();
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute presentTopicModal', fakeAsync(() => {
    modalSpy.onWillDismiss.and.resolveTo({ data: { chatRoomId: 1, topic: '' } });
    component.presentTopicModal({});
    tick(3000);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.presentTopicModal).toBeTruthy();
  }));
  it('execute presentTopicModal: new topic', fakeAsync(() => {
    component.selectedData = {};
    modalSpy.onWillDismiss.and.resolveTo({ data: { newTopicName: 'abc', groupName: '' } });
    component.presentTopicModal({ newTopicName: '' });
    tick(3000);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.presentTopicModal).toBeTruthy();
    expect(component.selectedData.topic).toBe('abc');
  }));
  it('execute goBack', () => {
    component.goBack();
    expect(component.goBack).toBeTruthy();
  });
  it('execute segmentChanged', () => {
    component.segmentChanged({ detail: { value: '' } });
    expect(component.segmentChanged).toBeTruthy();
  });
  it('execute updateConfigPermissions', () => {
    sharedService.userPermissions = '';
    component.updateConfigPermissions();
    expect(component.updateConfigPermissions).toBeTruthy();
  });
  it('execute updateConfigPermissions: patient default tab', () => {
    sharedService.userPermissions = 'chatWithPatients';
    sharedService.userData.config.default_category_of_chat_window_popup = Constants.patientValue;
    component.updateConfigPermissions();
    expect(component.updateConfigPermissions).toBeTruthy();
  });

  it('execute updateConfigPermissions: staff default tab', () => {
    sharedService.userPermissions = 'chatWithClinician';
    sharedService.userData.config.default_category_of_chat_window_popup = ChatWithTypes.STAFF;
    component.updateConfigPermissions();
    expect(component.updateConfigPermissions).toBeTruthy();
  });

  it('execute updateConfigPermissions: messageGroup default tab', () => {
    sharedService.userPermissions = 'chatWithMessageGroups';
    sharedService.userData.config.default_category_of_chat_window_popup = Constants.userTypes.groups;
    component.updateConfigPermissions();
    expect(component.updateConfigPermissions).toBeTruthy();
  });

  it('should set activeTabValue to PATIENT_GROUP when defaultCategory is pdg and showPdgTab is true', () => {
    sharedService.userPermissions = 'chatWithMessageGroups';
    sharedService.userData.config.default_category_of_chat_window_popup = Constants.userTypes.pdg;
    sharedService.userData.config.allow_virtual_patient = '1';
    component.updateConfigPermissions();
    expect(component.activeTabValue).toBe(ChatWithTypes.PATIENT_GROUP);
  });

  it('execute loadData', () => {
    component.loadData({});
    expect(component.loadData).toBeTruthy();
  });
  it('execute loadData', () => {
    component.filterSitesData([]);
    expect(component.filterSitesData).toBeTruthy();
  });
  it('execute loadData: when searchText exists', () => {
    component.searchText = 'abc';
    spyOn(component, 'fetchUsers').and.stub();
    component.filterSitesData([]);
    expect(component.filterSitesData).toBeTruthy();
    expect(component.fetchUsers).toHaveBeenCalled();
  });
  it('execute searchOperations', () => {
    component.searchOperations({ do: DoOperation.reset, value: '' });
    expect(component.filterSitesData).toBeTruthy();
  });
  it('execute searchOperations block for role list', () => {
    component.activeTabValue = ChatWithTypes.ROLE;
    component.searchOperations({ do: DoOperation.reset, value: '' });
    expect(component.filterSitesData).toBeTruthy();
  });
  it('execute createChatRoom: groups', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    spyOn(httpService, 'doGet').and.returnValue(of(''));
    component.createChatRoom({});
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute createChatRoom: throw error', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    spyOn(httpService, 'doPost').and.returnValue(throwError(''));
    component.createChatRoom({});
    expect(component.createChatRoom).toBeTruthy();
  });

  it('execute createChatRoom: patients', () => {
    component.activeTabValue = ChatWithTypes.PATIENT;
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    component.createChatRoom({});
    expect(component.createChatRoom).toBeTruthy();
  });

  it('execute createChatRoom: groups - No topic', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    spyOn(httpService, 'doGet').and.returnValue(of(''));
    component.createChatRoom({ topic: '' });
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute createChatRoom: staff', () => {
    component.activeTabValue = ChatWithTypes.STAFF;
    spyOn(httpService, 'doPost').and.returnValue(of({ data: 'data' }));
    component.createChatRoom({ userIds: [1, 2] });
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute fetchUsers: groups', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    spyOn(httpService, 'doGet').and.returnValue(of([{ name: '' }]));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute fetchUsers: roleList', () => {
    component.activeTabValue = ChatWithTypes.ROLE;
    component.usersList = [{}];
    spyOn(httpService, 'doPost').and.returnValue(of([{ name: '' }]));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute fetchUsers: groups throw error', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute fetchUsers: groups - No items found', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    spyOn(httpService, 'doGet').and.returnValue(of([]));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute fetchUsers: staff', () => {
    component.activeTabValue = ChatWithTypes.STAFF;
    spyOn(sharedService, 'fetchUsers').and.returnValue(of([{ id: 1 }]));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute fetchUsers: staff throw error', () => {
    component.activeTabValue = ChatWithTypes.STAFF;
    spyOn(sharedService, 'fetchUsers').and.returnValue(throwError(''));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute fetchUsers: staff - No items found', () => {
    component.activeTabValue = ChatWithTypes.STAFF;
    spyOn(sharedService, 'fetchUsers').and.returnValue(of([]));
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute fetchUsers: staff - Users already exist', () => {
    component.activeTabValue = ChatWithTypes.STAFF;
    component.staffList = [{ id: 1 }];
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });
  it('execute alternateContactModal', () => {
    modalSpy.onWillDismiss.and.resolveTo({ data: { patientSelected: true, selectedUser: {} } });
    component.alternateContactModal({});
    expect(component.alternateContactModal).toBeTruthy();
  });
  it('execute alternateContactModal', () => {
    modalSpy.onWillDismiss.and.resolveTo({ data: { patientSelected: false, selectedUser: {} } });
    component.alternateContactModal({});
    expect(component.alternateContactModal).toBeTruthy();
  });
  it('execute alternateContactModal from createChatRoom', () => {
    component.createChatRoom({ associatedPatient: 4, userId: 45 });
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute createChatRoom for role list', () => {
    component.associatedPatient = 2;
    component.activeTabValue = ChatWithTypes.ROLE;
    component.createChatRoom({ roleId: 2 });
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute createChatRoom for empty topic', () => {
    sharedService.userData.config.enable_multisite = '';
    component.createChatRoom({ topic: '' });
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute createChatRoom for group with allowMultiThreadChat', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    component.createChatRoom({});
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute createChatRoom for group with existing room id', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    component.createChatRoom({});
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute createChatRoom create chatroom api fails', () => {
    spyOn(httpService, 'doPost').and.returnValue(of({ data: {}, success: false }));
    component.createChatRoom({});
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute createChatRoom create chatroom for role list', () => {
    component.activeTabValue = ChatWithTypes.ROLE;
    spyOn(httpService, 'doPost').and.returnValue(of({ data: 43, success: true }));
    component.createChatRoom({});
    expect(component.createChatRoom).toBeTruthy();
  });
  it('execute addUserToMessageGroup', () => {
    spyOn(httpService, 'doGet').and.returnValue(of({}));
    component.addUserToMessageGroup({});
    expect(component.addUserToMessageGroup).toBeTruthy();
  });
  it('execute addUserToMessageGroup: user exists redirect to chatroom', () => {
    spyOn(httpService, 'doGet').and.returnValue(of({}));
    component.addUserToMessageGroup({});
    expect(component.addUserToMessageGroup).toBeTruthy();
  });
  it('execute addUsersToChatRoom no contact method', () => {
    component.activeTabValue = ChatWithTypes.PATIENT;
    component.addUsersToChatRoom({ userId: 122, displayname: 'abc', password: false });
    expect(component.addUsersToChatRoom).toBeTruthy();
  });
  it('execute addUsersToChatRoom multiselection not active', () => {
    component.activeTabValue = ChatWithTypes.PATIENT;
    component.addUsersToChatRoom({ userId: 122, displayname: 'abc', password: true });
    expect(component.addUsersToChatRoom).toBeTruthy();
  });
  it('execute addUsersToChatRoom multiselection active: push user', () => {
    component.activeTabValue = ChatWithTypes.STAFF;
    component.staffChatRoomRecipients = [];
    component.staffChatRoomRecipientsFull = [];
    component.addUsersToChatRoom({ userId: 122, displayname: 'abc', password: true });
    expect(component.addUsersToChatRoom).toBeTruthy();
  });
  it('execute addUsersToChatRoom multiselection active: remove user', () => {
    component.activeTabValue = ChatWithTypes.STAFF;
    component.staffChatRoomRecipients = [122];
    component.staffChatRoomRecipientsFull = [{ userId: 122, name: 'abc' }];
    component.addUsersToChatRoom({ userId: 122, displayname: 'abc', password: true });
    expect(component.addUsersToChatRoom).toBeTruthy();
  });
  it('execute initialLoadingText:groups', () => {
    component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
    expect(component.initialLoadingText).toBeTruthy();
  });
  it('execute startChat', () => {
    component.staffChatRoomRecipients = [122];
    component.staffChatRoomRecipientsFull = [{ userId: 122, name: 'abc' }];
    component.startChat();
    expect(component.startChat).toBeTruthy();
  });
  it('execute setErrorMessage:partner', () => {
    component.activeTabValue = ChatWithTypes.PARTNER;
    component.setErrorMessage();
    expect(component.setErrorMessage).toBeTruthy();
  });
  it('execute setErrorMessage:patient', () => {
    component.activeTabValue = ChatWithTypes.PATIENT;
    component.setErrorMessage();
    expect(component.setErrorMessage).toBeTruthy();
  });
  describe('resetAdmission', () => {
    it('should reset admission', () => {
      component.selectedData = { admissionId: '42376283' };
      component.admissionDisplayText = undefined;
      sharedService.userData.config[Config.enableMultiAdmissions] = '1';
      component.resetAdmission();
      expect(component.selectedData.admissionId).toBeUndefined();
      expect(component.admissionDisplayText).toBeDefined();
    });
    it('should reset admission', () => {
      component.selectedData = { admissionId: '42376283' };
      component.admissionDisplayText = undefined;
      component.groupTopicDisplayText = undefined;
      sharedService.userData.config[Config.enableMultiAdmissions] = '1';
      component.activeTabValue = ChatWithTypes.PATIENT_GROUP;
      component.resetAdmission();
      expect(component.selectedData.admissionId).toBeUndefined();
      expect(component.admissionDisplayText).toBeDefined();
      expect(component.groupTopicDisplayText).toBeDefined();
    });
  });
  describe('setTitleAndDisplayText', () => {
    it('should set title and display text: Staff', () => {
      component.displayText = undefined;
      component.modalHeaderTitle = undefined;
      component.activeTabValue = ChatWithTypes.STAFF;
      component.setTitleAndDisplayText();
      expect(component.modalHeaderTitle).toBe('LABELS.SELECT_STAFF');
      expect(component.displayText).toBeDefined();
    });
    it('should set title and display text: Partner', () => {
      component.displayText = undefined;
      component.modalHeaderTitle = undefined;
      component.activeTabValue = ChatWithTypes.PARTNER;
      component.setTitleAndDisplayText();
      expect(component.modalHeaderTitle).toBe('TITLES.SELECT_PARTNERS');
      expect(component.displayText).toBeDefined();
    });

    describe('getLabelText', () => {
      beforeEach(() => {
        sharedService.userData.tenantId = '2';
      });
      it('should return groupName for MESSAGE_GROUP', () => {
        component.activeTabValue = ChatWithTypes.MESSAGE_GROUP;
        const user = { groupName: 'Group 1' };
        const result = component.getLabelText(user);
        expect(result).toBe('Group 1');
      });

      it('should return roleName for ROLE', () => {
        component.activeTabValue = ChatWithTypes.ROLE;
        const user = { roleName: 'Admin', tenant_id: '1', tenant_name: 'Tenant 1' };
        component.userData.tenantId = '2';
        const result = component.getLabelText(user);
        expect(result).toBe('Admin [ Tenant 1 ]');
      });

      it('should return displayname with dob for PATIENT', () => {
        component.activeTabValue = ChatWithTypes.PATIENT;
        const user = { displayname: 'John Doe', dob: '01/01/2000', roleId: '3', tenantId: '2' };
        component.userData.tenantId = '2';
        const result = component.getLabelText(user);
        expect(result).toBe('John Doe - 01/01/2000');
      });

      it('should return displayname with IdentityValue for PATIENT', () => {
        component.activeTabValue = ChatWithTypes.PATIENT;
        const user = { displayname: 'John Doe', dob: '01/01/2000', IdentityValue: '12345', roleId: '3', tenantId: '2' };
        component.mrn = 'MRN';
        const result = component.getLabelText(user);
        expect(result).toBe('John Doe - 01/01/2000 [MRN: 12345]');
      });

      it('should return displayname with enrolledUser for PATIENT', () => {
        component.activeTabValue = ChatWithTypes.PATIENT;
        const user = { displayname: 'John Doe', passwordStatus: true, tenantId: '2' };
        component.enrolledUser = '(Enrolled)';
        const result = component.getLabelText(user);
        expect(result).toBe('John Doe (Enrolled)');
      });

      it('should return displayname with virtualUser for PATIENT', () => {
        component.activeTabValue = ChatWithTypes.PATIENT;
        const user = { displayname: 'John Doe', passwordStatus: false, tenantId: '2' };
        component.virtualUser = '(Virtual)';
        const result = component.getLabelText(user);
        expect(result).toBe('John Doe (Virtual)');
      });

      it('should return displayname with naTags for PATIENT', () => {
        component.activeTabValue = ChatWithTypes.PATIENT;
        const user = { displayname: 'John Doe', naTags: true, naTagNames: 'Tag1', tenantId: '2' };
        const result = component.getLabelText(user);
        expect(result).toBe('John Doe (Tag1)');
      });

      it('should return displayname with tenantName for PATIENT', () => {
        component.activeTabValue = ChatWithTypes.PATIENT;
        const user = { displayname: 'John Doe', tenantId: 1, tenantName: 'Tenant 1' };
        component.userData.tenantId = '2';
        const result = component.getLabelText(user);
        expect(result).toBe('John Doe [Tenant 1]');
      });

      it('should return displayname with siteName for PATIENT', () => {
        component.activeTabValue = ChatWithTypes.PATIENT;
        const user = { displayname: 'John Doe', siteName: 'Site 1', roleId: '3', tenantId: '2' };
        component.userData.config[Config.enableMultiSite] = '1';
        const result = component.getLabelText(user);
        expect(result).toBe('John Doe - Site 1');
      });
    });
  });

  

});
