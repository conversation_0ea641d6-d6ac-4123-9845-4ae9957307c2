<app-header-plain headerTitle="TITLES.CHAT_WITH" (close)="goBack()"></app-header-plain>
@if (activeTabValue !== chatWithTypes.ROLE) {
  <div class="common-tab-segment">
    <ion-segment [value]="activeTabValue" (ionChange)="segmentChanged($event)" id="active-tab" mode="ios" class="chat-with-segment">
      @if (showPdgTab) {
        <ion-segment-button [value]="chatWithTypes.PATIENT_GROUP" id="pdgs-tab">
          <ion-label>{{ 'LABELS.PDGS' | translate }}</ion-label>
        </ion-segment-button>
      }
      @if (showGroupsTab) {
        <ion-segment-button [value]="chatWithTypes.MESSAGE_GROUP" id="groups-tab">
          <ion-label>{{ 'LABELS.GROUPS' | translate }}</ion-label>
        </ion-segment-button>
      }
      @if (showStaffsTab) {
        <ion-segment-button [value]="chatWithTypes.STAFF" id="staff-tab">
          <ion-label>{{ 'GENERAL.STAFF' | translate }}</ion-label>
        </ion-segment-button>
        <ion-segment-button [value]="chatWithTypes.PARTNER" id="partner-tab">
          <ion-label>{{ 'GENERAL.PARTNERS' | translate }}</ion-label>
        </ion-segment-button>
      }
      @if (showPatientsTab) {
        <ion-segment-button [value]="chatWithTypes.PATIENT" id="patients-tab">
          <ion-label>{{ 'GENERAL.PATIENTS' | translate }}</ion-label>
        </ion-segment-button>
      }
    </ion-segment>
  </div>
}

<ion-content *ngIf="activeTabValue !== chatWithTypes.ROLE; else showList">
  <ion-row>
    <ion-col size="12">
      <div class="common-input-row">
        <div class="common-choose-recipients" tappable id="select-user" (click)="isModalOpen = true">
          <div class="display-text-wrap" readonly>
            <label class="display-text-label" [innerHTML]="displayText" for=""></label>
          </div>
        </div>
      </div>

      @if (sharedService.isMultiAdmissionsEnabled && selectedData?.userId) {
        <div class="common-input-row">
          <div class="common-choose-recipients" tappable id="select-patient-admission" (click)="selectAdmission()">
            <div class="display-text-wrap" readonly>
              <span class="display-text-label"> {{ admissionDisplayText }}</span>
            </div>
          </div>
        </div>
      }

      @if (
        (activeTabValue === chatWithTypes.MESSAGE_GROUP && selectedData?.allowMultiThreadChat === constants.configTrue) ||
        (activeTabValue === chatWithTypes.PATIENT_GROUP &&
          (sharedService.isMultiAdmissionsEnabled ? selectedData.admissionId : selectedData?.userId) &&
          (selectedData?.allowMultiThreadChat === constants.configTrue || selectedData?.allowMultiThreadChat === true))
      ) {
        <div class="common-input-row">
          <div class="common-choose-recipients" tappable id="select-pdgs" (click)="presentTopicModal(selectedData)">
            <div class="display-text-wrap" readonly>
              <label class="display-text-label" [innerHTML]="groupTopicDisplayText" for=""></label>
            </div>
          </div>
        </div>
      }
    </ion-col>
  </ion-row>
  <ion-modal [isOpen]="isModalOpen">
    <ng-template>
      <app-header-plain [headerTitle]="modalHeaderTitle" (close)="goBack()"></app-header-plain>
      <ng-container *ngTemplateOutlet="recipients_sites"></ng-container>
      <ng-container *ngTemplateOutlet="recipients_search"></ng-container>
      <ng-container *ngTemplateOutlet="showList"></ng-container>
      <ng-container *ngTemplateOutlet="chat_buttons"></ng-container>
      <ng-container *ngTemplateOutlet="multiple_selection_accordion"></ng-container>
    </ng-template>
  </ion-modal>
</ion-content>

<ng-template #showList>
  <ion-content>
    <ng-container *ngTemplateOutlet="show_user_list"></ng-container>
  </ion-content>
</ng-template>
<ng-template #show_user_list>
  <div class="common-list">
    @for (user of usersList; track user[getIdentifier]) {
      <ion-item
        tappable
        lines="none"
        (click)="goToChat(user)"
        id="choose-user-{{ user[getIdentifier] }}"
        class="users-list"
        [ngClass]="{ 'disabled-user': isContactDisabled(user), 'disabled-user-checkbox': multiSelectionActive && isContactDisabled(user) }"
      >
        @if (multiSelectionActive && !isContactDisabled(user)) {
          <ion-checkbox [checked]="staffChatRoomRecipients.includes(user.userId)" class="common-checkbox" mode="ios"> </ion-checkbox>
        }
        @if (user.isScheduled) {
          <ion-icon class="scheduled-icon" name="time-outline"></ion-icon>
        }
        <ion-label [innerHTML]="getLabelText(user)"></ion-label>
        @if (showChevronIcon(user)) {
          <ion-icon name="chevron-forward-outline" slot="end" color="medium"></ion-icon>
        }
      </ion-item>
    }
    @if (usersList && usersList.length === 0) {
      <div class="common-no-items error-message">
        {{ errorMessage }}
      </div>
    }
  </div>
  @if (showInitialLoader) {
    <app-list-loader [loaderText]="initialLoadingText"></app-list-loader>
  }
  @if (showLoadMore) {
    <ion-infinite-scroll threshold="100px" (ionInfinite)="loadData($event)" id="load-data">
      <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{ 'BUTTONS.LOAD_MORE' | translate }}"> </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  }
</ng-template>
<ng-template #recipients_search>
  @if (activeTabValue !== chatWithTypes.ROLE) {
    <app-search-bar-recipients [events]="eventsSubject.asObservable()" [tabActive]="tabActive" (searchAction)="searchOperations($event)">
    </app-search-bar-recipients>
  }
</ng-template>
<ng-template #recipients_sites>
  @if (activeTabValue !== chatWithTypes.ROLE) {
    <app-sites
      (filterSites)="filterSitesData($event)"
      (onLoadSites)="initialSiteData($event)"
      [crossSiteCommunication]="activeTabValue === chatWithTypes.STAFF || activeTabValue === chatWithTypes.PARTNER"
    ></app-sites>
  }
</ng-template>
<ng-container *ngTemplateOutlet="chat_buttons"></ng-container>
<ng-template #chat_buttons>
  <ion-row class="groupchat-buttons">
    @if (isStaffRecipientsSelected && isModalOpen) {
      <ion-col>
        <ion-button (click)="clearRecipients()" class="ion-text-capitalize" expand="block" id="clear-recipients">
          <ion-icon name="person-remove-outline" slot="start"></ion-icon>
          <ion-label>{{ 'BUTTONS.CLEAR_ALL' | translate }}</ion-label>
        </ion-button>
      </ion-col>
      <ion-col>
        <ion-button (click)="isModalOpen = false; setSelectionText()" class="ion-text-capitalize" expand="block" id="go-recipients">
          <ion-icon name="checkmark-outline" slot="start"></ion-icon>
          <ion-label>{{ 'BUTTONS.DONE' | translate }}</ion-label>
        </ion-button>
      </ion-col>
    }
    @if (!isModalOpen && activeTabValue !== chatWithTypes.ROLE) {
      <ion-col>
        <ion-button (click)="startChat()" class="ion-text-capitalize" expand="block" id="start-chat">
          <ion-icon name="chatbox-ellipses-outline" slot="start"></ion-icon>
          <ion-label>{{ 'BUTTONS.START_CHAT' | translate }}</ion-label>
        </ion-button>
      </ion-col>
    }
  </ion-row>
</ng-template>
<ng-template #multiple_selection_accordion>
  @if (isStaffRecipientsSelected) {
    <app-accordion-chips
      [items]="staffRecipientsForAccordion"
      [headerLabel]="'LABELS.SELECTED_USERS'"
      [showCount]="true"
      [maxHeight]="'120px'"
      [chipIdPrefix]="'user-chip'"
      (removeItem)="removeUserFromChatRoom($event.item)"
    >
    </app-accordion-chips>
  }
</ng-template>
