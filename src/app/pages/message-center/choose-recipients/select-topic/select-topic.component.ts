import { Component, OnInit, ViewChild } from '@angular/core';
import { IonContent, ModalController, NavParams } from '@ionic/angular';
import { APIs } from 'src/app/constants/apis';
import { Constants, RecipientIdType } from 'src/app/constants/constants';
import { GeneralResponse } from 'src/app/interfaces/common-interface';
import { MessageGroup, SearchAction, Topic, TopicsResponse } from 'src/app/interfaces/messages';
import { HttpService } from 'src/app/services/http-service/http.service';
import { isBlank, isPresent } from 'src/app/utils/utils';

@Component({
  selector: 'app-select-topic',
  templateUrl: './select-topic.component.html',
  styleUrls: ['./select-topic.component.scss']
})
export class SelectTopicComponent implements OnInit {
  @ViewChild(IonContent, { static: true }) content: IonContent;
  selectedGroup: MessageGroup;
  identifier: string;
  admissionId: string;
  topics: Topic[] = [];
  selectedTopic: Topic;
  newTopic = '';
  showNewTopic = true;
  showLoadMore = false;
  showInitialLoader = true;
  topicParams = {
    page: 1,
    limit: 20,
    searchText: ''
  };
  errorMessage: string;
  completed: boolean;
  constructor(
    private readonly modalController: ModalController,
    private readonly navParams: NavParams,
    private readonly httpService: HttpService
  ) {}

  ngOnInit(): void {
    this.selectedGroup = this.navParams.get('selectedGroup');
    this.identifier = this.navParams.get('identifier');
    this.admissionId = this.navParams.get('admissionId');
    this.fetchGroupTopics();
  }

  fetchGroupTopics(): void {
    this.errorMessage = '';
    const payload = {
      data: {
        id: +this.selectedGroup[this.identifier],
        filter: {
          groupType: this.identifier === RecipientIdType.MESSAGE_GROUP ? 'mg' : 'pdg',
          search: this.topicParams.searchText
        },
        pagination: {
          page: this.topicParams.page,
          limit: this.topicParams.limit
        },
        admissionId: this.admissionId
      }
    };
    if (isBlank(this.topicParams.searchText)) {
      delete payload.data.filter.search;
    }
    this.httpService
      .doGet({
        endpoint: APIs.getMessagGroupTopics,
        extraParams: { payload: JSON.stringify(payload), ...this.topicParams },
        loader: false,
        version: Constants.apiVersions.apiV5
      })
      .subscribe({
        next: (response: GeneralResponse<TopicsResponse>) => {
          if (!response.success) {
            this.showInitialLoader = false;
            this.errorMessage = response.status.message;
            return;
          }
          const { messageGroupTopics, patientGroupTopics } = response.data;
          const topics = this.identifier === RecipientIdType.MESSAGE_GROUP ? messageGroupTopics : patientGroupTopics;
          if (topics) {
            this.topics = [...this.topics, ...topics];
          }
          this.showLoadMore = topics.length === this.topicParams.limit;
          if (this.topicParams.page === 1) {
            this.showNewTopic =
              isPresent(this.topicParams.searchText) &&
              (isBlank(topics) || this.topicParams.searchText.toLowerCase() !== topics[0]?.subject.toLowerCase());
          }
          this.showInitialLoader = false;
          if (this.topicParams.page === 1) {
            this.content.scrollToTop();
          }
        },
        error: () => {
          this.showInitialLoader = false;
        },
        complete: () => {
          this.completed = true;
        }
      });
  }

  currentIndex(index: number): void {
    if (index > (this.topicParams.page + 1) * this.topicParams.limit && this.showLoadMore) {
      this.loadMore();
    }
  }
  loadMore(): void {
    if (this.showLoadMore && this.completed) {
      this.completed = false;
      this.topicParams.page = +this.topicParams.page + 1;
      this.fetchGroupTopics();
    }
  }

  cancel(): void {
    this.modalController.dismiss();
  }

  searchTopic(event: SearchAction): void {
    this.reset();
    this.topicParams.searchText = event.value.trim();
    this.fetchGroupTopics();
  }
  changeGroup(): void {
    this.reset();
    this.fetchGroupTopics();
  }
  reset() {
    this.topicParams.searchText = '';
    this.newTopic = '';
    this.selectedTopic = undefined;
    this.topics = [];
    this.topicParams.page = 1;
    this.showInitialLoader = true;
    this.completed = false;
    this.showNewTopic = false;
    this.showLoadMore = false;
  }

  selectTopic(topic?: Topic): void {
    if (!topic) {
      this.newTopic = isBlank(this.newTopic) ? this.topicParams.searchText.trim() : '';
      this.selectedTopic = undefined;
    } else {
      this.newTopic = '';
      this.selectedTopic = this.selectedTopic?.chatRoomId === topic?.chatRoomId ? undefined : topic;
    }
  }

  submit(): void {
    this.modalController.dismiss({
      chatRoomId: isBlank(this.newTopic) && this.selectedTopic?.chatRoomId ? this.selectedTopic.chatRoomId : undefined,
      newTopicName: this.newTopic,
      selectedGroup: this.selectedGroup,
      topic: this.selectedTopic,
      topics: this.topics,
      isParticipant: this.selectedTopic?.isParticipant
    });
  }

  get enableSubmit(): boolean {
    return (isPresent(this.topicParams.searchText.trim()) && isPresent(this.newTopic)) || (isBlank(this.newTopic) && isPresent(this.selectedTopic));
  }
}
