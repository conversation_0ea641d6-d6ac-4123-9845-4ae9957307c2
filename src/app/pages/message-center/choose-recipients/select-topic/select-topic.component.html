<app-header-plain headerTitle="TITLES.CHOOSE_TOPIC" (close)="cancel()"></app-header-plain>
<ion-item class="topic-item" lines="none">
  <ion-label class="group-label">{{ 'LABELS.GROUP' | translate }} :</ion-label>
  <ion-label>{{ selectedGroup.groupName }}</ion-label>
</ion-item>
<app-search-bar-recipients (searchAction)="searchTopic($event)" class="border-top"></app-search-bar-recipients>
<ion-content class="border-top">
  <cdk-virtual-scroll-viewport class="topic-viewport" itemSize="1" minBufferPx="1500" maxBufferPx="2200" (scrolledIndexChange)="currentIndex($event)">
    <div *ngIf="!topicParams.searchText && topics.length === 0 && !showInitialLoader && !errorMessage" class="common-no-items top-padding" translate>
      MESSAGES.NO_TOPICS
    </div>
    <div *ngIf="errorMessage" class="common-no-items top-padding">{{ errorMessage }}</div>
    <ion-item
      *ngIf="topicParams.searchText && showNewTopic"
      (click)="selectTopic()"
      lines="none"
      class="topic-item"
      [ngClass]="{ 'border-bottom': topics.length === 0 }"
    >
      <ion-checkbox [checked]="newTopic !== ''" class="common-checkbox" mode="ios" id="choose-sub-new"> </ion-checkbox>
      <ion-label>{{ topicParams.searchText }} ({{ 'BUTTONS.NEW' | translate }})</ion-label>
    </ion-item>
    <ion-item
      lines="none"
      *cdkVirtualFor="let topic of topics; trackByIdentifier: 'chatRoomId'; let i = index; let last = last"
      (click)="selectTopic(topic)"
      [ngClass]="{ 'border-bottom': last }"
      class="topic-item"
    >
      <ion-checkbox
        [checked]="topic.chatRoomId === selectedTopic?.chatRoomId"
        class="common-checkbox"
        mode="ios"
        id="choose-sub-{{ topic.chatRoomId }}"
      ></ion-checkbox>
      <ion-label>{{ topic.subject }}</ion-label>
    </ion-item>
    <app-list-loader
      *ngIf="showInitialLoader || showLoadMore"
      [loaderText]="showInitialLoader ? 'BUTTONS.LOADING_TOPICS' : 'BUTTONS.LOAD_MORE'"
    ></app-list-loader>
  </cdk-virtual-scroll-viewport>
</ion-content>
<ion-footer>
  <ion-row>
    <ion-col size="6">
      <ion-button (click)="cancel()" expand="block" color="de-york" id="group-cancel" class="ion-text-capitalize">
        {{ 'BUTTONS.CANCEL' | translate }}
      </ion-button>
    </ion-col>
    <ion-col size="6">
      <ion-button (click)="submit()" expand="block" color="de-york" [disabled]="!enableSubmit" id="group-ok" class="ion-text-capitalize">
        {{ 'BUTTONS.OK' | translate }}
      </ion-button>
    </ion-col>
  </ion-row>
</ion-footer>
