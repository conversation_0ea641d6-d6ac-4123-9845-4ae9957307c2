import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavParams } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { DoOperation, SearchAction, Topic } from 'src/app/interfaces/messages';
import { SelectTopicComponent } from './select-topic.component';

describe('SelectTopicComponent', () => {
  let component: SelectTopicComponent;
  let fixture: ComponentFixture<SelectTopicComponent>;
  let httpService: HttpService;
  const topics: Topic[] = [
    { subject: 'topic', groupId: '0', createdby: '0', isParticipant: '1', chatRoomId: '' },
    { subject: 'titanic', groupId: '0', createdby: '0', isParticipant: '1', chatRoomId: '' }
  ];
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SelectTopicComponent],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        {
          provide: NavParams,
          useValue: {
            get: () => {
              return 'test';
            }
          }
        },
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    fixture = TestBed.createComponent(SelectTopicComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('execute fetchGroupTopics', () => {
    spyOn(httpService, 'doGet').and.returnValue(of({ topics }));
    component.fetchGroupTopics();
    expect(component.fetchGroupTopics).toBeTruthy();
  });

  it('execute fetchGroupTopics: throw error', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.fetchGroupTopics();
    expect(component.fetchGroupTopics).toBeTruthy();
  });

  it('execute cancel', () => {
    component.cancel();
    expect(component.cancel).toBeTruthy();
  });

  it('execute searchTopic: no search text', () => {
    const event: SearchAction = {
      do: DoOperation.search,
      value: ''
    };
    component.searchTopic(event);
    expect(component.searchTopic).toBeTruthy();
  });

  it('execute searchTopic', () => {
    const event: SearchAction = {
      do: DoOperation.search,
      value: 'to'
    };
    component.searchTopic(event);
    expect(component.searchTopic).toBeTruthy();
  });

  it('execute searchTopic: topic match', () => {
    const event: SearchAction = {
      do: DoOperation.search,
      value: 'topic'
    };
    component.searchTopic(event);
    expect(component.searchTopic).toBeTruthy();
  });

  it('execute selectTopic', () => {
    component.selectTopic({ subject: 'titanic', groupId: '0', createdby: '0', isParticipant: '1', chatRoomId: '' });
    expect(component.selectTopic).toBeTruthy();
  });

  it('execute selectTopic: new topic', () => {
    component.selectTopic();
    expect(component.selectTopic).toBeTruthy();
  });
  it('execute submit', () => {
    component.submit();
    expect(component.submit).toBeTruthy();
  });
  it('execute currentIndex', () => {
    component.showLoadMore = true;
    component.currentIndex(50);
    expect(component.currentIndex).toBeTruthy();
  });
  it('execute changeGroup', () => {
    component.changeGroup();
    expect(component.changeGroup).toBeTruthy();
  });
});
