import { Component } from '@angular/core';
import { Constants } from 'src/app/constants/constants';
import { ModalController } from '@ionic/angular';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';

@Component({
  selector: 'app-choose-patient',
  templateUrl: './choose-patient.component.html',
  styleUrls: ['./choose-patient.component.scss']
})
export class ChoosePatientComponent {
  usersList = [];
  userTypes = Constants.userTypes;
  pageCount = 0;
  searchText = '';
  noData: boolean;
  errorMessage = this.commonService.getTranslateData('MESSAGES.NO_ITEM_FOUND');
  constructor(
    private readonly sharedService: SharedService,
    private readonly commonService: CommonService,
    private readonly modalController: ModalController
  ) {
    this.fetchUsers();
  }

  fetchUsers(): void {
    this.usersList = this.sharedService.userData?.alternate_contact_patient;
  }

  cancel(): void {
    this.modalController.dismiss();
  }

  chooseUser(user: any): void {
    this.modalController.dismiss({
      userId: user.userId
    });
  }
}
