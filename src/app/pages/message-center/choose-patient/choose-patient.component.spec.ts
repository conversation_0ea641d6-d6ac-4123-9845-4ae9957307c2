import { TestConstants } from 'src/app/constants/test-constants';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';

import { ChoosePatientComponent } from './choose-patient.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('ChoosePatientComponent', () => {
  let component: ChoosePatientComponent;
  let fixture: ComponentFixture<ChoosePatientComponent>;
  let modalController: ModalController;
  const modalSpy = TestConstants.modalSpy;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ChoosePatientComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(ChoosePatientComponent);
    component = fixture.componentInstance;
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call fetchUsers', () => {
    component.fetchUsers();
    expect(component.fetchUsers).toBeTruthy();
  });

  it('should call cancel', () => {
    component.cancel();
    expect(component.cancel).toBeTruthy();
  });

  it('should call chooseUser', () => {
    const user = {
      userid: 599
    };
    component.chooseUser(user);
    expect(component.chooseUser).toBeTruthy();
  });
});
