<app-header-plain headerTitle="BUTTONS.TAG_MESSAGE" (close)="cancel()"></app-header-plain>
<ion-text color="black" class="ion-padding-horizontal ion-padding-top" *ngIf="!allowIntegrationTags">
  <ion-icon class="pos-absolute" name="information-circle-outline" size="small"></ion-icon>&nbsp;
  <span class="ml-rem-1">{{
    (sharedService.isMultiAdmissionsEnabled ? 'MESSAGES.ONLY_TAGS_WITHOUT_INTEGRATION_MA' : 'MESSAGES.ONLY_TAGS_WITHOUT_INTEGRATION') | translate
  }}</span>
</ion-text>
<div class="common-list">
  <ion-item ion-button lines="none" (click)="presentAdvancedSelect()" id="select-tags">
    @if (this.allTagsAsString) {
      <ion-label class="common-capitalize-text ion-text-wrap">
        {{ this.allTagsAsString }}
      </ion-label>
    } @else {
      <ion-label>{{ 'LABELS.SELECT_ONE_OR_MORE_TAGS' | translate }}</ion-label>
    }
    <ion-icon name="caret-down-outline"></ion-icon>
  </ion-item>
</div>
<ion-content>
  <ion-row>
    <ion-col size="12">
      @if (allTagsAsString && listHasPatientFacing) {
        <div class="common-input-row">
          <div
            class="common-choose-recipients"
            tappable
            id="select-user"
            (click)="isModalOpen = true"
            [class.disabled-div]="disabledPatientSelection"
          >
            <div class="display-text-wrap" readonly>
              <label class="display-text-label" [innerHTML]="patientSelectedValue ? displayText : 'TITLES.CHOOSE_PATIENT'" translate></label>
            </div>
          </div>
        </div>
      }

      @if (patientSelectedValue && isMultiAdmissionsEnabled && allTagsAsString && listHasPatientFacing) {
        <div class="common-input-row">
          <div class="common-choose-recipients" tappable id="select-patient-admission" (click)="showAdmissionPopup()" [class.disabled-div]="disabledAdmissionSelection">
            <div class="display-text-wrap" readonly>
              <span class="display-text-label"> {{ admissionDisplayText }}</span>
            </div>
          </div>
        </div>
      }
    </ion-col>
  </ion-row>

  <ion-modal [isOpen]="isModalOpen">
    <ng-template>
      <app-header-plain [headerTitle]="'TITLES.SELECT_PATIENT'" (close)="goBack()"></app-header-plain>
      <div class="common-list">
        @if (listHasPatientFacing && enableSearch) {
          <div>
            <app-sites (filterSites)="filterSitesData($event)" (onLoadSites)="initialSiteData($event)" [singleSiteSelection]="true"></app-sites>
          </div>
          <ion-item lines="none">
            <app-search-bar-recipients
              (searchAction)="searchOperations($event)"
              [searchPleaseHolder]="'PLACEHOLDERS.SEARCH_PATIENTS'"
              [searchValue]="searchText"
            >
            </app-search-bar-recipients>
          </ion-item>
        }
      </div>
      <ion-content>
        @if (listHasPatientFacing) {
          <ion-radio-group [(ngModel)]="selectedUser">
            @for (user of usersList; track user?.userId; let i = $index) {
              <ion-row class="show-bottom-border">
                <ion-col size="12" class="ion-no-padding">
                  <ion-item lines="none" [id]="'list-item-' + i" (click)="showSelectedUser(user)">
                    <ion-label class="common-capitalize-text ion-text-wrap">
                      {{ sharedService.generateLabel(user) }}
                    </ion-label>
                    <ion-radio color="blumine" slot="end" [value]="+user.userId" id="user-{{ i }}" mode="md"></ion-radio>
                  </ion-item>
                </ion-col>
              </ion-row>
            }
          </ion-radio-group>
          @if (showNoUsers) {
            <div class="no-items text-center">{{ 'MESSAGES.NO_MATCHES_FOUND' | translate }}</div>
          }
        }

        @if (showLoadMore) {
          <ion-infinite-scroll threshold="100px" (ionInfinite)="loadData($event)">
            <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{ 'BUTTONS.LOAD_MORE' | translate }}"> </ion-infinite-scroll-content>
          </ion-infinite-scroll>
        }
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
@if (!isModalOpen) {
  <div class="multiple-tag-button">
    <button id="load-more-messages" (click)="tagMultiple()">{{ 'BUTTONS.TAG_MULTIPLE_MESSAGES' | translate }}</button>
  </div>
  <ion-footer>
    <!-- TODO! CHP-3595 -->
    <ion-row>
      <ion-col size="6">
        <ion-button (click)="cancel()" id="cancel-tag" expand="block" color="de-york" class="ion-text-capitalize">
          {{ 'BUTTONS.CANCEL' | translate }}
        </ion-button>
      </ion-col>
      <ion-col size="6">
        <ion-button (click)="save()" id="save-tag" expand="block" color="de-york" class="ion-text-capitalize">
          {{ 'BUTTONS.SAVE' | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-footer>
}
