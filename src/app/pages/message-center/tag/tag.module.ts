import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { TagComponent } from './tag.component';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { AdvancedSelectComponentModule } from 'src/app/components/advanced-select/advanced-select.module';
import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';
import { SitesModule } from 'src/app/components/sites/sites.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SharedModule,
    TranslateModule,
    HeaderPlainModule,
    AdvancedSelectComponentModule,
    SearchBarRecipientsModule,
    SitesModule
  ],
  declarations: [TagComponent],
  exports: [TagComponent]
})
export class TagComponentModule {}
