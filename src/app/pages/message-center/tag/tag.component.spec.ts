import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { IonicModule, NavParams, ModalController, AlertController } from '@ionic/angular';
import { TagComponent } from 'src/app/pages/message-center/tag/tag.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SafePipe } from 'src/app/pipes/safe.pipe';
import { CommonService } from 'src/app/services/common-service/common.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { of, throwError } from 'rxjs';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Config } from 'src/app/constants/config';

describe('TagComponent', () => {
  const { modalSpy, alertSpy } = TestConstants;
  let component: TagComponent;
  let fixture: ComponentFixture<TagComponent>;
  let commonService: CommonService;
  let modalController: ModalController;
  let sharedService: SharedService;
  let httpService: HttpService;
  let getTranslateDataSpy: jasmine.Spy;
  let alertController: AlertController;
  const navData = {
    chatRoomPatient: {
      userId: '1738735',
      displayname: 'Care1234 A',
      dob: '',
      IdentityValue: '',
      passwordStatus: '1',
      sitename: ''
    },
    patient: { displayname: '', userId: '' },
    tagedItems: undefined,
    patients: undefined,
    searchText: '',
    admissionDetails: undefined
  };
  let navParams = new NavParams(navData);

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [TagComponent],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        HttpService,
        SharedService,
        CommonService,
        { provide: NavParams, useValue: navParams },
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        ModalController,
        AlertController,
        SafePipe,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    sharedService = TestBed.inject(SharedService);
    commonService = TestBed.inject(CommonService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    sharedService.userPermissions = 'PatientSelectioninMessageTagging';
    getTranslateDataSpy = spyOn(commonService, 'getTranslateData');
    spyOn(commonService, 'showMessage').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'dismiss').and.stub();
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    alertController = TestBed.inject(AlertController);
    spyOn(alertController, 'dismiss').and.stub();
    spyOn(alertController, 'create').and.callFake(() => {
      return alertSpy;
    });
    alertSpy.present.and.stub();
    fixture = TestBed.createComponent(TagComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    component.selectedTags = ['test', 'test1'];
    expect(component).toBeTruthy();
  });

  it('should cancel function should be defined', () => {
    component.cancel();
    expect(component.cancel).toBeDefined();
  });
  it('should fetchTags function should be defined', () => {
    spyOn(httpService, 'doGet').and.returnValue(of({}));
    component.selectedTags = [{}];
    component.fetchTags();
    expect(component.fetchTags).toBeDefined();
  });
  it('fetchTags function should handle  error block', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError({}));
    component.fetchTags();
    expect(component.fetchTags).toBeTruthy();
  });
  it('should enableSearch function should be defined', () => {
    expect(!component.haveChatRoomPatient || (component.haveChatRoomPatient && component.searchPermission)).toBe(true);
    expect(component.enableSearch).toBeDefined();
  });

  it('should searchOperations function should be defined', () => {
    component.searchOperations({ value: 'test' });
    expect(component.searchOperations).toBeDefined();
  });

  it('should tagMultiple function should be defined', () => {
    component.tagMultiple();
    expect(component.tagMultiple).toBeDefined();
  });

  it('should generateTagLabel function should be defined', () => {
    component.selectedTags = [{ name: 'ab', meta: JSON.stringify({ patientFacing: true }) }];
    component.generateTagLabel();
    expect(component.generateTagLabel).toBeDefined();
  });

  it('should presentAdvancedSelect function should be defined', () => {
    component.presentAdvancedSelect();
    fixture.detectChanges();
    expect(component.presentAdvancedSelect).toBeDefined();
  });
  it('searchOperations function should check reset operation', () => {
    component.searchOperations({ do: 'reset', value: '' });
    expect(component.searchOperations).toBeTruthy();
  });
  describe('checkNavParam', () => {
    it('checkNavParam function should be defined', () => {
      component.checkNavParam();
      expect(component.checkNavParam).toBeDefined();
    });
    it('checkNavParam function should check patient value', () => {
      navData.patient = { displayname: 'Vinay', userId: '22' };
      navParams = new NavParams(navData);
      component.checkNavParam();
      expect(component.checkNavParam).toBeDefined();
    });
    it('checkNavParam function should be defined', () => {
      component.checkNavParam();
      expect(component.checkNavParam).toBeDefined();
    });
    it('checkNavParam function should check patients value', () => {
      navData.patients = [{ displayname: 'Vinay', userId: '22' }];
      navData.searchText = 's';
      jasmine.clock().install();
      navParams = new NavParams(navData);
      component.checkNavParam();
      jasmine.clock().tick(500);
      expect(component.checkNavParam).toBeDefined();
      jasmine.clock().uninstall();
    });
    it('checkNavParam function should check taged Items', () => {
      navData.tagedItems = [{ displayname: 'Vinay', userId: '22' }];
      navParams = new NavParams(navData);
      component.checkNavParam();
      expect(component.checkNavParam).toBeDefined();
    });
    it('checkNavParam function should fetch admissionName if not available', () => {
      navData.admissionDetails = { admissionId: '234HU-3423-RDSF2' };
      navParams = new NavParams(navData);
      sharedService.userData.config[Config.enableMultiAdmissions] = '1';
      spyOn(httpService, 'doGet').and.returnValue(of({ admissionName: 'Admission 1', admissionId: '234HU-3423-RDSF2' }));
      component.checkNavParam();
      expect(component.admissionDisplayText).toEqual('Admission 1');
    });
  });

  it('execute loadData', fakeAsync(() => {
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    spyOn(component, 'fetchUsers').and.stub();
    component.loadData({ target: spy });
    expect(component.fetchUsers).toHaveBeenCalled();
    expect(component.loadData).toBeTruthy();
  }));
  describe('fetchUsers', () => {
    it('fetchUsers function should be defined', () => {
      const mockResponse = [{ displayname: 'Vinay', userId: '2' }];
      spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of(mockResponse));
      component.fetchUsers();
      expect(component.fetchUsers).toBeTruthy();
    });
    it('fetchUsers function return empty and show no match found message', () => {
      const mockResponse = [];
      component.usersList = [];
      spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of(mockResponse));
      component.fetchUsers();
      expect(component.fetchUsers).toBeTruthy();
    });
  });
  describe('save', () => {
    it('should call save', () => {
      component.save();
      expect(component.save).toBeDefined();
    });
    it('execute save: tag selected', () => {
      component.selectedTags = [{}];
      component.usersList = [{ userId: '2', displayname: 'name' }];
      component.listHasPatientFacing = false;
      component.selectedUser = '2';
      component.save();
      expect(component.save).toBeDefined();
    });
    it('execute save: listHasPatientFacing', () => {
      component.selectedTags = [{}];
      component.usersList = [{ userId: '2', displayname: 'name' }];
      component.listHasPatientFacing = true;
      component.selectedUser = '2';
      spyOn(sharedService, 'getDocTypeIntegrationStatus').and.returnValue(of({ success: true, data: { patientIdentity: '', staffId: '' } }));
      component.save();
      expect(component.save).toBeDefined();
    });
    it('execute save: throw error', fakeAsync(() => {
      component.selectedTags = [{}];
      component.usersList = [{ userId: '2', displayname: 'name' }];
      component.listHasPatientFacing = true;
      component.selectedUser = '2';
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      spyOn(sharedService, 'getDocTypeIntegrationStatus').and.returnValue(throwError(() => ({ status: { message: 'Integration error' } })));
      tick(500);
      component.save();
      expect(commonService.showAlert).toHaveBeenCalled();
      expect(modalSpy.dismiss).not.toHaveBeenCalled();
      expect(component.save).toBeDefined();
    }));
    it('execute save: listHasPatientFacing and no users', () => {
      component.selectedTags = [{}];
      component.usersList = [];
      component.listHasPatientFacing = true;
      component.selectedUser = '2';
      component.save();
      expect(component.save).toBeDefined();
    });
    it('Show message to select tags', () => {
      component.selectedTags = [];
      component.save();
      expect(commonService.showMessage).toHaveBeenCalled();
    });
  });

  describe('showSelectedUser', () => {
    it('should showSelectedUser function be defined', () => {
      const user = { userId: '1', displayname: 'User 1' };
      component.showSelectedUser(user);
      expect(component.showSelectedUser).toBeDefined();
    });

    it('should reset socketMessageAdmissionDetails when reset is true', () => {
      const user = { userId: '1', displayname: 'User 1' };
      component.socketMessageAdmissionDetails = { admissionId: '123' };
      component.showSelectedUser(user, true);
      expect(component.socketMessageAdmissionDetails).toBeUndefined();
    });

    it('should not reset socketMessageAdmissionDetails when reset is false', () => {
      const user = { userId: '1', displayname: 'User 1' };
      component.socketMessageAdmissionDetails = { admissionId: '123', admissionName: 'Admission 123' };
      component.showSelectedUser(user, false);
      expect(component.admissionDisplayText).toEqual('Admission 123');
      expect(component.socketMessageAdmissionDetails).toEqual({ admissionId: '123', admissionName: 'Admission 123' });
    });

    it('should set patientSelectedValue and disabledPatientSelection', () => {
      const user = { userId: '1', displayname: 'User 1' };
      component.allowMultiPatientTagging = false;
      component.showSelectedUser(user);
      expect(component.patientSelectedValue).toEqual(user);
      expect(component.disabledPatientSelection).toBeTrue();
    });

    it('should set displayText using sharedService.generateLabel', () => {
      const user = { userId: '1', displayname: 'User 1' };
      spyOn(sharedService, 'generateLabel').and.returnValue('User 1 Label');
      component.showSelectedUser(user);
      expect(component.displayText).toBe('User 1 Label');
    });

    it('should handle isMultiAdmissionsEnabled logic', () => {
      const user = { userId: '1', displayname: 'User 1' };
      component.isMultiAdmissionsEnabled = true;
      component.socketMessageAdmissionDetails = { admissionId: '123', admissionName: 'Admission 123' };
      component.allowMultiPatientTagging = false;
      component.showSelectedUser(user, false);
      expect(component.disabledAdmissionSelection).toBeTrue();
      expect(component.patientSelectedValue).toEqual({ ...user, ...component.socketMessageAdmissionDetails });
      expect(component.admissionDisplayText).toBe('Admission 123');
    });

    it('should set admissionDisplayText to CHOOSE_ADMISSION when socketMessageAdmissionDetails is undefined', () => {
      const user = { userId: '1', displayname: 'User 1' };
      component.isMultiAdmissionsEnabled = true;
      component.socketMessageAdmissionDetails = undefined;
      getTranslateDataSpy.and.returnValue('Choose Admission');
      component.showSelectedUser(user);
      expect(component.admissionDisplayText).toBe('Choose Admission');
    });
  });
  describe('goBack', () => {
    it('should dismiss modal', () => {
      component.goBack();
      expect(modalController.dismiss).toHaveBeenCalled();
      expect(component.isModalOpen).toBeFalse();
    });
  });
  describe('showAdmissionPopup', () => {
    beforeEach(() => {
      spyOn(sharedService, 'selectAdmission').and.callFake((_params, _undefined, _component, callback) => {
        return callback({ admissionId: '124', admissionName: ' Admission 2' });
      });
    });
    it('should should set admissionDisplayText', () => {
      component.patientSelectedValue = { userId: '1', name: 'User 1', admissionName: 'Admission 1', admissionId: '123' };
      component.showAdmissionPopup();
      expect(component.admissionDisplayText).toBe(' Admission 2');
      expect(component.patientSelectedValue).toEqual({ userId: '1', name: 'User 1', admissionId: '124', admissionName: ' Admission 2' });
    });
  });
  it('execute initialSiteData: should set selectedSiteIds', () => {
    component.initialSiteData([3, 4]);
    expect(component.selectedSiteIds).toEqual([3, 4]);
  });
  it('execute filterSitesData: should set selectedSiteIds', () => {
    component.filterSitesData([3, 4]);
    expect(component.selectedSiteIds).toEqual([3, 4]);
  });
  describe('Tag handling with meta and metaData properties', () => {
    it('should properly map meta and metaData properties in checkNavParam', () => {
      const tagItems = [
        { id: 1, name: 'Tag1', meta: '{"patientFacing": true}' },
        { id: 2, name: 'Tag2', metaData: '{"approvalRequired": true}' }
      ];

      navData.tagedItems = tagItems;
      component.allowIntegrationTags = true;
      navParams = new NavParams(navData);

      component.checkNavParam();

      // Check that both meta and metaData are set correctly
      expect(component.selectedTags[0].metaData).toBe(tagItems[0].meta);
      expect(component.selectedTags[1].metaData).toBe(tagItems[1].metaData);
    });

    it('should properly map metaData in tag method', () => {
      const selectedUser = { id: '123', displayName: 'User 1' };
      const patientDetails = { userId: '123', displayname: 'User 1' };

      component.selectedTags = [
        { id: 1, name: 'Tag1', meta: '{"patientFacing": true}', metaData: null },
        { id: 2, name: 'Tag2', meta: null, metaData: '{"approvalRequired": true}' },
        { id: 3, name: 'Tag3' }
      ];

      component.tag(selectedUser, patientDetails);

      // Check modalController.dismiss was called with correct parameters
      expect(modalController.dismiss).toHaveBeenCalled();
      const dismissCall = (modalController.dismiss as jasmine.Spy).calls.mostRecent();
      const tagsInPayload = dismissCall.args[0].selectedTags;

      // Check that metaData is properly set for each tag
      expect(tagsInPayload[0].metaData).toBe('{"patientFacing": true}');
      expect(tagsInPayload[1].metaData).toBe('{"approvalRequired": true}');
      expect(tagsInPayload[2].metaData).toBeUndefined();
    });

    it('should handle complex tag objects in tag method', () => {
      const selectedUser = { id: '123', displayName: 'User 1' };
      const patientDetails = { userId: '123', displayname: 'User 1' };

      const complexTag = {
        id: 1,
        name: 'Complex Tag',
        meta: '{"nestedObj":{"prop1":"value1","prop2":true},"arrayProp":[1,2,3]}',
        otherProp: 'someValue'
      };

      component.selectedTags = [complexTag];
      component.tag(selectedUser, patientDetails);

      const dismissCall = (modalController.dismiss as jasmine.Spy).calls.mostRecent();
      const tagsInPayload = dismissCall.args[0].selectedTags;

      // Check that the complex tag is properly handled
      expect(tagsInPayload[0].metaData).toBe(complexTag.meta);
      expect(tagsInPayload[0].otherProp).toBe('someValue');
    });

    it('should generate tag label correctly with tags having meta property', () => {
      component.selectedTags = [
        { name: 'Tag1', meta: JSON.stringify({ patientFacing: true }) },
        { name: 'Tag2', meta: JSON.stringify({ approvalRequired: true }) }
      ];
      component.allowIntegrationTags = true;

      component.generateTagLabel();

      expect(component.listHasPatientFacing).toBeTrue();
      expect(component.autoApprove).toBeFalse();
      expect(component.allTagsAsString.includes('Tag1')).toBeTrue();
      expect(component.allTagsAsString.includes('Tag2')).toBeTrue();
    });
  });
});
