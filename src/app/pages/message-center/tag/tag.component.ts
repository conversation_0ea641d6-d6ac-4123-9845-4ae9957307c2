import { Component, OnInit } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller, ModalController, NavParams } from '@ionic/angular';
import { AdvancedSelectComponent } from 'src/app/components/advanced-select/advanced-select.component';
import { APIs } from 'src/app/constants/apis';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Constants, IntegrationType } from 'src/app/constants/constants';
import { isBlank, isPresent } from 'src/app/utils/utils';
import { CommonService } from 'src/app/services/common-service/common.service';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Permissions } from 'src/app/constants/permissions';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';
import { AdmissionDetails } from 'src/app/interfaces/common-interface';

@Component({
  selector: 'app-tag',
  templateUrl: './tag.component.html',
  styleUrls: ['./tag.component.scss']
})
export class TagComponent implements OnInit {
  isModalOpen = false;
  constantData;
  options;
  selectedTags;
  allTagsAsString;
  usersList;
  searchText;
  selectedUser;
  pageCount = Constants.defaultPageCount;
  searchPermission: boolean;
  haveChatRoomPatient: boolean;
  listHasPatientFacing = false;
  showNoUsers = false;
  showLoadMore = false;
  autoApprove: boolean;
  selectedSiteIds = [];
  admissionDisplayText = this.common.getTranslateData('TITLES.CHOOSE_ADMISSION');
  displayText = this.common.getTranslateData('TITLES.CHOOSE_PATIENT');
  isMultiAdmissionsEnabled = false;
  patientSelectedValue;
  disabledPatientSelection = false;
  disabledAdmissionSelection = false;
  socketMessageAdmissionDetails;
  allowMultiPatientTagging = false;
  allowIntegrationTags = true;
  selectedPatient;
  constructor(
    private readonly modalController: ModalController,
    public sharedService: SharedService,
    private readonly permissionService: PermissionService,
    private readonly common: CommonService,
    private readonly httpService: HttpService,
    private readonly navParams: NavParams,
    public alertController: AlertController
  ) {}
  checkNavParam() {
    const admissionDetails = this.navParams.get('admissionDetails');
    if (admissionDetails?.admissionId) {
      this.socketMessageAdmissionDetails = admissionDetails;
      if (!admissionDetails?.admissionName) {
        this.getAdmissionDetails();
      }
    }
    const patient = this.navParams.get('patient');
    if (isPresent(patient?.userId)) {
      this.usersList = [patient];
      this.selectedPatient = patient;
      this.selectedUser = patient.userId;
    }
    if (this.navParams.get('chatRoomPatient')) {
      this.haveChatRoomPatient = true;
      if (!this.usersList?.length) {
        this.selectedPatient = this.navParams.get('chatRoomPatient');
        this.usersList = [this.navParams.get('chatRoomPatient')];
        this.selectedUser = this.navParams.get('chatRoomPatient').userId;
      }
    }
    this.allowMultiPatientTagging = this.navParams.get('allowMultiPatientTagging');
    this.allowIntegrationTags = this.navParams.get('allowIntegrationTags');
    if (this.navParams.get('tagedItems') && this.navParams.get('integrationEnabled') === this.allowIntegrationTags) {
      this.selectedTags = this.navParams.get('tagedItems');
      this.selectedTags = this.selectedTags.map(this.normalizeTagMetadata);
      this.initializePatientSelection();
    }
    if (this.navParams.get('patients')) {
      this.usersList = this.navParams.get('patients');
      this.selectedUser = this.navParams.get('chatRoomPatient') ? this.navParams.get('chatRoomPatient').userId : '';
      setTimeout(() => {
        this.searchText = this.navParams.get('searchText') ? this.navParams.get('searchText') : '';
      }, 500);
    }
  }
  ngOnInit(): void {
    this.isMultiAdmissionsEnabled = this.sharedService.isMultiAdmissionsEnabled;
    this.checkNavParam();
    this.constantData = Constants;
    this.fetchTags();
    this.searchPermission = this.permissionService.userHasPermission(Permissions.patientSelectioninMessageTagging);
  }
  fetchTags(): void {
    const url = APIs.getAlltags;
    const params: any = {
      tag_type: 1,
      patientTenantId: this.usersList?.tenantId ? this.usersList.tenantId : this.sharedService.userData.tenantId,
    };
    if (!this.allowIntegrationTags) {
      params.integrationEnabled = false;
    }
    this.httpService.doGet({ endpoint: url, extraParams: params }).subscribe(
      (response) => {
        this.options = response;
        this.options = this.options.filter((availableTag) => {
          return (
            (this.sharedService.userData.group == String(Constants.patientGroupId) &&
              (availableTag.userType == Constants.tagUserTypes.patientFacing ||
                availableTag.userType == Constants.tagUserTypes.staffPatientFacing)) ||
            (this.sharedService.userData.group != String(Constants.patientGroupId) &&
              (availableTag.userType == Constants.tagUserTypes.staffFacing ||
                availableTag.userType == Constants.tagUserTypes.staffPatientFacing ||
                isBlank(availableTag.userType)))
          );
        });
        if (this.selectedTags) {
          this.generateTagLabel();
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
      }
    );
  }

  cancel(): void {
    this.modalController.dismiss();
  }

  get enableSearch(): boolean {
    return !this.haveChatRoomPatient || (this.haveChatRoomPatient && this.searchPermission);
  }

  async presentAdvancedSelect(): Promise<void> {
    const modal: any = await this.modalController.create({
      component: AdvancedSelectComponent,
      componentProps: {
        options: this.selectedTags ? this.sharedService.mergeSelectedData(this.selectedTags, this.options) : this.options,
        showSearch: true,
        buttons: { selectAll: false, clearAll: false, done: true }
      },
      cssClass: 'common-advanced-select',
      animated: true
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.selectedTags = this.options.filter((tag) => {
          return data.selectedData.find((row) => row.selected && tag.id === row.id);
        });
        this.initializePatientSelection();
      }
    });
    await modal.present();
  }
  initializePatientSelection() {
    this.generateTagLabel();
    if (this.navParams.get('chatRoomPatient') || (this.navParams.get('patient') && this.navParams.get('patient').userId)) {
      if (!this.allowMultiPatientTagging && this.selectedPatient) {
        this.showSelectedUser(this.selectedPatient, false);
      } else {
        this.patientSelectedValue = undefined;
        this.displayText = this.common.getTranslateData('TITLES.CHOOSE_PATIENT');
        if (this.isMultiAdmissionsEnabled) {
          this.admissionDisplayText = this.common.getTranslateData('TITLES.CHOOSE_ADMISSION');
        }
      }
    }
  }

  generateTagLabel(): void {
    this.allTagsAsString = '';
    this.listHasPatientFacing = false;
    this.autoApprove = true;
    this.selectedTags?.forEach((element) => {
      this.allTagsAsString += ` ${element.name},`;
      const metaObject = element.meta ? JSON.parse(element.meta) : null;
      if (metaObject && metaObject.patientFacing && this.allowIntegrationTags) {
        this.listHasPatientFacing = true;
      }
      if (metaObject && metaObject.approvalRequired) {
        this.autoApprove = false;
      }
    });
    this.allTagsAsString = this.allTagsAsString.slice(0, -1);
  }

  searchOperations(searchAction: any): void {
    this.searchText = searchAction.value;
    this.pageCount = Constants.defaultPageCount;
    this.usersList = [];
    if (this.searchText) {
      this.displayText = this.common.getTranslateData('TITLES.CHOOSE_PATIENT');
      this.fetchUsers();
    }
    if (searchAction.do === 'reset') {
      this.patientSelectedValue = undefined;
      this.searchText = '';
      this.selectedUser = undefined;
    }
  }

  fetchUsers(): void {
    this.showNoUsers = false;
    const payload = {
      pageCount: this.pageCount,
      tagId: '',
      searchKeyword: this.searchText,
      siteIds: this.selectedSiteIds.length > 0 ? this.selectedSiteIds.toString() : Constants.defaultSiteId
    };
    if (isBlank(this.searchText)) {
      return;
    }
    this.sharedService.getAssociatedPatientsByTagId(payload).subscribe((recipients) => {
      if (!isBlank(recipients)) {
        this.usersList = [...this.usersList, ...recipients];
        this.showLoadMore = recipients.length <= 0 ? false : true;
      }
      if (isBlank(this.usersList)) {
        this.showNoUsers = true;
      }
    });
  }

  get admissionId() {
    return this.isMultiAdmissionsEnabled ? this.socketMessageAdmissionDetails?.admissionId || this.patientSelectedValue?.admissionId : '';
  }
  save(): void {
    // TODO: Implement multiple functionality
    if (!(this.selectedTags && this.selectedTags.length)) {
      this.common.showMessage(this.common.getTranslateData('VALIDATION_MESSAGES.SELECT_ONE_OR_MORE_TAGS'));
      return;
    }
    let userName = '';
    let patientDetails = {};
    let userId = Constants.configFalse;
    let formattedDisplayname = '';
    let admissionId = '';
    if (this.selectedUser) {
      const chatIndex = this.usersList?.findIndex((item: any) => Number(item.userId) === Number(this.selectedUser));
      if (chatIndex !== -1) {
        patientDetails = this.usersList[chatIndex];
        userName = this.usersList[chatIndex].displayname;
        formattedDisplayname = this.displayText;
        admissionId = this.admissionId;
      }
      userId = this.selectedUser;
    }
    const selectedUser = { id: userId, displayName: userName, admissionId, formattedDisplayname };
    if (!this.selectedUser && this.listHasPatientFacing) {
      this.common.showMessage(this.common.getTranslateData('VALIDATION_MESSAGES.SEARCH_AND_SELECT_A_USER'));
      return;
    }
    if (isBlank(selectedUser.admissionId) && this.isMultiAdmissionsEnabled && this.listHasPatientFacing) {
      this.common.showMessage(this.common.getTranslateData('VALIDATION_MESSAGES.MANDATORY_ADMISSION'));
      return;
    }
    if (this.listHasPatientFacing && this.autoApprove) {
      const payload = {
        patientId: selectedUser.id,
        messageId: this.selectedTags[0].id,
        admissionId,
        action: IntegrationType.ADD_MESSAGE_TAG
      };
      this.sharedService.getDocTypeIntegrationStatus(payload).subscribe({
        next: (data) => {
          if (!isBlank(data) && data.success) {
            this.tag(selectedUser, patientDetails);
          }
        },
        error: (errorResponse) => {
          this.common
            .showAlert({
              message: `<center>
              <strong>${errorResponse.status.message}</strong> <br/><br/> ${this.common.getTranslateData('MESSAGES.YOU_CAN_CONTINUE_ANYWAY')}
              </center>`,
              header: '',
              cssClass: 'common-alert visit-alert ',
              buttons: [
                {
                  text: this.common.getTranslateData('BUTTONS.GO_BACK')
                },
                {
                  text: this.common.getTranslateData('BUTTONS.CONTINUE_ANYWAY')
                }
              ]
            })
            .then((confirmation) => {
              if (confirmation) {
                this.tag(selectedUser, patientDetails);
              }
            });
        }
      });
      return;
    }
    this.tag(selectedUser, patientDetails);
  }
  normalizeTagMetadata(tag) {
    return {
      ...tag,
      metaData: tag?.metaData ?? tag?.meta
    };
  }
  tag(selectedUser, patientDetails) {
    this.modalController.dismiss({
      selectedTags: this.selectedTags.map(this.normalizeTagMetadata),
      selectedUser,
      patientDetails
    });
  }

  tagMultiple(): void {
    this.modalController.dismiss({
      selectMultiple: true,
      history: {
        tagedItems: this.selectedTags,
        patients: this.usersList,
        chatRoomPatient: this.navParams.get('chatRoomPatient'),
        integrationEnabled: this.allowIntegrationTags,
        searchText: this.searchText
      }
    });
  }

  loadData(event: any) {
    event.target.complete();
    this.pageCount++;
    this.fetchUsers();
  }

  filterSitesData(data: number[]): void {
    this.selectedSiteIds = data;
    this.pageCount = Constants.defaultPageCount;
    this.usersList = [];
    this.fetchUsers();
  }

  initialSiteData(data: any): void {
    this.selectedSiteIds = data;
  }

  showAdmissionPopup(): void {
    const userId =
      this.listHasPatientFacing && this.autoApprove
        ? this.patientSelectedValue.id || this.patientSelectedValue.userId
        : this.patientSelectedValue.userId;
    const admissionId = this.patientSelectedValue?.admissionId || '';
    this.sharedService.selectAdmission({ userId, admissionId }, undefined, AdmissionComponent, (response: any) => {
      if (response) {
        this.patientSelectedValue.admissionId = response.admissionId;
        this.patientSelectedValue.admissionName = response.admissionName;
        this.admissionDisplayText = response.admissionName;
      }
    });
  }

  showSelectedUser(user: any, reset = true): void {
    if (reset) {
      this.socketMessageAdmissionDetails = undefined;
    }
    this.patientSelectedValue = user;
    this.disabledPatientSelection = !this.allowMultiPatientTagging;
    this.displayText = user?.formattedDisplayName || this.sharedService.generateLabel(user);
    if (this.isMultiAdmissionsEnabled) {
      this.disabledAdmissionSelection = this.socketMessageAdmissionDetails && this.disabledPatientSelection;
      if (this.socketMessageAdmissionDetails) {
        this.setAdmissionName(this.socketMessageAdmissionDetails);
      } else {
        this.admissionDisplayText = this.common.getTranslateData('TITLES.CHOOSE_ADMISSION');
      }
    }
    this.isModalOpen = false;
  }
  closeModal() {
    this.isModalOpen = false;
    this.modalController.dismiss();
  }
  goBack(): void {
    this.patientSelectedValue = undefined;
    this.selectedUser = undefined;
    this.displayText = this.common.getTranslateData('TITLES.CHOOSE_PATIENT');
    this.closeModal();
  }
  getAdmissionDetails(): void {
    if (this.sharedService.isMultiAdmissionsEnabled && isPresent(this.socketMessageAdmissionDetails?.admissionId)) {
      const url = `${APIs.getAdmissionDetails}${this.socketMessageAdmissionDetails?.admissionId}`;
      this.httpService
        .doGet({
          endpoint: url,
          loader: true
        })
        .subscribe({
          next: (admission: AdmissionDetails) => {
            this.setAdmissionName(admission);
          }
        });
    }
  }
  setAdmissionName(admission: AdmissionDetails): void {
    this.socketMessageAdmissionDetails = { ...this.socketMessageAdmissionDetails, admissionName: admission?.admissionName };
    this.patientSelectedValue = { ...this.patientSelectedValue, ...this.socketMessageAdmissionDetails };
    this.admissionDisplayText = admission?.admissionName;
  }
}
