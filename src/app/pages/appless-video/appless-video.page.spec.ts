import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { Apollo } from 'apollo-angular';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { ApplessVideoPage } from './appless-video.page';
import { Activity } from 'src/app/constants/activity';
describe('ApplessVideoPage', () => {
  let component: ApplessVideoPage;
  let fixture: ComponentFixture<ApplessVideoPage>;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ApplessVideoPage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, TranslateModule.forRoot()],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        Apollo,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ApplessVideoPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call verifyApplessVideoToken when tokenData is truthy', () => {
    component.tokenData = 'some_token_value';
    const verifyApplessVideoTokenSpy = spyOn<any>(component, 'verifyApplessVideoToken');
    component.ngOnInit();
    expect(verifyApplessVideoTokenSpy).toHaveBeenCalled();
  });
  it('should not call verifyApplessVideoToken when tokenData is falsy', () => {
    component.tokenData = null;
    const verifyApplessVideoTokenSpy = spyOn<any>(component, 'verifyApplessVideoToken');
    component.ngOnInit();
    expect(verifyApplessVideoTokenSpy).not.toHaveBeenCalled();
  });
  it('should call verifyApplessVideoToken', () => {
    component.verifyApplessVideoToken();
    expect(component.verifyApplessVideoToken).toBeTruthy();
  });

  it('should call initSocketEvents', () => {
    component.initSocketEvents();
    expect(component.initSocketEvents).toBeTruthy();
  });

  it('should call initVideoChat', () => {
    component.initVideoChat();
    expect(component.initVideoChat).toBeTruthy();
  });

  it('should call getRoomUsersAndAcceptCall', () => {
    component.getRoomUsersAndAcceptCall();
    expect(component.getRoomUsersAndAcceptCall).toBeTruthy();
  });
});
