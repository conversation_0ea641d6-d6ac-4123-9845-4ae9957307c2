import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Activity } from 'src/app/constants/activity';
import { APIs } from 'src/app/constants/apis';
import { Config } from 'src/app/constants/config';
import { Constants } from 'src/app/constants/constants';
import { Socket } from 'src/app/constants/socket';
import { VideoCall } from 'src/app/constants/video-call';
import { ApplessVideoTokenVerify } from 'src/app/interfaces/appless-interface';
import { ChatRoomUsersAndRoles } from 'src/app/interfaces/messages';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SessionService } from 'src/app/services/session-service/session.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { VideoCallService } from 'src/app/services/video-call/video-call.service';
import { setKeyToSession } from 'src/app/utils/storage-utils';
import { isPresent, stringToJSON } from 'src/app/utils/utils';

@Component({
  selector: 'app-appless-video',
  templateUrl: './appless-video.page.html',
  styleUrls: ['./appless-video.page.scss']
})
export class ApplessVideoPage implements OnInit {
  tokenData: string;
  verificationMessage: any;
  roomId: number;
  responseData: ApplessVideoTokenVerify;
  meetingEnded = false;
  meetingEndedText: string;
  constructor(
    private readonly route: ActivatedRoute,
    public sessionService: SessionService,
    private readonly commonService: CommonService,
    private readonly sharedService: SharedService,
    private readonly httpService: HttpService,
    private readonly socketService: SocketService,
    public videoCallService: VideoCallService
  ) {
    this.route.queryParamMap.subscribe((params) => {
      this.tokenData = params.get('token');
    });
    this.meetingEndedText = this.commonService.getTranslateData('VIDEO_MESSAGES.MEETING_END_CLOSE_APPLESS');
  }
  ngOnInit(): void {
    if (this.tokenData) {
      this.verifyApplessVideoToken();
    }
  }
  verifyApplessVideoToken(): void {
    this.sessionService.sessionLoader = false;
    this.verificationMessage = this.commonService.getTranslateData('VALIDATION_MESSAGES.APPLESS_TOKEN_VERIFICATION');
    this.sharedService.trackActivity({ type: Activity.pageAccess });
    const endPoint = APIs.verifyVideoToken;

    this.httpService
      .doGet({
        endpoint: endPoint,
        hasAuthToken: false,
        extraParams: { token: this.tokenData }
      })
      .subscribe((response: ApplessVideoTokenVerify) => {
        this.responseData = response;
        if (isPresent(response.userData)) {
          const options = {
            maxParticipants: VideoCall.maxParticipants,
            userData: this.sharedService.userData
          };
          this.videoCallService.init('vidyo', options);
          response.userData.userName = response.userData.username;
          this.sharedService.userData = response.userData;
          this.sharedService.userData.appLessSession = true;
          this.sharedService.setPermissions(response.userData.privileges);
          sessionStorage.setItem(Constants.storageKeys.isVirtual, Constants.trueAsString);
          setKeyToSession({ key: Constants.storageKeys.authToken, value: response.userData.authenticationToken });
          this.sharedService.joinAppSocket();
          this.sharedService.setSessionTimeoutTime();
          if (String(response.data.status) === Constants.videoCallSessionExpiredStatus) {
            this.commonService.showMessage(this.sharedService.appConfig.videoApplessExpiry);
            setTimeout(() => {
              this.meetingEndedText = this.commonService.getTranslateData('VIDEO_MESSAGES.MEETING_EXPIRED');
              this.meetingEnded = true;
              this.commonService.dismissPopover();
              this.videoCallService.disconnectApplessClient();
              this.commonService
                .showAlert({
                  cssClass: 'common-alert',
                  header: 'VIDEO_MESSAGES.LEAVE_MEETING',
                  message: 'VIDEO_MESSAGES.MEETING_EXPIRED',
                  buttons: [
                    {
                      text: this.commonService.getTranslateData('BUTTONS.OK').toUpperCase(),
                      confirm: false,
                      class: 'warn-btn alert-ok full-width'
                    }
                  ]
                })
                .then((data) => {
                  // close window
                });
            }, 1000);
          } else {
            this.roomId =
              response?.data?.chat_room && typeof Number(response.data.chat_room) === 'number'
                ? Number(response.data.chat_room)
                : undefined;
            if (this.roomId) {
              this.initSocketEvents();
            }
          }
        }
      });
  }
  initSocketEvents(): void {
    this.socketService.emitEvent(
      Socket.joinToChatroom,
      {
        user: this.sharedService.userData?.userId,
        room: this.roomId?.toString(),
        name: this.sharedService.userData?.displayName,
        avatar: this.sharedService.userData?.profileImageThumbUrl,
        citusRole: this.sharedService.userData?.group,
        patientReminderCheckingType: this.sharedService?.getConfigValue(Config.patientReminderCheckingType),
        sites: this.sharedService?.userData?.mySites,
        tenantId: this.sharedService?.userData?.tenantId,
        tenantKey: this.sharedService?.userData?.tenantKey,
        tenantName: this.sharedService?.userData?.tenantName,
        eventSource: JSON.stringify({
          environment: this.sharedService.platformValue,
          device: this.commonService.getDeviceType(),
          component: Constants.eventSourceComponent.applessVideo
        })
      },
      (data) => {
        if (data === Constants.trueAsString) {
          // As part of CHP-19627, vidyoapplesslinkclick socket event is removed, since multiple session is allowed.
          if (!this.videoCallService.checkVideoPluginLoaded()) {
            this.commonService.showMessage(this.sharedService.appConfig.videoApplessMessage);
          }
          this.initVideoChat();
        }
      }
    );
    this.socketService.subscribeEvent(Socket.applessLinkAlreadyInUse).subscribe((data) => {
      this.commonService.showMessage(this.sharedService.appConfig.applessLinkAlreadyInUse);
    });
    this.socketService.subscribeEvent(Socket.exitVideoChat).subscribe((result) => {
      this.videoCallService.disconnectApplessClient();
      const message = this.commonService.getTranslateData('VIDEO_MESSAGES.MEETING_END_APPLESS');
      this.commonService.showMessage(message);
      this.meetingEnded = true;
    });
  }
  initVideoChat(): void {
    this.sharedService
      .generateVidyoTocken({
        source: VideoCall.generateVidyoSource,
        userName: `${this.sharedService?.userData?.displayName}-${this.sharedService?.userData?.tenantName}`
      })
      .subscribe((tokenData) => {
        this.sharedService.vidyoToken = tokenData.token;
        setKeyToSession({ key: Constants.storageKeys.vidyoToken, value: tokenData.token });
      });

    if (!this.videoCallService.checkVideoPluginLoaded()) {
      if (!(this.sharedService.platform.is('ios') && this.sharedService.platform.is('capacitor'))) {
        this.videoCallService.createVcConnector((callBackVal: any) => {
          if (callBackVal) {
            this.getRoomUsersAndAcceptCall();
          } else {
            this.commonService.showToast({
              message: this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
              color: 'danger'
            });
          }
        });
      }
    } else {
      this.getRoomUsersAndAcceptCall();
    }
  }
  getRoomUsersAndAcceptCall(): void {
    const url = APIs.getRoomUsers;
    const params = {
      targetId: this.roomId,
      status: Constants.notRejected,
      tenantId: this.sharedService?.userData?.tenantId,
      userid: this.sharedService?.userData?.userId
    };
    this.httpService.doGet({ endpoint: url, extraParams: params }).subscribe((response: ChatRoomUsersAndRoles) => {
      this.videoCallService.videoChatData = this.responseData.data;
      const roomData = stringToJSON(this.responseData.data.roomData);
      if (roomData && typeof roomData === 'object') {
        this.videoCallService.videoChatData.roomData = roomData;
      }
      this.videoCallService.videoChatData.roomId = this.roomId;
      this.videoCallService.videoChatData.chatroomUsersList = response?.data?.allParticipants;
      this.videoCallService.acceptCall();
      this.videoCallService.sharedService.videoCall = true;

      const data = {
        action: VideoCall.sendAppLessAction.participantJoin,
        userid: this.sharedService.userData.userId,
        token: this.sharedService.userData.authenticationToken
      };
      this.videoCallService.sharedService.updateToAppLessVideo(data).subscribe();
    });
  }
}
