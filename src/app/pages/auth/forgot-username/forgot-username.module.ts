import { HeaderPlainModule } from './../../../components/header-plain/header-plain.module';
import { SharedModule } from '../../../shared.module';
import { CustomDatepickerComponentModule } from './../../../components/custom-datepicker/custom-datepicker.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ForgotUsernameRoutingModule } from './forgot-username-routing.module';
import { ForgotUsernameComponent } from './forgot-username.component';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [ForgotUsernameComponent],
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    IonicModule,
    ForgotUsernameRoutingModule,
    ReactiveFormsModule,
    TranslateModule,
    HeaderPlainModule,
    CustomDatepickerComponentModule
  ]
})
export class ForgotUsernameModule {}
