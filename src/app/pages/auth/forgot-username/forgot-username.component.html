<app-header-plain [headerTitle]="'LABELS.FORGOT_USERNAME'" [type]="''"></app-header-plain>
<ion-content class="forgot-username-page">
    <div class="common-box-container">
        <form name="forgot-username-form" id="forgot-username-form" [formGroup]="forgotUsernameForm"
            class="forgot-pass-form">
            <ion-col size="12"><span>{{'LABELS.TELL_US_WHO_YOU_ARE' | translate}}</span></ion-col>
            <ion-col size="12">
                <ion-item class="ion-no-padding">
                    <ion-select mode="md" class="common-input" placeholder="{{'LABELS.SELECT' | translate}}" [(ngModel)]="userType" [ngModelOptions]="{ standalone: true }" (ionChange)="onUserTypeChange()">
                        <ion-select-option value="1">{{ 'LABELS.PATIENT' | translate }}</ion-select-option>
                        <ion-select-option value="0">{{ 'LABELS.OTHER' | translate }}</ion-select-option>
                    </ion-select>
                </ion-item>
            </ion-col>
            <ng-container *ngIf="userType !== ''">
                <ion-item class="ion-margin-bottom ion-no-padding" mode="md">
                    <ion-label>
                        <ion-icon slot="start" src="assets/icon/material-svg/account.svg" color="fountain-blue"
                        class="set-round-icon ion-margin-end inner-icon"></ion-icon>
                    </ion-label>
                    <ion-input id="firstName" type="text" placeholder="{{ 'LABELS.FIRST_NAME' | translate }}"
                        formControlName="firstName" name="firstName"></ion-input>
                </ion-item>
                <ion-item class="ion-margin-bottom ion-no-padding" mode="md">
                    <ion-label>
                        <ion-icon slot="start" src="assets/icon/material-svg/account.svg" color="fountain-blue"
                        class="set-round-icon ion-margin-end inner-icon"></ion-icon>
                    </ion-label>
                    <ion-input id="lastName" type="text" placeholder="{{ 'LABELS.LAST_NAME' | translate }}"
                        formControlName="lastName" name="lastName"></ion-input>
                </ion-item>
                <ng-container *ngIf="userType === '1'">
                    <ion-item id="open-date-input" class="ion-margin-bottom ion-no-padding" mode="md">
                        <ion-label>
                            <ion-icon slot="start" src="assets/icon/material-svg/calendar.svg" color="fountain-blue"
                            class="set-round-icon ion-margin-end inner-icon"></ion-icon>
                        </ion-label>
                        <ion-input id="dob" type="text" id="dob" readonly="true"
                            placeholder="{{ 'LABELS.DOB_PLACEHOLDER' | translate }}" formControlName="dob">
                        </ion-input>
                        <app-custom-datepicker triggerID="open-date-input" pickerType="date" controlName="dob"
                            [minDate]="constants.minDate" [maxDate]="maxDate" (selectDate)="setDateValue($event)"
                            [selectedDate]="selectedDate">
                        </app-custom-datepicker>
                    </ion-item>
                </ng-container>
                <ng-container *ngIf="userType === '0'">
                    <ion-item class="ion-margin-bottom ion-no-padding" mode="md">
                        <ion-label>
                            <ion-icon slot="start" src="assets/icon/material-svg/email.svg" color="fountain-blue"
                            class="set-round-icon ion-margin-end inner-icon"></ion-icon>
                        </ion-label>
                        <ion-input id="email" type="text" placeholder="{{ 'LABELS.EMAIL' | translate }}"
                            formControlName="email" name="email" (ionInput)="onChange()"></ion-input>
                    </ion-item>
                    <ion-item class="ion-margin-bottom ion-no-padding" mode="md">
                        <ion-label>
                            <ion-icon slot="start" name="call" color="fountain-blue" class="set-round-icon ion-margin-end inner-icon"></ion-icon>
                        </ion-label>
                        <ion-input id="mobile" type="text" placeholder="{{ 'LABELS.MOBILE_NUMBER' | translate }}"
                            formControlName="mobile" name="mobile" (ionInput)="onChange()"></ion-input>
                    </ion-item>
                </ng-container>
                <ion-button [disabled]="!forgotUsernameForm.valid" id="user-lookup" (click)="userLookup()" expand="block"
                    class="ion-text-capitalize forgot-password-submit">
                    {{ 'BUTTONS.LOOKUP' | translate }}
                </ion-button>
            </ng-container>
        </form>
    </div>
</ion-content>
<app-footer></app-footer>