import { CustomDatepickerComponentModule } from 'src/app/components/custom-datepicker/custom-datepicker.module';
import { of, throwError } from 'rxjs';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ForgotUsernameComponent } from 'src/app/pages/auth/forgot-username/forgot-username.component';
import { RouterModule } from '@angular/router';
import { UntypedFormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { AngularDelegate, IonicModule, PopoverController } from '@ionic/angular';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AuthGuard } from 'src/app/services/auth-guard/auth.guard';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { HttpService } from 'src/app/services/http-service/http.service';

describe('ForgotUsernameComponent', () => {
  let component: ForgotUsernameComponent;
  let fixture: ComponentFixture<ForgotUsernameComponent>;
  let httpService: HttpService;
  let formBuilder: UntypedFormBuilder;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ForgotUsernameComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule,
        FormsModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        CustomDatepickerComponentModule
      ],
      providers: [
        TranslateService,
        PopoverController,
        AngularDelegate,
        HttpService,
        UntypedFormBuilder,
        TranslateService,
        PopoverController,
        AngularDelegate,
        AuthGuard,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();
    formBuilder = TestBed.inject(UntypedFormBuilder);
    httpService = TestBed.inject(HttpService);
    fixture = TestBed.createComponent(ForgotUsernameComponent);
    component = fixture.componentInstance;
    component.forgotUsernameForm = formBuilder.group({
      firstName: [null, [Validators.required]],
      lastName: [null, [Validators.required]],
      dob: '',
      email: '',
      mobile: '',
      communicationType: ''
    });
    fixture.detectChanges();
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('Verify user lookup with invalid response', () => {
    const myServiceSpy = spyOn(httpService, 'doGet').and.returnValue(throwError({ status: 404 }));
    component.userLookup();
    expect(myServiceSpy).toHaveBeenCalled();
  });

  it('Verify user lookup Patient', () => {
    component.userType = '1';
    const myServiceSpy = spyOn(httpService, 'doGet').and.returnValue(of({ status: 200, message: '' }));
    component.userLookup();
    expect(myServiceSpy).toHaveBeenCalled();
  });
  it('should execute onChange', () => {
    component.onChange();
    expect(component.onChange).toBeTruthy();
  });

  it('should execute onChange mobile exists', () => {
    component.forgotUsernameForm.get('mobile').patchValue('<EMAIL>');
    component.onChange();
    expect(component.onChange).toBeTruthy();
  });

  it('should execute onChange email exists', () => {
    component.forgotUsernameForm.get('email').patchValue('<EMAIL>');
    component.onChange();
    expect(component.onChange).toBeTruthy();
  });

  it('should execute onUserTypeChange: patient', () => {
    component.userType = '1';
    component.onUserTypeChange();
    expect(component.onUserTypeChange).toBeTruthy();
  });

  it('should execute onUserTypeChange: other users', () => {
    component.userType = '0';
    component.onUserTypeChange();
    expect(component.onUserTypeChange).toBeTruthy();
  });

  it('should execute get mobileNumber', () => {
    component.forgotUsernameForm.get('mobile').patchValue('74954 998');
    const { mobileNumber } = component;
    expect(mobileNumber).toEqual('74954998');
  });

  it('should execute mobileNumber', () => {
    component.forgotUsernameForm.get('mobile').patchValue('74954 998');
    const { formData } = component;
    expect(formData).toEqual({
      firstName: null,
      lastName: null,
      dob: '',
      email: '',
      mobile: '74954998'
    });
  });

  it('should execute setDateValue: dob with empty value', () => {
    component.setDateValue({
      value: '',
      pickerFormat: '',
      formControlName: 'dob'
    });
    expect(component.setDateValue).toBeTruthy();
  });

  it('should execute setDateValue: dob with value', () => {
    component.setDateValue({
      value: '09/08/1999',
      pickerFormat: '',
      formControlName: 'dob'
    });
    expect(component.setDateValue).toBeTruthy();
  });
});
