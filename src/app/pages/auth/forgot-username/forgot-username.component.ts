import { CommonService } from './../../../services/common-service/common.service';
import { Constants } from './../../../constants/constants';
import { formatDate, isBlank } from './../../../utils/utils';
import { DateSelectKeyValue } from './../../../interfaces/common-interface';
import { APIs } from 'src/app/constants/apis';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import * as moment from 'moment';

@Component({
  selector: 'app-forgot-username',
  templateUrl: './forgot-username.component.html',
  styleUrls: ['./forgot-username.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ForgotUsernameComponent implements OnInit {
  forgotUsernameForm: UntypedFormGroup;
  constants = Constants;
  selectedDate = formatDate(undefined, Constants.dateFormat.yyyMMDDT);
  maxDate = moment().format(Constants.dateFormat.yyyMMDDT);
  userType = '';
  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly httpService: HttpService,
    private readonly common: CommonService,
    private readonly cd: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.forgotUsernameForm = this.formBuilder.group({
      firstName: [null, [Validators.required]],
      lastName: [null, [Validators.required]],
      dob: '',
      email: '',
      mobile: ''
    });
  }

  setDateValue(val: DateSelectKeyValue) {
    this.selectedDate = isBlank(val.value) ? formatDate(undefined, Constants.dateFormat.yyyMMDDT) :
      formatDate(val.value, Constants.dateFormat.yyyMMDDT);

    if (isBlank(val.value)) {
      this.forgotUsernameForm.get(val.formControlName).reset();
    } else {
      this.forgotUsernameForm.get(val.formControlName).setValue(formatDate(val.value, Constants.dateFormat.mdy));
    }
  }

  get formData() {
    const formData = this.forgotUsernameForm.value;
    if (!isBlank(formData.mobile)) {
      formData.mobile = this.mobileNumber;
    }
    if (!isBlank(formData.email)) {
     formData.email = encodeURIComponent(formData.email);
    }
    return formData;
  }

  get mobileNumber() {
    return this.forgotUsernameForm.get('mobile').value.match(/\d/g).join('');
  }

  onUserTypeChange() {
    this.forgotUsernameForm.reset();
    ['dob', 'email', 'mobile'].forEach((key) => {
      this.forgotUsernameForm.get(key).clearValidators();
      this.forgotUsernameForm.get(key).updateValueAndValidity();
    });
    if (this.userType === '1') {
      this.forgotUsernameForm.get('dob').setValidators([Validators.required]);
      this.forgotUsernameForm.get('dob').updateValueAndValidity();
    } else if (this.userType === '0') {
      this.setEmailMobileValidations();
    }
    this.cd.markForCheck();
  }

  setEmailMobileValidations() {
    this.forgotUsernameForm.get('email').setValidators([Validators.required, Validators.email]);
    this.forgotUsernameForm.get('email').updateValueAndValidity();
    this.forgotUsernameForm.get('mobile').setValidators([Validators.required]);
    this.forgotUsernameForm.get('mobile').updateValueAndValidity();
  }

  onChange() {
    if (!isBlank(this.forgotUsernameForm.get('email').value)) {
      this.forgotUsernameForm.get('mobile').clearValidators();
      this.forgotUsernameForm.get('mobile').updateValueAndValidity();
    } else if (!isBlank(this.forgotUsernameForm.get('mobile').value)) {
      this.forgotUsernameForm.get('email').clearValidators();
      this.forgotUsernameForm.get('email').updateValueAndValidity();
    } else {
      this.setEmailMobileValidations();
    }
    this.cd.markForCheck();
  }

  userLookup() {
    const data = this.formData;
    if (this.userType === '1') {
      delete data.email;
      delete data.mobile;
    } else {
      delete data.dob;
      if (isBlank(data.email)) {
        delete data.email;
      }
      if (isBlank(data.mobile)) {
        delete data.mobile;
      }
    }
    this.httpService
      .doGet({ endpoint: APIs.userLookupEndpoint, extraParams: data, loader: true })
      .subscribe({
      next: (res: { status: boolean; message: string }) => {
        this.common.showMessage(res.message);
        this.cd.markForCheck();
      },
      error: (error) => {
        this.common.showMessage(error);
        this.cd.markForCheck();
      }
    });
  }
}
