<ion-header mode="ios">
  <ion-toolbar color="blumine">
    <ion-title>{{ (isDeleteAccount ? 'LABELS.DELETE_ACCOUNT' : 'TITLES.ACCOUNT_CREATION') | translate }}</ion-title>
    <ion-buttons slot="end">
      <ion-button fill="clear" color="de-york" (click)="modalController.dismiss()" class="ion-text-capitalize">
        {{ 'BUTTONS.CLOSE' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content>
  <ion-row>
    @if (isDeleteAccount) {
      <ion-col size="12" class="ion-padding">
        <p>{{ 'MESSAGES.DELETE_ACCOUNT_MESSAGE' | translate }}</p>
      </ion-col>
    } @else {
      <ion-col size="12" class="ion-padding" *ngIf="bundleIdentifier !== 'com.myardon.health'">
        <p class="ion-padding-horizontal">
          {{ (bundleIdentifier === 'com.anovorx.myanovo' ? 'MESSAGES.SIGN_UP_MESSAGE4' : 'MESSAGES.SIGN_UP_MESSAGE1') | translate }}
        </p>
        <p class="ion-padding-horizontal">{{ 'MESSAGES.SIGN_UP_MESSAGE2' | translate }}</p>
      </ion-col>
      <ion-col size="12" class="ion-padding" *ngIf="bundleIdentifier === 'com.myardon.health'">
        <p>{{ 'MESSAGES.SIGN_UP_MESSAGE3' | translate }}</p>
      </ion-col>
    }
  </ion-row>
</ion-content>
