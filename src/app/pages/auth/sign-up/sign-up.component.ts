import { Urls } from 'src/app/constants/urls';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { ModalController, NavParams } from '@ionic/angular';
import { Component } from '@angular/core';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { theme } from 'src/theme/theme';

@Component({
  selector: 'app-sign-up',
  templateUrl: './sign-up.component.html',
  styleUrls: ['./sign-up.component.scss']
})
export class SignUpComponent {
  public bundleIdentifier = '';
  public isDeleteAccount = false;
  constructor(
    public modalController: ModalController,
    private sharedService: SharedService,
    private inAppBrowser: InAppBrowser,
    public navParams: NavParams
  ) {
    this.bundleIdentifier = theme.bundleIdentifier;
    this.isDeleteAccount = this.navParams?.get('isDeleteAccount');
  }

}
