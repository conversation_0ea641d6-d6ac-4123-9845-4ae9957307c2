import { SignUpComponent } from './sign-up.component';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../../../shared.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, SharedModule, TranslateModule],
  declarations: [SignUpComponent],
  exports: [SignUpComponent]
})
export class SignUpComponentModule {}
