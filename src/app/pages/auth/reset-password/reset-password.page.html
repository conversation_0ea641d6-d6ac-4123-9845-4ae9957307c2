<ion-header class="common-header" mode="md">
    <ion-toolbar mode="md">
        <ion-title class="header-ion-title">
            <h1 class="header-title">{{ 'LABELS.USER_REGISTRATION' | translate }}</h1>
        </ion-title>
    </ion-toolbar>
</ion-header>

<ion-content class="reset-pass-page">
    <div class="common-box-container">
        <div class="reset-pass-head">{{'BUTTONS.RESET_PASS' | translate}}</div>
        <form name="reset-password-form" id="reset-password-form" [formGroup]="resetPasswordForm"
            class="reset-pass-form">
            <div class="list">
                <div class="common-auth-input-row">
                    <ion-input formControlName="password" name="password" [type]="isShowPassword ? 'text':'password'"
                        id="password" class="common-auth-input" placeholder="{{ 'LABELS.PASSWORD' | translate }}"
                        mode="md" clearOnEdit="false">
                    </ion-input>
                    <ion-label>
                        <ion-icon slot="start" [name]="isShowPassword ? 'eye' : 'eye-off'" (click)="showHidePassword()"
                        color="fountain-blue" class="icon"></ion-icon>
                    </ion-label>
                </div>
                <div *ngIf="(isBlank(this.oktaTokenStorage) && resetPasswordForm.controls.password.errors)">
                    <div *ngIf="resetPasswordForm.controls.password.errors.minlength"
                        class="common-validation-error error-message">
                        {{ 'VALIDATION_MESSAGES.SHORT_PASSWORD' | translate }}</div>
                </div>
                <div *ngIf="resetPasswordForm.value.password">
                    <div *ngIf="(!isBlank(this.oktaTokenStorage) && !this.passwordValidator(resetPasswordForm.value.password))"
                        class="common-validation-error error-message">
                        {{ 'VALIDATION_MESSAGES.PASSWORD_REQUIREMENTS' | translate }}</div>
                </div>
                <div class="common-auth-input-row">
                    <ion-label></ion-label>
                    <ion-input formControlName="confirmPassword" name="confirm-password" type="password"
                        id="confirm-password" class="common-auth-input"
                        placeholder="{{ 'LABELS.CONFIRM_PASSWORD' | translate }}" clearOnEdit="false"></ion-input>

                </div>
                <div *ngIf="resetPasswordForm.value.password && resetPasswordForm.value.confirmPassword">
                    <div *ngIf="resetPasswordForm.value.password !== resetPasswordForm.value.confirmPassword"
                        class="common-validation-error error-message">
                        {{ 'VALIDATION_MESSAGES.PASSWORD_MISMATCH' | translate }}</div>
                </div>
            </div>
            <!-- TODO! CHP-3595 -->
            <ion-button (click)="resetPassword()" color="de-york" expand="block" class="ion-text-capitalize">
                {{ 'BUTTONS.SUBMIT' | translate }}
            </ion-button>
        </form>
    </div>
</ion-content>

<ion-footer class="common-footer">
    <ion-toolbar>
    </ion-toolbar>
</ion-footer>