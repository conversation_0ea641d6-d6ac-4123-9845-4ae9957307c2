import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { AuthGuard } from 'src/app/services/auth-guard/auth.guard';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { ResetPasswordPage } from './reset-password.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of } from 'rxjs';

describe('ResetPasswordPage', () => {
  let component: ResetPasswordPage;
  let fixture: ComponentFixture<ResetPasswordPage>;
  let common: CommonService;
  let sharedService: SharedService;
  let httpService: HttpService;
  let formBuilder: UntypedFormBuilder;
  const data = { password: '', confirmPassword: '' };
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ResetPasswordPage],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule,
        FormsModule,
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [
        SharedService,
        CommonService,
        HttpService,
        AuthGuard,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        UntypedFormBuilder
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    common = TestBed.inject(CommonService);
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(common, 'showMessage').and.stub();
    sharedService = TestBed.inject(SharedService);
    sharedService.userData = {
      ...sharedService.userData,
      tenantId: '1',
      tenantKey: '2124',
      userId: '20',
      userName: 'demoUser'
    };
    spyOn(sharedService, 'loggedUserIsPatient').and.returnValue(true);
    fixture = TestBed.createComponent(ResetPasswordPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  beforeEach(() => {
    formBuilder = TestBed.inject(UntypedFormBuilder);
    component.resetPasswordForm = formBuilder.group(data);
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('validateForm function should defined', () => {
    component.validateForm();
    expect(component.validateForm).toBeTruthy();
  });
  it('validateForm function should check password field is empty or not', () => {
    component.resetPasswordForm.controls['password'].setValue('password123.');
    component.resetPasswordForm.controls['confirmPassword'].setValue('password123.');
    component.validateForm();
    expect(component.validateForm).toBeTruthy();
  });
  it('validateForm function should check confirmPassword field is empty or not', () => {
    component.resetPasswordForm.controls['password'].setValue('11111111');
    component.resetPasswordForm.controls['confirmPassword'].setValue('');
    component.validateForm();
    expect(component.validateForm).toBeTruthy();
  });
  it('validateForm function should validate password and confirmPassword fields', () => {
    component.resetPasswordForm.controls['password'].setValue('11111111');
    component.resetPasswordForm.controls['confirmPassword'].setValue('1111111');
    component.validateForm();
    expect(component.validateForm).toBeTruthy();
  });

  it('resetPassword function should defined', () => {
    spyOn(component, 'validateForm').and.returnValue(true);
    spyOn(httpService, 'doPost').and.returnValue(of({ status: 0 }));
    component.resetPassword();
    expect(component.resetPassword).toBeTruthy();
  });
});
