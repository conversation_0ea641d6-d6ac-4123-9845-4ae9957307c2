import { Component } from '@angular/core';
import { UntypedFormGroup, Validators, UntypedFormBuilder } from '@angular/forms';
import { isBlank } from '../../../utils/utils';
import { CommonService } from '../../../services/common-service/common.service';
import { SharedService } from '../../../services/shared-service/shared.service';
import { HttpService } from '../../../services/http-service/http.service';
import { APIs } from 'src/app/constants/apis';
import { Activity } from '../../../constants/activity';
import { Router } from '@angular/router';
import { Config } from '../../../constants/config';
import { Constants } from 'src/app/constants/constants';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.page.html',
  styleUrls: ['./reset-password.page.scss']
})
export class ResetPasswordPage {
  resetPasswordForm: UntypedFormGroup;
  isShowPassword = false;
  oldPassword: any = {};
  oktaTokenStorage = JSON.parse(localStorage.getItem(Constants.storageKeys.oktaTokenStorage));
  isBlank = isBlank;
  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly common: CommonService,
    private readonly sharedService: SharedService,
    private readonly httpService: HttpService,
    private readonly router: Router
  ) {
    if (this.router.getCurrentNavigation()?.extras.state) {
      this.oldPassword = this.router.getCurrentNavigation().extras.state.oldPassword;
    }
    this.resetPasswordForm = this.formBuilder.group({
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]]
    });
  }

  resetPassword(): void {
    if (this.validateForm()) {
      const payload = {
        oldPassword: this.oldPassword,
        password: this.resetPasswordForm.value.password,
        userId: this.sharedService.userData.userId,
        tenantId: this.sharedService.userData.tenantId,
        tenantKey: this.sharedService.userData.tenantKey,
        username: this.sharedService.userData.userName
      };
      this.httpService.doPost({ endpoint: APIs.resetPassword, payload }).subscribe((response) => {
        // TODO: Update success response check after correcting api response
        if (response.status === 0) {
          this.sharedService.trackActivity({
            type: Activity.userAccess,
            name: Activity.setEnrollPassword,
            des: {
              data: {
                userName: this.sharedService.userData.displayName,
                userId: this.sharedService.userData.userId
              },
              desConstant: Activity.setEnrollPasswordDes
            }
          });
          const resetSuccessMessage = this.oktaTokenStorage
            ? this.common.getTranslateData('SUCCESS_MESSAGES.OKTA_PASSWORD_RESET_SUCCESSFUL')
            : this.common.getTranslateData('SUCCESS_MESSAGES.PASSWORD_RESET_SUCCESSFUL');
          this.common.showMessage(resetSuccessMessage);
          if (this.oktaTokenStorage) {
            this.sharedService.logout();
          } else {
            this.router.navigate(['home']);
          }
          if (this.sharedService.loggedUserIsPatient()) {
            const config = {
              message: this.sharedService.getConfigValue(Config.newPatientWelcomeMessage),
              buttons: [
                {
                  text: this.common.getTranslateData('BUTTONS.OK'),
                  confirm: false,
                  class: 'cancel-btn'
                }
              ]
            };
            this.common.showAlert(config);
          }
        } else {
          const resetFailureMessage = this.oktaTokenStorage
            ? response.errorMessage
            : this.common.getTranslateData('ERROR_MESSAGES.FAILURE_PASSWORD_RESET');
          this.common.showMessage(resetFailureMessage);
        }
      });
    }
  }
  validateForm(): boolean {
    if (isBlank(this.resetPasswordForm.value.password)) {
      const alertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.FILL_ALL_THE_FIELDS');
      this.common.showMessage(alertMessage);
      return false;
    }
    if (!this.resetPasswordForm.controls.password.valid) {
      const alertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.PASSWORD_LENGTH_ERROR');
      this.common.showMessage(alertMessage);
      return false;
    }
    if (isBlank(this.resetPasswordForm.value.confirmPassword)) {
      const alertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.FILL_ALL_THE_FIELDS');
      this.common.showMessage(alertMessage);
      return false;
    }
    if (environment.oktaFlow && !isBlank(this.oktaTokenStorage) && !this.passwordValidator(this.resetPasswordForm.value.password)) {
      const alertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.PASSWORD_REQUIREMENTS');
      this.common.showMessage(alertMessage);
      return false;
    }
    if (this.resetPasswordForm.value.password !== this.resetPasswordForm.value.confirmPassword) {
      const alertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.PASSWORD_MISMATCH');
      this.common.showMessage(alertMessage);
      return false;
    }
    if (this.resetPasswordForm.invalid) {
      return false;
    }
    return true;
  }

  passwordValidator(value: string): boolean {
    const { username, firstName, secondName: lastName } = this.sharedService.userData;
    const lowerValue = value.toLowerCase();

    const containsUsername = lowerValue.includes(username.toLowerCase());
    const containsFirstName = lowerValue.includes(firstName.toLowerCase());
    const containsLastName = lowerValue.includes(lastName.toLowerCase());

    const isValidPassword = Constants.validationPattern.oktaPasswordPattern.test(value);

    const isValid =
    value.length >= 9 &&
    isValidPassword &&
    !containsUsername &&
    !containsFirstName &&
    !containsLastName;
    return isValid;
  }

  showHidePassword(): void {
    this.isShowPassword = !this.isShowPassword;
  }
}
