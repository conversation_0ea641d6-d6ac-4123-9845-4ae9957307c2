import { TranslateModule } from '@ngx-translate/core';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { AuthGuard } from 'src/app/services/auth-guard/auth.guard';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ReactiveFormsModule } from '@angular/forms';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavController } from '@ionic/angular';
import { ForgotPasswordPage } from 'src/app/pages/auth/forgot-password/forgot-password.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';

describe('ForgotPasswordPage', () => {
  let component: ForgotPasswordPage;
  let fixture: ComponentFixture<ForgotPasswordPage>;
  let authService: AuthService;
  let navController: NavController;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ForgotPasswordPage],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule,
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [
        AuthGuard,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        NavController,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    authService = TestBed.inject(AuthService);
    navController = TestBed.inject(NavController);
    spyOn(navController, 'navigateBack').and.stub();
    fixture = TestBed.createComponent(ForgotPasswordPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should execute forgotPassword: username required', () => {
    component.forgotPasswordForm.patchValue({});
    component.forgotPassword();
    expect(component.forgotPassword).toBeTruthy();
  });
  it('should execute forgotPassword: invalid email', () => {
    component.forgotPasswordForm.patchValue({ emailId: 'te657m' });
    component.forgotPassword();
    expect(component.forgotPassword).toBeTruthy();
  });
  it('should execute forgotPassword: valid email: api returns 40', () => {
    component.forgotPasswordForm.patchValue({ emailId: '<EMAIL>' });
    spyOn(authService, 'forgotPassword').and.returnValue(of({ status: 40 }));
    component.forgotPassword();
    expect(component.forgotPassword).toBeTruthy();
  });
  it('should execute forgotPassword: valid email: success', () => {
    component.forgotPasswordForm.patchValue({ emailId: '<EMAIL>' });
    spyOn(authService, 'forgotPassword').and.returnValue(of({ status: 0 }));
    component.forgotPassword();
    expect(component.forgotPassword).toBeTruthy();
  });
  it('should execute goBackLogin', () => {
    component.goBackLogin();
    expect(component.goBackLogin).toBeTruthy();
  });
});
