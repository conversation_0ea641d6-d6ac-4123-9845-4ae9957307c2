import { NavController } from '@ionic/angular';
import { Component, OnInit } from '@angular/core';
import { UntypedFormGroup, Validators, UntypedFormBuilder } from '@angular/forms';
import { AuthService } from '../../../services/auth-service/auth.service';
import { CommonService } from '../../../services/common-service/common.service';
import { isBlank } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';
import { Activity } from '../../../constants/activity';
import { SharedService } from '../../../services/shared-service/shared.service';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.page.html',
  styleUrls: ['./forgot-password.page.scss']
})
export class ForgotPasswordPage implements OnInit {
  forgotPasswordForm: UntypedFormGroup;
  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly common: CommonService,
    private readonly sharedService: SharedService,
    private readonly navController: NavController
  ) {
    this.forgotPasswordForm = this.formBuilder.group({
      emailId: [null, [Validators.required, Validators.pattern(Constants.validationPattern.emailPattern)]]
    });
  }

  ngOnInit(): void {
    this.sharedService.trackActivity({ type: Activity.pageAccess });
  }

  forgotPassword(): void {
    const userEmail = this.forgotPasswordForm.value;
    if (this.validateForm()) {
      this.authService.forgotPassword(userEmail).subscribe((result) => {
        if (result.status === 40) {
          const data = { userEmail: userEmail.emailId };
          this.sharedService.trackActivity({
            type: Activity.userAccess,
            name: Activity.forgotPasswordException,
            des: { data, desConstant: Activity.forgotPasswordExceptionDes }
          });
          const failureAlertMesaage = this.common.getTranslateData('ERROR_MESSAGES.FAILURE_ALERT_MESSAGE');
          this.common.showMessage(failureAlertMesaage);
        } else {
          const data = { userEmail: userEmail.emailId };
          this.sharedService.trackActivity({
            type: Activity.userAccess,
            name: Activity.forgotPassword,
            des: { data, desConstant: Activity.forgotPasswordDes }
          });
          const successAlertMessage = this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESS_ALERT_MESSAGE');
          this.common.showMessage(successAlertMessage);
          this.common.redirectToPage('/login');
        }
      });
    }
  }

  validateForm(): boolean {
    if (isBlank(this.forgotPasswordForm.value.emailId)) {
      const emailAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.USERNAME_REQUIRED');
      this.common.showMessage(emailAlertMessage);
      return false;
    }

    if (!this.forgotPasswordForm.controls.emailId.valid) {
      const emailAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.USERNAME_INCORRECT');
      this.common.showMessage(emailAlertMessage);
      return false;
    }
    return true;
  }

  goBackLogin() {
    this.navController.back();
  }
}
