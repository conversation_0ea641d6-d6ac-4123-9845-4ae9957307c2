<ion-header class="common-header" mode="md">
    <ion-toolbar mode="md">
        <ion-title class="header-ion-title">
            <h1 class="header-title">{{ 'LABELS.FORGOT_PASSWORD' | translate }}</h1>
        </ion-title>
    </ion-toolbar>
</ion-header>

<ion-content class="forgot-pass-page">

    <div class="common-box-container">
        <form name="forgot-password-form" id="forgot-password-form" [formGroup]="forgotPasswordForm"
            class="forgot-pass-form">
            <!-- TODO! CHP-3595 -->
            <ion-item class="ion-margin-bottom ion-no-padding" mode="md">
                <ion-label class="label-with-icon">
                    <ion-icon slot="start" src="assets/icon/material-svg/account.svg" color="fountain-blue" class="set-round-icon ion-margin-end"></ion-icon>
                </ion-label>
                <ion-input type="email" placeholder="{{ 'LABELS.USERNAME' | translate }}" formControlName="emailId"
                    name="emailId"></ion-input>
            </ion-item>
            <!-- TODO! CHP-3595 -->
            <ion-button (click)="forgotPassword()" expand="block" class="ion-text-capitalize forgot-password-submit">
                {{ 'BUTTONS.RESET_PASS' | translate }}
            </ion-button>
        </form>
    </div>

</ion-content>

<ion-footer class="common-footer">
    <ion-toolbar>
        <ion-buttons slot="start">
            <ion-icon tappable class="forgot-back-btn" src="assets/icon/material-svg/arrow-left-bold.svg"
                (click)="goBackLogin()">
            </ion-icon>
        </ion-buttons>
    </ion-toolbar>
</ion-footer>