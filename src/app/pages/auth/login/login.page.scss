.login-page {
    .forgot-pass-link {
        width: 100%;
        float: left;
        text-align: center;
        color: #555555;
        line-height: 20px;
        font-size: 16px;
        margin: 10px 0 3px;
        position: relative;

        a {
            font-size: 20px;
            color: #359cb1;
            text-decoration: none;
        }
    }

    .account-message {
        text-align: center;
        font-size: 18px;
        padding-top: 20px;
        margin-top: 40px;
        margin-left: 4%;
        margin-right: 4%;
        color: #58595b;

        .account-message-text {
            margin: 0 auto;
            width: 280px;
        }
    }

    .logo-container {
        text-align: center;
        margin: 0 auto;
        height: 180px;

        .login-top-logo {
            margin: auto;
            position: absolute;
            width: 90%;
            height: 180px;
            background-position-x: center;
            background-repeat: no-repeat;
        }
    }
}

.setLogo {
    width: 180px;
    display: inline-block;
}

.set-round-icon {
    border: 1px solid var(--ion-color-fountain-blue);
    border-radius: 50%;
    padding: 5px;
}
.signin {
    --background: var(--ion-color-sign-in);
    &.ion-activated {
        --background-activated: var(--ion-color-sign-in);
    }
}
.forgot-password,
.create-account, 
.load-otp {
    color: var(--ion-color-link);
    cursor: pointer;
}
.contact-us {
    color: var(--ion-color-text-light);
    a {
        color: var(--ion-color-link-dark);
    }
}

.otp-label a {
    color: var(--ion-color-link) !important;
  }
.otp-label,
.check-for-login-type {
    font-size: 13px;

    a {
        text-decoration: none;
    }
}

.auto-height {
    height: auto;
}

.forgot-back-btn {
    color: var(--ion-color-skin-secondary-bright);
    font-size: 35px;
    margin-bottom: 8px;
}