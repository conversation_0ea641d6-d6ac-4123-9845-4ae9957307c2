import { <PERSON> } from 'apollo-angular';
import { TestConstants } from 'src/app/constants/test-constants';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { AuthGuard } from 'src/app/services/auth-guard/auth.guard';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ComponentFixture, TestBed, fakeAsync, tick, discardPeriodicTasks } from '@angular/core/testing';
import { IonicModule, ModalController, NavController } from '@ionic/angular';
import { LoginPage } from './login.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { BehaviorSubject, Subscription, of, throwError } from 'rxjs';
import { HttpService } from 'src/app/services/http-service/http.service';
import { KeychainTouchId } from '@ionic-native/keychain-touch-id/ngx';
import { APIs } from 'src/app/constants/apis';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { VideoCallService } from 'src/app/services/video-call/video-call.service';
import { OKTA_AUTH, OKTA_CONFIG, OktaAuthModule } from '@okta/okta-angular';
import { environment } from 'src/environments/environment';
import { OktaService } from 'src/app/services/okta/okta.service';
import { Constants } from 'src/app/constants/constants';
import { Organizations } from 'src/assets/organizations/organizations';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Activity } from 'src/app/constants/activity';

describe('LoginPage', () => {
  let component: LoginPage;
  let fixture: ComponentFixture<LoginPage>;
  let sharedService: SharedService;
  let persistentService: PersistentService;
  let common: CommonService;
  let httpService: HttpService;
  let authService: AuthService;
  let keychainTouchId: KeychainTouchId;
  let modalController: ModalController;
  let navController: NavController;
  const { modalSpy } = TestConstants;
  let route: ActivatedRoute;
  let mockSubscription: Subscription;
  let router: Router;
  let videoCallService: VideoCallService;
  let inAppBrowser: InAppBrowser;
  let oktaService: OktaService;

  beforeEach(() => {
    mockSubscription = new Subscription();
    const oktaAuthMock = {
      getOriginalUri: () => '',
      setOriginalUri: () => { },
      handleLoginRedirect: () => { },
      getAccessToken: () => new BehaviorSubject<string>(''),
      getIdToken: () => new BehaviorSubject<string>('')
    };
    TestBed.configureTestingModule({
      declarations: [LoginPage],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule,
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        OktaAuthModule
      ],
      providers: [
        AuthGuard,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        SharedService,
        ModalController,
        Apollo,
        KeychainTouchId,
        NativeStorage,
        SQLite,
        PersistentService,
        OktaService,
        InAppBrowser,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        { provide: OKTA_AUTH, useValue: oktaAuthMock },
        {
          provide: OKTA_CONFIG,
          useValue: {
            issuer: environment.oktaIssuer,
            redirectUri: environment.oktaRedirectUri,
            clientId: environment.oktaClientId,
            scopes: ['openid', 'profile', 'email']
          }
        }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    authService = TestBed.inject(AuthService);
    keychainTouchId = TestBed.inject(KeychainTouchId);
    common = TestBed.inject(CommonService);
    spyOn(common, 'showMessage').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    sharedService = TestBed.inject(SharedService);
    persistentService = TestBed.inject(PersistentService) as jasmine.SpyObj<PersistentService>;
    router = TestBed.inject(Router);
    route = TestBed.inject(ActivatedRoute);
    navController = TestBed.inject(NavController);
    videoCallService = TestBed.inject(VideoCallService);
    inAppBrowser = TestBed.inject(InAppBrowser);
    oktaService = TestBed.inject(OktaService);
    fixture = TestBed.createComponent(LoginPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  // used to hide dom element from screen
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
    mockSubscription.unsubscribe();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('execute login', () => {
    component.loginForm.setValue({ username: '<EMAIL>', password: '111111111' });
    component.login();
    expect(component.login).toBeTruthy();
  });

  it('execute showHidePassword', () => {
    component.showHidePassword();
    expect(component.showHidePassword).toBeTruthy();
  });

  it('execute checkPlatformToShowSSOButton', () => {
    component.checkPlatformToShowSSOButton();
    expect(component.checkPlatformToShowSSOButton).toBeTruthy();
  });

  it('execute checkPassword : No password', () => {
    component.checkPassword();
    expect(component.checkPassword).toBeTruthy();
  });

  it('should navigate to forgot-username page', () => {
    component.goToForgotUsernamePage();
    expect(component.goToForgotUsernamePage).toBeTruthy();
  });

  it('should navigate to forgot-password page', () => {
    component.goToForgotPasswordPage();
    expect(component.goToForgotPasswordPage).toBeTruthy();
  });

  it('should navigate to "home" and reset login form if setRouterLink is blank', () => {
    sharedService.setRouterLink = '';
    component.routeAfterLogin();
    expect(component.routeAfterLogin).toBeTruthy();
  });

  it('should reset setRouterLink in the shared service', () => {
    sharedService.setRouterLink = 'some-link';
    component.routeAfterLogin();
    expect(component.routeAfterLogin).toBeTruthy();
  });

  it('execute  to consoloWorkflow', () => {
    component.consoloWorkflow([]);
    expect(component.consoloWorkflow).toBeTruthy();
  });

  it('execute checkPassword', () => {
    component.loginForm.value.password = 33333333;
    component.checkPassword();
    expect(component.checkPassword).toBeTruthy();
  });

  it('execute checkEmail', () => {
    component.loginForm.value.username = '';
    component.checkEmail();
    expect(component.checkEmail).toBeTruthy();
  });

  it('execute checkEmail : invalid username', () => {
    component.loginForm.value.username = '<EMAIL>';
    component.loginForm.controls.username.invalid;
    component.checkEmail();
    expect(component.checkEmail).toBeTruthy();
  });

  it('execute getEmailDomain', () => {
    component.loginForm.value.username = '<EMAIL>';
    component.getEmailDomain();
    expect(component.getEmailDomain).toBeTruthy();
  });

  it('getAuthAppURL  function should be defined', () => {
    component.oktaLoginFlow = false;
    component.isEmailCheckingDone = true;
    component.isMobilePlatform = false;
    component.getAuthAppURL();
    expect(component.getAuthAppURL).toBeTruthy();
  });

  it('execute getAuthAppURL : if checkEmail is true', () => {
    component.oktaLoginFlow = false;
    component.isEmailCheckingDone = false;
    component.isMobilePlatform = true;
    component.loginForm.setValue({ username: '<EMAIL>', password: '111111111' });
    component.getAuthAppURL();
    expect(component.getAuthAppURL).toBeTruthy();
  });

  it('execute idpConfigFetch', () => {
    const response = {
      status: 1,
      data: [{ name: 'test' }]
    };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.loginForm.value.username = '<EMAIL>';
    component.idpConfigFetch('testtttt');
    expect(component.idpConfigFetch).toBeTruthy();
  });

  it('execute ssoLogin : status is 0', () => {
    const response = {
      status: 0,
      data: [{ name: 'test' }]
    };
    const data = { url: 'state testt' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.ssoLogin(data);
    expect(component.ssoLogin).toBeTruthy();
  });

  it('execute ssoLogin : status is 1', () => {
    const response = {
      status: 1,
      data: [{ name: 'test' }]
    };
    const data = { url: 'state testt' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.ssoLogin(data);
    expect(component.ssoLogin).toBeTruthy();
  });

  it('execute ssoLogin : throw error', () => {
    const data = { url: 'state testt' };
    spyOn(httpService, 'doPost').and.returnValue(throwError(''));
    component.ssoLogin(data);
    expect(component.ssoLogin).toBeTruthy();
  });

  it('execute loadPreAPICalls', () => {
    const response = {
      status: 1,
      data: [{ name: 'test' }]
    };
    spyOn(sharedService, 'fetchAllMessages').and.returnValue(of(response as any));
    component.loadPreAPICalls();
    expect(component.loadPreAPICalls).toBeTruthy();
  });

  it('execute resetPassword', () => {
    component.oktaLoginFlow = false;
    component.resetPassword();
    expect(component.resetPassword).toBeTruthy();
  });

  it('execute showSignUPModal', () => {
    component.showSignUPModal();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.showSignUPModal).toBeTruthy();
  });

  it('execute fingerprintLogin', fakeAsync(() => {
    Object.defineProperty(component.platform, 'is', {
      value: () => {
        return true;
      }
    });
    spyOn(keychainTouchId, 'isAvailable').and.resolveTo('YES' as any);
    spyOn(keychainTouchId, 'has').and.resolveTo('OK' as any);
    spyOn(keychainTouchId, 'verify').and.resolveTo('password');
    component.fingerprintLogin('<EMAIL>');
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.fingerprintLogin).toBeTruthy();
  }));

  it('execute fingerprintLogin for okta with touchIdOktaAuth', fakeAsync(() => {
    Object.defineProperty(component.platform, 'is', {
      value: () => {
        return true;
      }
    });
    spyOn(keychainTouchId, 'isAvailable').and.resolveTo('YES' as any);
    spyOn(keychainTouchId, 'has').and.resolveTo('OK' as any);
    spyOn(keychainTouchId, 'verify').and.resolveTo('password');
    spyOn(localStorage, 'getItem').and.returnValue('true');
    spyOn(component, 'getUsernameControl').and.returnValue(document.createElement('input'));
    spyOn(component, 'getPasswordControl').and.returnValue(document.createElement('input'));

    component.fingerprintLogin('<EMAIL>');
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.fingerprintLogin).toBeTruthy();
  }));

  it('should handle offline login when no internet is available', fakeAsync(() => {
    Object.defineProperty(component.platform, 'is', {
      value: () => {
        return true;
      }
    });
    spyOn(keychainTouchId, 'isAvailable').and.resolveTo('YES' as any);
    spyOn(keychainTouchId, 'has').and.resolveTo('OK' as any);
    spyOn(keychainTouchId, 'verify').and.resolveTo('password');
    spyOnProperty(Navigator.prototype, 'onLine').and.returnValue(false);
    const userData = JSON.stringify({ privileges: '' });
    persistentService.getOfflineUserData = jasmine.createSpy().and.resolveTo({ userData });
    spyOn(sharedService, 'isOfflineFormsEnabled').and.returnValue(true);
    spyOn(navController, 'navigateForward').and.stub();
    component.fingerprintLogin('<EMAIL>');
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(keychainTouchId.isAvailable).toHaveBeenCalled();
    expect(component.fingerprintLogin).toBeTruthy();
  }));

  it('should update otp and set isOtpValid to true when otp length is 6', () => {
    const mockValue = ['1', '2', '3', '4', '5', '6'];
    component.isOtpValid = false;

    component.handleOtpChange(mockValue);

    expect(component.otp).toEqual('123456');
    expect(component.isOtpValid).toBe(true);
  });

  it('should update otp without changing isOtpValid when otp length is not 6', () => {
    const mockValue = ['1', '2', '3', '4', '5'];
    component.isOtpValid = true;

    component.handleOtpChange(mockValue);

    expect(component.otp).toEqual('12345');
    expect(component.isOtpValid).toBe(false);
  });

  it('should update otp value', () => {
    const mockValue = '123456';

    component.handleFillEvent(mockValue);

    expect(component.otp).toEqual(mockValue);
  });

  it('should toggle loginWithOtp and call resetOtpInfo if loginWithOtp is false', () => {
    component.oktaLoginFlow = false;
    component.loginWithOtp = true;
    spyOn(component, 'resetOtpInfo');
    component.loginWithOtpOrPassword();
    expect(component.loginWithOtp).toBe(false);
    expect(component.resetOtpInfo).toHaveBeenCalled();
  });

  it('should toggle loginWithOtp and call sendOtp if loginWithOtp is true', () => {
    component.oktaLoginFlow = false;
    component.loginWithOtp = false;
    spyOn(component, 'sendOtp');
    component.loginWithOtpOrPassword();
    expect(component.loginWithOtp).toBe(true);
    expect(component.sendOtp).toHaveBeenCalled();
  });

  it('should send OTP and show expiry timer when sessionId is empty', () => {
    const mockResponse = { sessionId: 'session-id', message: 'OTP sent successfully', otpExpiryTime: 60 };
    component.otpInfo.sessionId = '';
    spyOn(httpService, 'doPost').and.returnValue(of(mockResponse));
    spyOn(component, 'showExpiryTimer');

    component.sendOtp();

    expect(httpService.doPost).toHaveBeenCalledWith({
      endpoint: APIs.sendOtpEndpoint,
      payload: { username: component.username.value }
    });
    expect(component.otpLoader).toBe(false);
    expect(component.showExpiryTimer).toHaveBeenCalledWith(mockResponse);
  });

  it('should resend OTP and show expiry timer when sessionId is not empty', () => {
    const mockResponse = { sessionId: 'session-id', message: 'OTP resent successfully', otpExpiryTime: 60 };
    component.otpInfo.sessionId = 'existing-session-id';
    spyOn(httpService, 'doPut').and.returnValue(of(mockResponse));
    spyOn(component, 'showExpiryTimer');

    component.sendOtp();

    expect(httpService.doPut).toHaveBeenCalledWith({
      endpoint: APIs.reSendOtpEndpoint,
      payload: {
        username: component.username.value,
        sessionId: component.otpInfo.sessionId
      }
    });
    expect(component.otpLoader).toBe(false);
    expect(component.showExpiryTimer).toHaveBeenCalledWith(mockResponse);
  });

  it('should handle error when sending OTP', () => {
    const mockError = 'Failed to send OTP';
    component.otpInfo.sessionId = '';
    spyOn(httpService, 'doPost').and.returnValue(throwError(mockError));

    component.sendOtp();

    expect(component.loginWithOtp).toBe(true);
    expect(component.otpLoader).toBe(false);
  });

  it('should handle error when resending OTP', () => {
    const mockError = 'Failed to resend OTP';
    component.otpInfo.sessionId = 'existing-session-id';
    spyOn(httpService, 'doPut').and.returnValue(throwError(mockError));

    component.sendOtp();

    expect(component.otpLoader).toBe(false);
  });

  it('should update otpInfo and start timer', fakeAsync(() => {
    const res = {
      sessionId: 'your_session_id',
      message: 'Your OTP is valid.',
      otpExpiryTime: 120 // 2 minutes
    };

    spyOn(window, 'clearInterval');

    component.showExpiryTimer(res);

    expect(component.otpInfo.loginWithOtp).toEqual(component.loginWithOtp);
    expect(component.otpInfo.resendOtp).toBeFalse();
    expect(component.otpInfo.otpExpiryInMins).toBe('');
    expect(component.otpInfo.sessionId).toEqual(res.sessionId);
    expect(component.otpInfo.otpSuccessMsg).toEqual(res.message);
    expect(component.otpInfo.otpErrorMsg).toBe('');

    tick(1000); // Wait for 1 second
    expect(component.otpInfo.otpExpiryInMins).toBe('01:59'); // Check if the time is updated correctly

    tick(120000); // Wait for 2 minutes (otpExpiryTime)
    discardPeriodicTasks();
    expect(component.otpInfo.resendOtp).toBeTrue();
    expect(component.otpInfo.otpSuccessMsg).toBe('');
    expect(window.clearInterval).toHaveBeenCalled();
  }));

  it('should return false and show alert message when OTP is valid', () => {
    component.isOtpValid = false;

    const result = component.checkOTPisValid();

    expect(result).toBeFalse();
  });

  it('should return true when OTP is invalid', () => {
    component.isOtpValid = true;

    const result = component.checkOTPisValid();

    expect(result).toBeTrue();
  });

  it('should reset otpInfo, clear expiryInterval, and call resetOtpFields', () => {
    const defaultOtpInfo = {
      loginWithOtp: false,
      resendOtp: false,
      otpExpiryInMins: '',
      sessionId: '',
      otpSuccessMsg: '',
      otpErrorMsg: ''
    };

    component.otpInfo = {
      loginWithOtp: true,
      resendOtp: true,
      otpExpiryInMins: '10',
      sessionId: 'session-id',
      otpSuccessMsg: 'OTP success',
      otpErrorMsg: 'OTP error'
    };
    component.expiryInterval = setInterval(() => {
      return;
    }, 1000); // Create a dummy interval

    spyOn(window, 'clearInterval');
    spyOn(component, 'resetOtpFields');

    component.resetOtpInfo();

    expect(component.otpInfo).toEqual(defaultOtpInfo);
    expect(window.clearInterval).toHaveBeenCalledWith(component.expiryInterval);
    expect(component.resetOtpFields).toHaveBeenCalled();
  });
  describe('isOtpAnd2faEnabledForTenant', () => {
    it('execute isOtpAnd2faEnabledForTenant without touch id', () => {
      const response = {
        otpEnabled: false,
        twoFaEnabled: true,
        otpResponse: true
      };
      component.isTouchIdLogin = false;
      spyOn(httpService, 'doGet').and.returnValue(of(response));
      component.isOtpAnd2faEnabledForTenant();
      expect(component.isOtpAnd2faEnabledForTenant).toBeDefined();
    });
    it('execute isOtpAnd2faEnabledForTenant without touch id and oktaEnabled', () => {
      const response = {
        otpEnabled: false,
        twoFaEnabled: true,
        otpResponse: true
      };
      component.isTouchIdLogin = false;
      component.oktaLoginFlow = true;
      spyOn(component, 'hideUsername').and.returnValue();
      spyOn(httpService, 'doGet').and.returnValue(of(response));
      component.isOtpAnd2faEnabledForTenant();
      expect(component.isOtpAnd2faEnabledForTenant).toBeDefined();
    });
    it('execute isOtpAnd2faEnabledForTenant with touch id and oktaEnabled', () => {
      const response = {
        otpEnabled: false,
        twoFaEnabled: false,
        otpResponse: true
      };
      component.isTouchIdLogin = true;
      spyOn(localStorage, 'getItem').and.returnValue('true');
      spyOn(httpService, 'doGet').and.returnValue(of(response));
      component.isOtpAnd2faEnabledForTenant();
      expect(component.isOtpAnd2faEnabledForTenant).toBeDefined();
    });
    it('execute isOtpAnd2faEnabledForTenant with touch id', () => {
      const response = {
        otpEnabled: false,
        twoFaEnabled: false,
        otpResponse: true
      };
      component.isTouchIdLogin = true;
      spyOn(httpService, 'doGet').and.returnValue(of(response));
      component.isOtpAnd2faEnabledForTenant();
      expect(component.isOtpAnd2faEnabledForTenant).toBeDefined();
    });
    it('execute isOtpAnd2faEnabledForTenant throw error', () => {
      spyOn(httpService, 'doGet').and.returnValue(throwError(''));
      component.isOtpAnd2faEnabledForTenant();
      expect(component.isOtpAnd2faEnabledForTenant).toBeDefined();
    });
  });
  it('execute checkPasswordAndOTP', () => {
    component.checkPasswordAndOTP();
    expect(component.checkPasswordAndOTP).toBeDefined();
  });
  it('should unsubscribe ssoLoginSubscribe', () => {
    spyOn(mockSubscription, 'unsubscribe');
    component.ssoLoginSubscribe = mockSubscription;
    component.ionViewDidLeave();
    expect(component.isEmailCheckingDone).toBe(false);
    expect(component.isShowPassword).toBe(false);
    expect(component.isIdpRedirectionProcess).toBe(false);
    expect(mockSubscription.unsubscribe).toHaveBeenCalled();
  });
  it('should call login if email checking is done', () => {
    component.isEmailCheckingDone = true;
    spyOn(component, 'login');
    component.getAuthAppURL();
    expect(component.login).toHaveBeenCalled();
  });
  describe('getAuthAppURL', () => {
    it('should call checkEmail and authService.doCheckAuth if email checking is not done', () => {
      component.isEmailCheckingDone = false;
      spyOn(component, 'checkEmail').and.returnValue(true);
      spyOn(component, 'getEmailDomain').and.returnValue('example.com');
      const mockResponse = { app_auth_link: 'example-link' };
      spyOn(authService, 'doCheckAuth').and.returnValue(of(mockResponse));
      component.getAuthAppURL();

      expect(component.checkEmail).toHaveBeenCalled();
      expect(component.getEmailDomain).toHaveBeenCalled();
    });

    it('should log error if authService.doCheckAuth throws an error', () => {
      component.isEmailCheckingDone = false;
      spyOn(component, 'checkEmail').and.returnValue(true);
      spyOn(component, 'getEmailDomain').and.returnValue('example.com');
      spyOn(authService, 'doCheckAuth').and.returnValue(throwError(''));
      component.getAuthAppURL();

      expect(component.checkEmail).toHaveBeenCalled();
      expect(component.getEmailDomain).toHaveBeenCalled();
    });

    it('should call isOtpAnd2faEnabledForTenant if app_auth_link does not exist', () => {
      component.isEmailCheckingDone = false;
      spyOn(component, 'checkEmail').and.returnValue(true);
      spyOn(component, 'getEmailDomain').and.returnValue('example.com');
      const mockResponse = {};
      spyOn(authService, 'doCheckAuth').and.returnValue(of(mockResponse));
      spyOn(component, 'isOtpAnd2faEnabledForTenant').and.stub();
      component.getAuthAppURL();

      expect(component.checkEmail).toHaveBeenCalled();
      expect(component.getEmailDomain).toHaveBeenCalled();
      expect(component.isOtpAnd2faEnabledForTenant).toHaveBeenCalled();
    });

    it('should call handleAuthCheck oktaEnabled and is oktaLoginFlow', () => {
      component.isEmailCheckingDone = false;
      spyOn(component, 'checkEmail').and.returnValue(true);
      spyOn(component, 'getEmailDomain').and.returnValue('example.com');
      component.oktaLoginFlow = true;
      spyOn(component, 'getUsernameContainerControl').and.returnValue(document.createElement('input'));
      spyOn(component, 'getPasswordContainerControl').and.returnValue(document.createElement('input'));
      spyOn(component, 'getUsernameControl').and.returnValue(document.createElement('input'));
      spyOn(component, 'getPasswordControl').and.returnValue(document.createElement('input'));
      const mockResponse = { app_auth_link: '', oktaEnabled: true, ssoId: '434235' };
      spyOn(authService, 'doCheckAuth').and.returnValue(of(mockResponse));
      component.getAuthAppURL();
      expect(component.checkEmail).toHaveBeenCalled();
    });

    it('should call handleAuthCheck oktaEnabled and no ssoId', () => {
      component.isEmailCheckingDone = false;
      spyOn(component, 'checkEmail').and.returnValue(true);
      spyOn(component, 'getEmailDomain').and.returnValue('example.com');
      const mockResponse = { app_auth_link: '', oktaEnabled: true };
      spyOn(authService, 'doCheckAuth').and.returnValue(of(mockResponse));
      component.getAuthAppURL();
      expect(component.checkEmail).toHaveBeenCalled();
      expect(component.getAuthAppURL).toBeTruthy();
    });

    it('should call checkPassword isEmailCheckingDone and oktaEnabled', () => {
      component.isEmailCheckingDone = true;
      component.oktaLoginFlow = true;
      component.oktaLogin = true;
      spyOn(component, 'getUsernameControl').and.returnValue(document.createElement('input'));
      spyOn(component, 'getPasswordControl').and.returnValue(document.createElement('input'));
      spyOn(component, 'checkEmail').and.returnValue(true);
      spyOn(component, 'getEmailDomain').and.returnValue('example.com');
      const mockResponse = { app_auth_link: '', oktaEnabled: true };
      spyOn(authService, 'doCheckAuth').and.returnValue(of(mockResponse));
      spyOn(component, 'checkPassword').and.returnValue(true);
      component.getAuthAppURL();
      expect(component.checkPassword).toHaveBeenCalled();
      expect(component.getAuthAppURL).toBeTruthy();
    });
  });
  describe('initVideoChat', () => {
    it('execute initVideoChat', () => {
      sharedService.userData = TestConstants.userData;
      component.initVideoChat();
      expect(component.initVideoChat).toBeDefined();
    });
    it('should call isOtpAnd2faEnabledForTenant if app_auth_link does not exist', () => {
      sharedService.userData = TestConstants.userData;
      const mockResponse = {
        token: '121212'
      };
      spyOn(sharedService, 'generateVidyoTocken').and.returnValue(of(mockResponse));
      spyOn(videoCallService, 'callAllFunction').and.stub();
      component.initVideoChat();
      expect(videoCallService.callAllFunction).toHaveBeenCalled();
    });
  });

  it('should call customizeOktaWidget with hidePassword', () => {
    component.oktaLoginFlow = true;
    spyOn(component, 'setupInputField');
    const hidePasswordSpy = spyOn(component, 'hidePassword');
    component.customizeOktaWidget();
    expect(hidePasswordSpy).toHaveBeenCalled();
  });

  it('should click the okta sign-in button if it exists and set isLoading to true', () => {
    const mockButton = document.createElement('button');
    mockButton.id = 'okta-signin-submit';
    document.body.appendChild(mockButton);
    spyOn(mockButton, 'click');
    component.clickOktaSignin();
    expect(mockButton.click).toHaveBeenCalled();
    expect(sharedService.isLoading).toBeTrue();
    document.body.removeChild(mockButton);
  });

  it('should add an ion-icon element for recovery-username input field', () => {
    const container = document.createElement('div');
    container.innerHTML = `
      <input id="inputField" type="text">
    `;
    const iconSrc = 'assets/icon.svg';
    const inputId = 'account-recovery-username';
    component.setupInputField(inputId, iconSrc, container);

    const iconDiv = container.querySelector('.ion-icon-top');
    const ionIcon = container.querySelector('ion-icon');

    expect(iconDiv).toBeTruthy();
    expect(ionIcon).toBeTruthy();
    expect(ionIcon.getAttribute('src')).toBe(iconSrc);
    expect(ionIcon.getAttribute('color')).toBe('fountain-blue');
    expect(ionIcon.getAttribute('class')).toBe('ion-margin-end okta-round');
    expect(container.children[0]).toBe(iconDiv);
    expect(container.children[1]).toBe(container.querySelector('input'));
  });

  it('should add an ion-icon element for okta-signin-username input field', () => {
    const container = document.createElement('div');
    container.innerHTML = `
      <input id="inputField" type="text">
    `;
    const iconSrc = 'assets/icon.svg';
    const inputId = 'okta-signin-username';
    component.setupInputField(inputId, iconSrc, container);

    const iconDiv = container.querySelector('.ion-icon-top');
    const ionIcon = container.querySelector('ion-icon');

    expect(iconDiv).toBeTruthy();
    expect(ionIcon).toBeTruthy();
    expect(ionIcon.getAttribute('src')).toBe(iconSrc);
    expect(ionIcon.getAttribute('color')).toBe('fountain-blue');
    expect(ionIcon.getAttribute('class')).toBe('ion-margin-end okta-round');
    expect(container.children[0]).toBe(iconDiv);
    expect(container.children[1]).toBe(container.querySelector('input'));
  });

  it('should add an ion-icon element for  input field', () => {
    const container = document.createElement('div');
    container.innerHTML = `
      <input id="inputField" type="text">
    `;
    const iconSrc = 'assets/icon.svg';
    const inputId = 'inputField';
    component.setupInputField(inputId, iconSrc, container);

    const iconDiv = container.querySelector('.ion-icon-top');
    const ionIcon = container.querySelector('ion-icon');

    expect(iconDiv).toBeTruthy();
    expect(ionIcon).toBeTruthy();
    expect(ionIcon.getAttribute('src')).toBe(iconSrc);
    expect(ionIcon.getAttribute('color')).toBe('fountain-blue');
    expect(ionIcon.getAttribute('class')).toBe('ion-margin-end okta-round');
    expect(container.children[0]).toBe(iconDiv);
    expect(container.children[1]).toBe(container.querySelector('input'));
  });

  it('should trigger click on forgot password link if it exists', () => {
    const forgotPasswordLink = document.createElement('a');
    forgotPasswordLink.classList.add('js-forgot-password');
    spyOn(forgotPasswordLink, 'click');

    document.body.appendChild(forgotPasswordLink);
    component.customizeForgotPassword();
    expect(forgotPasswordLink.click).toHaveBeenCalled();
    document.body.removeChild(forgotPasswordLink);
  });

  it('should call setupInputField with correct arguments if username container exists', () => {
    const usernameContainer = document.createElement('div');
    usernameContainer.id = 'account-recovery-username';
    document.body.appendChild(usernameContainer);

    spyOn(component, 'setupInputField');
    component.customizeForgotPassword();

    expect(component.setupInputField).toHaveBeenCalledWith('account-recovery-username', 'assets/icon/material-svg/account.svg', usernameContainer);
    document.body.removeChild(usernameContainer);
  });

  it('should handle Okta button click and check email', () => {
    const resetEmailButton = document.createElement('a');
    resetEmailButton.setAttribute('data-se', 'email-button');
    document.body.appendChild(resetEmailButton);

    const forgotEmailControl = document.createElement('input');
    forgotEmailControl.value = '<EMAIL>';
    spyOn(component, 'getForgotEmailControl').and.returnValue(forgotEmailControl);
    spyOn(component, 'checkEmail').and.returnValue(true);
    spyOn(component, 'getEmailDomain').and.returnValue('example.com');

    const response = { oktaEnabled: true, ssoId: 'ssoId' };
    spyOn(authService, 'doCheckAuth').and.returnValue(of(response));

    component.handleOktaButtonClick();

    resetEmailButton.click();

    expect(component.loginForm.get('username').value).toBe('<EMAIL>');
    expect(authService.doCheckAuth).toHaveBeenCalledWith({ domain: 'example.com', username: '<EMAIL>' });

    document.body.removeChild(resetEmailButton);
  });

  it('should handle Okta button click and handle forgot password', () => {
    const resetEmailButton = document.createElement('a');
    resetEmailButton.setAttribute('data-se', 'email-button');
    document.body.appendChild(resetEmailButton);

    const forgotEmailControl = document.createElement('input');
    forgotEmailControl.value = '<EMAIL>';
    spyOn(component, 'getForgotEmailControl').and.returnValue(forgotEmailControl);
    spyOn(component, 'checkEmail').and.returnValue(true);
    spyOn(component, 'getEmailDomain').and.returnValue('example.com');

    const response = { oktaEnabled: false, ssoId: null };
    spyOn(authService, 'doCheckAuth').and.returnValue(of(response));
    spyOn(authService, 'forgotPassword').and.returnValue(of({ status: 200 }));

    component.handleOktaButtonClick();

    resetEmailButton.click();

    expect(component.loginForm.get('username').value).toBe('<EMAIL>');
    expect(authService.doCheckAuth).toHaveBeenCalledWith({ domain: 'example.com', username: '<EMAIL>' });
    expect(authService.forgotPassword).toHaveBeenCalledWith({ emailId: '<EMAIL>' });

    document.body.removeChild(resetEmailButton);
  });

  it('should handle Okta button click and handle forgot password exception', () => {
    const resetEmailButton = document.createElement('a');
    resetEmailButton.setAttribute('data-se', 'email-button');
    document.body.appendChild(resetEmailButton);

    const forgotEmailControl = document.createElement('input');
    forgotEmailControl.value = '<EMAIL>';
    spyOn(component, 'getForgotEmailControl').and.returnValue(forgotEmailControl);
    spyOn(component, 'checkEmail').and.returnValue(true);
    spyOn(component, 'getEmailDomain').and.returnValue('example.com');

    const response = { oktaEnabled: false, ssoId: null };
    spyOn(authService, 'doCheckAuth').and.returnValue(of(response));
    spyOn(authService, 'forgotPassword').and.returnValue(of({ status: 40 }));

    component.handleOktaButtonClick();

    resetEmailButton.click();

    expect(component.loginForm.get('username').value).toBe('<EMAIL>');
    expect(authService.doCheckAuth).toHaveBeenCalledWith({ domain: 'example.com', username: '<EMAIL>' });
    expect(authService.forgotPassword).toHaveBeenCalledWith({ emailId: '<EMAIL>' });

    document.body.removeChild(resetEmailButton);
  });

  it('should handle Okta button click and handle error', () => {
    const resetEmailButton = document.createElement('a');
    resetEmailButton.setAttribute('data-se', 'email-button');
    document.body.appendChild(resetEmailButton);

    const forgotEmailControl = document.createElement('input');
    forgotEmailControl.value = '<EMAIL>';
    spyOn(component, 'getForgotEmailControl').and.returnValue(forgotEmailControl);
    spyOn(component, 'checkEmail').and.returnValue(true);
    spyOn(component, 'getEmailDomain').and.returnValue('example.com');

    spyOn(authService, 'doCheckAuth').and.returnValue(of(throwError('error')));

    component.handleOktaButtonClick();

    resetEmailButton.click();

    expect(component.loginForm.get('username').value).toBe('<EMAIL>');
    expect(authService.doCheckAuth).toHaveBeenCalledWith({ domain: 'example.com', username: '<EMAIL>' });

    document.body.removeChild(resetEmailButton);
  });

  it('should call handleOktaButtonClick after 500ms', fakeAsync(() => {
    spyOn(component, 'handleOktaButtonClick');
    component.customizeForgotPassword();
    tick(500);
    expect(component.handleOktaButtonClick).toHaveBeenCalled();
  }));

  it('should call customizeForgotPassword if oktaLoginFlow ', () => {
    component.oktaLoginFlow = true;
    component.goToForgotPasswordPage();
    expect(component.goToForgotPasswordPage).toBeTruthy();
  });

  it('should trigger click event on the forgot password link if it exists', () => {
    const mockDispatchEvent = spyOn(HTMLAnchorElement.prototype, 'dispatchEvent');
    const fakeLink = document.createElement('a');
    fakeLink.classList.add('link', 'help', 'js-back');
    document.body.appendChild(fakeLink);
    component.goBackLogin();
    expect(mockDispatchEvent).toHaveBeenCalledWith(jasmine.any(MouseEvent));
    document.body.removeChild(fakeLink);
  });

  it('should not trigger click event if forgot password link does not exist', () => {
    const mockDispatchEvent = spyOn(HTMLAnchorElement.prototype, 'dispatchEvent');
    component.goBackLogin();
    expect(mockDispatchEvent).not.toHaveBeenCalled();
  });

  describe('technicalAndPharmacySupport', () => {
    it('should return true if appTheme is ameripharma', () => {
      component.appTheme = 'ameripharma';
      expect(component.technicalAndPharmacySupport).toBe(true);
    });

    it('should return false if appTheme is not ameripharma', () => {
      component.appTheme = 'some-other-theme';
      expect(component.technicalAndPharmacySupport).toBe(false);
    });
  });
  describe('hereToHelpTechnicalSupport', () => {
    it('should return true if appTheme is in hereToHelpTechnicalSupport', () => {
      component.appTheme = 'myanovo';
      expect(component.hereToHelpTechnicalSupport).toBeTrue();
    });

    it('should return false if appTheme is not in hereToHelpTechnicalSupport', () => {
      component.appTheme = 'someOtherTheme';
      expect(component.hereToHelpTechnicalSupport).toBeFalse();
    });
  });
  describe('showEmailUsAt', () => {
    it('should return true if appTheme is premierinfusion', () => {
      component.appTheme = 'premierinfusion';
      expect(component.showEmailUsAt).toBe(true);
    });

    it('should return false if appTheme is not premierinfusion', () => {
      component.appTheme = 'some-other-theme';
      expect(component.showEmailUsAt).toBe(false);
    });
  });
  it('execute ionViewWillEnter', () => {
    component.ionViewWillEnter();
    expect(component.ionViewWillEnter).toBeDefined();
  });

  it('should handle Okta button click and check email', () => {
    const resetEmailButton = document.createElement('a');
    resetEmailButton.setAttribute('data-se', 'email-button');
    document.body.appendChild(resetEmailButton);

    const forgotEmailControl = document.createElement('input');
    forgotEmailControl.value = '<EMAIL>';
    spyOn(component, 'getForgotEmailControl').and.returnValue(forgotEmailControl);
    spyOn(component, 'checkEmail').and.returnValue(true);
    spyOn(component, 'getEmailDomain').and.returnValue('example.com');

    const response = { oktaEnabled: true, ssoId: 'ssoId' };
    spyOn(authService, 'doCheckAuth').and.returnValue(of(response));

    component.handleOktaButtonClick();

    resetEmailButton.click();

    expect(component.loginForm.get('username').value).toBe('<EMAIL>');
    expect(authService.doCheckAuth).toHaveBeenCalledWith({ domain: 'example.com', username: '<EMAIL>' });

    document.body.removeChild(resetEmailButton);
  });

  it('should handle Okta button click and handle forgot password exception', () => {
    const resetEmailButton = document.createElement('a');
    resetEmailButton.setAttribute('data-se', 'email-button');
    document.body.appendChild(resetEmailButton);

    const forgotEmailControl = document.createElement('input');
    forgotEmailControl.value = '<EMAIL>';
    spyOn(component, 'getForgotEmailControl').and.returnValue(forgotEmailControl);
    spyOn(component, 'checkEmail').and.returnValue(true);
    spyOn(component, 'getEmailDomain').and.returnValue('example.com');

    const response = { oktaEnabled: false, ssoId: null };
    spyOn(authService, 'doCheckAuth').and.returnValue(of(response));
    spyOn(authService, 'forgotPassword').and.returnValue(of({ status: 40 }));

    component.handleOktaButtonClick();

    resetEmailButton.click();

    expect(component.loginForm.get('username').value).toBe('<EMAIL>');
    expect(authService.doCheckAuth).toHaveBeenCalledWith({ domain: 'example.com', username: '<EMAIL>' });
    expect(authService.forgotPassword).toHaveBeenCalledWith({ emailId: '<EMAIL>' });

    document.body.removeChild(resetEmailButton);
  });

  it('should dispatch click event on forgot password link', () => {
    const forgotPasswordLink = document.createElement('a');
    spyOn(document, 'querySelector').withArgs('.link.help.js-back').and.returnValue(forgotPasswordLink);
    const spyDispatch = spyOn(forgotPasswordLink, 'dispatchEvent');
    component.goBackLogin();
    expect(spyDispatch).toHaveBeenCalledWith(jasmine.any(MouseEvent));
    expect(component.isForgotEmailCheckingDone).toBeFalse();
  });

  it('should dispatch click event on MFA back button when isMfaVerify is true', () => {
    component.isMfaVerify = true;
    const mfaOktaBackButton = document.createElement('a');
    spyOn(document, 'querySelector').and.callFake((selector: string) => {
      if (selector === '.link.goto') {
        return mfaOktaBackButton;
      }
      return null;
    });
    const spyDispatch = spyOn(mfaOktaBackButton, 'dispatchEvent');
    component.goBackLogin();
    expect(spyDispatch).toHaveBeenCalledWith(jasmine.any(MouseEvent));
    expect(component.isEmailCheckingDone).toBeFalse();
  });

  it('should not dispatch click event on MFA back button when isMfaVerify is false', () => {
    component.isMfaVerify = false;
    spyOn(document, 'querySelector').and.callFake((selector: string) => {
      if (selector === '.link.goto') {
        return null;
      }
      return null;
    });
    component.goBackLogin();
    expect(document.querySelector).toHaveBeenCalled();
  });

  it('should set isMfaEnabled to false and isMfaVerify to false when context.controller is primary-auth', () => {
    component.ngOnInit();
    expect(component.isMfaEnabled).toBeFalse();
    expect(component.isMfaVerify).toBeFalse();
  });

  it('should handle Okta button click success', () => {
    const resetEmailButton = document.createElement('a');
    resetEmailButton.setAttribute('data-se', 'email-button');
    document.body.appendChild(resetEmailButton);

    const forgotEmailControl = document.createElement('input');
    forgotEmailControl.value = '<EMAIL>';
    spyOn(component, 'getForgotEmailControl').and.returnValue(forgotEmailControl);
    spyOn(component, 'checkEmail').and.returnValue(true);
    spyOn(component, 'getEmailDomain').and.returnValue('example.com');

    const response = { oktaEnabled: false, ssoId: null };
    spyOn(authService, 'doCheckAuth').and.returnValue(of(response));
    spyOn(authService, 'forgotPassword').and.returnValue(of({ status: 200 }));

    component.handleOktaButtonClick();

    resetEmailButton.click();

    expect(component.loginForm.get('username').value).toBe('<EMAIL>');
    expect(authService.doCheckAuth).toHaveBeenCalledWith({ domain: 'example.com', username: '<EMAIL>' });
    expect(authService.forgotPassword).toHaveBeenCalledWith({ emailId: '<EMAIL>' });

    document.body.removeChild(resetEmailButton);
  });
  it('should handle queryParams without required fields and set organization', () => {
    spyOn(sharedService, 'getSiteLabelBasedOnMultiAdmission').and.returnValue('SITE_LABEL');
    spyOn(sharedService, 'loggedUserIsPatient').and.returnValue(false);
    spyOn(sharedService, 'loggedUserIsPartner').and.returnValue(false);
    const params = {
      source: 'default'
    };
    spyOn(route.queryParams, 'subscribe').and.callFake((callback: (params: any) => void) => {
      callback(params);
      return {
        unsubscribe: () => {}
      } as Subscription;
    });
    spyOn(localStorage, 'setItem');
    spyOn(router, 'navigateByUrl');
    component.ngOnInit();
    expect(component.isConsolo).toBe(false);
    if (Organizations.config[params.source]) {
      expect(sharedService.organization).toEqual(Organizations.config[params.source]);
      expect(sharedService.appTheme).toBe(Organizations.config[params.source].theme);
    }
    expect(sharedService.organization).toEqual(Organizations.config[params.source]);
    expect(sharedService.appTheme).toBe(Organizations.config[params.source].theme);
  });

  it('should handle username container control', () => {
    const mockContainer = document.createElement('div');
    mockContainer.className = 'o-form-fieldset-container';
    const usernameDiv = document.createElement('div');
    usernameDiv.id = 'username-field';
    mockContainer.appendChild(usernameDiv);
    document.body.appendChild(mockContainer);
    spyOn(document, 'getElementsByClassName').and.returnValue([mockContainer] as any);
    const usernameContainer = component.getUsernameContainerControl();
    expect(usernameContainer).toBeTruthy();
    expect(usernameContainer.id).toBe('username-field');
    expect(usernameContainer instanceof HTMLDivElement).toBe(true);
    document.body.removeChild(mockContainer);
  });

  it('should handle password container control', () => {
    const mockContainer = document.createElement('div');
    mockContainer.className = 'o-form-fieldset-container';
    const usernameDiv = document.createElement('div');
    usernameDiv.id = 'username-field';
    mockContainer.appendChild(usernameDiv);
    const passwordDiv = document.createElement('div');
    passwordDiv.id = 'password-field';
    mockContainer.appendChild(passwordDiv);
    document.body.appendChild(mockContainer);
    const getElementsSpy = spyOn(document, 'getElementsByClassName');
    getElementsSpy.withArgs('o-form-fieldset-container').and.returnValue([mockContainer] as any);
    const passwordContainer = component.getPasswordContainerControl();
    expect(passwordContainer).toBeTruthy();
    expect(passwordContainer.id).toBe('password-field');
    expect(passwordContainer instanceof HTMLDivElement).toBe(true);
    document.body.removeChild(mockContainer);
  });

  it('should handle username input control', () => {
    const usernameInput = document.createElement('input');
    usernameInput.id = 'okta-signin-username';
    document.body.appendChild(usernameInput);
    const getElementByIdSpy = spyOn(document, 'getElementById');
    getElementByIdSpy.withArgs('okta-signin-username').and.returnValue(usernameInput);
    const usernameControl = component.getUsernameControl();
    expect(usernameControl).toBeTruthy();
    expect(usernameControl.id).toBe('okta-signin-username');
    expect(usernameControl instanceof HTMLInputElement).toBe(true);
    document.body.removeChild(usernameInput);
  });

  it('should handle forgot email input control', () => {
    const forgotEmailInput = document.createElement('input');
    forgotEmailInput.id = 'account-recovery-username';
    document.body.appendChild(forgotEmailInput);
    spyOn(document, 'getElementById').and.returnValue(forgotEmailInput);
    const forgotEmailControl = component.getForgotEmailControl();
    expect(forgotEmailControl).toBeTruthy();
    expect(forgotEmailControl.id).toBe('account-recovery-username');
    expect(forgotEmailControl instanceof HTMLInputElement).toBe(true);
    document.body.removeChild(forgotEmailInput);
  });

  it('should get password input control', () => {
    const passwordInput = document.createElement('input');
    passwordInput.id = 'okta-signin-password';
    document.body.appendChild(passwordInput);
    const getElementByIdSpy = spyOn(document, 'getElementById');
    getElementByIdSpy.withArgs('okta-signin-password').and.returnValue(passwordInput);
    const passwordControl = component.getPasswordControl();
    expect(passwordControl).toBeTruthy();
    expect(passwordControl.id).toBe('okta-signin-password');
    expect(passwordControl instanceof HTMLInputElement).toBe(true);
    document.body.removeChild(passwordInput);
  });
  it('should handle afterRender event for password-reset-email-sent and trigger necessary actions', () => {
    const mockContext = { controller: 'password-reset-email-sent' };
    component.oktaLoginFlow = true;
    spyOn(component, 'initOktaWidget');
    spyOn(component, 'customizeOktaWidget');
    spyOn(component, 'goBackLogin');
    const backButton = document.createElement('a');
    backButton.classList.add('data-se', 'back-button');
    spyOn(document, 'querySelector').and.returnValue(backButton);
    spyOn(backButton, 'click');
    component.oktaSignIn = { on: () => null };
    spyOn(component.oktaSignIn, 'on').and.callFake((event: string, callback: (context) => void) => {
      if (event === 'afterRender') {
        callback(mockContext);
      }
    });
    spyOn(component.ngZone, 'run').and.callThrough();
    component.ngOnInit();
    expect(component.ngZone.run).toHaveBeenCalled();
    expect(component.isOktaForgetPassword).toBeFalse();
    expect(component.isForgotEmailCheckingDone).toBeFalse();
    expect(component.goBackLogin).toHaveBeenCalled();
  });
  it('should handle afterError event and reset isEmailCheckingDone and oktaLogin', () => {
    const mockContext = { controller: 'primary-auth' };
    const mockError = { message: 'Invalid token provided' };
    component.oktaLoginFlow = true;
    spyOn(component, 'initOktaWidget');
    spyOn(component, 'hidePassword');
    spyOn(component, 'renderOktaSignInWidget');
    component.oktaSignIn = { on: () => null };
    spyOn(component.oktaSignIn, 'on').and.callFake((event: string, callback: (context, error) => void) => {
      if (event === 'afterError') {
        callback(mockContext, mockError);
      }
    });
    const oktaPwd = document.createElement('input');
    oktaPwd.id = 'okta-signin-password';
    document.body.appendChild(oktaPwd);
    spyOn(component, 'customizeOktaWidget').and.callThrough();
    spyOn(component.ngZone, 'run').and.callThrough();
    component.ngOnInit();
    expect(component.isEmailCheckingDone).toBeFalse();
  });
  describe('handleRememberMeChange', () => {
    it('should set rememberMe to true when checked', () => {
      component.handleRememberMeChange(true);
      expect(component.rememberMe).toBeTrue();
    });

    it('should set rememberMe to false when unchecked', () => {
      component.handleRememberMeChange(false);
      expect(component.rememberMe).toBeFalse();
    });
  });

  describe('customizeOktaWidget - stored username handling', () => {
    let usernameInput: HTMLInputElement;
    let signInWidget: HTMLDivElement;
    let setupInputFieldSpy: jasmine.Spy;
    let getUsernameControlSpy: jasmine.Spy;
    let hidePasswordSpy: jasmine.Spy;

    beforeEach(() => {
      usernameInput = document.createElement('input');
      usernameInput.id = 'okta-signin-username';
      usernameInput.type = 'text';
      document.body.appendChild(usernameInput);

      signInWidget = document.createElement('div');
      signInWidget.id = 'sign-in-widget';
      document.body.appendChild(signInWidget);
      const usernameContainer = document.createElement('div');
      usernameContainer.className = 'o-form-input-name-username';
      const passwordContainer = document.createElement('div');
      passwordContainer.className = 'o-form-input-name-password';
      document.body.appendChild(usernameContainer);
      document.body.appendChild(passwordContainer);

      setupInputFieldSpy = spyOn(component, 'setupInputField').and.stub();
      getUsernameControlSpy = spyOn(component, 'getUsernameControl').and.returnValue(usernameInput);
      hidePasswordSpy = spyOn(component, 'hidePassword').and.stub();

      spyOn(document, 'querySelector').and.callFake((selector: string) => {
        if (selector === '.o-form-input-name-username') return usernameContainer;
        if (selector === '.o-form-input-name-password') return passwordContainer;
        return null;
      });
    });

    afterEach(() => {
      const elementsToRemove = [
        usernameInput,
        signInWidget,
        ...Array.from(document.querySelectorAll('.o-form-input-name-username')),
        ...Array.from(document.querySelectorAll('.o-form-input-name-password'))
      ];

      elementsToRemove.forEach((element) => {
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
      localStorage.removeItem(Constants.storageKeys.rememberUsername);
    });

    it('should populate username field and dispatch input event when stored username exists', () => {
      const storedUsername = '<EMAIL>';
      spyOnProperty(component, 'rememberMeUsername', 'get').and.returnValue(storedUsername);
      spyOn(usernameInput, 'dispatchEvent').and.callThrough();
      component.customizeOktaWidget();

      expect(setupInputFieldSpy).toHaveBeenCalledTimes(2);
      expect(getUsernameControlSpy).toHaveBeenCalled();
      expect(usernameInput.value).toBe(storedUsername);
      expect(usernameInput.dispatchEvent).toHaveBeenCalledWith(jasmine.any(Event));

      const dispatchedEvent = (usernameInput.dispatchEvent as jasmine.Spy).calls.mostRecent().args[0];
      expect(dispatchedEvent.bubbles).toBe(true);
      expect(dispatchedEvent.type).toBe('input');
      expect(hidePasswordSpy).toHaveBeenCalled();
    });

    it('should not populate username field when no stored username exists', () => {
      spyOnProperty(component, 'rememberMeUsername', 'get').and.returnValue(null);
      spyOn(usernameInput, 'dispatchEvent').and.callThrough();

      component.customizeOktaWidget();

      expect(setupInputFieldSpy).toHaveBeenCalledTimes(2);
      expect(getUsernameControlSpy).not.toHaveBeenCalled();
      expect(usernameInput.value).toBe('');
      expect(usernameInput.dispatchEvent).not.toHaveBeenCalled();
      expect(hidePasswordSpy).toHaveBeenCalled();
    });
  });

  describe('In-App Browser Management', () => {
    describe('closeAuthBrowser', () => {
      it('should close the browser and clear references', () => {
        const mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['close']);
        component.currentAuthBrowser = mockBrowser;
        component.authBrowserTimeout = setTimeout(() => {}, 1000);
        component['closeAuthBrowser']();
        expect(mockBrowser.close).toHaveBeenCalled();
        expect(component.currentAuthBrowser).toBeNull();
        expect(component.authBrowserTimeout).toBeNull();
      });

      it('should handle errors when closing browser gracefully', () => {
        const mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['close']);
        mockBrowser.close.and.throwError('Browser close error');
        component.currentAuthBrowser = mockBrowser;
        expect(() => component['closeAuthBrowser']()).toThrow();
        expect(component.currentAuthBrowser).toBeNull();
      });

      it('should clear timeout even if no browser is present', () => {
        component.currentAuthBrowser = null;
        component.authBrowserTimeout = setTimeout(() => {}, 1000);
        component['closeAuthBrowser']();
        expect(component.authBrowserTimeout).toBeNull();
      });
    });

    describe('routeAfterLogin', () => {
      it('should close auth browser before navigation', () => {
        spyOn(component as any, 'closeAuthBrowser');
        spyOn(router, 'navigateByUrl').and.returnValue(Promise.resolve(true));
        component.oktaLoginFlow = false;
        sharedService.setRouterLink = '';
        component.routeAfterLogin();
        expect(component['closeAuthBrowser']).toHaveBeenCalled();
      });
    });

    describe('ionViewDidLeave', () => {
      it('should close auth browser when leaving the page', () => {
        spyOn(component as any, 'closeAuthBrowser');
        component.ssoLoginSubscribe = mockSubscription;
        component.ionViewDidLeave();
        expect(component['closeAuthBrowser']).toHaveBeenCalled();
      });
    });

  describe('handleTalentLMSRedirect', () => {
    let platformSpy: jasmine.Spy;
    let sharedServicePlatformSpy: jasmine.Spy;
    let openTalentLMSInAppBrowserSpy: jasmine.Spy;

    beforeEach(() => {
      component.oktaSignIn = {
        authClient: {
          token: {
            getWithRedirect: jasmine.createSpy('getWithRedirect')
          }
        }
      };
      spyOn(sessionStorage, 'setItem');

      // Create fresh spies for each test
      component.platform.is = jasmine.createSpy('platform.is');
      (component as any).sharedService.platform.is = jasmine.createSpy('sharedService.platform.is');

      platformSpy = component.platform.is as jasmine.Spy;
      sharedServicePlatformSpy = (component as any).sharedService.platform.is as jasmine.Spy;
      openTalentLMSInAppBrowserSpy = spyOn(component, 'openTalentLMSInAppBrowser');
    });

    it('should not execute if oktaSignIn is not available', () => {
      component.oktaSignIn = null;

      component.handleTalentLMSRedirect('test-idp-id');

      expect(sessionStorage.setItem).not.toHaveBeenCalled();
    });

    it('should not execute if talentlmsIdpId is blank', () => {
      component.handleTalentLMSRedirect('');

      expect(openTalentLMSInAppBrowserSpy).not.toHaveBeenCalled();
      expect(sessionStorage.setItem).not.toHaveBeenCalled();
    });

    it('should set session storage items when valid parameters are provided', () => {
      const talentlmsIdpId = 'test-idp-id';
      platformSpy.and.returnValue(false);

      component.handleTalentLMSRedirect(talentlmsIdpId);

      expect(sessionStorage.setItem).toHaveBeenCalledWith('isSSOLogin', 'true');
      expect(sessionStorage.setItem).toHaveBeenCalledWith('oktaEnabled', 'true');
      expect(sessionStorage.setItem).toHaveBeenCalledWith('ssoFirstLogin', 'true');
    });

    it('should call openTalentLMSInAppBrowser when platform is capacitor and not iOS', () => {
      const talentlmsIdpId = 'test-idp-id';
      platformSpy.and.callFake((platform: string) => platform === 'capacitor');
      sharedServicePlatformSpy.and.callFake((platform: string) => platform !== 'ios');

      component.handleTalentLMSRedirect(talentlmsIdpId);

      expect(openTalentLMSInAppBrowserSpy).toHaveBeenCalledWith(talentlmsIdpId);
      expect(component.oktaSignIn.authClient.token.getWithRedirect).not.toHaveBeenCalled();
    });

    it('should call oktaSignIn.authClient.token.getWithRedirect when platform is not capacitor', () => {
      const talentlmsIdpId = 'test-idp-id';
      const mockRedirectUri = 'https://test-redirect.com';
      spyOnProperty(component, 'redirectUri', 'get').and.returnValue(mockRedirectUri);
      platformSpy.and.returnValue(false);

      component.handleTalentLMSRedirect(talentlmsIdpId);

      expect(openTalentLMSInAppBrowserSpy).not.toHaveBeenCalled();
      expect(component.oktaSignIn.authClient.token.getWithRedirect).toHaveBeenCalledWith({
        redirectUri: mockRedirectUri,
        idp: talentlmsIdpId,
        responseType: ['code'],
        scopes: ['openid', 'profile', 'email']
      });
    });
  });

  describe('openTalentLMSInAppBrowser', () => {
    let mockBrowser: jasmine.SpyObj<InAppBrowserObject>;
    let mockSubscription: jasmine.SpyObj<any>;

    beforeEach(() => {
      mockSubscription = jasmine.createSpyObj('Subscription', ['subscribe']);
      mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['on', 'close', 'executeScript']);
      mockBrowser.on.and.returnValue(mockSubscription);
      spyOn(inAppBrowser, 'create').and.returnValue(mockBrowser);
      spyOn(component as any, 'buildOktaAuthorizationUrl').and.returnValue('https://test-auth-url.com');
      spyOn(window, 'setTimeout').and.returnValue(123 as any);
      spyOn(window, 'clearTimeout');
    });

    it('should create InAppBrowser with correct configuration', () => {
      const talentlmsIdpId = 'test-idp-id';

      component.openTalentLMSInAppBrowser(talentlmsIdpId);

      expect(component['buildOktaAuthorizationUrl']).toHaveBeenCalledWith(talentlmsIdpId);
      expect(inAppBrowser.create).toHaveBeenCalledWith('https://test-auth-url.com', '_blank', jasmine.objectContaining({
        location: 'no',
        toolbar: 'yes',
        clearcache: 'yes',
        zoom: 'no',
        closebuttoncolor: '#FFFFFF',
        closebuttoncaption: 'Back to App',
        navigationbuttoncolor: '#FFFFFF'
      }));
      expect(sharedService.browser).toBe(mockBrowser);
    });

    it('should set currentAuthBrowser and authBrowserTimeout', () => {
      const talentlmsIdpId = 'test-idp-id';

      component.openTalentLMSInAppBrowser(talentlmsIdpId);

      expect(component.currentAuthBrowser).toBe(mockBrowser);
      expect(sharedService.browser).toBe(mockBrowser);
      expect(window.setTimeout).toHaveBeenCalledWith(jasmine.any(Function), 5 * 60 * 1000);
    });

    it('should set up browser event listeners', () => {
      const talentlmsIdpId = 'test-idp-id';

      component.openTalentLMSInAppBrowser(talentlmsIdpId);

      expect(mockBrowser.on).toHaveBeenCalledWith('loadstart');
      expect(mockBrowser.on).toHaveBeenCalledWith('loadstop');
      expect(mockBrowser.on).toHaveBeenCalledWith('exit');
      expect(mockBrowser.on).toHaveBeenCalledWith('loaderror');
    });

    it('should fallback to oktaSignIn.authClient.token.getWithRedirect on error', () => {
      const talentlmsIdpId = 'test-idp-id';
      const mockRedirectUri = 'https://test-redirect.com';
      spyOnProperty(component, 'redirectUri', 'get').and.returnValue(mockRedirectUri);
      component.oktaSignIn = {
        authClient: {
          token: {
            getWithRedirect: jasmine.createSpy('getWithRedirect')
          }
        }
      };

      (component as any).buildOktaAuthorizationUrl = jasmine.createSpy('buildOktaAuthorizationUrl').and.throwError('Test error');

      component.openTalentLMSInAppBrowser(talentlmsIdpId);

      expect(component.oktaSignIn.authClient.token.getWithRedirect).toHaveBeenCalledWith({
        redirectUri: mockRedirectUri,
        idp: talentlmsIdpId,
        responseType: ['code'],
        scopes: ['openid', 'profile', 'email']
      });
    });
  });

  describe('buildOktaAuthorizationUrl', () => {
    beforeEach(() => {
      spyOn(oktaService, 'getOktaRedirectUri').and.returnValue('https://test-redirect.com');
      spyOn(sessionStorage, 'setItem');
      spyOn(component as any, 'generateRandomString').and.callFake((length: number) => 'a'.repeat(length));
      spyOn(component as any, 'generateCodeChallenge').and.returnValue('test-code-challenge');
    });

    it('should build correct authorization URL with all required parameters', () => {
      const talentlmsIdpId = 'test-idp-id';

      const result = (component as any).buildOktaAuthorizationUrl(talentlmsIdpId);

      expect(component['generateRandomString']).toHaveBeenCalledWith(128);
      expect(component['generateRandomString']).toHaveBeenCalledWith(64);
      expect(component['generateRandomString']).toHaveBeenCalledWith(64);
      expect(component['generateCodeChallenge']).toHaveBeenCalled();
      expect(sessionStorage.setItem).toHaveBeenCalledWith('pkce_code_verifier', jasmine.any(String));
      expect(result).toContain(`${environment.oktaIssuer}/v1/authorize`);
      expect(result).toContain(`client_id=${environment.oktaClientId}`);
      expect(result).toContain(`idp=${talentlmsIdpId}`);
      expect(result).toContain('response_type=code');
      expect(result).toContain('scope=openid+profile+email');
    });

    it('should throw error when buildOktaAuthorizationUrl fails', () => {
      const talentlmsIdpId = 'test-idp-id';
      (component as any).generateRandomString = jasmine.createSpy('generateRandomString').and.throwError('Random generation failed');

      expect(() => (component as any).buildOktaAuthorizationUrl(talentlmsIdpId)).toThrowError('Failed to build authorization URL');
    });
  });

  describe('generateCodeChallenge', () => {
    it('should generate valid code challenge from code verifier', () => {
      const codeVerifier = 'a'.repeat(128);

      const result = (component as any).generateCodeChallenge(codeVerifier);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle invalid code verifier length', () => {
      const shortCodeVerifier = 'short';

      const result = (component as any).generateCodeChallenge(shortCodeVerifier);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    it('should handle errors gracefully with fallback', () => {
      const invalidCodeVerifier = null;

      const result = (component as any).generateCodeChallenge(invalidCodeVerifier);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });
  });

  describe('generateRandomString', () => {
    it('should generate random string of specified length', () => {
      const length = 64;

      const result = (component as any).generateRandomString(length);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.length).toBe(length);
    });

    it('should generate different strings on multiple calls', () => {
      const length = 32;

      const result1 = (component as any).generateRandomString(length);
      const result2 = (component as any).generateRandomString(length);

      expect(result1).not.toBe(result2);
    });

    it('should use fallback when crypto is not available', () => {
      spyOn(window.crypto, 'getRandomValues').and.returnValue(undefined);

      const result = (component as any).generateRandomString(32);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result.length).toBe(32);
    });
  });

  describe('processIdpAuthenticationSuccess', () => {
    beforeEach(() => {
      spyOn(component as any, 'closeAuthBrowser');
      spyOn(sharedService.ssoLoginValueGet, 'next');
    });

    it('should process authentication success when URL contains redirect URI and code', () => {
      const redirectUri = 'https://test-redirect.com';
      const url = `${redirectUri}?code=test-code&state=test-state`;

      (component as any).processIdpAuthenticationSuccess(url, redirectUri);

      expect(component['closeAuthBrowser']).toHaveBeenCalled();
      expect(sharedService.ssoLoginValueGet.next).toHaveBeenCalledWith({
        code: 'test-code',
        scheme: redirectUri,
        url: url,
        state: 'test-state'
      });
    });

    it('should not process when URL does not contain redirect URI', () => {
      const redirectUri = 'https://test-redirect.com';
      const url = 'https://other-domain.com?code=test-code';

      (component as any).processIdpAuthenticationSuccess(url, redirectUri);

      expect(component['closeAuthBrowser']).not.toHaveBeenCalled();
      expect(sharedService.ssoLoginValueGet.next).not.toHaveBeenCalled();
    });

    it('should not process when URL does not contain code parameter', () => {
      const redirectUri = 'https://test-redirect.com';
      const url = `${redirectUri}?state=test-state`;

      (component as any).processIdpAuthenticationSuccess(url, redirectUri);

      expect(component['closeAuthBrowser']).not.toHaveBeenCalled();
      expect(sharedService.ssoLoginValueGet.next).not.toHaveBeenCalled();
    });
  });

  describe('closeAuthBrowser', () => {
    let mockBrowser: jasmine.SpyObj<InAppBrowserObject>;

    beforeEach(() => {
      mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['close']);
      spyOn(window, 'clearTimeout');
    });

    it('should clear timeout and close browser when both exist', () => {
      component.authBrowserTimeout = 123;
      component.currentAuthBrowser = mockBrowser;

      (component as any).closeAuthBrowser();

      expect(window.clearTimeout).toHaveBeenCalledWith(123);
      expect(mockBrowser.close).toHaveBeenCalled();
      expect(component.authBrowserTimeout).toBeNull();
      expect(component.currentAuthBrowser).toBeNull();
    });

    it('should handle browser close error gracefully', () => {
      component.authBrowserTimeout = 123;
      component.currentAuthBrowser = mockBrowser;
      mockBrowser.close.and.throwError('Close error');

      expect(() => (component as any).closeAuthBrowser()).toThrow();
      expect(component.currentAuthBrowser).toBeNull();
    });

    it('should handle when no browser or timeout exists', () => {
      component.authBrowserTimeout = null;
      component.currentAuthBrowser = null;

      expect(() => (component as any).closeAuthBrowser()).not.toThrow();
    });
  });

  describe('injectTalentLMSEnhancements', () => {
    let mockBrowser: jasmine.SpyObj<InAppBrowserObject>;

    beforeEach(() => {
      mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['executeScript']);
    });

    it('should inject CSS styles for mobile optimization', () => {
      (component as any).injectTalentLMSEnhancements(mockBrowser);

      expect(mockBrowser.executeScript).toHaveBeenCalledTimes(2);
      const firstCall = mockBrowser.executeScript.calls.argsFor(0)[0];
      expect(firstCall.code).toContain('mobileStyle.innerHTML');
      expect(firstCall.code).toContain('font-size: 16px !important');
      expect(firstCall.code).toContain('#tlms-back-button');
    });

    it('should inject back button functionality', () => {
      (component as any).injectTalentLMSEnhancements(mockBrowser);
      const secondCall = mockBrowser.executeScript.calls.argsFor(1)[0];
      expect(secondCall.code).toContain('backButton.id = \'tlms-back-button\'');
      expect(secondCall.code).toContain('← Back to App');
      expect(secondCall.code).toContain('closeTalentLMSBrowser');
      expect(secondCall.code).toContain('addEventListener');
    });
  });

  describe('Integration tests for TalentLMS flow', () => {


    it('should track activity when browser is closed', () => {
      const talentlmsIdpId = 'test-idp-id';
      const mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['on', 'close', 'executeScript']);
      const mockSubscription = jasmine.createSpyObj('Subscription', ['subscribe']);
      const mockLoadStartSubscription = jasmine.createSpyObj('LoadStartSubscription', ['subscribe']);
      const mockLoadStopSubscription = jasmine.createSpyObj('LoadStopSubscription', ['subscribe']);
      const mockExitSubscription = jasmine.createSpyObj('ExitSubscription', ['subscribe']);
      const mockLoadErrorSubscription = jasmine.createSpyObj('LoadErrorSubscription', ['subscribe']);
      mockBrowser.on.and.callFake((eventType: string) => {
        switch (eventType) {
          case 'loadstart': return mockLoadStartSubscription;
          case 'loadstop': return mockLoadStopSubscription;
          case 'exit': return mockExitSubscription;
          case 'loaderror': return mockLoadErrorSubscription;
          default: return mockSubscription;
        }
      });
      spyOn(inAppBrowser, 'create').and.returnValue(mockBrowser);
      spyOn(component as any, 'buildOktaAuthorizationUrl').and.returnValue('https://test-auth-url.com');
      spyOn(sharedService, 'trackActivity');
      component.openTalentLMSInAppBrowser(talentlmsIdpId);

      // Verify that sharedService.browser is set
      expect(sharedService.browser).toBe(mockBrowser);

      const exitHandler = mockExitSubscription.subscribe.calls.argsFor(0)[0];
      exitHandler();
      expect(sharedService.trackActivity).toHaveBeenCalledWith({
        type: Activity.userAccess,
        name: Activity.ssoLogin,
        des: {
          data: {
            action: 'TALENT_LMS_BROWSER_CLOSED',
            idpId: talentlmsIdpId
          },
          desConstant: 'User closed TalentLMS authentication browser'
        }
      });
    });
  });

  });
});
