<ion-header [translucent]="true" [ngClass]="{ 'common-header': isOktaForgetPassword }" mode="ios">
    <ion-toolbar mode="ios">
        <ion-title class="header-ion-title" *ngIf="!isOktaForgetPassword">
            <h1 class="header-title">{{ 'LABELS.FORGOT_PASSWORD' | translate }}</h1>
        </ion-title>
    </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="login-background">
    <ion-header collapse="condense" mode="ios">
        <ion-toolbar mode="ios">
        </ion-toolbar>
    </ion-header>
    <div [ngClass]="{'hide': isIdpRedirectionProcess ||  isConsolo}">
        <form name="login-form" id="login-form" [formGroup]="loginForm">
            <ion-row class="ion-margin">
                <ion-col size="12" size-lg="6" offset-lg="3" size-sm="12" size-md="6" offset-md="3" size-xl="4"
                    offset-xl="4" class="ion-text-center">
                    <ion-img [src]="setSrcLogo()" class="setLogo" alt="login-logo"></ion-img>
                </ion-col>
                <ion-col size="12" size-lg="6" offset-lg="3" size-sm="12" size-md="6" offset-md="3" size-xl="4"
                    offset-xl="4">
                    <!-- CHP-4157 -->
                    <ion-item *ngIf="isEmailCheckingDone && !isMfaEnabled" lines="none" class="ion-margin-bottom">
                        <ion-label class="ion-text-wrap">{{'LABELS.LOGIN_AS' | translate}}
                            <b>{{loginForm.value.username}}</b> <span id="not-you" (click)="resetPassword()">
                                <ion-text color="de-york">
                                    {{'LABELS.NOT_YOU' | translate}}?
                                </ion-text>
                            </span>
                        </ion-label>
                    </ion-item>
                    <div id="sign-in-widget"></div>

                    <ion-item class="ion-margin-bottom ion-no-padding username-bg" mode="md"
                        *ngIf="!isEmailCheckingDone && !oktaLoginFlow">
                        <ion-label>
                            <ion-icon slot="start" src="assets/icon/material-svg/account.svg" color="fountain-blue"
                                class="set-round-icon ion-margin-end inner-icon"></ion-icon>
                        </ion-label>
                        <ion-input type="email" placeholder="{{'LABELS.USERNAME' | translate }}" id="username" 
                            formControlName="username" name="username"></ion-input>
                    </ion-item>
                    <ng-container *ngIf="isEmailCheckingDone && !oktaLoginFlow && !isMfaVerify">
                        <ion-item class="ion-margin-bottom ion-no-padding" mode="md"
                         *ngIf="!otpInfo.loginWithOtp; else loginWithOtp;">
                         <ion-label>
                            <ion-icon slot="start" src="assets/icon/material-svg/lock-open.svg" color="fountain-blue"
                                class="set-round-icon ion-margin-end inner-icon"></ion-icon>
                         </ion-label>
                            <ion-input [type]="isShowPassword ? 'text':'password'" id="password"
                                placeholder="{{ 'LABELS.PASSWORD' | translate}}" formControlName="password" name="password"
                                clearOnEdit="false">
                            </ion-input>
                            <ion-icon slot="end" [name]="isShowPassword ? 'eye' : 'eye-off'" color="dark" id="toggle-password" class="inner-icon"
                                (click)="showHidePassword()" color="fountain-blue"></ion-icon>
                        </ion-item>
                        <div *ngIf="otp2faInfo.twoFaEnabled">
                            <ng-container *ngTemplateOutlet="loginWithOtp"></ng-container>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="isEmailCheckingDone && oktaLoginFlow">
                        <div *ngIf="!otpInfo.loginWithOtp; else loginWithOtp;">
                        </div>
                        <div *ngIf="otp2faInfo.twoFaEnabled">
                            <ng-container *ngTemplateOutlet="loginWithOtp"></ng-container>
                        </div>
                    </ng-container>
                    <ng-template #loginWithOtp>
                        <ion-row *ngIf="otpInfo.otpSuccessMsg">
                            <ion-col class="justify-center">
                                <ion-chip class="auto-height otp-message" color="success" mode="ios"><strong>{{ otpInfo.otpSuccessMsg }}</strong></ion-chip>
                            </ion-col>
                        </ion-row>
                        <ion-row class="otp-label">
                            <ion-col>
                              <div class="ion-text-start">
                                {{ 'LABELS.OTP' | translate }}
                              </div>
                            </ion-col>
                            <ion-col size="auto">
                              <div class="ion-text-end" >
                                <a *ngIf="otpInfo.resendOtp; else displayExpiry;" href="javascript: void(0);" (click)="sendOtp()" class="pull-right cat__core__link--blue cat__core__link--underlined">&nbsp;{{ 'LABELS.RESEND_OTP_Q' | translate }}</a>
                                <ng-template #displayExpiry><strong *ngIf="otpInfo.otpExpiryInMins !== ''">{{ 'MESSAGES.OTP_EXPIRES_IN_SECONDS' | translate:{'expiry': otpInfo.otpExpiryInMins} }}</strong></ng-template>
                              </div>
                            </ion-col>
                          </ion-row>
                          <app-otp-input #ngOtpInput [config]="otpInputConfig" (otpChange)="handleOtpChange($event)" (fill)="handleFillEvent($event)">
                        </app-otp-input>
                    </ng-template>

                </ion-col>
        <ion-col
          size="12"
          size-lg="6"
          offset-lg="3"
          size-sm="12"
          size-md="6"
          offset-md="3"
          size-xl="4"
          offset-xl="4"
          *ngIf="!isOktaForgetPassword && !isMfaEnabled"
        >
          <!-- Add Remember Me checkbox -->
          <ion-item lines="none">
            <ion-checkbox
              mode="android"
              class="common-checkbox ion-no-margin ion-margin-end"
              id="remember-username"
              color="de-york"
              slot="start"
              [checked]="rememberMe"
              (ionChange)="handleRememberMeChange($event.detail.checked)"
            >
            </ion-checkbox>
            <ion-label>{{ 'LABELS.REMEMBER_USERNAME' | translate }}</ion-label>
          </ion-item>
        </ion-col>
                <ion-col size="12" size-lg="6" offset-lg="3" size-sm="12" size-md="6" offset-md="3" size-xl="4"  *ngIf="!isOktaForgetPassword  &&  !isMfaEnabled" 
                    offset-xl="4" class="ion-text-center">
                    <!-- TODO! CHP-3595 -->
                    <ion-button expand="block" (click)="getAuthAppURL()"
                        [id]="isEmailCheckingDone?'signin':'next'"
                        #signIn
                        class="ion-text-capitalize signin">
                        {{isEmailCheckingDone ? ('BUTTONS.SIGN_IN' | translate) : ('BUTTONS.NEXT' |
                        translate)}}
                    </ion-button>
                    <ion-row>
                        <ion-col class="ion-text-left" size="6">
                            <ion-button size="small"  *ngIf="!isEmailCheckingDone" fill="clear"
                                class="ion-text-center ion-text-capitalize forgot-password ion-no-padding" mode="ios"
                                id="forgot-password" (click)="goToForgotUsernamePage(); loginForm.reset(); resetOtpInfo();">
                                {{'LABELS.FORGOT_USERNAME' | translate}}?
                            </ion-button>
              <ion-button
                size="small"
                fill="clear"
                class="ion-text-center ion-text-capitalize ion-no-padding load-otp"
                mode="ios"
                *ngIf="isEmailCheckingDone && otp2faInfo.otpEnabled && !otp2faInfo.twoFaEnabled"
                (click)="loginWithOtpOrPassword()"
              >
                {{ (otpInfo.loginWithOtp ? 'LABELS.LOGIN_WITH_PASSWORD_Q' : 'LABELS.LOGIN_WITH_OTP_Q') | translate }}
              </ion-button>
                        </ion-col>
                            
                        <ion-col class="ion-text-right" size="6">
                            <ion-button size="small"  fill="clear"
                                class="ion-text-center ion-text-capitalize forgot-password ion-no-padding" mode="ios"
                                id="forgot-password" (click)="goToForgotPasswordPage(); loginForm.reset(); resetOtpInfo();">
                                {{'LABELS.FORGOT_PASSWORD' | translate}}?
                            </ion-button>
                        </ion-col>
                    </ion-row>
                </ion-col>
                <ion-col size="12" class="ion-text-center" *ngIf="!signUpSectionCheck && !isOktaForgetPassword && !isMfaEnabled">
                    {{'LABELS.SIGN_UP' | translate}}?
                    <span (click)="showSignUPModal()" id="click-here">
                        <ion-text class="create-account"> {{'LABELS.CLICK_HERE' | translate}} </ion-text>
                    </span>
                </ion-col>
                <ion-col size="12" class="ion-text-center" (click)="showSignUPModal()" *ngIf="signUpSectionCheck">
                    <ion-text id="click-here" class="create-account" role="button">{{'LABELS.CREATE_ACCOUNT' | translate
                        }}</ion-text>
                </ion-col>
            </ion-row>
            <ion-row class="ion-margin contact-us" *ngIf="hereToHelpSectionDisplay">
                <ion-col size="12" class="ion-text-center">
          <ion-text
            >{{
              (hereToHelpTechnicalSupport ? 'MESSAGES.TECHNICAL_SUPPORT_WE_ARE_HERE_TO_HELP' : 'MESSAGES.QUESTIONS_WE_ARE_HERE_TO_HELP') | translate
            }}
          </ion-text>
                </ion-col>
                <ion-col size="12" class="ion-text-center">
                    <ion-text>{{( technicalAndPharmacySupport ? 'MESSAGES.TECHNICAL_SUPPORT' : 'MESSAGES.CALL_US_AT') | translate }}
                        <a [href]="contact.number">{{contact.display}}</a>
                    </ion-text>    
                  </ion-col>
                <ion-col size="12" class="ion-text-center" *ngIf="technicalAndPharmacySupport">
                    <ion-text>{{ 'MESSAGES.PHARMACY_SUPPORT' | translate }}
                        <a [href]="'tel:+1' + contact.alternativeContact">{{ contact.alternativeContact }}</a>
                    </ion-text>
                </ion-col>
        <ion-col size="12" *ngIf="showEmailUsAt" class="ion-text-center">
          <ion-text>
            <span [innerHtml]="'MESSAGES.MAIL_US_AT' | translate: { email: contact.email } | autolinker | safe: 'html'"> </span>
          </ion-text>
        </ion-col>
            </ion-row>
            <ion-row class="ion-margin contact-us" *ngIf="medicalEmergencyDisplay">
                <ion-col size="12" class="ion-text-center">
                    <ion-text>{{ 'MESSAGES.EMERGENCY_CONTACT' | translate }}
                    </ion-text>
                </ion-col>
                <ion-col size="12" class="ion-text-center">
                    <ion-text>
                        <span
                            [innerHtml]="'MESSAGES.APP_QUESTION' | translate :{email:contact.email} |autolinker | safe:'html'">
                        </span>
                    </ion-text>
                </ion-col>
            </ion-row>
            <!-- <app-sign-up triggerID="sign-up"></app-sign-up> -->
        </form>
    </div>
    <ng-container *ngIf="isIdpRedirectionProcess || isConsolo">
        <ion-row>
            <ion-col size="12" size-lg="6" offset-lg="3" size-sm="12" size-md="6" offset-md="3" size-xl="4"
                offset-xl="4" class="ion-text-center">
                <ion-img [src]="setSrcLogo()" class="setLogo" alt="logo">
                </ion-img>
            </ion-col>
            <ion-col size="12" class="ion-text-center" *ngIf="isIdpRedirectionProcess">
                {{'MESSAGES.TAKING_ORGANIZATION_SIGN_IN_PAGE' | translate}}
            </ion-col>
            <ion-col size="12" class="ion-text-center" *ngIf="isConsolo">
                {{'MESSAGES.SESSION_VALIDATION_IN_PROGRESS' | translate}}
            </ion-col>
            <ion-col size="12" class="ion-text-center" *ngIf="isIdpRedirectionProcess">
                <ion-button (click)="isIdpRedirectionProcess = false" color="de-york" id="cancel"
                    class="ion-text-capitalize">
                    {{'BUTTONS.CANCEL' | translate}}
                </ion-button>
            </ion-col>
        </ion-row>
    </ng-container>
</ion-content>

<ion-footer class="common-footer" *ngIf="isOktaForgetPassword || isMfaVerify ">
    <ion-toolbar>
        <ion-buttons slot="start">
            <ion-icon tappable class="forgot-back-btn" src="assets/icon/material-svg/arrow-left-bold.svg"
                (click)="goBackLogin()">
            </ion-icon>
        </ion-buttons>
    </ion-toolbar>
</ion-footer>
