import { SessionService } from 'src/app/services/session-service/session.service';
import { SignUpComponent } from 'src/app/pages/auth/sign-up/sign-up.component';
import { appThemeConfig, ConfigValues, oktaEnabledConfig } from 'src/assets/config/config';
import { Platform, ModalController, NavController } from '@ionic/angular';
import { Component, NgZone, OnInit, ViewChildren, ViewChild, Inject } from '@angular/core';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { UntypedFormGroup, Validators, UntypedFormBuilder, UntypedFormControl } from '@angular/forms';
import { Router, ActivatedRoute, NavigationExtras } from '@angular/router';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants } from 'src/app/constants/constants';
import { isBlank, getCurrentDate, getCurrentDayNumber, getCurrentTime, isPresent } from 'src/app/utils/utils';
import { VideoCallService } from 'src/app/services/video-call/video-call.service';
import { Activity } from 'src/app/constants/activity';
import { VideoCall } from 'src/app/constants/video-call';
import { Location } from '@angular/common';
import { Organizations } from 'src/assets/organizations/organizations';
import { Config } from 'src/app/constants/config';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { APIs } from 'src/app/constants/apis';
import { Signature } from 'src/app/constants/signature';
import { AppAuthURLResponse, IdpConfigFetchResponse, LoginResponse } from 'src/app/interfaces/login';
import { Subscription, Subject } from 'rxjs';
import { KeychainTouchId } from '@ionic-native/keychain-touch-id/ngx';
import { PageRoutes } from 'src/app/constants/page-routes';
import { theme } from 'src/theme/theme';
import { MessageCenterService } from 'src/app/services/message-center/message-center.service';
import { setKeyToSession } from 'src/app/utils/storage-utils';
import { finalize } from 'rxjs/operators';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { NgxOtpInputConfig } from 'src/app/components/otp-input/otp-input.model';
import { OKTA_AUTH } from '@okta/okta-angular';
import { OktaAuth, Tokens } from '@okta/okta-auth-js';
import OktaSignIn from '@okta/okta-signin-widget';
import { environment } from 'src/environments/environment';
import { OktaService } from 'src/app/services/okta/okta.service';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { getComputedStyleProperty } from 'src/app/utils/utils';
import * as CryptoJS from 'crypto-js';
import { IosAuthSessionService } from 'src/app/services/ios-auth-session/ios-auth-session.service';


declare let Oidc: any;

export interface OtpResponse {
  status: boolean;
  message: string;
  sessionId: string;
  otpExpiryTime: number;
}

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss']
})
export class LoginPage implements OnInit {
  loginForm: UntypedFormGroup;
  theme: string;
  isuserLoggedIn: boolean;
  isConsolo = false;
  isMobilePlatform = false;
  isEmailCheckingDone = false;
  isForgotEmailCheckingDone = false;
  isIdpRedirectionProcess = false;
  showLoginCheckMessage: string;
  pendingAccountActivationMessage: string;
  dischargedUserMessage: string;
  isShowPassword = false;
  userManager: any;
  ssoLoginSubscribe: Subscription;
  isTouchIdLogin = false;
  isTouchIdAvailable = false;
  appTheme = theme.theme;
  contact: any;
  oktaLoginFlow = environment.oktaFlow && oktaEnabledConfig.enabledApps.includes(this.appTheme);
  isOktaForgetPassword = false;
  isOktaForgetPasswordFooter = false;
  oktaLogin = false;
  oktaEnabled = false;
  showIdmMigrationLoader = false;
  rememberMe = false;

  @ViewChildren('formOtpRow') otpFields;
  @ViewChild('signIn') signIn;
  otpInfo = this.defaultOtpInfo;
  otp2faInfo = { otpEnabled: false, twoFaEnabled: false };
  isOtpValid = false;
  loginWithOtp = false;
  otpLoader = false;
  expiryInterval;
  enableForgotPassword;
  hasEmailDomainFlow = true;
  username = new UntypedFormControl(null, [Validators.required]);
  @ViewChild('ngOtpInput', { static: false }) ngOtpInput: any;
  otp: string;
  public networkOnline: boolean = navigator.onLine;
  oktaSignIn: any;
  currentAuthBrowser: InAppBrowserObject;
  authBrowserTimeout: any;
  private authPollingInterval: ReturnType<typeof setTimeout> = null;

  private readonly destroy$ = new Subject();
  commonService: any;
  isMfaEnabled = false;
  isMfaVerify = false;
  constructor(
    @Inject(OKTA_AUTH) public oktaAuth: OktaAuth,
    private readonly authService: AuthService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly location: Location,
    private readonly sharedService: SharedService,
    private readonly common: CommonService,
    private readonly videoCallService: VideoCallService,
    private readonly httpService: HttpService,
    private readonly socketService: SocketService,
    private readonly messageCenterService: MessageCenterService,
    public platform: Platform,
    private modalController: ModalController,
    private keychainTouchId: KeychainTouchId,
    private navController: NavController,
    public ngZone: NgZone,
    private oktaService: OktaService,
    private persistentService: PersistentService,
    private sessionService: SessionService,
    private inAppBrowser: InAppBrowser,
    private iosAuthSessionService: IosAuthSessionService

  ) {
    if (this.oktaLoginFlow) {
      this.oktaSignIn = new OktaSignIn({
        issuer: environment.oktaIssuer,
        baseUrl: environment.oktaBaseUrl,
        clientId: environment.oktaClientId,
        redirectUri: this.oktaService.getOktaRedirectUri(),
        useClassicEngine: true,
        scopes: ['openid', 'profile', 'email', 'offline_access'],
        features: {
          showPasswordToggleOnSignInPage: true,
          disableAutocomplete: true
        },
        authParams: {
          responseType: ['code'],
          pkce: true,
          display: 'page'
        }
      });
    }
    this.route.queryParams.subscribe((params) => {
      if (
        !isBlank(params.clientId) &&
        !isBlank(params.patientMRN) &&
        !isBlank(params.type) &&
        ((params.type.toLowerCase() === Signature.documentCategories.toLowerCase() && !isBlank(params.documentCategoryId)) ||
          (params.type.toLowerCase() === Constants.forms && !isBlank(params.formId)))
      ) {
        this.isConsolo = true;
        this.sharedService.consoloLoader = true;
        this.consoloWorkflow(params);
      } else {
        // Check source param to set organization
        this.isConsolo = false;
        let organization = Organizations.config.default;
        if (!isBlank(Organizations.config[params.source])) {
          organization = Organizations.config[params.source];
        }
        localStorage.setItem(Constants.storageKeys.organization, organization.alias);
        this.sharedService.organization = organization;
        this.theme = this.sharedService.appTheme = organization.theme;
        // Set source theme and redirect to login
        this.router.navigateByUrl('/login', { replaceUrl: true });
      }
    });
    if (isBlank(this.sharedService.automaticLinkedItems) && !this.isConsolo) {
      this.loginForm = this.formBuilder.group({
        username: this.username,
        password: new UntypedFormControl('', Validators.required)
      });

      // Set stored username if available
      const storedUsername = this.rememberMeUsername;
      if (storedUsername) {
        this.loginForm.patchValue({
          username: storedUsername
        });
        this.rememberMe = true;
      }
    }
  }

  ngOnInit(): void {
    if (this.oktaLoginFlow) {
      const originalUri = this.oktaAuth.getOriginalUri();
      if (!originalUri || originalUri === window.location.origin) {
        this.oktaAuth.setOriginalUri('/');
      }

      this.initOktaWidget();
      this.oktaSignIn.on('afterRender', (context) => {
        this.ngZone.run(() => {
          if (context.controller === 'password-reset-email-sent') {
            const backbutton = document.querySelector('[data-se="back-button"]') as HTMLAnchorElement;
            if (backbutton) {
              this.isOktaForgetPassword = false;
              this.isForgotEmailCheckingDone = false;
              backbutton.click();
              const successAlertMessage = this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESS_ALERT_MESSAGE');
              this.common.showMessage(successAlertMessage);
              this.goBackLogin();
            }
          }
          if (context.controller === 'enroll-choices' || context.controller === 'mfa-verify') {
            this.sharedService.isLoading = false;
            this.isMfaEnabled = true;
            this.isMfaVerify = context.controller === 'mfa-verify';
          } else if (context.controller === 'primary-auth') {
            this.isMfaEnabled = false;
            this.isMfaVerify = false;
          }
        });
        this.customizeOktaWidget();
      });
      this.oktaSignIn.on('afterError', (context, error) => {
        if (error.message === 'Invalid token provided') {
          this.ngZone.run(() => {
            this.isEmailCheckingDone = false;
            this.oktaLogin = false;
          });
        }
        this.sharedService.isLoading = false;
        const oktaTokenStorage = JSON.parse(localStorage.getItem(Constants.storageKeys.oktaTokenStorage));
        const isInvalidCredential = error.statusCode === 401 && context.controller === 'primary-auth' && isBlank(oktaTokenStorage);
        const invalidEmailPassword = isInvalidCredential 
          ? this.common.getTranslateData('VALIDATION_MESSAGES.INVALID_EMAIL_PASSWORD') 
          : this.common.getTranslateData(error.message);
        this.common.showMessage(invalidEmailPassword);
        this.sharedService.trackActivity({
          name: 'Okta Authentication Error',
          type: 'Authentication',
          des: `Error in Okta authentication: ${invalidEmailPassword}`
        });
        if (error.statusCode !== 401) {
          this.renderOktaSignInWidget();
        }
      });
    }

    this.showLoginCheckMessage = ConfigValues.messages.consolo.consoloLoginCheckingMessage;
    this.pendingAccountActivationMessage = ConfigValues.messages.pendingAccountActivation;
    this.dischargedUserMessage = ConfigValues.messages.dischargedUser;
    if (this.hereToHelpSectionDisplay || this.medicalEmergencyDisplay) {
      this.contact = theme.details.contact;
    }
  }

  initOktaWidget(): void {
    this.oktaSignIn
      .showSignInToGetTokens({
        el: '#sign-in-widget',
        scopes: ['openid', 'profile', 'email', 'offline_access']
      })
      .then((tokens: Tokens) => {
        // Remove the widget
        this.oktaSignIn.remove();

        // In this flow the redirect to Okta occurs in a hidden iframe
        this.oktaAuth.handleLoginRedirect(tokens);
        this.oktaLoginFlow = false;
        localStorage.setItem(Constants.storageKeys.oktaTokenExpiry, tokens.accessToken.expiresAt.toString());
        this.login(true);
        this.refreshTokenEvent();
      })
      .catch((err: any) => {
        // Typically due to misconfiguration
        // Known errors: CONFIG_ERROR, UNSUPPORTED_BROWSER_ERROR
        this.oktaLoginFlow = false;
        this.oktaSignIn.remove();
        console.log(' ngOnInit.showSignInToGetTokens error : ', err);
        throw err;
      });
  }

  refreshTokenEvent(): void {
    // Set up all token event handlers (renewal and error) using shared service
    this.sharedService.setupOktaTokenEventHandlers(this.oktaAuth);
  }

  renderOktaSignInWidget(): void {
    this.resetPassword();
    // this.oktaSignIn.off();
    this.oktaSignIn.remove();
    this.initOktaWidget();
  }

  get signUpSectionCheck(): boolean {
    return appThemeConfig.signUpSectionCheck.includes(this.appTheme);
  }
  get hereToHelpSectionDisplay(): boolean {
    return appThemeConfig.hereToHelpSectionDisplay.includes(this.appTheme);
  }
  get medicalEmergencyDisplay(): boolean {
    return appThemeConfig.medicalEmergencyDisplay.includes(this.appTheme);
  }
  get technicalAndPharmacySupport(): boolean {
    return appThemeConfig.technicalAndPharmacySupport.includes(this.appTheme);
  }
  get hereToHelpTechnicalSupport(): boolean {
    return appThemeConfig.hereToHelpTechnicalSupport.includes(this.appTheme);
  }
  get showEmailUsAt(): boolean {
    return appThemeConfig.showEmailUsAt.includes(this.appTheme);
  }
  setSrcLogo(): string {
    const alias = this.sharedService.brandConfig ? this.sharedService.brandConfig.alias : theme.theme;
    return `assets/organizations/${alias}/images/ch-logo.png`;
  }
  ionViewWillEnter(): void {
    const touchIdKey = localStorage.getItem(Constants.storageKeys.touchIdKey);
    this.fingerprintLogin(touchIdKey);
    this.ssoLoginSubscribe = this.sharedService.ssoLoginValueGet.subscribe((data: any) => {
      if (data.email) {
        this.loginForm.get('username').setValue(data.email);
      } else if(sessionStorage.getItem('talentlmsEnabled') === 'true' && sessionStorage.getItem('ssoFirstLogin') === 'true' && !this.platform.is('ios') ) {
        const appAuthUrl = localStorage.getItem(Constants.storageKeys.idpAppAuthLink);
        this.idpConfigFetch(appAuthUrl);
      } else {
        this.ssoLogin(data);
      }
    });
    this.checkPlatformToShowSSOButton();
    if (!this.isConsolo) {
      this.isuserLoggedIn = this.sharedService.isUserLoggedIn();
      if (this.authService.isUserLoggedIn() && this.networkOnline) {
        // Logged in user redirects to home.
        this.isuserLoggedIn = this.sharedService.isUserLoggedIn();
        this.router.navigate(['home']);
      } else {
        const pageTitle = !isBlank(this.route.snapshot.data.title) ? this.common.getTranslateData(this.route.snapshot.data.title) : '';
        this.route.snapshot.data.title = pageTitle;
        this.sharedService.saveRouteData(this.route.snapshot); // Save route from Login Page.
        this.sharedService.trackActivity({ type: Activity.pageAccess });
      }
    }
  }

  clickOktaSignin() {
    const signInButton = document.getElementById('okta-signin-submit');
    if (signInButton) {
      signInButton.click();
      this.sharedService.isLoading = true;
    }
  }

  customizeOktaWidget() {
    const usernameContainer = document.querySelector('.o-form-input-name-username') as HTMLDivElement;
    const passwordContainer = document.querySelector('.o-form-input-name-password') as HTMLDivElement;

    this.setupInputField('okta-signin-username', 'assets/icon/material-svg/account.svg', usernameContainer);
    this.setupInputField('okta-signin-password', 'assets/icon/material-svg/lock-open.svg', passwordContainer);

    const signInWidget = document.getElementById('sign-in-widget');
    if (signInWidget) {
      signInWidget.style.paddingTop = '12px';
      signInWidget.style.paddingBottom = '14px';
    }
    // Set stored username if available for Okta
    const storedUsername = this.rememberMeUsername;
    if (storedUsername) {
      const usernameInput = this.getUsernameControl();
      if (usernameInput) {
        usernameInput.value = storedUsername;
        // Trigger input event to update Okta widget state
        const inputEvent = new Event('input', { bubbles: true });
        usernameInput.dispatchEvent(inputEvent);
      }
    }

    this.hidePassword();
  }

  handleRememberMeChange(checked: boolean): void {
    this.rememberMe = checked;
    const username = this.oktaLoginFlow
      ? (document.getElementById('okta-signin-username') as HTMLInputElement)?.value
      : this.loginForm.get('username').value;
    this.manageUsernamePersistence(username);
  }
  /**
   * Save username to localStorage if remember me is checked
   * @param username Username to save
   */
  manageUsernamePersistence(username = ''): void {
    if (!this.rememberMe) {
      this.removeRememberMeUsername();
    } else if (isPresent(username)) {
      this.setRememberMeUsername(username);
    }
  }

  /**
   * Save username to localStorage
   * @param username Username to save
   */
  setRememberMeUsername(username: string): void {
    localStorage.setItem(Constants.storageKeys.rememberUsername, username);
  }

  /**
   * Get stored username from localStorage
   * @returns Stored username or null if not found
   */
  get rememberMeUsername(): string | null {
    const username = localStorage.getItem(Constants.storageKeys.rememberUsername);
    return isPresent(username) ? username : null;
  }

  /**
   * Remove stored username from localStorage
   */
  removeRememberMeUsername(): void {
    localStorage.removeItem(Constants.storageKeys.rememberUsername);
  }

  // This below code adds an ion-icon element before the username/password input field
  // It creates a div and aion-icon ,then the ion-icon is then appended to the div
  // Finally, the div with ion-icon is inserted before the username/password input field
  setupInputField(inputId: string, iconSrc: string, container: HTMLDivElement): void {
    const inputElement = container.querySelector('input');
    inputElement.classList.add('custom-class');
    if (inputElement) {
      if (inputId === 'account-recovery-username') {
        inputElement.setAttribute('placeholder', 'Rest mail');
      } else if (inputId === 'okta-signin-username') {
        inputElement.setAttribute('placeholder', this.common.getTranslateData('LABELS.USERNAME'));
      } else {
        inputElement.setAttribute('placeholder', this.common.getTranslateData('LABELS.PASSWORD'));
      }

      const iconDiv = document.createElement('div');
      iconDiv.setAttribute('class', 'ion-icon-top');
      const existingIcon = container.querySelector(`ion-icon[src="${iconSrc}"]`);
      if (!existingIcon) {
        const ionIcon = document.createElement('ion-icon');
        ionIcon.setAttribute('src', iconSrc);
        ionIcon.setAttribute('color', 'fountain-blue');
        ionIcon.setAttribute('class', 'ion-margin-end okta-round');
        iconDiv.appendChild(ionIcon);
      }

      container.insertBefore(iconDiv, inputElement);
    }
  }

  customizeForgotPassword() {
    this.isOktaForgetPassword = true;
    const forgotPasswordLink = document.querySelector('.js-forgot-password') as HTMLAnchorElement;
    if (forgotPasswordLink) {
      forgotPasswordLink.click();
    }

    const usernameContainer = document.getElementById('account-recovery-username') as HTMLDivElement;
    if (usernameContainer) {
      this.setupInputField('account-recovery-username', 'assets/icon/material-svg/account.svg', usernameContainer);
    }
    setTimeout(() => {
      this.handleOktaButtonClick();
    }, 500);
  }

  handleOktaButtonClick() {
    const resetEmailButton = document.querySelector('[data-se="email-button"]') as HTMLAnchorElement;
    resetEmailButton.textContent = 'Reset Password';
    resetEmailButton.style.textDecoration = 'none';
    let allowRealClick = false;
    const handleClick = (event: Event) => {
      if (allowRealClick) {
        return;
      }
      event.stopImmediatePropagation();
      event.preventDefault();
      this.loginForm.get('username').setValue(this.getForgotEmailControl()?.value.trim());
      if (this.checkEmail()) {
        const params = {
          domain: this.getEmailDomain(),
          username: this.username.value
        };
        this.authService.doCheckAuth(params).subscribe(
          (response: AppAuthURLResponse) => {
            if (response.oktaEnabled && response.ssoId) {
              allowRealClick = true;
              resetEmailButton.removeEventListener('click', handleClick);
              resetEmailButton.click();
              const oktaBackButtonLink = document.querySelector('[data-se="back-button"]') as HTMLAnchorElement;
              oktaBackButtonLink.addEventListener('click', (event: MouseEvent) => {
                this.isForgotEmailCheckingDone = false;
                this.isOktaForgetPassword = false;
              });
            } else {
              this.authService.forgotPassword({ emailId : params.username }).subscribe((result) => {
                if (result.status === 40) {
                  const data = { userEmail: params.username };
                  this.sharedService.trackActivity({
                    type: Activity.userAccess,
                    name: Activity.forgotPasswordException,
                    des: { data, desConstant: Activity.forgotPasswordExceptionDes }
                  });
                  const failureAlertMesaage = this.common.getTranslateData('ERROR_MESSAGES.FAILURE_ALERT_MESSAGE');
                  this.common.showMessage(failureAlertMesaage);
                } else {
                  const data = { userEmail: params.username };
                  this.sharedService.trackActivity({
                    type: Activity.userAccess,
                    name: Activity.forgotPassword,
                    des: { data, desConstant: Activity.forgotPasswordDes }
                  });
                  const successAlertMessage = this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESS_ALERT_MESSAGE');
                  this.common.showMessage(successAlertMessage);
                  this.goBackLogin();
                }
              });
            }
          },
          (error) => {
            console.log(error);
          }
        );
      }
    };
    resetEmailButton.addEventListener('click', handleClick, true);
  }

  goBackLogin() {
    this.oktaLogin = false;
    this.isOktaForgetPassword = false;
    const forgotPasswordLink = document.querySelector('.link.help.js-back') as HTMLAnchorElement;
    if (forgotPasswordLink) {
      forgotPasswordLink.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
    }
    this.isForgotEmailCheckingDone = false;
    if (this.isMfaVerify) {
      const mfaOktaBackButton = document.querySelector('.link.goto') as HTMLAnchorElement;
      if (mfaOktaBackButton) {
        mfaOktaBackButton.dispatchEvent(new MouseEvent('click', { bubbles: true, cancelable: true }));
        this.isEmailCheckingDone = false;
      }
    }
  }

  getUsernameContainerControl(): HTMLDivElement {
    return document.getElementsByClassName('o-form-fieldset-container')[0].children[0] as HTMLDivElement;
  }

  getPasswordContainerControl(): HTMLDivElement {
    return document.getElementsByClassName('o-form-fieldset-container')[0].children[1] as HTMLDivElement;
  }

  hidePassword(): void {
    const passwordContainer = this.getPasswordContainerControl();
    if (passwordContainer) {
      this.getPasswordControl().value = '';
      passwordContainer.style.display = 'none';
    }
  }

  showPassword(): void {
    const passwordContainer = this.getPasswordContainerControl();
    if (passwordContainer) {
      this.getPasswordControl().value = '';
      passwordContainer.style.display = 'block';
    }
  }

  hideUsername(): void {
    const usernameContainer = this.getUsernameContainerControl();
    if (usernameContainer) {
      this.getUsernameControl().setAttribute('readonly', 'readonly');
      usernameContainer.style.display = 'none';
    }
  }

  showUsername(): void {
    const usernameContainer = this.getUsernameContainerControl();
    if (usernameContainer) {
      this.getUsernameControl().removeAttribute('readonly');
      usernameContainer.style.display = 'block';
    }
  }

  getUsernameControl(): HTMLInputElement {
    return document.getElementById('okta-signin-username') as HTMLInputElement;
  }

  getPasswordControl(): HTMLInputElement {
    return document.getElementById('okta-signin-password') as HTMLInputElement;
  }

  getForgotEmailControl(): HTMLInputElement {
    return document.getElementById('account-recovery-username') as HTMLInputElement;
  }
  /**
   * fingerprintLogin function used to login with fingerprint.
   * @param username string
   */
  fingerprintLogin(username: string): void {
    const verifyMessage = this.common.getTranslateData('MESSAGES.USE_FINGER_TO_LOGIN');
    if (this.platform.is('capacitor')) {
      this.keychainTouchId.isAvailable().then((res: string) => {
        // For ios device res return null
        const checkTouchIDAvailable = this.platform.is('ios') ? Constants.yesValue : res.toLowerCase();
        if (checkTouchIDAvailable == Constants.yesValue) {
          this.isTouchIdAvailable = true;
          this.isTouchIdLogin = false;
          if (isPresent(username) && !this.sharedService.userData) {
            this.keychainTouchId.has(username).then((val: any) => {
              // For ios device val return null
              /* Return OK when user is registered, throw error when false. No need to handle when user is not registered at startup */
              const checkTouchIDMatch = this.platform.is('ios') ? Constants.okValue : val.toLowerCase();
              if (checkTouchIDMatch == Constants.okValue) {
                this.keychainTouchId.verify(username, verifyMessage).then((value: string) => {
                  this.isTouchIdLogin = true;
                  if (navigator.onLine) {
                    /* Return stored password for username input */
                    this.loginForm.patchValue({ username, password: value });
                    // this.login();
                    if (localStorage.getItem(Constants.storageKeys.touchIdOktaAuth) === Constants.trueAsString) {
                      this.syncOktaFormWithLoginForm();
                      this.handleAuthCheck(true);
                    } else {
                      this.isOtpAnd2faEnabledForTenant();
                    }
                  } else {
                    this.persistentService.getOfflineUserData(username).then((data) => {
                      if (data) {
                        this.sharedService.userData = JSON.parse(data.userData);
                        if (this.sharedService.isOfflineFormsEnabled()) {
                          this.sharedService.offlineLogin = true;
                          this.sharedService.setPermissions(this.sharedService.userData.privileges);
                          this.loginForm.reset();
                          this.navController.navigateForward(PageRoutes.offlineForms);
                        }
                      }
                    });
                  }
                });
              }
            });
          }
        }
      });
    }
  }

  login(hasOktaAuthToken = false): void {
    const invalidEmailPassword = this.common.getTranslateData('VALIDATION_MESSAGES.INVALID_EMAIL_PASSWORD');
    if (
      !this.checkEmail() ||
      (this.otp2faInfo.twoFaEnabled && !this.checkPasswordAndOTP()) ||
      (!this.otpInfo.loginWithOtp && !this.checkPassword()) ||
      (this.otpInfo.loginWithOtp && !this.checkOTPisValid())
    ) {
      return;
    }
    if (this.loginForm.valid) {
      // Save username if remember is checked
      this.manageUsernamePersistence(this.loginForm.get('username')?.value);
      let params = this.loginForm.value;
      if (this.otpInfo.loginWithOtp || this.otp2faInfo.twoFaEnabled) {
        params = {
          ...this.loginForm.value,
          otp: this.otp,
          password: this.otp2faInfo.twoFaEnabled ? params.password : '',
          sessionId: this.otpInfo.sessionId
        };
      }
      if (this.oktaEnabled && !this.loginWithOtp) {
        params = {
          ...this.loginForm.value,
          accessKey: btoa(params.password)
        };
      }
      if (this.showIdmMigrationLoader) {
        this.sharedService.loaderMessage = 'LOADER_MESSAGES.IDM_MIGRATION_LOADER';
      }
      this.authService.doLogin(params, hasOktaAuthToken).subscribe(
        (result: LoginResponse) => {
          this.sharedService.loaderMessage = '';
          if (result && this.platform.is('capacitor')) {
            const username = this.loginForm.value.username || localStorage.getItem(Constants.storageKeys.touchIdKey);
            this.persistentService.getOfflineUserData(username).then((data) => {
              if (data) {
                this.persistentService.updateOfflineUserData(data.id, JSON.stringify(result)).then(() => {
                })
              } else {
                this.persistentService.addOfflineUser(result.tenantId, username, JSON.stringify(result)).then(() => {
                })
              }
            });
          }
          if (hasOktaAuthToken) {
            localStorage.setItem(Constants.storageKeys.oktaLogin, Constants.configTrue);
          }
          if (result && result.idmMigrated && result.ssoId && !result.first_login) {
            this.showIdmMigrationLoader = false;
            this.renderOktaSignInWidget();
            this.common.showToast({ message: this.common.getTranslateData('MESSAGES.IDM_MIGRATION_COMPLETED') });
            this.sharedService.logout();
          } else {
            this.manageDataAfterLoginSuccess(result);
          }
        },
        (error) => {
          // API error
          this.sharedService.loaderMessage = '';
          this.ngOtpInput && this.ngOtpInput.clear();
          this.common.showMessage(invalidEmailPassword);
          this.trackErrorLogin(invalidEmailPassword);
        }
      );
    }
  }

  manageDataAfterLoginSuccess(result: any): void {
    this.common.dismissPopover();
    const invalidEmailPassword = this.common.getTranslateData('VALIDATION_MESSAGES.INVALID_EMAIL_PASSWORD');
    if (!isBlank(result.userId)) {
      // TODO: There is no API is to get the user details. Need to udate here when new API available.
      result.userName = this.loginForm.value.username;
      this.sharedService.userData = result;
      this.sharedService.setPermissions(result.privileges);
      sessionStorage.setItem(Constants.storageKeys.isVirtual, Constants.falseAsString);
      setKeyToSession({ key: Constants.storageKeys.authToken, value: result.authenticationToken });
      localStorage.setItem(Constants.storageKeys.showBanner, Constants.trueAsString);
      const showSiteSelection =
        this.sharedService.isEnableConfig(Config.enableMultiSite) && result.group !== String(Constants.patientGroupId) && result.mySites.length > 1;
      if (showSiteSelection) {
        let selectedSiteFilter = result.mySites;
        if (!isBlank(result.defaultSitesFilter)) {
          selectedSiteFilter = result.defaultSitesFilter;
        }
        this.sessionService.setDefaultSelectedIds(selectedSiteFilter);
      }
      this.socketService.socketInit = true;
      this.loadPreAPICalls();
      this.sharedService.joinAppSocket();
      this.sharedService.setSessionTimeoutTime();
      this.initVideoChat();

      const data = {
        userName: this.loginForm.value.username,
        activeLogins: '0'
      };
      this.resetOtpInfo();
      // TODO: Active logins
      this.sharedService.trackActivity({
        type: Activity.userAccess,
        name: Activity.userLogin,
        des: { data, desConstant: Activity.login }
      });
      this.sharedService.updateFirstLogin(result);
      if (result.first_login && result.status === Constants.userFirsrtLoginStatus) {
        const oldPassword = this.loginForm.value.password;
        this.loginForm.reset();
        this.resetOtpInfo();
        const navigationExtras: NavigationExtras = {
          state: {
            oldPassword
          }
        };
        this.router.navigate(['reset-password'], navigationExtras);
      } else if (result?.status === Constants.loginResponseStatus.pendingActivation) {
        this.common.showMessage(this.pendingAccountActivationMessage);
        this.trackErrorLogin(result.message);
      } else if (result?.status === Constants.loginResponseStatus.disable || result?.status === Constants.loginResponseStatus.disableAccount) {
        const disabledMessage = this.common.getTranslateData('VALIDATION_MESSAGES.ACCOUNT_DISABLED_CONTACT');
        this.common.showMessage(disabledMessage);
        this.trackErrorLogin(result.message);
      } else if (result?.status === Constants.loginResponseStatus.dischargeUser) {
        this.common.showMessage(this.dischargedUserMessage);
        this.trackErrorLogin(result.message);
      } else {
        if (result.first_login && this.sharedService.loggedUserIsPatient()) {
          const config = {
            message: this.sharedService.getConfigValue(Config.newPatientWelcomeMessage),
            buttons: [
              {
                text: this.common.getTranslateData('BUTTONS.OK'),
                confirm: false,
                class: 'cancel-btn'
              }
            ]
          };
          this.common.showAlert(config);
        }
        const user = this.loginForm.value;
        if (this.platform.is('capacitor') && user.password && !this.isTouchIdLogin && this.isTouchIdAvailable) {
          localStorage.setItem(Constants.storageKeys.touchIdKey, user.username);
          localStorage.setItem(
            Constants.storageKeys.touchIdOktaAuth,
            localStorage.getItem(Constants.storageKeys.oktaLogin) ? Constants.trueAsString : Constants.falseAsString
          );
          this.keychainTouchId.has(user.username).then(
            (val: any) => {
              /* Return Ok when user is registered, return error when false.  */
              this.keychainTouchId.save(user.username, user.password, true);
              this.routeAfterLogin();
            },
            (error) => {
              /* User login credentials stored here */
              this.keychainTouchId.save(user.username, user.password, true);
              this.routeAfterLogin();
            }
          );
        } else {
          this.routeAfterLogin();
        }
      }
    } else if (result?.code === Constants.loginResponseStatus.invalid || result?.code === Constants.loginResponseStatus.Unauthorized) {
      this.common.showMessage(invalidEmailPassword);
      this.trackErrorLogin(invalidEmailPassword);
    } else if (result?.status === Constants.loginResponseStatus.pendingActivation) {
      this.common.showMessage(this.pendingAccountActivationMessage);
      this.trackErrorLogin(result.message);
    } else if (result?.status === Constants.loginResponseStatus.disable || result?.status === Constants.loginResponseStatus.disableAccount) {
      const disabledMessage = this.common.getTranslateData('VALIDATION_MESSAGES.ACCOUNT_DISABLED_CONTACT');
      this.common.showMessage(disabledMessage);
      this.trackErrorLogin(result.message);
    } else if (result?.status === Constants.loginResponseStatus.dischargeUser) {
      this.common.showMessage(this.dischargedUserMessage);
      this.trackErrorLogin(result.message);
    } else if ((result.status !== undefined || !result.userId) && !isBlank(result.message)) {
      // Handle the error case
      // Show message from API user not exists
      this.common.showMessage(result.message);
      this.ngOtpInput && this.ngOtpInput.clear();
      this.trackErrorLogin(result.message);
    }
  }
  routeAfterLogin(): void {
    if (this.oktaLoginFlow) {
      this.oktaSignIn.remove();
    }

    // Ensure auth browser is closed before navigating to main app
    this.closeAuthBrowser();

    // redirecting to push notification link
    if (!isBlank(this.sharedService.setRouterLink)) {
      this.router.navigateByUrl(this.sharedService.setRouterLink, { replaceUrl: true });
    } else {
      this.router.navigateByUrl('home', { replaceUrl: true });
      this.loginForm.reset();
    }
    this.sharedService.setRouterLink = '';
  }
  loadPreAPICalls(): void {
    this.messageCenterService.updateDeliveryMessages();
    this.messageCenterService.resendAllFailedMessages();
  }
  initVideoChat(): void {
    this.sharedService
      .generateVidyoTocken({
        source: VideoCall.generateVidyoSource,
        userName: `${this.sharedService.userData.displayName}-${this.sharedService.userData.tenantName}`
      })
      .subscribe((tokenData) => {
        this.sharedService.vidyoToken = tokenData.token;
        setKeyToSession({ key: Constants.storageKeys.vidyoToken, value: tokenData.token });
        this.videoCallService.callAllFunction();
      });
    const options = {
      maxParticipants: VideoCall.maxParticipants,
      userData: this.sharedService.userData
    };
    if (!this.videoCallService.checkVideoPluginLoaded()) {
      this.videoCallService.init('vidyo', options);
    }
  }
  trackErrorLogin(message: any): void {
    const data = {
      userName: this.loginForm.value.username,
      message
    };
    this.sharedService.trackActivity({
      type: Activity.userAccess,
      name: Activity.authException,
      des: { data, desConstant: Activity.authExceptionDescription }
    });
  }
  consoloWorkflow(params: any): void {
    let socketIo: any;
    const code = params.code ? params.code : undefined;
    const clientId = params.clientId;
    socketIo = this.socketService.socket.io;
    this.socketService.isConnected();
    if (isBlank(code)) {
      const reqData = {
        externalId: clientId
      };
      const client = socketIo.engine.id;
      const urlParams = `action=config&externalId=${encodeURIComponent(reqData.externalId)}&client=${client}`;
      this.httpService
        .doPost({
          endpoint: APIs.chatWindowWrapperAuthenticateForgerok,
          payload: urlParams,
          contentType: 'form'
        })
        .subscribe((res) => {
          if (res && res.status === 1) {
            const tenantConfigurations = res.data;
            const baseUrl = tenantConfigurations.consolo_baseurl;
            const authorizeUrl = tenantConfigurations.consolo_authorize;
            const consoloclientId = tenantConfigurations.consolo_clientId;
            const scope = tenantConfigurations.consolo_scope;
            const oldUrl = escape(`${window.location.href}&`);
            const state =
              (new Date().getTime() * 0x10000).toString(15).substring(2) +
              'Citus' +
              (new Date().getUTCMilliseconds() * 0x10000).toString(15).substring(2);
            const redirectUrl = `${authorizeUrl}?scope=${scope}&response_type=code&client_id=${consoloclientId}&state=${state}&redirect_uri=${oldUrl}`;
            window.location.replace(redirectUrl);
          } else {
            this.sharedService.consoloLoader = false;
            this.isConsolo = false;
            this.common.showMessage(res.message);
          }
        });
    } else {
      const patientMrn = params.patientMRN;
      const type = params.type === Signature.documentCategories ? 1 : params.type === Constants.Forms ? 2 : 0;
      const documentCategoryId = type === 1 ? params.documentCategoryId : params.formId;
      const callbackUrl = !isBlank(params.callbackUrl) ? params.callbackUrl : false;
      const externalDocumentId = !isBlank(params.externalDocumentId) ? params.externalDocumentId : '';
      let reqData: any;
      reqData = {
        code,
        patientMrn,
        categoryId: documentCategoryId,
        type,
        externalId: clientId
      };
      reqData.date = getCurrentDate(Constants.dateFormat.yymmdd);
      reqData.dayNumber = getCurrentDayNumber();
      reqData.currentTime = getCurrentTime();
      reqData.clientLoginedTimeZone = new Date().getTimezoneOffset() * -1;
      reqData.client = socketIo.engine.id;
      const urlParams = `categoryId=${reqData.categoryId}&type=${encodeURIComponent(reqData.type)}&patientMrn=${encodeURIComponent(
        reqData.patientMrn
      )}&externalId=${encodeURIComponent(reqData.externalId)}&date=${reqData.date}&dayNumber=${reqData.dayNumber}&currentTime=${reqData.currentTime
        }&clientLoginedTimeZone=${reqData.clientLoginedTimeZone}&client=${reqData.client}&platform=${this.sharedService.platformValue}&code=${reqData.code
        }`;
      this.httpService
        .doPost({
          endpoint: APIs.chatWindowWrapperAuthenticateForgerok,
          payload: urlParams,
          contentType: 'form'
        })
        .subscribe((response) => {
          if (response && parseInt(response.userStatus, 10) === 1 && !isBlank(response.userId)) {
            const patientDetails = response.patientDetails;
            this.sharedService.disableSideMenu = false;
            this.sharedService.automaticLinkedItems = {
              type,
              patientMrn: patientDetails.mrn,
              patientGuid: patientDetails.guid,
              callbackUrl,
              externalDocumentId,
              patient: {
                id: patientDetails.id,
                displayName: patientDetails.displayName,
                firstName: patientDetails.firstName,
                lastName: patientDetails.lastName,
                dob: patientDetails.dob,
                gender: patientDetails.gender,
                mobile: patientDetails.mobile,
                email: patientDetails.email
              }
            };
            if (type === 1) {
              (this.sharedService.automaticLinkedItems.documentCategoryId = documentCategoryId),
                (this.sharedService.automaticLinkedItems.recipients = {
                  id: 0,
                  displayName: '',
                  cmisId: 0,
                  careGiver: {
                    id: 0,
                    displayName: ''
                  }
                }),
                (this.sharedService.automaticLinkedItems.associate = {
                  id: 0,
                  displayName: '',
                  cmisId: 0
                }),
                (this.sharedService.automaticLinkedItems.fileDetails = response.fileDetails),
                (this.sharedService.automaticLinkedItems.documentTypeDetails = response.documentTypeDetails);
              this.sharedService.consoloUserData = response;
              this.sharedService.userData = response;
              if (response?.privileges) {
                this.sharedService.setPermissions(response.privileges);
              }
              sessionStorage.setItem(Constants.storageKeys.isVirtual, Constants.trueAsString);
              setKeyToSession({ key: Constants.storageKeys.authToken, value: response.authenticationToken });
              this.sharedService.setSessionTimeoutTime();
              this.common.redirectToPage('document-center/select-document');
            } else if (type === 2) {
              this.sharedService.disableSideMenu = true;
              this.sharedService.automaticLinkedItems.formId = documentCategoryId;
              this.sharedService.consoloCheckFormType(documentCategoryId).subscribe((result: any) => {
                if (result && result.status === Constants.active && result.type) {
                  this.sharedService.consoloUserData = response;
                  this.sharedService.userData = response;
                  this.sharedService.automaticLinkedItems.formType = result.type;
                  this.sharedService.automaticLinkedItems.formName = result.form_name;
                  sessionStorage.setItem(Constants.storageKeys.isVirtual, Constants.trueAsString);
                  setKeyToSession({ key: Constants.storageKeys.authToken, value: response.authenticationToken });
                  if (response?.privileges) {
                    this.sharedService.setPermissions(response.privileges);
                  }
                  this.sharedService.setSessionTimeoutTime();
                  const form: any = {
                    facing: Constants.staffValue,
                    form: {
                      externalDocumentId: externalDocumentId,
                      patientData: encodeURIComponent(JSON.stringify(this.sharedService.automaticLinkedItems.patient)),
                      patientMRN: encodeURIComponent(patientDetails.mrn),
                      formId: documentCategoryId,
                      formName: result.form_name,
                      name: result.form_name,
                      staffFacing: true,
                      patientId: this.sharedService.automaticLinkedItems.patient.id,
                      loginUserId: this.sharedService.automaticLinkedItems.patient.id,
                      patient_id: this.sharedService.automaticLinkedItems.patient.id,
                      patientName: this.sharedService.automaticLinkedItems.patient.displayName,
                      id: documentCategoryId
                    }
                  };
                  this.sharedService.consoloLoader = false;
                  const navigationExtras: NavigationExtras = {
                    state: {
                      viewData: form
                    }
                  };
                  this.router.navigate([`.${PageRoutes.viewForms}`], navigationExtras);
                }
              });
            }
          } else {
            this.sharedService.consoloLoader = false;
            this.isConsolo = false;
            this.common.showMessage(response.message);
          }
        });
    }
  }

  showHidePassword(): void {
    this.isShowPassword = !this.isShowPassword;
  }

  /**
   * check platform mobile/web
   */
  checkPlatformToShowSSOButton(): void {
    this.isMobilePlatform = this.platform.is('capacitor');
  }

  checkEmail(): boolean {
    if (isBlank(this.loginForm.value.username)) {
      const emailAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.USERNAME_REQUIRED');
      this.common.showMessage(emailAlertMessage);
      return false;
    } else if (!this.loginForm.controls.username.valid) {
      const emailAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.EMAIL_PATTERN_ERROR');
      this.common.showMessage(emailAlertMessage);
      return false;
    }
    return true;
  }

  checkPassword(): boolean {
    if (isBlank(this.loginForm.value.password) && !this.loginWithOtp) {
      const passwordAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.PASSWORD_REQUIRED');
      this.common.showMessage(passwordAlertMessage);
      return false;
    }
    return true;
  }

  /**
   * CP-4157 check user auth email
   */
  getAuthAppURL(): void {
    if (this.oktaLoginFlow) {
      this.setLoginFormValues();
    }
    if (this.isEmailCheckingDone) {
      if (this.oktaLoginFlow && this.oktaLogin && !this.loginWithOtp) {
        if (this.checkPassword()) {
          this.clickOktaSignin();
        }
      } else {
        this.login();
      }
      return;
    }
    this.resetPassword();
    if (this.checkEmail()) {
      this.resetPassword();
      this.handleAuthCheck();
    }
  }

  handleAuthCheck(isFingerprintLogin = false) {
    const params = {
      domain: this.getEmailDomain(),
      username: this.username.value
    };
    this.authService.doCheckAuth(params).subscribe(
      (response: AppAuthURLResponse) => {
        // TODO: handle the login and redirection
        // 1. if  SSO enabled --> SSO redirection flow
        // 2. OKTA Enabled + ssoId --> Enables the oktaLogin
        // 3. Normal ICAMPP flow
        const isTalentlmsEnabled = response?.talentlmsEnabled && !isBlank(response?.talentlmsIdpId);
        sessionStorage.setItem('talentlmsEnabled', JSON.stringify(isTalentlmsEnabled));
        sessionStorage.setItem('oktaEnabled', JSON.stringify(response.oktaEnabled));
        sessionStorage.setItem('ssoId', response?.ssoId ? response.ssoId : '');
        if (response.app_auth_link) {
          sessionStorage.setItem('isSSOLogin', 'true');
          this.isIdpRedirectionProcess = true;
          if (isTalentlmsEnabled && this.oktaLoginFlow && !this.platform.is('ios')) {
            localStorage.setItem(Constants.storageKeys.idpAppAuthLink, response.app_auth_link);
            this.handleTalentLMSRedirect(response.talentlmsIdpId);
          } else {
            this.idpConfigFetch(response.app_auth_link);
          }
        } else {
          if (response.oktaEnabled) {
            this.oktaEnabled = true;
            if (response.ssoId) {
              this.oktaLogin = true;
            } else {
              this.showIdmMigrationLoader = true;
            }
          }
          this.isOtpAnd2faEnabledForTenant(isFingerprintLogin);
        }
      },
      (error) => {
        console.log(error);
      }
    );
  }
  handleTalentLMSRedirect(talentlmsIdpId: string): void {
    if (this.oktaSignIn && !isBlank(talentlmsIdpId)) {
      sessionStorage.setItem('isSSOLogin', 'true');
      sessionStorage.setItem('oktaEnabled', 'true');
      sessionStorage.setItem('ssoFirstLogin', 'true');
      if (this.platform.is('capacitor')) {
        if (this.platform.is('ios')) {
          // Use iOS-specific authentication with session sharing
          this.openTalentLMSiOSAuth(talentlmsIdpId);
        } else {
          // Use InAppBrowser for Android
          this.openTalentLMSInAppBrowser(talentlmsIdpId);
        }
      } else {
        // Web fallback
        this.oktaSignIn.authClient.token.getWithRedirect({
          redirectUri: this.redirectUri,
          idp: talentlmsIdpId,
          responseType: ['code'],
          scopes: ['openid', 'profile', 'email']
        });
      }
    }
  }

  openTalentLMSInAppBrowser(talentlmsIdpId: string): void {
    try {
      const authUrl = this.buildOktaAuthorizationUrl(talentlmsIdpId);
      this.sharedService.browser = this.inAppBrowser.create(authUrl, '_blank', {
        location: 'no',
        toolbar: 'yes',
        clearcache: 'yes',
        zoom: 'no',
        closebuttoncolor: '#FFFFFF',
        toolbarcolor: getComputedStyleProperty('--ion-color-primary') || '#3880ff',
        closebuttoncaption: 'Back to App',
        navigationbuttoncolor: '#FFFFFF',
        enableViewportScale: 'yes',
        mediaPlaybackRequiresUserAction: 'no',
        allowInlineMediaPlaybook: 'yes',
        presentationstyle: 'pagesheet',
        transitionstyle: 'fliphorizontal',
        usewkwebview: 'yes'
      });
      this.currentAuthBrowser = this.sharedService.browser;
      this.authBrowserTimeout = setTimeout(() => {
        console.warn('Authentication timeout - closing browser');
        this.closeAuthBrowser();
        this.common.showMessage('Authentication timeout. Please try again.');
      }, 5 * 60 * 1000);

      this.currentAuthBrowser.on('loadstart').subscribe((event) => {
        const url = event.url;
        const redirectUri = this.oktaService.getOktaRedirectUri();
        this.isIdpRedirectionProcess = true;
        this.processIdpAuthenticationSuccess(url, redirectUri);
      });

      this.currentAuthBrowser.on('loadstop').subscribe((event) => {
        this.injectTalentLMSEnhancements(this.currentAuthBrowser);
      });

      this.startAuthPolling();

      this.currentAuthBrowser.on('exit').subscribe(() => {
        this.currentAuthBrowser = null;
        this.stopAuthPolling();
        this.sharedService.trackActivity({
          type: Activity.userAccess,
          name: Activity.ssoLogin,
          des: {
            data: {
              action: 'TALENT_LMS_BROWSER_CLOSED',
              idpId: talentlmsIdpId
            },
            desConstant: 'User closed TalentLMS authentication browser'
          }
        });
      });

      this.currentAuthBrowser.on('loaderror').subscribe(() => {
        this.closeAuthBrowser();
      });
    } catch (error) {
      this.oktaSignIn.authClient.token.getWithRedirect({
        redirectUri: this.redirectUri,
        idp: talentlmsIdpId,
        responseType: ['code'],
        scopes: ['openid', 'profile', 'email']
      });
    }
  }

  /**
   * iOS-specific TalentLMS authentication using ASWebAuthenticationSession
   * Provides better session sharing and automatic login capabilities
   */
  async openTalentLMSiOSAuth(talentlmsIdpId: string): Promise<void> {
    try {
      const authUrl = this.buildOktaAuthorizationUrl(talentlmsIdpId);
      const callbackScheme = 'citushealth'; // Your app's custom URL scheme

      // Track activity
      this.sharedService.trackActivity({
        type: Activity.userAccess,
        name: Activity.ssoLogin,
        des: {
          data: {
            action: 'TALENT_LMS_IOS_AUTH_STARTED',
            idpId: talentlmsIdpId
          },
          desConstant: 'Started TalentLMS iOS authentication session'
        }
      });

      // Use ASWebAuthenticationSession for better session sharing
      const result = await this.iosAuthSessionService.openTalentLMSAuth(authUrl, callbackScheme);

      if (result) {
        // Process the callback URL
        const redirectUri = this.oktaService.getOktaRedirectUri();
        this.processIdpAuthenticationSuccess(result, redirectUri);

        // Share session with Safari for automatic login
        await this.shareAuthSessionWithSafari();
      }
    } catch (error) {
      console.error('iOS TalentLMS authentication failed:', error);

      // Fallback to standard Okta redirect
      this.oktaSignIn.authClient.token.getWithRedirect({
        redirectUri: this.redirectUri,
        idp: talentlmsIdpId,
        responseType: ['code'],
        scopes: ['openid', 'profile', 'email']
      });
    }
  }

  /**
   * Shares authentication session with Safari for automatic login
   */
  private async shareAuthSessionWithSafari(): Promise<void> {
    try {
      if (this.platform.is('ios')) {
        // Get authentication token from session storage
        const authToken = sessionStorage.getItem('authToken');
        const domain = window.location.hostname;

        if (authToken) {
          // Inject authentication cookies into Safari session
          await this.iosAuthSessionService.injectAuthCookies([
            {
              name: 'auth_token',
              value: authToken,
              domain: domain,
              path: '/'
            },
            {
              name: 'session_id',
              value: sessionStorage.getItem('sessionId') || '',
              domain: domain,
              path: '/'
            }
          ]);
        }
      }
    } catch (error) {
      console.warn('Failed to share auth session with Safari:', error);
    }
  }

  private processIdpAuthenticationSuccess(url, redirectUri) {
    if (url.includes(redirectUri) && url.includes('code=')) {
      this.closeAuthBrowser();
      let passParams = {};
      const urlParams = new URLSearchParams(url.split('?')[1]);
      const code = urlParams.get('code');
      const state = urlParams.get('state');
      if (code) {
        passParams = {
          code: code,
          scheme: redirectUri,
          url: url,
          state: state
        };
        this.sharedService.ssoLoginValueGet.next(passParams);
      }
    }
  }
  private closeAuthBrowser(): void {
    if (this.authBrowserTimeout) {
      clearTimeout(this.authBrowserTimeout);
      this.authBrowserTimeout = null;
    }
    if (this.currentAuthBrowser) {
      try {
        this.isIdpRedirectionProcess = false;
        this.currentAuthBrowser.close();
      } catch (error) {
        throw error;
      } finally {
        this.currentAuthBrowser = null;
      }
    }
  }
  private openIdpSigninInAppBrowser(signinArgs: any): void {
    try {
      this.userManager.createSigninRequest(signinArgs).then((signinRequest) => {
        const authUrl = signinRequest.url;
        this.sharedService.browser = this.inAppBrowser.create(authUrl, '_blank', {
          location: 'no',
          toolbar: 'yes',
          clearcache: 'no',
          zoom: 'no',
          closebuttoncolor: '#FFFFFF',
          toolbarcolor: getComputedStyleProperty('--ion-color-primary') || '#3880ff',
          closebuttoncaption: 'Back to App',
          navigationbuttoncolor: '#FFFFFF',
          enableViewportScale: 'yes',
          mediaPlaybackRequiresUserAction: 'no',
          allowInlineMediaPlaybook: 'yes',
          presentationstyle: 'pagesheet',
          transitionstyle: 'fliphorizontal',
          usewkwebview: 'yes'
        });
        this.currentAuthBrowser = this.sharedService.browser;
        this.authBrowserTimeout = setTimeout(() => {
          this.closeAuthBrowser();
          this.common.showMessage('Authentication timeout. Please try again.');
        }, 5 * 60 * 1000);
        this.currentAuthBrowser.on('loadstart').subscribe((event) => {
          const url = event.url;
          this.isIdpRedirectionProcess = true;
          if (url.includes(this.redirectUri)) {
            this.currentAuthBrowser.close();
            this.processIdpAuthenticationSuccess(url, this.redirectUri);
          }
        });
        this.currentAuthBrowser.on('exit').subscribe(() => {
          this.currentAuthBrowser = null;
          this.sharedService.trackActivity({
            type: Activity.userAccess,
            name: Activity.ssoLogin,
            des: {
              data: {
                action: 'IDP_BROWSER_CLOSED',
                username: this.loginForm.value.username
              },
              desConstant: 'User closed IDP authentication browser'
            }
          });
        });
        this.currentAuthBrowser.on('loaderror').subscribe(() => {
          this.closeAuthBrowser();
        });
      }).catch((error) => {
        this.common.showMessage('Failed to initiate authentication. Please try again.');
      });
    } catch (error) {
      this.userManager.signinRedirect(signinArgs);
    }
  }

  private injectTalentLMSEnhancements(browser: InAppBrowserObject): void {
    browser.executeScript({
      code: `
        let mobileStyle = document.createElement('style');
        mobileStyle.innerHTML = \`
          body {
            font-size: 16px !important;
            -webkit-text-size-adjust: 100% !important;
          }
          input, textarea, select {
            font-size: 16px !important;
            -webkit-appearance: none !important;
          }
          #tlms-back-button {
            position: fixed !important;
            top: 15px !important;
            left: 15px !important;
            z-index: 99999 !important;
            background: ${getComputedStyleProperty('--ion-color-primary')} !important;
            color: white !important;
            border: 2px solid white !important;
            border-radius: 30px !important;
            padding: 14px 24px !important;
            font-size: 16px !important;
            font-weight: bold !important;
            box-shadow: 0 6px 20px rgba(0,0,0,0.4) !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            min-height: 48px !important;
            min-width: 120px !important;
            justify-content: center !important;
            text-align: center !important;
            user-select: none !important;
            -webkit-user-select: none !important;
            -webkit-tap-highlight-color: transparent !important;
            transform: scale(1) !important;
          }
          #tlms-back-button:active {
            transform: scale(0.95) !important;
            opacity: 0.8 !important;
          }
          #tlms-back-button:hover {
            opacity: 0.9 !important;
          }
        \`;
        document.head.appendChild(mobileStyle);
      `
    });

    browser.executeScript({
      code: `
        let backButton = document.createElement('button');
        backButton.id = 'tlms-back-button';
        backButton.innerHTML = '← Back to App';
        backButton.title = 'Return to CitusHealth App';
        backButton.type = 'button';

        function closeTalentLMSBrowser() {
          try {
            // Set flag for Angular component to detect
            localStorage.setItem('tlms_close_request', 'true');
            return true;
          } catch (e) {
            console.warn('Error setting close request flag:', e);
            return false;
          }
        }

        backButton.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          backButton.innerHTML = '⏳ Closing...';
          backButton.style.opacity = '0.7';
          if (!closeTalentLMSBrowser()) {
            setTimeout(() => {
              backButton.innerHTML = '← Back to App';
              backButton.style.opacity = '1';
              alert('Please tap the X button in the top toolbar to return to the CitusHealth app.');
            }, 1000);
          }
        });

        document.body.appendChild(backButton);
      `
    });
  }

  generateState(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
  get redirectUri() {
    return isPresent(this.sharedService.localConfig?.bundleIdentifier) &&
              isPresent(this.sharedService.appConfig?.themes) &&
              isPresent(this.sharedService.appConfig.themes[this.sharedService.localConfig.bundleIdentifier]) &&
              isPresent(this.sharedService.appConfig.themes[this.sharedService.localConfig.bundleIdentifier].redirect_uri)
              ? this.sharedService.appConfig.themes[this.sharedService.localConfig.bundleIdentifier].redirect_uri
              : ConfigValues.defaultRedirectURI;
  }
  setLoginFormValues(): void {
    this.loginForm.get('username').setValue(this.getUsernameControl()?.value.trim());
    this.loginForm.get('password').setValue(this.getPasswordControl()?.value);
  }

  syncOktaFormWithLoginForm() {
    const usernameControl = this.getUsernameControl();
    const passwordControl = this.getPasswordControl();

    usernameControl.removeAttribute('readonly');
    usernameControl.value = this.loginForm.value.username;
    passwordControl.value = this.loginForm.value.password;

    const inputEvent = new Event('input', { bubbles: true });
    usernameControl.dispatchEvent(inputEvent);
    passwordControl.dispatchEvent(inputEvent);
  }

  getEmailDomain(): string {
    const getDomainName: string = this.loginForm.value.username.substring(this.loginForm.value.username.lastIndexOf('@') + 1);
    return getDomainName;
  }

  resetPassword(): any {
    if (this.oktaLoginFlow) {
      this.hidePassword();
      this.showUsername();
      this.getPasswordControl().value = '';
    }
    this.isEmailCheckingDone = false;
    this.isTouchIdLogin = false;
    this.loginForm.controls['password'].setValue('');
    this.resetOtpInfo();
  }

  ionViewDidLeave(): void {
    this.isShowPassword = false;
    this.isEmailCheckingDone = this.isIdpRedirectionProcess = false;
    this.ssoLoginSubscribe.unsubscribe();
    // Ensure auth browser is closed when leaving the page
    this.stopAuthPolling();
    this.closeAuthBrowser();
  }

  startAuthPolling(): void {
    if (this.authPollingInterval) {
      clearInterval(this.authPollingInterval);
    }

    if (!this.currentAuthBrowser) {
      return;
    }

    this.authPollingInterval = setInterval(() => {
      if (this.currentAuthBrowser) {
        this.currentAuthBrowser
          .executeScript({ code: "localStorage.getItem('tlms_close_request');" })
          .then((values) => {
            if (values && values.indexOf('true') > -1) {
              this.currentAuthBrowser?.executeScript({ code: "localStorage.setItem('tlms_close_request', '');" });
              this.closeAuthBrowser();
            }
          })
          .catch(() => {});
      } else {
        this.stopAuthPolling();
      }
    }, 1000);
  }

  stopAuthPolling(): void {
    if (this.authPollingInterval) {
      clearInterval(this.authPollingInterval);
      this.authPollingInterval = null;
    }
  }

  ngOnDestroy(): void {
    this.stopAuthPolling();
    this.closeAuthBrowser();
  }

  private buildOktaAuthorizationUrl(talentlmsIdpId: string): string {
    try {
      const codeVerifier = this.generateRandomString(128);
      const codeChallenge = this.generateCodeChallenge(codeVerifier);
      const state = this.generateRandomString(64);
      const nonce = this.generateRandomString(64);
      sessionStorage.setItem('pkce_code_verifier', codeVerifier);
      const params = new URLSearchParams({
        client_id: environment.oktaClientId,
        code_challenge: codeChallenge,
        code_challenge_method: 'S256',
        idp: talentlmsIdpId,
        nonce: nonce,
        redirect_uri: this.oktaService.getOktaRedirectUri(),
        response_type: 'code',
        state: state,
        scope: 'openid profile email'
      });
      return `${environment.oktaIssuer}/v1/authorize?${params.toString()}`;
    } catch (error) {
      throw new Error('Failed to build authorization URL');
    }
  }
  private generateCodeChallenge(codeVerifier: string): string {
    try {
      if (!codeVerifier || typeof codeVerifier !== 'string' || codeVerifier.length < 43 || codeVerifier.length > 128) {
        return this.generateFallbackCodeChallenge();
      }
      // Step 1: Generate SHA256 hash of the code verifier
      const hash = CryptoJS.SHA256(codeVerifier);
      // Step 2: Convert hash to base64 string
      const base64 = CryptoJS.enc.Base64.stringify(hash);
      // Step 3: Convert base64 to base64url encoding
      const base64url = base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
      return base64url;
    } catch (error) {
      return this.generateFallbackCodeChallenge();
    }
  }

  private generateFallbackCodeChallenge(): string {
    const fallbackVerifier = this.generateRandomString(128);
    const fallbackHash = CryptoJS.SHA256(fallbackVerifier);
    const fallbackBase64 = CryptoJS.enc.Base64.stringify(fallbackHash);
    return fallbackBase64
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }
  private generateRandomString(length: number): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    let result = '';
    const values = new Uint8Array(length);

    if (window.crypto && window.crypto.getRandomValues) {
      window.crypto.getRandomValues(values);
      for (let i = 0; i < length; i++) {
        result += charset[values[i] % charset.length];
      }
    } else {
      for (let i = 0; i < length; i++) {
        result += charset[Math.floor(Math.random() * charset.length)];
      }
    }
    return result;
  };

  idpConfigFetch(authUrl: string): void {
    const socketIo: any = this.socketService.socket.io;
    const client = socketIo.engine.id;
    const urlParams = {
      loginplatform: 'idpclient',
      email_domain_name: this.getEmailDomain(),
      appAuthUrl: authUrl
    };
    this.httpService
      .doPost({
        endpoint: APIs.chatWindowWrapperAuthenticateForgerok,
        payload: urlParams,
        parseToString: true,
        contentType: 'form',
        extraParams: {
          action: 'config',
          client
        }
      })
      .subscribe((response: IdpConfigFetchResponse) => {
        if (response.status === 1 && response.data) {        
          const setting = {
            authority: response.data.consolo_baseurl,
            client_id: response.data.consolo_clientId,
            redirect_uri: this.redirectUri,
            scope: response.data.consolo_scope,
            post_logout_redirect_uri: this.redirectUri,
            response_type: response.data.response_type,
            loadUserInfo: true
          };
          localStorage.setItem(Constants.storageKeys.idpAppAuthLink, authUrl);
          localStorage.setItem(Constants.storageKeys.idpEmailDomainName, this.getEmailDomain());
          localStorage.setItem(Constants.storageKeys.idpEmail, this.loginForm.value.username);
          this.userManager = new Oidc.UserManager(setting);
          if (sessionStorage.getItem('talentlmsEnabled') === 'true' && !this.platform.is('ios')) {
            this.openIdpSigninInAppBrowser({
              login_hint: this.loginForm.value.username
            });
          } else {
            this.userManager.signinRedirect({
              login_hint: this.loginForm.value.username
            });
          }
          sessionStorage.setItem('ssoFirstLogin', 'false');
          this.sharedService.trackActivity({
            type: Activity.userAccess,
            name: Activity.ssoLogin,
            des: {
              data: {
                clientId: response.data.consolo_clientId,
                redirectUri: this.redirectUri,
                username: this.loginForm.value.username,
                baseUrl: response.data.consolo_baseurl
              },
              desConstant: Activity.ssologinDes
            }
          });
        }
      });
  }

  /**
   *
   * @param data Full URL,code,scheme
   */
  ssoLogin(data: any): void {
    const getState = data.url.includes('state') ? data.url.split('&state=')[1] : '';
    const payload = {
      ...data,
      ...this.authService.apiExtraData,
      state: getState,
      appAuthUrl: localStorage.idp_app_auth_link,
      email_domain_name: localStorage.idp_email_domain_name,
      loginplatform: 'idpclient'
    };
    this.httpService
      .doPost({
        endpoint: APIs.chatWindowWrapperAuthenticateForgerok,
        payload,
        extraParams: { bundleIdentifier: ConfigValues.bundleIdentifier },
        contentType: 'form',
        parseToString: true
      })
      .subscribe(
        (response: LoginResponse) => {
          // Ensure browser is closed after authentication attempt
          this.closeAuthBrowser();

          if (response.status === 0) {
            this.common.showMessage(response.message);
          } else {
            this.manageDataAfterLoginSuccess(response);
          }
        },
        (error: any) => {
          // Ensure browser is closed on error as well
          this.closeAuthBrowser();
          console.log('Error => ', error);
        }
      );
  }

  async showSignUPModal() {
    const modal = await this.modalController.create({
      component: SignUpComponent,
      initialBreakpoint: 0.3,
      breakpoints: [0.3, 0.5, 1]
    });
    modal.present();
  }

  goToForgotUsernamePage() {
    this.navController.navigateForward('forgot-username');
  }

  goToForgotPasswordPage() {
    if (this.oktaLoginFlow) {
      this.isEmailCheckingDone = false;
      this.customizeForgotPassword();
    } else {
      this.navController.navigateForward('forgot-password');
    }
  }

  otpInputConfig: NgxOtpInputConfig = {
    otpLength: 6,
    autofocus: true
  };

  handleOtpChange(value: string[]): void {
    this.otp = value.filter((e) => e).join('');
    if (this.otp.length === 6) {
      this.isOtpValid = true;
    } else {
      this.isOtpValid = false;
    }
  }

  handleFillEvent(value: string): void {
    this.otp = value;
  }

  loginWithOtpOrPassword(): void {
    this.loginWithOtp = !this.loginWithOtp;
    if (!this.loginWithOtp) {
      this.resetOtpInfo();
      if (this.oktaLoginFlow) {
        this.showPassword();
      }
    } else {
      if (this.oktaLoginFlow) {
        this.hidePassword();
      }
      this.sendOtp();
    }
  }

  sendOtp(): void {
    this.loginForm.get('password').clearValidators();
    this.loginForm.get('password').updateValueAndValidity();
    this.otpLoader = true;
    if (!this.otpInfo.sessionId) {
      this.httpService
        .doPost({
          endpoint: APIs.sendOtpEndpoint,
          payload: { username: this.username.value }
        })
        .pipe(
          finalize(() => {
            this.otpLoader = false;
          })
        )
        .subscribe(
          (res: OtpResponse) => {
            this.showExpiryTimer(res);
          },
          (error) => {
            this.loginWithOtp = !this.loginWithOtp;
            if (!this.loginWithOtp && this.oktaLoginFlow) {
              this.showPassword();
            }
            this.common.showMessage(error);
          }
        );
    } else {
      this.httpService
        .doPut({
          endpoint: APIs.reSendOtpEndpoint,
          payload: {
            username: this.username.value,
            sessionId: this.otpInfo.sessionId
          }
          // loader: true
        })
        .pipe(
          finalize(() => {
            this.otpLoader = false;
          })
        )
        .subscribe(
          (res: OtpResponse) => {
            this.showExpiryTimer(res);
          },
          (error) => {
            this.common.showMessage(error);
          }
        );
    }
  }

  showExpiryTimer(res): void {
    this.otpInfo = {
      loginWithOtp: this.loginWithOtp,
      resendOtp: false,
      otpExpiryInMins: '',
      sessionId: res.sessionId,
      otpSuccessMsg: res.message,
      otpErrorMsg: ''
    };
    let otpExpiryTime = res.otpExpiryTime;
    this.ngZone.runOutsideAngular(() => {
      this.ngZone.run(() => {
        this.expiryInterval = setInterval(() => {
          otpExpiryTime -= 1;
          const minutes: number = Math.floor(otpExpiryTime / 60);
          this.otpInfo.otpExpiryInMins = this.addZeroForMmSs(minutes) + ':' + this.addZeroForMmSs(otpExpiryTime - minutes * 60);
          if (otpExpiryTime === 0) {
            this.otpInfo.resendOtp = true;
            this.otpInfo.otpSuccessMsg = '';
            clearInterval(this.expiryInterval);
          }
        }, 1000);
      });
    });
  }

  addZeroForMmSs(value): string {
    return value < 10 ? '0' + value : value;
  }

  resetOtpInfo(): void {
    this.loginWithOtp = false;
    this.otpInfo = this.defaultOtpInfo;
    if (this.expiryInterval) {
      clearInterval(this.expiryInterval);
    }
    this.resetOtpFields();
  }

  resetOtpFields(): void {
    this.ngOtpInput && this.ngOtpInput.clear();
    this.loginForm.get('password').setValidators([Validators.required]);
    this.loginForm.get('password').updateValueAndValidity();
  }

  private get defaultOtpInfo() {
    return {
      loginWithOtp: false,
      resendOtp: false,
      otpExpiryInMins: '',
      sessionId: '',
      otpSuccessMsg: '',
      otpErrorMsg: ''
    };
  }

  isOtpAnd2faEnabledForTenant(isFingerprintLogin = false): void {
    this.httpService
      .doGet({
        endpoint: APIs.otpEnabledEndpoint.replace(/{username}/g, this.username.value)
      })
      .subscribe(
        (value: { otpEnabled: boolean; twoFaEnabled: boolean; otpResponse?: any; }) => {
          this.otp2faInfo = value;
          if (this.isTouchIdLogin && !value.twoFaEnabled) {
            if (localStorage.getItem(Constants.storageKeys.touchIdOktaAuth) === Constants.trueAsString) {
              this.clickOktaSignin();
            } else {
              this.login();
            }
          } else {
            this.isEmailCheckingDone = true;
            if (!isFingerprintLogin) {
              if (this.oktaLoginFlow) {
                this.hideUsername();
                this.showPassword();
              }
            }
            if (this.otp2faInfo.twoFaEnabled) {
              this.showExpiryTimer(value.otpResponse);
            }
          }
        },
        (error) => {
          this.common.showMessage(error);
        }
      );
  }

  checkOTPisValid(): boolean {
    if (!this.isOtpValid) {
      const otpAlertMessage = this.common.getTranslateData('VALIDATION_MESSAGES.PLEASE_ENTER_OTP');
      this.common.showMessage(otpAlertMessage);
      return false;
    }
    return true;
  }

  checkPasswordAndOTP() {
    if (!this.isOtpValid || isBlank(this.loginForm.value.password)) {
      const message = this.common.getTranslateData('VALIDATION_MESSAGES.PLEASE_ENTER_PASSWORD_AND_OTP');
      this.common.showMessage(message);
      return false;
    }
    return true;
  }
}
