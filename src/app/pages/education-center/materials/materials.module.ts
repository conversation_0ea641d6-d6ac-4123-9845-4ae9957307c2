import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { MaterialsPageRoutingModule } from './materials-routing.module';

import { MaterialsPage } from './materials.page';
import { SharedModule } from 'src/app/shared.module';
import { SearchBarModule } from 'src/app/components/search-bar/search-bar.module';
import { DataListModule } from 'src/app/components/data-list/data-list.module';
import { SelectListGridModule } from 'src/app/components/select-list-grid/select-list-grid.module';
import { DataGridModule } from 'src/app/components/data-grid/data-grid.module';
import { AdvancedViewerComponentModule } from 'src/app/components/advanced-viewer/advanced-viewer.module';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        MaterialsPageRoutingModule,
        SharedModule,
        SearchBarModule,
        DataListModule,
        SelectListGridModule,
        AdvancedViewerComponentModule,
        DataGridModule,
        ActionButtonModule
    ],
    declarations: [MaterialsPage]
})
export class MaterialsPageModule { }
