import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';

import { MaterialsPage } from './materials.page';
import { HttpClientModule } from '@angular/common/http';
import { TestConstants } from 'src/app/constants/test-constants';
import { throwError } from 'rxjs';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { Constants } from 'src/app/constants/constants';

describe('MaterialsPage', () => {
  let component: MaterialsPage;
  let fixture: ComponentFixture<MaterialsPage>;
  let sharedService: SharedService;
  let httpService: HttpService;
  let modalController: ModalController;
  let persistentService: PersistentService;
  let commonService: CommonService;
  const { modalSpy } = TestConstants;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [MaterialsPage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        InAppBrowser,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        SQLite,
        NativeStorage
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    sharedService = TestBed.inject(SharedService);
    httpService = TestBed.inject(HttpService);
    persistentService = TestBed.inject(PersistentService);
    commonService = TestBed.inject(CommonService);
    fixture = TestBed.createComponent(MaterialsPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should initialize the component and call getEduMaterials', () => {
    const storedSearchText = 'stored search text';
    spyOn(persistentService, 'getPersistentData').and.returnValue({ searchText: storedSearchText });
    spyOn(commonService, 'notifySearchFilterApplied');
    spyOn(component, 'getEduMaterials');
    component.ionViewWillEnter();
    expect(component.eduMaterials).toEqual([]);
    expect(component.searchText).toEqual(storedSearchText);
    expect(commonService.notifySearchFilterApplied).toHaveBeenCalledWith(true);
    expect(component.getEduMaterials).toHaveBeenCalled();
  });
  it('execute getEduMaterials : throw error', fakeAsync(() => {
    sharedService.userData = TestConstants.userData;
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.getEduMaterials();
    expect(component.getEduMaterials).toBeTruthy();
  }));

  it('should call searchMaterials function', () => {
    sharedService.userData = TestConstants.userData;
    spyOn(component.persistentService, 'setPersistentData');
    spyOn(component, 'getEduMaterials');
    component.searchMaterials({ text: 'test' });
    expect(component.searchMaterials).toBeDefined();
    expect(component.eduMaterials).toEqual([]);
    expect(component.searchText).toBe('test');
    expect(component.persistentService.setPersistentData).toHaveBeenCalledWith(Constants.storageKeys.searchTextEducationMaterials, { searchText: 'test' });
    expect(component.getEduMaterials).toHaveBeenCalled();
  });

  it('should call loadMaterials function', () => {
    spyOn(component.persistentService, 'removePersistentData');
    spyOn(component.commonService, 'notifySearchFilterApplied');
    spyOn(component, 'getEduMaterials');
    component.loadMaterials();
    expect(component.searchText).toEqual('');
    expect(component.eduMaterials).toEqual([]);
    expect(component.persistentService.removePersistentData).toHaveBeenCalledWith(Constants.storageKeys.searchTextEducationMaterials);
    expect(component.commonService.notifySearchFilterApplied).toHaveBeenCalledWith(false);
    expect(component.getEduMaterials).toHaveBeenCalled();
  });

  it('should call selectAction function', () => {
    sharedService.userData = TestConstants.userData;
    component.selectAction({ item: { id: '12', tenantId: '12' } });
    expect(component.selectAction).toBeDefined();
  });

  it('execute loadData', fakeAsync(() => {
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    component.loadData({ target: spy });
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(component.loadData).toBeTruthy();
    });
  }));
});
