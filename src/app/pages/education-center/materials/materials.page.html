<app-header headerTitle="MENU.EDUCATION_CENTER"></app-header>
<div class="site-header-padding"></div>
<app-search-bar (seachAction)="searchMaterials($event)" (closeSearch)="loadMaterials()" [searchText]="searchText"></app-search-bar>
<app-select-list-grid subHead="TITLES.EDUCATION_MATERIALS" (viewType)="isList=$event" [showIcons]="eduMaterials?.length"></app-select-list-grid>
<ion-content class="materials-page">
    <app-data-list *ngIf="isList" [listData]="eduMaterials" [extraData]="extraData"
        (itemBtnClick)="selectAction($event)">
    </app-data-list>
    <app-data-grid *ngIf="!isList" [gridData]="eduMaterials" [extraData]="extraData"
        (itemBtnClick)="selectAction($event)"></app-data-grid>
    <!-- TODO! CHP-3598-->
    <ion-infinite-scroll *ngIf="showLoadMore" threshold="100px" (ionInfinite)="loadData($event)">
        <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{'BUTTONS.LOAD_MORE' | translate}}">
        </ion-infinite-scroll-content>
    </ion-infinite-scroll>

</ion-content>
<app-action-button [button]="button"></app-action-button>
<app-footer></app-footer>