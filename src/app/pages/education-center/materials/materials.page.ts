import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Urls } from 'src/app/constants/urls';
import { Component } from '@angular/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { APIs } from 'src/app/constants/apis';
import { Constants, UserGroup } from 'src/app/constants/constants';
import { isBlank } from 'src/app/utils/utils';
import { environment } from 'src/environments/environment';
import { ModalController } from '@ionic/angular';
import { AdvancedViewerComponent } from 'src/app/components/advanced-viewer/advanced-viewer.component';
import { Activity } from 'src/app/constants/activity';
import { LoginResponse } from 'src/app/interfaces/login';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { CommonService } from 'src/app/services/common-service/common.service';

@Component({
  selector: 'app-materials',
  templateUrl: './materials.page.html',
  styleUrls: ['./materials.page.scss']
})
export class MaterialsPage {
  eduMaterials = [];
  userData: LoginResponse;
  searchText: string;
  includeTenants = 1;
  button: { iconCustom?: boolean; type?: string; buttonType?: string; buttonIcon?: string; colorTheme?: string } = {};
  pageCount = Constants.defaultPageCount;
  extraData: { showNoDataMessage?: boolean; search?: { text?: string }; image?: string; type?: string; trackBy?: string } = {};
  isList = true;
  showLoadMore: boolean;
  constructor(
    private readonly httpService: HttpService,
    private readonly modalController: ModalController,
    private readonly sharedService: SharedService,
    private inAppBrowser: InAppBrowser,
    public persistentService: PersistentService,
    public commonService: CommonService
  ) {
    this.userData = this.sharedService.userData;
    this.extraData = {
      image: 'icon/forms/form-grid.png',
      type: 'materials',
      showNoDataMessage: false,
      trackBy: 'id',
      search: {}
    };

    if (Number(this.sharedService.userData?.group) === UserGroup.PATIENT) {
      this.button = {
        iconCustom: false,
        type: 'new-chat',
        buttonType: 'ion-button',
        buttonIcon: 'chatbox-ellipses',
        colorTheme: 'de-york'
      };
    }
  }
  ionViewWillEnter() {
    this.eduMaterials = [];
    const storedSearchText = this.persistentService.getPersistentData(Constants.storageKeys.searchTextEducationMaterials);
    if (this.searchText) {
      this.commonService.notifySearchFilterApplied(true);
    } else {
      this.searchText = storedSearchText?.searchText;
      if (this.searchText) {
        this.commonService.notifySearchFilterApplied(true);
      }
    }
    this.getEduMaterials();
  }

  getEduMaterials(): void {
    this.showLoadMore = false;
    this.extraData.showNoDataMessage = false;
    const body = {
      toId: this.userData?.userId,
      tenantId: this.userData?.tenantId,
      searchKeyword: '',
      pageCount: this.pageCount,
      includeTenants: this.includeTenants
    };
    if (!isBlank(this.searchText)) {
      body.searchKeyword = this.searchText;
    }
    this.extraData.search = { text: this.searchText };
    this.httpService.doGet({ endpoint: APIs.getEduMaterials, extraParams: body, loader: false }).subscribe(
      (response) => {
        if (!isBlank(response)) {
          const materials = response.map((r) => ({
            ...r,
            displayLabel: r.materialName
          }));
          this.showLoadMore = materials.length >= Constants.defaultLimit + 10;
          this.eduMaterials = this.eduMaterials.concat(Object.assign(materials));
        } else if (isBlank(this.eduMaterials)) {
          this.extraData.showNoDataMessage = true;
        }
      },
      () => {
        if (isBlank(this.eduMaterials)) {
          this.extraData.showNoDataMessage = true;
        }
      }
    );
  }

  loadMore(): void {
    this.pageCount += 1;
    this.getEduMaterials();
  }

  searchMaterials(searchData): void {
    this.eduMaterials = [];
    this.searchText = searchData.text;
    if (!isBlank(this.searchText)) {
      this.persistentService.setPersistentData(Constants.storageKeys.searchTextEducationMaterials, { searchText: this.searchText });
    }
    this.getEduMaterials();
    this.sharedService.trackActivity({
      type: Activity.educationMaterial,
      name: Activity.searchEducationMaterial,
      des: {
        data: {
          displayName: this.sharedService.userData.displayName,
          keyWord: this.searchText
        },
        desConstant: Activity.searchEducationMaterialDes
      }
    });
  }

  loadMaterials(): void {
    this.eduMaterials = [];
    this.searchText = '';
    this.persistentService.removePersistentData(Constants.storageKeys.searchTextEducationMaterials);
    this.commonService.notifySearchFilterApplied(false);
    this.getEduMaterials();
  }

  async presentImageViewerModal(selectedMaterial): Promise<void> {
    const modal = await this.modalController.create({
      component: AdvancedViewerComponent,
      componentProps: {
        url: selectedMaterial.viewUrl,
        downloadUrl: selectedMaterial.viewUrl,
        type: 'pdf',
        name: selectedMaterial.name,
        id: selectedMaterial.id
      }
    });
    return modal.present();
  }

  selectAction(event): void {
    const viewUrl = `${environment.apiServer}${Urls.writableFilePath}education-materials/${event.item.tenantId}/${event.item.id}-education-material.pdf`;
    const materialDetails = {
      name: event.item.materialName,
      id: event.item.materialId,
      viewUrl
    };
    // TODO: In Mobile show pdf in system browser and for we show in "AdvancedViewerComponent"
    if (this.sharedService.platform.is('capacitor')) {
      this.inAppBrowser.create(viewUrl, '_system');
    } else {
      this.presentImageViewerModal(materialDetails);
    }
    this.sharedService.trackActivity({
      type: Activity.educationMaterial,
      name: Activity.viewEducationMaterial,
      des: {
        data: {
          displayName: this.sharedService.userData.displayName,
          materialName: event.item.materialName,
          fromName: event.item.fromName
        },
        desConstant: Activity.viewEducationMaterialDes
      }
    });
  }

  loadData(event) {
    // TODO! CHP-3598
    setTimeout(() => {
      event.target.complete();
      this.loadMore();
    }, 1500);
  }
}
