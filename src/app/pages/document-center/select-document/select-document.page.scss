.document-sub-header {
    border-bottom: 1px solid #e6e6e6;
    padding: 18px 5px 10px;

    .header-buttons {
        display: block;
        color: var(--ion-color-skin-secondary-bright);

        .start {
            float: left;
        }

        .end {
            float: right;
        }
    }
}

.select-document-page {
    .select-doc-wrapper {
        padding: 20px;

        .sign-label {
            @media (min-width: 320px) and (max-width: 600px) {
                width: 50%;
            }
            width: 25%;
            float: left;
            margin-top: 10px;
        }

        .sign-button {
            @media (min-width: 320px) and (max-width: 600px) {
                width: 50%;
            }
            width: 75%;
            margin-top: 4px;
            float: left;
            margin-bottom: 5px;
        }
    }

    .bgimg {
        background: url(/assets/images/select-list-arrow.png) no-repeat top right;
        background-color: #ffffff;
    }
    .file-form {
        display: none;
    }
    .choose-document-box {
        text-align: center;
        margin-top: 18px;

        .file-browse {
            background: #369db3;
        }

        .file-upload-name {
            color: #16665a;
            font-size: 16px;
            display: block;
            margin-top: 8px;
        }

        .file-upload-browse {
            height: 120px;
            width: 100px;
            display: block;
            margin: auto;
            font-size: 60px;
            color: #fff;

            ion-icon {
                height: 100%;
                margin: auto;
            }
        }

        .modal-close {
            width: 30px;
            height: 30px;
            position: absolute;
            background: var(--ion-color-skin-secondary-bright);
            border-radius: 50%;
            margin-left: 28px;
            top: 4px;
            padding: 0px;
            line-height: 32px;
            color: #ffffff;
        }
    }

    .file-selected {
        background: #fafafa;
        border: solid 1px #e6e6e6;
    }

    .file-img {
        margin-top: 10px;
        width: 100px;
        height: 100px;
        display: flex;
        img {
            object-fit: contain;
            width: auto;
            height: auto;
            min-width: 100%;
            min-height: 100%;
        }
    }
}

.disbaled-div {
    pointer-events: none;
    opacity: 0.3;
}
