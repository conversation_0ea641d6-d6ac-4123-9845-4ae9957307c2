import { CommonService } from 'src/app/services/common-service/common.service';
import { ActionSheetController, ModalController, IonicModule } from '@ionic/angular';
import { ViewDocumentPageModule } from 'src/app/pages/document-center/view-document/view-document.module';
import { RequestSignaturePageModule } from 'src/app/pages/document-center/request-signature/request-signature.module';
import { RouterTestingModule } from '@angular/router/testing';
import { Apollo } from 'apollo-angular';
import { ReactiveFormsModule } from '@angular/forms';
import { NgxPermissionsStore, NgxPermissionsService, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA, Component } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { SelectDocumentPage } from 'src/app/pages/document-center/select-document/select-document.page';
import { TestConstants } from 'src/app/constants/test-constants';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { of, throwError } from 'rxjs';
import { Constants, FolderType } from 'src/app/constants/constants';
import { Config } from 'src/app/constants/config';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Activity } from 'src/app/constants/activity';

@Component({
  selector: 'app-admission',
  template: ''
})
class AdmissionStubComponent {}
describe('SelectDocumentPage', () => {
  let component: SelectDocumentPage;
  let fixture: ComponentFixture<SelectDocumentPage>;
  let commonService: CommonService;
  let actionSheet: ActionSheetController;
  const actionSpy = TestConstants.actionSheetSpy;
  let modalController: ModalController;
  const { modalSpy } = TestConstants;
  let sharedService: SharedService;
  let graphqlService: GraphqlService;
  let httpService: HttpService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SelectDocumentPage, AdmissionStubComponent],
      imports: [
        IonicModule.forRoot(),
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        ReactiveFormsModule,
        RouterTestingModule.withRoutes([
          { path: 'document-center/request-signature', component: RequestSignaturePageModule },
          { path: 'view-document/:documentId/:senderTenant', component: ViewDocumentPageModule }
        ])
      ],
      providers: [
        Apollo,
        ActionSheetController,
        ModalController,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        CommonService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    sharedService.userData = {
      ...sharedService.userData,
      displayName: 'Alvin'
    };
    sharedService.userData.config = {
      ...sharedService.userData.config,
      enable_appless_model: 'true'
    };
    sharedService.localConfig = {
      ...sharedService.localConfig,
      apiVersion: 'v4',
      developerMode: {
        isEnabled: false,
        testCase: null
      }
    };
    commonService = TestBed.inject(CommonService);
    httpService = TestBed.inject(HttpService);
    spyOn(commonService, 'showMessage').and.stub();
    spyOn(commonService, 'redirectToPage').and.stub();
    spyOn(commonService, 'showAlert').and.stub();
    spyOn(commonService, 'showWarningAlert').and.returnValue(Promise.resolve(true));
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    actionSheet = TestBed.inject(ActionSheetController);
    spyOn(actionSheet, 'create').and.callFake(() => {
      return actionSpy;
    });
    const response = {
      data: {
        item: { folder: '' },
        actualFileName: '',
        date: '',
        recipient: {
          displayText: '',
          dob: '09/11/2010',
          passwordStatus: 'true',
          displayname: ''
        }
      }
    };
    actionSpy.present.and.stub();
    modalSpy.present.and.stub();
    spyOn(actionSheet, 'dismiss').and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    actionSpy.onDidDismiss.and.resolveTo(response);
    fixture = TestBed.createComponent(SelectDocumentPage);
    graphqlService = TestBed.inject(GraphqlService);
    component = fixture.componentInstance;
    component.selectDocState = { folder: { doctype: '', name: '' } };
    component.singleDocFile = {
      multiple: false
    };
    component.folder = component.selectDocState.folder;
    fixture.detectChanges();
  });
  // used to hide dom element from screen
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call change personal signature status', () => {
    component.data.allowPersonalSignature = true;
    component.changePersonalSignatureStatus(true);
    expect(component.changePersonalSignatureStatus).toBeDefined();
  });

  it('should call setPage', () => {
    component.setPage('', 'filingcenter');
    expect(component.setPage).toBeDefined();
  });

  it('should call check display text', () => {
    component.checkDisplayText('Pre signed, Snap & Send');
    component.checkDisplayText('Estimate Create & Send');
    component.checkDisplayText('Estimate Create, Save & Sign');
    component.checkDisplayText('Estimate Pre-signed Snap & Send');
    component.checkDisplayText('Pre-sign Snap & Send');
    component.checkDisplayText('Presigned Snap & Send');
    component.checkDisplayText('Create & Send');
    component.checkDisplayText('Create, Save & Sign');
    component.checkDisplayText('Pre-signed Snap & Send');
    component.checkDisplayText('Pre-Signed, Snap & Send');
    component.checkDisplayText('Pre-signed');
    component.checkDisplayText('Pre-Signed');
    component.checkDisplayText('Presigned');
    component.checkDisplayText('Snap & Send');
    expect(component.checkDisplayText).toBeDefined();
  });

  it('should call choose option for upload', () => {
    component.chooseOptionsForUpload();
    expect(component.chooseOptionsForUpload).toBeDefined();
  });

  it('should call chooseFromModal associate', fakeAsync(() => {
    const response = {
      data: {
        item: { folder: '' },
        actualFileName: '',
        date: '',
        data: {
          displayText: '',
          dob: '12/11/2010',
          passwordStatus: 'true',
          displayname: '',
          alternateContacts: [{ patientDisplayName: '', displayName: '' }]
        },
        alternateIndex: 0
      }
    };
    component.documentType = { allowAssociateRoles: true };
    modalSpy.onDidDismiss.and.resolveTo(response);
    component.chooseFromModal('associate');
    tick();
    expect(component.chooseFromModal).toBeTruthy();
  }));

  it('should call chooseFromModal patient', fakeAsync(() => {
    const response = {
      data: {
        item: { folder: '' },
        actualFileName: '',
        date: '',
        data: {
          displayText: '',
          dob: '11/11/2020',
          passwordStatus: 'true',
          displayname: '',
          alternateContacts: [{ patientDisplayName: '', displayName: '' }]
        },
        alternateIndex: 0
      }
    };
    component.documentType = { allowAssociateRoles: true };
    modalSpy.onDidDismiss.and.resolveTo(response);
    component.chooseFromModal('patient');
    tick();
    expect(component.chooseFromModal).toBeTruthy();
  }));

  it('should call chooseFromModal: Recipient', fakeAsync(() => {
    const response = {
      data: {
        item: { folder: '' },
        actualFileName: '',
        date: '',
        recipient: {
          displayText: '',
          dob: '09/11/2010',
          passwordStatus: 'true',
          displayname: ''
        }
      }
    };
    component.documentType = { allowAssociateRoles: true };
    modalSpy.onDidDismiss.and.resolveTo(response);
    component.chooseFromModal('recipient');
    tick();
    expect(component.chooseFromModal).toBeTruthy();
  }));

  it('should call removeFile', () => {
    component.removeFile();
    expect(component.removeFile).toBeDefined();
  });

  it('should call selectFileFromType', () => {
    component.selectFileFromType('');
    expect(component.selectFileFromType).toBeDefined();
  });

  it('should call validationForRecipients', () => {
    component.documentType = { allowRecipientRoles: true };
    component.validationForRecipients('');
    expect(component.validationForRecipients).toBeDefined();
  });
  it('should call validationForRecipients check allowAssociatePatient  case', () => {
    component.documentType = { allowAssociatePatient: true };
    component.data = { patient: '' };
    component.validationForRecipients('');
    expect(component.validationForRecipients).toBeDefined();
  });
  it('should call validationForRecipients check allowAssociateRoles  case', () => {
    component.documentType = { allowAssociateRoles: true };
    component.data = { associate: '' };
    component.validationForRecipients('');
    expect(component.validationForRecipients).toBeDefined();
  });
  it('should call setMetaData', () => {
    component.documentType = { enableApplessWorkflow: true };
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    component.setMetaData({});
    expect(component.setMetaData).toBeDefined();
  });
  it('should call trackActivity', () => {
    spyOn(sharedService, 'trackActivity').and.callThrough();
    component.trackActivity('');
    expect(component.trackActivity).toBeDefined();
  });
  it('should call removeGalleryFile', () => {
    component.removeGalleryFile(1);
    expect(component.removeGalleryFile).toBeDefined();
  });
  it('should call ionViewDidEnter', () => {
    component.ionViewDidEnter();
    expect(component.ionViewDidEnter).toBeDefined();
  });
  it('should call cancel', () => {
    component.cancel();
    expect(component.cancel).toBeDefined();
  });
  it('should call next', () => {
    component.documentType = { allowAssociateRoles: true };
    sharedService.isLoading = true;
    component.next();
    expect(component.next).toBeDefined();
  });

  it('should call checkConsoloValidations', () => {
    component.documentType = { allowRecipientRoles: true, id: 1 };
    component.consoloData = { recipients: { id: 1 } };
    component.checkConsoloValidations();
    expect(component.checkConsoloValidations).toBeDefined();
  });
  it('checkConsoloValidations function should check empty recipients', () => {
    component.documentType = { allowRecipientRoles: true, id: 1 };
    component.consoloData = { recipients: { id: null } };
    component.checkConsoloValidations();
    expect(component.checkConsoloValidations).toBeDefined();
  });
  it('checkConsoloValidations function check allowAssociateRoles ', () => {
    component.documentType = { allowAssociateRoles: true };
    component.consoloData = { recipients: { id: null }, associate: 'associate' };
    component.checkConsoloValidations();
    expect(component.checkConsoloValidations).toBeDefined();
  });
  it('checkConsoloValidations function check allowAssociatePatient ', () => {
    component.documentType = { allowAssociatePatient: true };
    component.consoloData = { patientGuid: 32142, recipients: { id: null }, associate: 'associate' };
    component.checkConsoloValidations();
    expect(component.checkConsoloValidations).toBeDefined();
  });
  it('checkConsoloValidations function check allowAssociatePatient and return error message ', () => {
    component.documentType = { allowAssociatePatient: true };
    component.consoloData = { patientGuid: null, recipients: { id: null }, associate: 'associate' };
    component.checkConsoloValidations();
    expect(component.checkConsoloValidations).toBeDefined();
  });
  it('should call obtainSignPollingData', () => {
    component.documentType = { notifyOnSubmitSignatureUsers: '[]' };
    component.obtainSignPollingData({});
    expect(component.obtainSignPollingData).toBeDefined();
  });
  it('should call redirectToPage', () => {
    component.redirectToPage({}, {});
    expect(component.redirectToPage).toBeDefined();
  });
  it('should call checkModalConditions', () => {
    component.checkModalConditions('');
    expect(component.checkModalConditions).toBeDefined();
  });

  it('should call setGalleryDocName', () => {
    expect(component.setGalleryDocName).toBeDefined();
  });

  it('setPatientPrePopulatedData : no input', () => {
    component.setPatientPrePopulatedData();
    expect(component.setPatientPrePopulatedData).toBeTruthy();
  });

  it('setPatientPrePopulatedData cover automaticLinkedItems', () => {
    sharedService.automaticLinkedItems = { patientMrn: 'hgf' };
    component.setPatientPrePopulatedData();
    expect(component.setPatientPrePopulatedData).toBeTruthy();
  });

  it('setPatientPrePopulatedData cover patient', () => {
    component.data.patient = { IdentityValue: 'g' };
    component.setPatientPrePopulatedData();
    expect(component.setPatientPrePopulatedData).toBeTruthy();
  });

  it('setPatientPrePopulatedData cover recipient', () => {
    component.data.recipient = { IdentityValue: 'g' };
    component.setPatientPrePopulatedData();
    expect(component.setPatientPrePopulatedData).toBeTruthy();
  });

  it('setPatientPrePopulatedData cover recipient is caregiver', () => {
    component.data.recipient = { IdentityValue: 'g', careGiver: { userName: '' } };
    component.setPatientPrePopulatedData();
    expect(component.setPatientPrePopulatedData).toBeTruthy();
  });

  it('setPatientPrePopulatedData cover recipient is alternate contact', () => {
    component.data.recipient = { IdentityValue: 'g', alternateContact: true };
    component.setPatientPrePopulatedData();
    expect(component.setPatientPrePopulatedData).toBeTruthy();
  });

  it('should update documentDisplayText and galleryDocDisplayText when allowFileNameEditing is true', () => {
    const event = {
      target: {
        value: 'newFileName'
      }
    };
    component.documentType = {
      allowFileNameEditing: true
    };
    component.changeFileName(event);
    expect(component.documentDisplayText).toEqual('newFileName');
    expect(component.galleryDocDisplayText).toEqual('newFileName');
  });

  it('should not update documentDisplayText and galleryDocDisplayText when allowFileNameEditing is false', () => {
    const event = {
      target: {
        value: 'newFileName'
      }
    };
    component.documentType = {
      allowFileNameEditing: false
    };
    const previousDocumentDisplayText = component.documentDisplayText;
    const previousGalleryDocDisplayText = component.galleryDocDisplayText;

    component.changeFileName(event);

    expect(component.documentDisplayText).toEqual(previousDocumentDisplayText);
    expect(component.galleryDocDisplayText).toEqual(previousGalleryDocDisplayText);
  });

  it('should not update documentDisplayText and galleryDocDisplayText when event is undefined', () => {
    const event = undefined;
    component.documentType = {
      allowFileNameEditing: true
    };
    const previousDocumentDisplayText = component.documentDisplayText;
    const previousGalleryDocDisplayText = component.galleryDocDisplayText;
    component.changeFileName(event);
    expect(component.documentDisplayText).toEqual(previousDocumentDisplayText);
    expect(component.galleryDocDisplayText).toEqual(previousGalleryDocDisplayText);
  });

  it('should set defaultFilingCenterFolder when data is present', async () => {
    const siteId = 123;
    const expectedData = { id: 1, name: 'Default Folder' };
    spyOn(graphqlService, 'fetchSites').and.returnValue(Promise.resolve(of(expectedData)));
    await component.fetchDefaultFolder(siteId);
    expect(component.defaultFilingCenterFolder).toBeDefined();
  });

  it('should not set defaultFilingCenterFolder when data is not present', async () => {
    const siteId = 123;
    spyOn(graphqlService, 'fetchSites').and.returnValue(Promise.resolve(null));
    const previousDefaultFolder = component.defaultFilingCenterFolder;
    await component.fetchDefaultFolder(siteId);
    expect(component.defaultFilingCenterFolder).toEqual(previousDefaultFolder);
  });
  it('execute selectFileFromType: source camera', () => {
    component.selectFileFromType(Constants.docSourceCam);
    expect(component.selectFileFromType).toBeTruthy();
  });

  it('execute selectFileFromType: source gallery', () => {
    component.selectFileFromType(Constants.docSourceGallery);
    expect(component.selectFileFromType).toBeTruthy();
  });

  describe('checkModalConditions', () => {
    it('should call selectFileFromType when enableMultiSite=true and userData != userData', () => {
      component.enableMultiSite = true;
      component.userData = { mySites: [{}] };
      component.documentType = {
        allowRecipientRoles: false,
        allowAssociateRoles: false,
        allowAssociatePatient: false
      };
      fixture.detectChanges();
      spyOn(component, 'selectFileFromType'); // Spy on the method
      component.checkModalConditions('someType');
      expect(component.selectFileFromType).toHaveBeenCalledWith('someType');
    });

    it('should call chooseFromModal when enableMultiSite=true and userData=undefined', () => {
      component.enableMultiSite = true;
      component.userData = undefined;
      component.documentType = {
        allowRecipientRoles: false,
        allowAssociateRoles: false,
        allowAssociatePatient: false
      };
      fixture.detectChanges();
      spyOn(component, 'chooseFromModal');
      component.checkModalConditions('someType');
      expect(component.chooseFromModal).toHaveBeenCalledWith('recipient', 'someType');
    });

    it('should call chooseFromModal when enableMultiSite=true allowRecipientRoles=true and allowAssociateRoles=true', () => {
      component.enableMultiSite = true;
      component.documentType = {
        allowRecipientRoles: true,
        allowAssociateRoles: true,
        allowAssociatePatient: false
      };
      fixture.detectChanges();
      spyOn(component, 'chooseFromModal');
      component.checkModalConditions('someType');
      expect(component.chooseFromModal).toHaveBeenCalledWith('recipient', 'someType');
    });

    it('should call chooseFromModal when enableMultiSite=true allowRecipientRoles=true', () => {
      component.enableMultiSite = true;
      component.documentType = {
        allowRecipientRoles: true,
        allowAssociateRoles: false,
        allowAssociatePatient: false
      };
      component.disablePatientorRecipient = true;
      fixture.detectChanges();
      spyOn(component, 'chooseFromModal');
      component.checkModalConditions('someType');
      expect(component.chooseFromModal).toHaveBeenCalledWith('recipient', 'someType');
    });

    it('should call chooseFromModal when when enableMultiSite=true allowAssociateRoles=true', () => {
      component.enableMultiSite = true;
      component.documentType = {
        allowRecipientRoles: false,
        allowAssociateRoles: true,
        allowAssociatePatient: false
      };
      component.disablePatientorRecipient = true;
      fixture.detectChanges();
      spyOn(component, 'chooseFromModal');
      component.checkModalConditions('someType');
      expect(component.chooseFromModal).toHaveBeenCalledWith('associate', 'someType');
    });

    it('should call selectFileFromType when enableMultiSite=true allowRecipientRoles=true and allowAssociatePatient=true', () => {
      component.enableMultiSite = true;
      component.documentType = {
        allowRecipientRoles: true,
        allowAssociateRoles: false,
        allowAssociatePatient: true
      };
      component.disablePatientorRecipient = true;
      fixture.detectChanges();
      spyOn(component, 'selectFileFromType');
      component.checkModalConditions('someType');
      expect(component.selectFileFromType).toHaveBeenCalledWith('someType');
    });

    it('should call selectFileFromType when enableMultiSite=true allowAssociateRoles=true and allowAssociatePatient=true', () => {
      component.enableMultiSite = true;
      component.documentType = {
        allowRecipientRoles: false,
        allowAssociateRoles: true,
        allowAssociatePatient: true
      };
      component.disablePatientorRecipient = true;
      fixture.detectChanges();
      spyOn(component, 'selectFileFromType');
      component.checkModalConditions('someType');
      expect(component.selectFileFromType).toHaveBeenCalledWith('someType');
    });
  });

  it('should call showMultiSelectFileUploadPopup when type is capacitor device', () => {
    const multiSelectSpy = spyOn(component, 'showMultiSelectFileUploadPopup');
    spyOn(sharedService.platform, 'is').and.returnValue(true);
    component.selectFileFromType('Camera');
    expect(component.showMultiSelectFileUploadPopup).toBeDefined();
    expect(multiSelectSpy).toHaveBeenCalled();
  });

  it('should call showMultiSelectFileUploadPopup when type is Gallery', () => {
    spyOn(sharedService.platform, 'is').and.returnValue(false);
    component.selectFileFromType('Gallery');
    expect(component.selectFileFromType).toBeDefined();
  });

  it('should call showMultiSelectFileUploadPopup when type is Camera', () => {
    spyOn(sharedService.platform, 'is').and.returnValue(false);
    component.selectFileFromType('Camera');
    expect(component.selectFileFromType).toBeDefined();
  });

  it('should call fetchSiteRegistrationId with error', () => {
    spyOn(graphqlService, 'siteDetails').and.returnValue(throwError({}));
    component.fetchSiteRegistrationId('259');
    expect(component.fetchSiteRegistrationId).toBeDefined();
  });
  it('should display error message and clear input when HTML tag is present', () => {
    const mockEvent = { target: { value: '<script>alert("Hello");</script>' } };
    component.checkValidInputDocumentName(mockEvent);
    expect(commonService.showMessage).toHaveBeenCalledWith('ERROR_MESSAGES.INVALID_INPUT_MSG_WITH_TAG');
    expect(mockEvent.target.value).toBe('');
    expect(component.documentDisplayText).toBe('');
  });
  it('should not display error message when valid input is provided', () => {
    const mockEvent = { target: { value: 'validInput' } };
    component.checkValidInputDocumentName(mockEvent);
    expect(commonService.showMessage).not.toHaveBeenCalled();
    expect(mockEvent.target.value).toBe('validInput');
    expect(component.documentDisplayText).toBe(undefined);
  });

  it('should open modal for select admission', fakeAsync(() => {
    modalSpy.onDidDismiss.and.resolveTo({ data: { admissionId: 'admissionId', admissionName: 'Admission Name' } });
    component.data.patient = { userId: 'patientId', userid: 'patientId' };
    component.documentType = {};
    component.selectAdmission();
    tick();
    expect(component.data.patientAdmissionId).toBe('admissionId');
    expect(component.admissionDisplayText).toBe('Admission Name');
  }));
  describe('presentFilingCenterModal', () => {
    it('should set filing center details after dismiss', fakeAsync(() => {
      modalSpy.onDidDismiss.and.resolveTo({
        data: {
          selectedSite: '1',
          selectedSites: '1,2',
          fcmMappingData: {
            defaultFolderFromFilingCenter: 'Atlanta',
            folderBySite: 'Atlanta folder',
            responseData: {
              folder: 'Atlanta folder1'
            }
          }
        }
      });
      component.selectDocState = { folder: { doctype: '', name: '' } };
      component.presentFilingCenterModal(FolderType.fillingCenter);
      tick();
      expect(component.selectedSite).toBe('1');
      expect(component.selectedSites).toBe('1,2');
      expect(component.defaultFilingCenterFolder).toEqual('Atlanta');
      expect(component.selectDocState.folder.name).toEqual('Atlanta folder');
    }));
    it('should set filing center details after dismiss, folder name from mapping data', fakeAsync(() => {
      modalSpy.onDidDismiss.and.resolveTo({
        data: {
          selectedSite: '1',
          selectedSites: '1,2',
          fcmMappingData: {
            defaultFolderFromFilingCenter: 'Atlanta',
            folderBySite: '',
            responseData: {
              folder: 'Atlanta folder1'
            }
          }
        }
      });
      component.selectDocState = { folder: { doctype: '', name: '' } };
      component.presentFilingCenterModal(FolderType.fillingCenter);
      tick();
      expect(component.selectedSite).toBe('1');
      expect(component.selectedSites).toBe('1,2');
      expect(component.defaultFilingCenterFolder).toEqual('Atlanta');
      expect(component.selectDocState.folder.name).toEqual('Atlanta folder1');
    }));
    it('should set filing center details after dismiss, multi-admission on case', fakeAsync(() => {
      sharedService.userData.config[Config.enableMultiAdmissions] = '1';
      modalSpy.onDidDismiss.and.resolveTo({
        data: {
          selectedSite: '1',
          selectedSites: '1,2',
          fcmMappingData: {
            defaultFolderFromFilingCenter: 'Atlanta',
            folderBySite: '',
            responseData: {
              folder: 'Atlanta folder1'
            }
          },
          admission: { admissionId: '543523-43523423', admissionName: 'Admission name' }
        }
      });
      component.selectDocState = { folder: { doctype: '', name: '' } };
      component.presentFilingCenterModal(FolderType.fillingCenter);
      tick();
      expect(component.selectedSite).toBe('1');
      expect(component.selectedSites).toBe('1,2');
      expect(component.defaultFilingCenterFolder).toEqual('Atlanta');
      expect(component.selectDocState.folder.name).toEqual('Atlanta folder1');
    }));
  });
  describe('selectRecipientsCompleteDoc', () => {
    it('should set recipient details after dismiss', fakeAsync(() => {
      modalSpy.onDidDismiss.and.resolveTo({ data: {} });
      component.data = { recipient: { userId: 'recipientId', userid: 'recipientId' } };
      component.documentDisplayText = 'Document Name';
      component.documentType = { allowRecipientRoles: '3', allowAssociatePatient: true };
      component.selectRecipientsCompleteDoc();
      tick();
    }));
  });
  describe('documentUpload', () => {
    beforeEach(() => {
      component.galleryFile = [{ type: '' }];
      component.galleryFileName = [];
      component.selectedDocument = { path: 'path/to/document', name: '' };
    });
    it('should call documentUpload and handle success response', fakeAsync(() => {
      component.enableDocumentManagement = true;
      component.singleDocFile = false;
      component.documentType = { allowAssociateRoles: true, sendDocumentWithoutSignature: false };
      component.singleDocFile = { multiple: false };
      component.documentSource = component.fromGallery;
      component.galleryDocDisplayText = 'Gallery Document';
      component.data = { recipient: { id: 1 }, patient: { userId: 2 }, associate: { id: 3 } };
      component.selectedSite = [1];
      component.defaultFilingCenterFolder = 'Default Folder';
      component.ownerIdCopyFilingCenter = '123';
      component.filenameFormatCopyFilingCenter = 'File Format';
      spyOn(component, 'redirectToPage').and.stub();
      spyOn(component, 'obtainSignPollingData').and.stub();
      spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
      spyOn(graphqlService, 'getExternalSystemsByPatient').and.returnValue(of({ data: { getSessionTenant: { externalSystems: [{}] } } }));
      spyOn(graphqlService, 'copyToFilingCenterAfterSignature').and.returnValue(of({}));
      spyOn(httpService, 'fileUpload').and.returnValue(
        of({ gqlResp: { id: 1, signatureStatus: 'completed', document: { displayText: 'Document' } } })
      );
      component.documentUpload();
      tick();
      expect(component.redirectToPage).toHaveBeenCalled();
    }));
    it('should call documentUpload: sendDocumentWithoutSignature', fakeAsync(() => {
      component.enableDocumentManagement = true;
      component.singleDocFile = false;
      component.documentType = {};
      component.selectedDocument = { fileId: 1 };
      spyOn(component, 'redirectToPage').and.stub();
      spyOn(component, 'obtainSignPollingData').and.stub();
      spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
      spyOn(graphqlService, 'getExternalSystemsByPatient').and.returnValue(of({ data: { getSessionTenant: { externalSystems: [{}] } } }));
      spyOn(graphqlService, 'copyToFilingCenterAfterSignature').and.returnValue(of({}));
      spyOn(httpService, 'fileUpload').and.returnValue(
        of({ gqlResp: { id: 1, signatureStatus: 'completed', document: { displayText: 'Document' } } })
      );
      component.documentUpload();
      tick();
      expect(httpService.fileUpload).toHaveBeenCalledWith(
        jasmine.objectContaining({ payload: jasmine.objectContaining({ fileId: 1 }), field: 'fileId' })
      );
    }));
    it('should call fileUpload, ', fakeAsync(() => {
      component.enableDocumentManagement = true;
      component.singleDocFile = false;
      component.documentType = { sendDocumentWithoutSignature: true };
      component.selectedDocument = { fileId: 1 };
      spyOn(component, 'redirectToPage').and.stub();
      spyOn(component, 'obtainSignPollingData').and.stub();
      spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
      spyOn(graphqlService, 'getExternalSystemsByPatient').and.returnValue(of({ data: { getSessionTenant: { externalSystems: [{}] } } }));
      spyOn(graphqlService, 'copyToFilingCenterAfterSignature').and.returnValue(of({}));
      spyOn(httpService, 'fileUpload').and.returnValue(
        of({ gqlResp: { id: 1, signatureStatus: 'completed', document: { displayText: 'Document' } } })
      );
      component.documentUpload();
      tick();
      expect(httpService.fileUpload).toHaveBeenCalledWith(
        jasmine.objectContaining({ payload: jasmine.objectContaining({ fileId: 1 }), field: 'fileId' })
      );
    }));
    it('should call documentUpload and handle error response', fakeAsync(() => {
      component.enableDocumentManagement = true;
      component.documentType = { allowAssociateRoles: true, sendDocumentWithoutSignature: false };
      component.singleDocFile = { multiple: false };
      component.selectedDocument = { path: 'path/to/document' };
      component.documentSource = component.fromGallery;
      component.galleryDocDisplayText = 'Gallery Document';
      component.data = { recipient: { id: 1 }, patient: { userId: 2 }, associate: { id: 3 } };
      component.selectedSite = [1];
      component.defaultFilingCenterFolder = 'Default Folder';
      component.ownerIdCopyFilingCenter = '123';
      component.filenameFormatCopyFilingCenter = 'File Format';
      spyOn(component, 'redirectToPage').and.stub();
      spyOn(component, 'obtainSignPollingData').and.stub();
      spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
      spyOn(graphqlService, 'getExternalSystemsByPatient').and.returnValue(of({ data: { getSessionTenant: { externalSystems: [{}] } } }));
      spyOn(graphqlService, 'copyToFilingCenterAfterSignature').and.returnValue(of({}));
      spyOn(httpService, 'fileUpload').and.returnValue(throwError({}));
      component.documentUpload();
      tick();
      expect(sharedService.isLoading).toBeFalse();
    }));

    describe('GraphQL Error Handling in documentUpload', () => {
      beforeEach(() => {
        component.galleryFile = [{ type: '' }];
        component.galleryFileName = [];
        component.selectedDocument = { path: 'path/to/document', name: '' };
        component.enableDocumentManagement = true;
        component.documentType = { allowAssociateRoles: true, sendDocumentWithoutSignature: false };
        component.singleDocFile = { multiple: false };
        component.documentSource = component.fromGallery;
        component.galleryDocDisplayText = 'Gallery Document';
        component.data = { recipient: { id: 1 }, patient: { userId: 2 }, associate: { id: 3 } };
        component.selectedSite = [1];
        component.defaultFilingCenterFolder = 'Default Folder';
        component.ownerIdCopyFilingCenter = '123';
        component.filenameFormatCopyFilingCenter = 'File Format';
        spyOn(component, 'redirectToPage').and.stub();
        spyOn(component, 'obtainSignPollingData').and.stub();
        spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
        spyOn(graphqlService, 'getExternalSystemsByPatient').and.returnValue(of({ data: { getSessionTenant: { externalSystems: [{}] } } }));
        spyOn(graphqlService, 'copyToFilingCenterAfterSignature').and.returnValue(of({}));
        spyOn(sharedService, 'trackActivity').and.stub();
        spyOn(sharedService, 'consoloAppExit').and.stub();
      });

      it('should handle GraphQL error for Consolo flow and show warning alert', fakeAsync(() => {
        // Arrange
        const gqlErrorMessage = 'You do not have the required role to access this document';
        const mockResponse = {
          gqlError: gqlErrorMessage,
          gqlResp: null
        };
        sharedService.automaticLinkedItems = { document: { id: 1 } }; // Consolo flow
        spyOn(httpService, 'fileUpload').and.returnValue(of(mockResponse));

        // Act
        component.documentUpload();
        tick();

        // Assert
        expect(sharedService.consoloLoader).toBeFalse();
        expect(sharedService.trackActivity).toHaveBeenCalledWith({
          type: Activity.signatureRequest,
          name: Activity.sendDocument,
          des: {
            data: {
              displayName: sharedService.userData?.displayName || '',
              documentType: component.documentType?.name || '',
              errorMessage: gqlErrorMessage,
              isConsoloFlow: true
            },
            desConstant: Activity.documentSendErrorDes
          },
          linkageId: sharedService.userData?.displayName || ''
        });
        expect(commonService.showWarningAlert).toHaveBeenCalledWith({
          message: gqlErrorMessage,
          icon: 'warning',
          iconColor: '#eb445a',
          header: 'LABELS.ERROR'
        });
        expect(sharedService.consoloAppExit).toHaveBeenCalled();
      }));

      it('should handle GraphQL error for non-Consolo flow and show message', fakeAsync(() => {
        // Arrange
        const gqlErrorMessage = 'Access denied due to insufficient permissions';
        const mockResponse = {
          gqlError: gqlErrorMessage,
          gqlResp: null
        };
        sharedService.automaticLinkedItems = null; // Non-Consolo flow
        spyOn(httpService, 'fileUpload').and.returnValue(of(mockResponse));

        // Act
        component.documentUpload();
        tick();

        // Assert
        expect(sharedService.consoloLoader).toBeFalse();
        expect(sharedService.trackActivity).toHaveBeenCalledWith({
          type: Activity.signatureRequest,
          name: Activity.sendDocument,
          des: {
            data: {
              displayName: sharedService.userData?.displayName || '',
              documentType: component.documentType?.name || '',
              errorMessage: gqlErrorMessage,
              isConsoloFlow: false
            },
            desConstant: Activity.documentSendErrorDes
          },
          linkageId: sharedService.userData?.displayName || ''
        });
        expect(commonService.showMessage).toHaveBeenCalledWith(gqlErrorMessage);
        expect(commonService.showWarningAlert).not.toHaveBeenCalled();
        expect(sharedService.consoloAppExit).not.toHaveBeenCalled();
      }));

      it('should handle GraphQL error with empty automaticLinkedItems and show message', fakeAsync(() => {
        // Arrange
        const gqlErrorMessage = 'Role validation failed';
        const mockResponse = {
          gqlError: gqlErrorMessage,
          gqlResp: null
        };
        sharedService.automaticLinkedItems = {}; // Empty object (non-Consolo)
        spyOn(httpService, 'fileUpload').and.returnValue(of(mockResponse));

        // Act
        component.documentUpload();
        tick();

        // Assert
        expect(sharedService.consoloLoader).toBeFalse();
        expect(sharedService.trackActivity).toHaveBeenCalledWith({
          type: Activity.signatureRequest,
          name: Activity.sendDocument,
          des: {
            data: {
              displayName: sharedService.userData?.displayName || '',
              documentType: component.documentType?.name || '',
              errorMessage: gqlErrorMessage,
              isConsoloFlow: false
            },
            desConstant: Activity.documentSendErrorDes
          },
          linkageId: sharedService.userData?.displayName || ''
        });
        expect(commonService.showMessage).toHaveBeenCalledWith(gqlErrorMessage);
        expect(commonService.showWarningAlert).not.toHaveBeenCalled();
        expect(sharedService.consoloAppExit).not.toHaveBeenCalled();
      }));

      it('should proceed normally when no GraphQL error is present', fakeAsync(() => {
        // Arrange
        const mockResponse = {
          gqlResp: { id: 1, signatureStatus: 'completed', document: { displayText: 'Document' } }
        };
        sharedService.automaticLinkedItems = { document: { id: 1 } };
        spyOn(httpService, 'fileUpload').and.returnValue(of(mockResponse));

        // Act
        component.documentUpload();
        tick();

        // Assert
        expect(commonService.showWarningAlert).not.toHaveBeenCalled();
        expect(commonService.showMessage).not.toHaveBeenCalled();
        expect(sharedService.consoloAppExit).not.toHaveBeenCalled();
        expect(component.redirectToPage).toHaveBeenCalled();
      }));

      it('should track activity with correct data structure for GraphQL error', fakeAsync(() => {
        // Arrange
        const gqlErrorMessage = 'Test error message';
        const mockResponse = {
          gqlError: gqlErrorMessage,
          gqlResp: null
        };
        sharedService.automaticLinkedItems = { document: { id: 1 } };
        component.documentType = { name: 'Test Document Type' };
        spyOn(httpService, 'fileUpload').and.returnValue(of(mockResponse));

        // Act
        component.documentUpload();
        tick();

        // Assert
        expect(sharedService.trackActivity).toHaveBeenCalledWith({
          type: Activity.signatureRequest,
          name: Activity.sendDocument,
          des: {
            data: {
              displayName: 'Alvin', // Use the actual value from beforeEach setup
              documentType: 'Test Document Type',
              errorMessage: gqlErrorMessage,
              isConsoloFlow: true
            },
            desConstant: Activity.documentSendErrorDes
          },
          linkageId: 'Alvin' // Use the actual value from beforeEach setup
        });
      }));

      it('should handle GraphQL error with missing userData properties gracefully', fakeAsync(() => {
        // Arrange
        const gqlErrorMessage = 'Test error message';
        const mockResponse = {
          gqlError: gqlErrorMessage,
          gqlResp: null
        };
        sharedService.automaticLinkedItems = { document: { id: 1 } };
        // Don't modify userData to avoid breaking other parts of the code
        spyOn(httpService, 'fileUpload').and.returnValue(of(mockResponse));

        // Act
        component.documentUpload();
        tick();

        // Assert
        expect(sharedService.trackActivity).toHaveBeenCalled();
        expect(commonService.showWarningAlert).toHaveBeenCalledWith({
          message: gqlErrorMessage,
          icon: 'warning',
          iconColor: '#eb445a',
          header: 'LABELS.ERROR'
        });
      }));
    });
  });
  describe('loadDocument', () => {
    it('should call loadDocument : format not supported', fakeAsync(() => {
      const data = { target: { files: [{ name: 'file1.doc', size: 2097152 }] } };
      component.allowedFileTypes = 'pdf';
      component.loadDocument(data);
      expect(commonService.showMessage).toHaveBeenCalledWith('ERROR_MESSAGES.FILE_FORMAT');
    }));
    it('should call loadDocument : size exceeded', fakeAsync(() => {
      const data = { target: { files: [{ name: 'file1.pdf', size: 20971522 }] } };
      component.allowedFileTypes = 'pdf';
      component.loadDocument(data);
      expect(commonService.showMessage).toHaveBeenCalledWith('ERROR_MESSAGES.FILE_UPLOADSIZE');
    }));
    it('should call loadDocument : html tag available', fakeAsync(() => {
      const data = { target: { files: [{ name: 'file1<a><a/>.pdf', size: 2097152 }] } };
      component.allowedFileTypes = 'pdf';
      component.loadDocument(data);
      expect(commonService.showMessage).toHaveBeenCalledWith('ERROR_MESSAGES.ERROR_MSG_INVALID_INPUT');
    }));
    it('should call loadDocument : load document success', fakeAsync(() => {
      const data = { target: { files: [{ name: 'file1.pdf', size: 2097152, type: 'application/pdf' }] } };
      component.allowedFileTypes = 'pdf';
      component.galleryFile = [];
      component.loadDocument(data);
      expect(component.galleryFile).toEqual([
        {
          e: jasmine.objectContaining({ name: 'file1.pdf', size: 2097152, type: 'application/pdf' }),
          name: 'file1.pdf',
          size: 2097152,
          type: 'application/pdf',
          extension: 'pdf',
          multiple: false,
          url: ''
        }
      ]);
    }));
  });
  describe('resetAllPatientDependentData', () => {
    it('should reset recipient and associate data when multiple sites are selected', () => {
      // Arrange
      component.selectedSite = [1, 2]; // Multiple sites
      component.data = {
        recipient: { id: 123, name: 'John Doe' },
        associate: { id: 456, name: 'Jane Smith' }
      };
      component.recipientDisplayText = 'John Doe';
      component.associateDisplayText = 'Jane Smith';
      spyOn(commonService, 'getTranslateData').and.callFake((key) => key);

      // Act
      component.resetAllPatientDependentData();

      // Assert
      expect(component.recipientDisplayText).toBe('TITLES.CHOOSE_RECIPIENTS');
      expect(component.associateDisplayText).toBe('TITLES.CHOOSE_DELEGATED_STAFF');
      expect(component.data.recipient).toEqual({});
      expect(component.data.associate).toEqual({});
      expect(commonService.getTranslateData).toHaveBeenCalledWith('TITLES.CHOOSE_RECIPIENTS');
      expect(commonService.getTranslateData).toHaveBeenCalledWith('TITLES.CHOOSE_DELEGATED_STAFF');
    });

    it('should not reset data when there is only one selected site', () => {
      // Arrange
      component.selectedSite = [1]; // Single site
      component.data = {
        recipient: { id: 123, name: 'John Doe' },
        associate: { id: 456, name: 'Jane Smith' }
      };
      const originalRecipient = { ...component.data.recipient };
      const originalAssociate = { ...component.data.associate };
      const originalRecipientText = component.recipientDisplayText;
      const originalAssociateText = component.associateDisplayText;

      // Act
      component.resetAllPatientDependentData();

      // Assert
      expect(component.data.recipient).toEqual(originalRecipient);
      expect(component.data.associate).toEqual(originalAssociate);
      expect(component.recipientDisplayText).toEqual(originalRecipientText);
      expect(component.associateDisplayText).toEqual(originalAssociateText);
    });

    it('should not reset data when selectedSite is not present', () => {
      // Arrange
      component.selectedSite = null;
      component.data = {
        recipient: { id: 123, name: 'John Doe' },
        associate: { id: 456, name: 'Jane Smith' }
      };
      const originalRecipient = { ...component.data.recipient };
      const originalAssociate = { ...component.data.associate };
      const originalRecipientText = component.recipientDisplayText;
      const originalAssociateText = component.associateDisplayText;

      // Act
      component.resetAllPatientDependentData();

      // Assert
      expect(component.data.recipient).toEqual(originalRecipient);
      expect(component.data.associate).toEqual(originalAssociate);
      expect(component.recipientDisplayText).toEqual(originalRecipientText);
      expect(component.associateDisplayText).toEqual(originalAssociateText);
    });
  });

  describe('fetchSiteRegistrationId', () => {
    it('should update siteRegistrationId for selectedRecipient when data is returned', fakeAsync(() => {
      // Arrange
      const siteId = '123';
      const mockResponse = {
        data: {
          getSiteDetails: {
            registrationId: 'REG-001'
          }
        }
      };
      component.enableMultiSite = true;
      component.selectedRecipient = { name: 'Test Recipient' };

      // Spy on graphqlService.siteDetails
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      spyOn(graphqlService, 'siteDetails').and.returnValue(of(mockResponse) as any);

      // Act
      component.fetchSiteRegistrationId(siteId);
      tick();

      // Assert
      expect(graphqlService.siteDetails).toHaveBeenCalledWith(parseInt(siteId, 10), component.admissionId);
      expect(component.selectedRecipient.siteRegistrationId).toBe('REG-001');
    }));

    it('should update siteRegistrationId for data.patient when data is returned and patient exists', fakeAsync(() => {
      // Arrange
      const siteId = '123';
      const mockResponse = {
        data: {
          getSiteDetails: {
            registrationId: 'REG-001'
          }
        }
      };
      component.enableMultiSite = true;
      component.data = { patient: { name: 'Test Patient' } };

      // Spy on graphqlService.siteDetails
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      spyOn(graphqlService, 'siteDetails').and.returnValue(of(mockResponse) as any);

      // Act
      component.fetchSiteRegistrationId(siteId);
      tick();

      // Assert
      expect(graphqlService.siteDetails).toHaveBeenCalledWith(parseInt(siteId, 10), component.admissionId);
      expect(component.data.patient.siteRegistrationId).toBe('REG-001');
    }));

    it('should not call graphqlService if enableMultiSite is false', () => {
      // Arrange
      const siteId = '123';
      component.enableMultiSite = false;

      // Spy on graphqlService.siteDetails
      spyOn(graphqlService, 'siteDetails');

      // Act
      component.fetchSiteRegistrationId(siteId);

      // Assert
      expect(graphqlService.siteDetails).not.toHaveBeenCalled();
    });

    it('should not call graphqlService if siteId is not provided', () => {
      // Arrange
      const siteId = null;
      component.enableMultiSite = true;

      // Spy on graphqlService.siteDetails
      spyOn(graphqlService, 'siteDetails');

      // Act
      component.fetchSiteRegistrationId(siteId);

      // Assert
      expect(graphqlService.siteDetails).not.toHaveBeenCalled();
    });

    it('should track activity when site details are not found', fakeAsync(() => {
      // Arrange
      const siteId = '123';
      const mockResponse = {
        data: {
          getSiteDetails: null
        }
      };
      component.enableMultiSite = true;

      // Spy on graphqlService.siteDetails and sharedService.trackActivity
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      spyOn(graphqlService, 'siteDetails').and.returnValue(of(mockResponse) as any);
      spyOn(sharedService, 'trackActivity');

      // Act
      component.fetchSiteRegistrationId(siteId);
      tick();

      // Assert
      expect(graphqlService.siteDetails).toHaveBeenCalledWith(parseInt(siteId, 10), component.admissionId);
      expect(sharedService.trackActivity).toHaveBeenCalledWith({
        type: Activity.signatureRequest,
        name: Activity.fetchSiteRegistrationId,
        des: Activity.siteIdNotFound
      });
    }));
  });
});
