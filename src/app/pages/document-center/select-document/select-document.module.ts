import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { RouterModule } from '@angular/router';
import { SelectDocumentPageRoutingModule } from './select-document-routing.module';

import { SelectDocumentPage } from './select-document.page';
import { FilingCenterPageModule } from '../filing-center/filing-center.module';
import { DocumentCenterRecipientsComponentModule } from '../document-center-recipients/document-center-recipients.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SelectDocumentPageRoutingModule,
    SharedModule,
    RouterModule,
    FilingCenterPageModule,
    DocumentCenterRecipientsComponentModule,
    ReactiveFormsModule
  ],
  declarations: [SelectDocumentPage]
})
export class SelectDocumentPageModule {}
