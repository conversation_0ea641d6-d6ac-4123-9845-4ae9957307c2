<app-header headerTitle="TITLES.SELECT_DOCUMENT"></app-header>
<div class="document-sub-header">
  <ion-buttons class="header-buttons">
    <ion-button class="start ion-text-capitalize" id="cancel" (click)="cancel()" color="de-york">{{ 'BUTTONS.CANCEL' | translate }} </ion-button>
    <ion-button class="end ion-text-capitalize" id="next" (click)="next()" color="de-york">{{ docUploadTitle }} </ion-button>
  </ion-buttons>
</div>
<ion-content class="select-document-page">
  <form [formGroup]="selectedDocForm" id="selected-doc-form">
    <div class="choose-document-box" *ngIf="stage === defaultPage">
      <div class="selected-document" tappable (click)="chooseOptionsForUpload()">
        <span class="file-upload-browse file-browse" id="obtain-sign-snap">
          <ion-icon name="add-sharp"> </ion-icon>
        </span>
      </div>
      <span class="file-upload-name">{{ 'TITLES.SELECT_DOCUMENT' | translate }}</span>
    </div>
    <div enctype="multipart/form-data" id="document-selection-form" class="file-form">
      <input multiple #docInput type="file" id="file-choose" (change)="loadDocument($event)" (click)="docInput.value = null" />

      <input
        multiple
        #cameraInput
        type="file"
        id="file-choose-camera"
        capture="camera"
        accept="image/*"
        (change)="loadDocument($event)"
        (click)="cameraInput.value = null"
      />
    </div>
    <div class="choose-document-box" *ngIf="stage === selectedDocPage && documentSource === fromFilingCenter">
      <div id="remove_up" tappable>
        <span class="modal-close ion-close-round" id="remove-document" (click)="removeFile()" id="attach-file">x</span>
      </div>
      <div class="selected-document">
        <div class="file-upload-browse file-selected">
          <div class="file-img">
            <img src="assets/images/doc-types/{{ selectedDocument?.fileType }}.png" alt="file-img" />
          </div>
        </div>
      </div>
    </div>
    <div class="choose-document-box" *ngIf="stage === selectedDocPage && documentSource === fromGallery && getFileExt">
      <div id="remove-up" class="" tappable (click)="removeFile()">
        <span class="modal-close ion-close-round" id="remove-document" id="attach-file">x</span>
      </div>
      <div class="selected-document">
        <div class="file-upload-browse file-selected">
          <div class="selected-document" tappable (click)="chooseOptionsForUpload()" id="choose-options">
            <div class="file-img">
              <img src="assets/images/doc-types/{{ getFileExt }}.png" *ngIf="singleDocFile?.url === ''; else imageType" alt="file-ext" />
              <ng-template #imageType>
                <ion-skeleton-text [animated]="true" *ngIf="!singleDocFile?.url"> </ion-skeleton-text>
                <img [src]="singleDocFile?.url" alt="file-ext" *ngIf="singleDocFile?.url" />
              </ng-template>
            </div>
          </div>
        </div>
      </div>
      <div class="common-uploads" *ngIf="galleryFile">
        <div class="upload-item-top">
          <div class="upload-item filename" *ngFor="let f of galleryFile; let i = index">
            <span> {{ f.name }} </span>
            <span class="file-close" (click)="removeGalleryFile(i)" [id]="'remove-file-' + i">x</span>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="stage === selectedDocPage" class="select-doc-wrapper">
      <div>
        <div class="common-input-row" *ngIf="documentSource === fromFilingCenter || documentSource === fromGallery">
          <input
            class="common-input"
            id="save-as-document-display"
            type="text"
            [readonly]="!documentType.allowFileNameEditing"
            placeholder="{{ 'BUTTONS.SAVE_AS' | translate }}"
            value="{{ documentDisplayText }}"
            (focusout)="checkValidInputDocumentName($event)"
          />
        </div>
        <div class="common-input-row" *ngIf="documentType.allowPersonalSignature">
          <label class="sign-label" for="">
            {{ 'LABELS.SIGN_BEFORE_SEND' | translate }}
          </label>
          <span class="sign-button">
            <ion-toggle
              mode="ios"
              checked="data.allowPersonalSignature"
              id="toggle-sign"
              (ionChange)="changePersonalSignatureStatus($event)"
              color="de-york"
            >
            </ion-toggle>
          </span>
        </div>
        <div class="common-input-row" *ngIf="documentType.allowRecipientRoles && recipientDisplayText">
          <div
            class="common-choose-recipients"
            tappable
            id="select-recipient"
            (click)="chooseFromModal(constants.recipient)"
            [class.disbaled-div]="
              (enableMultiSite && disablePatientorRecipient && !documentType.allowAssociatePatient) || (enableMultiSite && disableRecipientField)
            "
          >
            <div class="display-text-wrap" readonly>
              <label class="display-text-label" for=""> {{ recipientDisplayText }}</label>
            </div>
          </div>
        </div>

        <div class="common-input-row" *ngIf="documentType.allowAssociatePatient">
          <div
            class="common-choose-recipients"
            tappable
            id="select-patient"
            [class.disbaled-div]="enableMultiSite && disablePatientorRecipient && documentType.allowAssociatePatient"
            (click)="chooseFromModal(constants.patientValue)"
          >
            <div class="display-text-wrap" *ngIf="patientDisplayText" readonly>
              <label class="display-text-label" for=""> {{ patientDisplayText }}</label>
            </div>
          </div>
        </div>
        <div
          class="common-input-row"
          id="select-patient-admission"
          [class.disbaled-div]="enableMultiSite && disablePatientAdmission"
          *ngIf="sharedService.isMultiAdmissionsEnabled && (documentType.allowAssociatePatient ? data.patient : isRolePatient && data.recipient)"
        >
          <div class="common-choose-recipients" tappable id="select-patient-admission" (click)="selectAdmission()">
            <div class="display-text-wrap" *ngIf="admissionDisplayText" readonly>
              <span class="display-text-label"> {{ admissionDisplayText }}</span>
            </div>
          </div>
        </div>
        <div class="common-input-row" *ngIf="documentType.allowAssociateRoles">
          <div class="common-choose-recipients" tappable id="select-associate" (click)="chooseFromModal(constants.associate)">
            <div class="display-text-wrap" *ngIf="associateDisplayText" readonly>
              <label class="display-text-label" for=""> {{ associateDisplayText }}</label>
            </div>
          </div>
        </div>

        <div
          class="common-input-row"
          *ngIf="
            documentType?.sendCompletedDocument &&
            (documentType.allowAssociatePatient ? data.patient : isRolePatient && data.recipient) &&
            (!this.sharedService.isMultiAdmissionsEnabled || admissionId)
          "
        >
          <div class="common-choose-recipients" tappable id="select-recipients-complete" (click)="selectRecipientsCompleteDoc()">
            <div class="display-text-wrap" readonly>
              <label class="display-text-label" for=""> {{ recipientsCompletedDocument }}</label>
            </div>
          </div>
        </div>

        <div *ngIf="enableApplessMode && associatePatientModal">
          <select name="channel-method" formControlName="interactionMethod" id="channel-method" class="common-input bgimg">
            <option
              *ngFor="let option of interactionMethodValues"
              id="document-send-mode"
              class="form-check-input"
              name="documentSendMode"
              type="radio"
              [ngValue]="option.key"
            >
              {{ option.value }}
            </option>
          </select>
        </div>
      </div>
    </div>
  </form>
</ion-content>

<app-footer></app-footer>
