import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { DocumentsPageRoutingModule } from './documents-routing.module';
import { DocumentsPage } from './documents.page';
import { SharedModule } from 'src/app/shared.module';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        DocumentsPageRoutingModule,
        SharedModule,
        ActionButtonModule
    ],
    declarations: [DocumentsPage]
})
export class DocumentsPageModule { }
