import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Apollo } from 'apollo-angular';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavParams } from '@ionic/angular';

import { DocumentsPage } from './documents.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonService } from 'src/app/services/common-service/common.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { of, throwError } from 'rxjs';

describe('DocumentsPage', () => {
  let component: DocumentsPage;
  let fixture: ComponentFixture<DocumentsPage>;
  let commonService: CommonService;
  let sharedService: SharedService;
  let graphqlService: GraphqlService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DocumentsPage],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        ReactiveFormsModule
      ],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        NavParams,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(DocumentsPage);
    component = fixture.componentInstance;
    commonService = TestBed.inject(CommonService);
    sharedService = TestBed.inject(SharedService);
    graphqlService = TestBed.inject(GraphqlService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update counts and get document details on ionViewWillEnter', () => {
    spyOn(component, 'updateCounts');
    spyOn(component, 'getDocumentsDetailedCounts');
    component.ionViewWillEnter();
    expect(component.updateCounts).toHaveBeenCalled();
    expect(component.getDocumentsDetailedCounts).toHaveBeenCalled();
  });

  it('should redirect to document center request signature page', () => {
    spyOn(commonService, 'redirectToPage');
    component.doAction();
    expect(commonService.redirectToPage).toHaveBeenCalledWith('/document-center/request-signature');
  });
  describe('updateCounts', () => {
    it('should update counts based on sharedService.documentCount', () => {
      const documentCount = {
        totalPendingCount: 5,
        totalSignedCount: 10,
        totalArchiveCount: 3
      };

      sharedService.documentCount = documentCount;
      component.updateCounts();
      expect(component.pending).toEqual(documentCount.totalPendingCount);
      expect(component.completed).toEqual(documentCount.totalSignedCount);
      expect(component.archived).toEqual(documentCount.totalArchiveCount);
    });
    it('should not update counts if sharedService.documentCount is not set', () => {
      component.updateCounts();
      expect(component.pending).toBeUndefined();
      expect(component.completed).toBeUndefined();
      expect(component.archived).toBeUndefined();
    });
  });
  describe('getDocumentsDetailedCounts', () => {
    it('should update counts and sharedService properties on successful GraphQL response', () => {
      spyOn(graphqlService, 'getDocumentsDetailedCounts').and.returnValue({ valueChanges: of({ data: {} }) });
      component.getDocumentsDetailedCounts();
      expect(component.getDocumentsDetailedCounts).toBeDefined();
    });

    it('should handle errors and call sharedService.errorHandler on GraphQL error', () => {
      spyOn(graphqlService, 'getDocumentsDetailedCounts').and.returnValue({ valueChanges: throwError({}) });
      component.getDocumentsDetailedCounts();
      expect(component.getDocumentsDetailedCounts).toBeTruthy();
    });
  });
});
