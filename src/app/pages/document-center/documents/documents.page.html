<app-header headerTitle="MENU.DOCUMENT_CENTER"></app-header>

<ion-content [fullscreen]="true" class="document-page common-page-layout">
    <!-- TODO! CHP-3597 -->
    <div class="common-page-sections">
        <div class="common-page-section row" tappable [routerLink]="['pending-documents']" id="pending-documents">
            <div class="common-page-btns common-btn1 col common-full-width" id="pending">
                <span>{{ 'LABELS.PENDING_DOCUMENTS' | translate }}</span>
                <i><img src="../../assets/icon/documents/pending.png" alt="pending-documents">
                    <div *ngIf="pending>0" class="common-count">{{ pending }}</div>
                </i>
            </div>
        </div>
        <div class="common-page-section row" tappable [routerLink]="['completed-documents']" id="completed-documents">
            <div class="common-page-btns common-btn2 col common-full-width" id="completed">
                <span>{{ 'LABELS.COMPLETED_DOCUMENTS' | translate }}</span>
                <i><img src="../../assets/icon/documents/completed.png" alt="completed-documents">
                    <div *ngIf="completed>0" class="common-count">{{ completed }}</div>
                </i>

            </div>
        </div>
        <div class="common-page-section row" tappable [routerLink]="['archived-documents']" id="archived-documents">
            <div class="common-page-btns common-btn3 col common-full-width" id="archived">
                <span>{{ 'LABELS.ARCHIVED_DOCUMENTS' | translate }}</span>
                <i><img src="../../assets/icon/documents/archived.png" alt="archived-documents">
                    <div *ngIf="archived>0" class="common-count">{{ archived }}</div>
                </i>
            </div>
        </div>
    </div>
</ion-content>
<!-- TODO! CHP-3597 -->
<app-action-button [button]="button" (buttonClick)="doAction()"></app-action-button>
<app-footer backButtonLink="home"></app-footer>