import { Component } from '@angular/core';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { DocumentDetailedCount } from 'src/app/interfaces/document-request-signature';

@Component({
  selector: 'app-documents',
  templateUrl: './documents.page.html',
  styleUrls: ['./documents.page.scss']
})
export class DocumentsPage {
  pending: number;
  completed: number;
  archived: number;
  button: any = {};
  constructor(
    private readonly graphqlService: GraphqlService,
    private readonly common: CommonService,
    private readonly sharedService: SharedService
  ) {
    if (Number(this.sharedService.userData?.group) === Constants.patientGroupId) {
      // TODO! CHP-3597
      this.button = {
        iconCustom: false,
        type: 'new-chat',
        buttonType: 'ion-button',
        buttonIcon: 'chatbox-ellipses',
        colorTheme: 'de-york'
      };
    } else {
      // TODO! CHP-3597
      this.button = {
        iconCustom: true,
        customClass: 'document-center-btn',
        buttonType: 'ion-button',
        buttonIcon: 'assets/icon/button/signature.svg',
        colorTheme: 'de-york',
        ...this.common.setActionButton('BUTTONS.REQUEST_SIGNATURE', 'request-signature.png')
      };
    }
  }

  ionViewWillEnter(): void {
    if (this.sharedService.branchSwitched) {
      this.sharedService.resetSelectedDateFilterData();
      this.sharedService.branchSwitched = false;
    }
    this.updateCounts();
    this.getDocumentsDetailedCounts();
  }
  /**
   * getDocumentsDetailedCounts calls mySignatureRequestCount graphQL API and set counts
   */
  getDocumentsDetailedCounts(): void {
    if (!this.sharedService.documentCount) {
      this.sharedService.isLoading = true;
    }
    this.graphqlService.getDocumentsDetailedCounts()?.valueChanges.subscribe(
      ({ data }: DocumentDetailedCount) => {
        this.sharedService.isLoading = false;
        this.sharedService.updateDocumentDetailedCount(data);
        this.updateCounts();
      },
      (error) => {
        this.sharedService.errorHandler(error);
      }
    );
  }
  /**
   * updateCounts set individual counts using sharedService.documentCount
   */
  updateCounts(): void {
    if (this.sharedService.documentCount) {
      this.pending = this.sharedService.documentCount.totalPendingCount;
      this.completed = this.sharedService.documentCount.totalSignedCount;
      this.archived = this.sharedService.documentCount.totalArchiveCount;
    }
  }
  doAction(): void {
    this.common.redirectToPage('/document-center/request-signature');
  }
}
