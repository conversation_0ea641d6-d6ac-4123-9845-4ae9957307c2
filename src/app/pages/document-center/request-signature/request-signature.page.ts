import { Component, OnInit } from '@angular/core';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { isBlank } from 'src/app/utils/utils';
import { Router, NavigationExtras } from '@angular/router';
import { CommonService } from 'src/app/services/common-service/common.service';
import { InputCustomEvent } from '@ionic/angular';

@Component({
  selector: 'app-request-signature',
  templateUrl: './request-signature.page.html',
  styleUrls: ['./request-signature.page.scss']
})
export class RequestSignaturePage {
  requestTypes;
  loadedRequestTypes;
  errorMessage = '';
  searchTerm: string = '';
  constructor(
    private readonly graphqlService: GraphqlService,
    private readonly router: Router,
    private readonly common: CommonService
  ) {}
  ionViewWillEnter(): void {
    this.getSignatureRequestTypes();
  }
  getSignatureRequestTypes(): void {
    this.graphqlService.signatureRequestTypesDisplay()?.valueChanges.subscribe(
      ({ data }) => {
        if (!isBlank(data.signatureRequestTypesDisplay)) {
          this.loadedRequestTypes = data.signatureRequestTypesDisplay;
          this.filterList();
        } else {
          this.handleError();
        }
      },
      () => {
        this.handleError();
      }
    );
  }

  handleError(): void {
    this.requestTypes = [];
    this.loadedRequestTypes = [];
  }

  filterList(event?: InputCustomEvent): void {
    this.requestTypes = this.loadedRequestTypes;
    if (event) {
      this.searchTerm = event?.target?.value?.toString();
    }
    if (!this.searchTerm) {
      return;
    }
    this.requestTypes = this.requestTypes?.filter((form) => {
      return form.name && this.searchTerm && form.name.toLowerCase().indexOf(this.searchTerm.toLowerCase()) > -1;
    });
    if (this.requestTypes?.length === 0) {
      this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
    }
  }

  chooseItem(item): void {
    const navigationExtras: NavigationExtras = {
      state: {
        folder: {
          id: item.id,
          name: item.toFilingCenter,
          doctype: item
        }
      }
    };
    this.router.navigate(['/document-center/select-document'], navigationExtras);
  }
}
