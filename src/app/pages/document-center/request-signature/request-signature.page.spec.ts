import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Apollo } from 'apollo-angular';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { Router, RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { RequestSignaturePage } from './request-signature.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { of, throwError } from 'rxjs';

describe('RequestSignaturePage', () => {
  let component: RequestSignaturePage;
  let fixture: ComponentFixture<RequestSignaturePage>;
  let graphqlService: GraphqlService;
  let router: Router;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [RequestSignaturePage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    router = TestBed.inject(Router);
    spyOn(router, 'navigate').and.stub();
    fixture = TestBed.createComponent(RequestSignaturePage);
    graphqlService = TestBed.inject(GraphqlService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('execute filterList', () => {
    const event = { target: { value: 'test' } } as any;
    component.loadedRequestTypes = [
      {
        name: 'testing'
      }
    ];
    component.filterList(event);
    expect(component.filterList).toBeTruthy();
  });

  it('execute filterList : name is empty', () => {
    const event = { target: { value: '' } } as any;
    component.requestTypes = [
      {
        name: ''
      }
    ];
    component.filterList(event);
    expect(component.filterList).toBeTruthy();
  });

  it('execute filterList : requestTypes.length == 0', () => {
    component.loadedRequestTypes = [
      {
        name: 'testing'
      }
    ];
    component.requestTypes = [];
    const event = { target: { value: 'apple' } } as any;
    expect(component.requestTypes.length).toEqual(0);
    component.filterList(event);
    expect(component.filterList).toBeTruthy();
  });

  it('execute getSignatureRequestTypes', () => {
    const data = {
      data: {
        signatureRequestTypesDisplay: true
      }
    };
    spyOn(graphqlService, 'signatureRequestTypesDisplay').and.returnValue({ valueChanges: of(data) });
    component.getSignatureRequestTypes();
    expect(component.getSignatureRequestTypes).toBeDefined();
  });

  it('execute getSignatureRequestTypes : throw error', () => {
    spyOn(graphqlService, 'signatureRequestTypesDisplay').and.returnValue({ valueChanges: throwError('') });
    component.getSignatureRequestTypes();
    expect(component.getSignatureRequestTypes).toBeDefined();
  });

  it('execute chooseItem', () => {
    component.chooseItem('');
    expect(component.chooseItem).toBeDefined();
  });
});
