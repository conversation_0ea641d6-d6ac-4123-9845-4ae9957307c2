<app-header headerTitle="LABELS.SELECT_DOC_TYPE"></app-header>
<ion-searchbar [disabled]="!loadedRequestTypes" (ionInput)="filterList($event)" id="search-bar"></ion-searchbar>
<ion-content class="request-signature-page">
  <ion-list>
    <ion-item lines="none" *ngFor="let item of requestTypes; let id = index" class="item" (click)="chooseItem(item)" id="doc-type-{{ id }}">
      <ion-label>{{ item?.name }}</ion-label>
      <ion-icon slot="end" name="chevron-forward" color="de-york"></ion-icon>
    </ion-item>
  </ion-list>
  <app-skeleton-loader [skeletonWidths]="[100]" [count]="10" *ngIf="!loadedRequestTypes"></app-skeleton-loader>
  <div class="common-no-items" *ngIf="requestTypes?.length === 0">{{ errorMessage }}</div>
</ion-content>
<app-footer></app-footer>
