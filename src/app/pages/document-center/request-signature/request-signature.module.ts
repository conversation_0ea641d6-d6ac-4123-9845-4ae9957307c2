import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RequestSignaturePageRoutingModule } from './request-signature-routing.module';

import { RequestSignaturePage } from './request-signature.page';
import { SharedModule } from 'src/app/shared.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        RequestSignaturePageRoutingModule,
        SharedModule
    ],
    declarations: [RequestSignaturePage]
})
export class RequestSignaturePageModule { }
