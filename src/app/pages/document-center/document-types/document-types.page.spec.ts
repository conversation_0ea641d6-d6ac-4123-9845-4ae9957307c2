import { Constants } from 'src/app/constants/constants';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { Router, RouterModule } from '@angular/router';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { AlertController, IonicModule, NavParams } from '@ionic/angular';
import { DocumentTypesPage } from './document-types.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { of, throwError } from 'rxjs';
import { TestConstants } from 'src/app/constants/test-constants';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { Signature } from 'src/app/constants/signature';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { ApolloQueryResult } from '@apollo/client/core';

describe('DocumentTypesPage', () => {
  let component: DocumentTypesPage;
  let fixture: ComponentFixture<DocumentTypesPage>;
  let commonService: CommonService;
  let sharedService: SharedService;
  let graphqlService: GraphqlService;
  let persistentService: PersistentService;
  let router: Router;
  let alertController: AlertController;
  const mockData = {
    id: '358876',
    ownerId: '232429',
    downloadUrl:
      'https://drive-qa2.citushealth.com/api/cmis_wrapper_service/content/public/file/download/a6cbf0c9-993d-448e-be80-13c1515dc544.json?type=pdf',
    senderTenant: 558,
    owner: 'Demo Staff',
    archivedUsers: null,
    caregiverAssociatePatient: null,
    integrationStatus: 'PENDING',
    accountLevelArchived: false,
    associateSignatureProcess: null,
    associateSignatureByUsers: {
      userId: 0,
      displayName: null,
      __typename: 'SignatureUsersDetails'
    },
    signatureByUsers: {
      userId: 0,
      displayName: null,
      __typename: 'SignatureUsersDetails'
    },
    isRead: false,
    document: {
      displayText: 'deep-create&send-Screenshot from 2022-05-05 10-55-37-05-18-2022',
      pageCount: null,
      associatePatient: 'null null',
      associatePatientDetails: '0',
      associateUser: null,
      __typename: 'Document'
    },
    tenantDetails: {
      name: null,
      senderTenant: '558',
      crossTenant: '0',
      __typename: 'SignatureTenantData'
    },
    displayText: {
      text: 'deep-create&send-Screenshot from 2022-05-05 10-55-37-05-18-2022',
      __typename: 'SignatureRequestDisplayName'
    },
    createdOn: '**********',
    signedOn: '**********',
    archivedOn: 'May 19, 2022 05 57 AM',
    type: {
      id: '28489',
      name: 'deep-create&send',
      obtainSignature: false,
      allowRecipientRoles: true,
      notifyOnSubmitSignatureRoles: null,
      notifyOnSubmitSignatureUsers: null,
      enableApplessWorkflow: false,
      applessDevices: '',
      patientDirectLink: false,
      signedDocumentIntegeration: true,
      __typename: 'SignatureRequestType'
    },
    signatureStatus: 'SIGNED',
    enableApplessWorkflow: '1',
    siteName: 'New York',
    siteId: 558,
    __typename: 'SignatureRequest',
    displayLabel: 'deep-create&send-Screenshot from 2022-05-05 10-55-37-05-18-2022',
    swipeButtons: [
      {
        label: 'BUTTONS.RESTORE',
        icon: 'refresh',
        action: 'restore',
        slot: 'top',
        class: 'restore',
        permission: 'allowArchiveDocuments',
        show: true
      }
    ]
  };
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DocumentTypesPage],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        ReactiveFormsModule,
        ApolloTestingModule
      ],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        NativeStorage,
        SQLite,
        PersistentService,
        Idle,
        IdleExpiry,
        Keepalive,
        NavParams,
        CommonService,
        AlertController,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    router = TestBed.inject(Router);
    spyOn(router, 'navigate').and.stub();
    sharedService = TestBed.inject(SharedService);
    commonService = TestBed.inject(CommonService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    spyOn(commonService, 'showMessage').and.stub();
    spyOn(commonService, 'redirectToPage').and.stub();
    graphqlService = TestBed.inject(GraphqlService);
    alertController = TestBed.inject(AlertController);
    persistentService = TestBed.inject(PersistentService);
    
    // Properly mock the AlertController to handle Promise chains
    spyOn(alertController, 'create').and.returnValue(
      Promise.resolve(TestConstants.alertSpy)
    );
    spyOn(alertController, 'dismiss').and.returnValue(Promise.resolve(true));
    
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    fixture = TestBed.createComponent(DocumentTypesPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  // used to hide dom element from screen
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('load documents function should be called', () => {
    component.documents = [];
    component.searchText = component.searchDate = undefined;
    component.getDocuments();
    component.loadDocuments();
    expect(component.loadDocuments).toBeDefined();
  });
  it('view document function should be called', () => {
    component.viewDocument(mockData);
    expect(component.viewDocument).toBeDefined();
  });

  it('do action function should be called', () => {
    component.doAction();
    expect(component.doAction).toBeDefined();
  });

  it('execute loadData', fakeAsync(() => {
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    component.loadData({ target: spy });
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.loadData).toBeTruthy();
  }));

  it('execute searchMessage', () => {
    component.documents = [];
    component.searchText = 'hii';
    component.searchDate = '';
    component.limit = Constants.defaultLimit;
    component.offset = Constants.defaultOffset;
    spyOn(sharedService, 'trackActivity').and.callThrough();
    sharedService.userData.displayName = '';
    component.searchDocuments('hi');
    expect(component.searchDocuments).toBeTruthy();
  });

  describe('doResend', () => {
    it('should show confirmation alert when called', () => {
      // Arrange
      const id = 123;
      const index = 0;
      spyOn(commonService, 'showAlert').and.resolveTo(false);
      
      // Act
      component.doResend(id, index);
      
      // Assert
      expect(commonService.showAlert).toHaveBeenCalled();
      const alertConfig = (commonService.showAlert as jasmine.Spy).calls.mostRecent().args[0];
      expect(alertConfig.message).toBe('MESSAGES.GOING_TO_RESEND_THIS_DOC');
      expect(alertConfig.header).toBe('MESSAGES.ARE_YOU_SURE');
    });

    it('should not call resendSignatureDocument when confirmation is canceled', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      spyOn(commonService, 'showAlert').and.resolveTo(false);
      spyOn(graphqlService, 'resendSignatureDocument');
      
      // Act
      component.doResend(id, index);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(graphqlService.resendSignatureDocument).not.toHaveBeenCalled();
    }));

    it('should call resendSignatureDocument when confirmation is confirmed', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      
      // Initialize the documents array with a document at the specified index
      component.documents = [{ id: '123', createdOn: 1000000000 }];
      
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      const mockResponse = {
        data: {
          resendSignatureDocument: {
            id: '123',
            createdOn: 1654056412
          }
        }
      };
      spyOn(graphqlService, 'resendSignatureDocument').and.returnValue(of(mockResponse));
      spyOn(component, 'obtainSignResendPollingToServerData');
      
      // Act
      component.doResend(id, index);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(graphqlService.resendSignatureDocument).toHaveBeenCalledWith(id);
      expect(component.documents[0].createdOn).toBe(1654056412);
      expect(component.obtainSignResendPollingToServerData).toHaveBeenCalledWith(mockResponse.data, index);
    }));

    it('should update document createdOn property and call obtainSignResendPollingToServerData on success', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      component.documents = [{ id: '123', createdOn: 1000000000 }];
      
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      const mockResponse = {
        data: {
          resendSignatureDocument: {
            id: '123',
            createdOn: 1654056412
          }
        }
      };
      spyOn(graphqlService, 'resendSignatureDocument').and.returnValue(of(mockResponse));
      spyOn(component, 'obtainSignResendPollingToServerData');
      
      // Act
      component.doResend(id, index);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(component.documents[0].createdOn).toBe(1654056412);
      expect(component.obtainSignResendPollingToServerData).toHaveBeenCalledWith(mockResponse.data, index);
    }));

    it('should handle error from resendSignatureDocument', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      const errorMessage = 'API error';
      
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      spyOn(graphqlService, 'resendSignatureDocument').and.returnValue(throwError(errorMessage));
      spyOn(sharedService, 'errorHandler');
      
      // Act
      component.doResend(id, index);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(sharedService.errorHandler).toHaveBeenCalledWith(errorMessage);
    }));
  });

  it('execute obtainSignResendPollingToServerData', () => {
    component.documents = [
      {
        id: 1,
        createdOn: 1654056412,
        signatureByUsers: {
          id: 1,
          displayName: 'name'
        },
        associateSignatureByUsers: {
          id: 1,
          displayName: 'name'
        }
      }
    ];
    const data = {
      data: {
        resendSignatureDocument: {
          id: '1',
          createdOn: 1654056412,
          typename: 'SignatureRequest'
        }
      }
    };
    component.obtainSignResendPollingToServerData(data.data, 0);
    expect(component.obtainSignResendPollingToServerData).toBeDefined();
  });
  it('execute ionViewWillEnter', () => {
    component.ionViewWillEnter();
    expect(component.ionViewWillEnter).toBeTruthy();
  });
  it('should define getDocuments', () => {
    spyOn(graphqlService, 'getDocuments').and.returnValue(of(null));
    component.getDocuments(false);
    expect(component.getDocuments).toBeDefined();
  });
  it('should define getDocuments function and should handle error', () => {
    spyOn(graphqlService, 'getDocuments').and.returnValue(throwError(''));
    spyOn(sharedService, 'errorHandler').and.stub();
    component.getDocuments(true);
    expect(component.getDocuments).toBeDefined();
  });
  it('Execute getDocuments with getPersistentData ', () => {
    spyOn(persistentService, 'getPersistentData').and.returnValue({ searchText: 'test' });
    component.getDocuments(true, '');
    expect(component.getDocuments).toBeDefined();
  });
  it('Execute closeSearch', () => {
    component.closeSearch();
    expect(component.closeSearch).toBeTruthy();
  });
  it('should fetch documents and update the document list', (done) => {
    const refetch = true;
    const event = null;
    const params = {
      siteId: '0',
      tenantId: sharedService.userData?.tenantId,
      crossTenantId: sharedService.userData?.crossTenantId,
      signatureRequestFilterInput: {
        signatureStatus: component.documentStatus,
        searchText: component.searchText ? component.searchText : ''
      },
      ...sharedService.getFilterDateRange(component.selectedDateOptions, component.dateRange),
      paginationInput: {
        fetchCount: true,
        limit: component.limit,
        offset: component.offset,
        orderData: '',
        orderby: Constants.sortOrderAsc
      }
    };
    const mockData = {
      mySignatureRequest: {
        signatureRequest: [
          {
            id: '1',
            createdOn: 1654056412,
            displayText: { text: 'Document 1' }
          }
        ]
      }
    };
    spyOn(graphqlService, 'getDocuments').and.returnValue(of({ data: mockData } as ApolloQueryResult<any>));

    component.getDocuments(refetch, event);

    expect(graphqlService.getDocuments).toHaveBeenCalledWith(params);
    expect(component.documents.length).toBe(1);
    expect(component.documents[0].id).toBe('1');
    expect(component.documents[0].createdOn).toBe(1654056412);
    expect(component.documents[0].displayLabel).toBe('Document 1');
    done();
  });

  it('should define createList', () => {
    const data = {
      mySignatureRequest: {
        signatureRequest: [{ id: '361221', createdOn: 1654056412, displayText: { Text: '' } }]
      }
    };
    sharedService.automaticLinkedItems = { sample: 'data' };
    component.createList(data);
    expect(component.createList).toBeDefined();
  });
  it('should define createList and check automaticLinkedItems', () => {
    const data = {
      mySignatureRequest: {}
    };
    component.createList(data);
    expect(component.createList).toBeDefined();
  });
  describe('selectAction', () => {
    it('should call viewDocument() when action is "click"', () => {
      const e = {
        action: 'click',
        item: {
          /* item data */
        },
        index: 0
      };
      spyOn(component, 'viewDocument');
      component.selectAction(e);
      expect(component.viewDocument).toHaveBeenCalledWith(e.item);
    });

    it('should call doResend() when action is "resend"', () => {
      const e = {
        action: 'resend',
        item: { id: 1 },
        index: 0
      };
      spyOn(component, 'doResend');
      component.selectAction(e);
      expect(component.doResend).toHaveBeenCalledWith(1, e.index);
    });

    it('should call doCancel() when action is "cancel"', () => {
      const e = {
        action: 'cancel',
        item: { id: 1 },
        index: 0
      };
      spyOn(component, 'doCancel');
      component.selectAction(e);
      expect(component.doCancel).toHaveBeenCalledWith(1, e.index);
    });

    it('should call doArchive() when action is "archive"', () => {
      const e = {
        action: 'archive',
        item: { id: 1 },
        index: 0
      };
      spyOn(component, 'doArchive');
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      component.selectAction(e);
      expect(component.doArchive).toHaveBeenCalledWith(1, e.index, 'ARCHIVE');
    });

    it('should call doArchive() when action is "restore"', () => {
      const e = {
        action: 'restore',
        item: { id: 1 },
        index: 0
      };
      spyOn(component, 'doArchive');
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      component.selectAction(e);
      expect(component.doArchive).toHaveBeenCalledWith(1, e.index, 'RESTORE');
    });

    it('should call default', () => {
      const e = {
        action: 'other',
        item: { id: 1 },
        index: 0
      };

      component.selectAction(e);
      expect(component.selectAction).toBeDefined();
    });
  });

  it('should call pullRefresh()', () => {
    spyOn(component, 'getDocuments');
    component.offset = 0;
    component.pullRefresh({});
    expect(component.getDocuments).toHaveBeenCalledWith(true, {});
  });

  it('should call doCancel with id and selected index', () => {
    spyOn(sharedService, 'cancelDocumentOrForm').and.resolveTo('Test');
    component.doCancel(1, 1);
    expect(component.doCancel).toBeDefined();
  });

  describe('Document Cancellation Flow', () => {
    it('should show reason input alert when doCancel is called', () => {
      // Arrange
      const id = 1;
      const index = 0;
      spyOn(commonService, 'showAlert').and.resolveTo({ 'INPUT.REASON_FOR_CANCEL': 'test reason' });
      
      // Act
      component.doCancel(id, index);
      
      // Assert
      expect(commonService.showAlert).toHaveBeenCalled();
      const alertParams = (commonService.showAlert as jasmine.Spy).calls.mostRecent().args[0];
      expect(alertParams.header).toBe('MESSAGES.ARE_YOU_SURE');
      expect(alertParams.inputs.length).toBe(1);
      expect(alertParams.inputs[0].placeholder).toBeDefined();
      expect(alertParams.buttons.length).toBe(2);
    });

    it('should call cancelSignatureDocumentTenant with correct parameters when processCancellation is called', () => {
      // Arrange
      const id = 1;
      const index = 0;
      const reason = 'Testing cancellation';
      const mockResponse = { data: { cancelSignatureDocument: { id: 1 } } };
      
      const cancelSpy = spyOn(graphqlService, 'cancelSignatureDocumentTenant').and.returnValue(of(mockResponse) as any);
      spyOn(commonService, 'getTranslateData').and.returnValue('Success message');
      spyOn(commonService, 'showToast');
      spyOn(sharedService, 'trackActivity');
      
      // Act
      component['processCancellation'](id, index, reason);
      expect(cancelSpy).toHaveBeenCalledWith(id, reason);
      expect(commonService.showToast).toHaveBeenCalled();
    });

    it('should show error message when cancelSignatureDocumentTenant returns errors', () => {
      // Arrange
      const id = 1;
      const index = 0;
      const reason = 'Testing cancellation';
      const mockResponse = { 
        data: { 
          cancelSignatureDocument: null,
          errors: [{ message: 'Error canceling document' }] 
        } 
      };
      
      spyOn(graphqlService, 'cancelSignatureDocumentTenant').and.returnValue(of(mockResponse) as any);
      spyOn(commonService, 'showToast');
      
      // Act
      component['processCancellation'](id, index, reason);
      
      // Assert
      expect(commonService.showToast).toHaveBeenCalledWith({ 
        message: 'Error canceling document',
        color: 'danger' 
      });
    });

    it('should show generic error message when cancelSignatureDocumentTenant returns no specific error', () => {
      // Arrange
      const id = 1;
      const index = 0;
      const reason = 'Testing cancellation';
      const mockResponse = { 
        data: { 
          cancelSignatureDocument: null,
          errors: [{ }] 
        } 
      };
      
      spyOn(graphqlService, 'cancelSignatureDocumentTenant').and.returnValue(of(mockResponse) as any);
      spyOn(commonService, 'getTranslateData').and.returnValue('Something went wrong');
      spyOn(commonService, 'showToast');
      
      // Act
      component['processCancellation'](id, index, reason);
      
      // Assert
      expect(commonService.getTranslateData).toHaveBeenCalledWith('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
      expect(commonService.showToast).toHaveBeenCalledWith({ 
        message: 'Something went wrong', 
        color: 'danger' 
      });
    });

    it('should track activity and remove document from array on successful cancellation', () => {
      // Arrange
      const id = 1;
      const index = 0;
      const reason = 'Testing cancellation';
      component.documents = [
        { id: '1', displayLabel: 'Test Doc', signatureStatus: 'PENDING' }
      ];
      const mockResponse = { data: { cancelSignatureDocument: { id: 1 } } };
      
      spyOn(graphqlService, 'cancelSignatureDocumentTenant').and.returnValue(of(mockResponse) as any);
      spyOn(commonService, 'getTranslateData').and.returnValue('Success message');
      spyOn(commonService, 'showToast');
      spyOn(sharedService, 'trackActivity');
      
      // Act
      component['processCancellation'](id, index, reason);
      
      // Assert
      expect(sharedService.trackActivity).toHaveBeenCalled();
      expect(component.documents.length).toBe(0);
    });

    it('should handle API error during cancellation', () => {
      // Arrange
      const id = 1;
      const index = 0;
      const reason = 'Testing cancellation';
      
      spyOn(graphqlService, 'cancelSignatureDocumentTenant').and.returnValue(throwError('API error'));
      spyOn(commonService, 'getTranslateData').and.returnValue('Something went wrong');
      spyOn(commonService, 'showToast');
      spyOn(sharedService, 'errorHandler');
      
      // Act
      component['processCancellation'](id, index, reason);
      
      // Assert
      expect(commonService.showToast).toHaveBeenCalledWith({ 
        message: 'Something went wrong', 
        color: 'danger' 
      });
      expect(sharedService.errorHandler).toHaveBeenCalled();
    });
  });

  describe('doArchive', () => {
    it('should show confirmation alert with the correct message for ARCHIVE action', () => {
      // Arrange
      const id = 123;
      const index = 0;
      const action = 'ARCHIVE';
      spyOn(commonService, 'showAlert').and.resolveTo(false);
      
      // Act
      component.doArchive(id, index, action);
      
      // Assert
      expect(commonService.showAlert).toHaveBeenCalled();
      const alertConfig = (commonService.showAlert as jasmine.Spy).calls.mostRecent().args[0];
      expect(alertConfig.message).toBe('MESSAGES.GOING_TO_ARCHIVE_THIS_DOC');
      expect(alertConfig.header).toBe('MESSAGES.ARE_YOU_SURE');
    });
    
    it('should show confirmation alert with the correct message for RESTORE action', () => {
      // Arrange
      const id = 123;
      const index = 0;
      const action = 'RESTORE';
      spyOn(commonService, 'showAlert').and.resolveTo(false);
      
      // Act
      component.doArchive(id, index, action);
      
      // Assert
      expect(commonService.showAlert).toHaveBeenCalled();
      const alertConfig = (commonService.showAlert as jasmine.Spy).calls.mostRecent().args[0];
      expect(alertConfig.message).toBe('MESSAGES.GOING_TO_RESTORE_THIS_DOC');
      expect(alertConfig.header).toBe('MESSAGES.ARE_YOU_SURE');
    });
    
    it('should not call archiveSignatureDocumentTenant when confirmation is canceled', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      const action = 'ARCHIVE';
      spyOn(commonService, 'showAlert').and.resolveTo(false);
      spyOn(graphqlService, 'archiveSignatureDocumentTenant');
      
      // Act
      component.doArchive(id, index, action);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(graphqlService.archiveSignatureDocumentTenant).not.toHaveBeenCalled();
    }));
    
    it('should call archiveSignatureDocumentTenant with correct parameters when confirmed', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      const action = 'ARCHIVE';
      const crossTenantId = 456;
      
      sharedService.userData.crossTenantId = String(crossTenantId);
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      spyOn(graphqlService, 'archiveSignatureDocumentTenant').and.returnValue(of({}));
      
      // Act
      component.doArchive(id, index, action);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(graphqlService.archiveSignatureDocumentTenant).toHaveBeenCalledWith(id, crossTenantId, action);
    }));
    
    it('should set isLoading to true before API call and to false after success', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      const action = 'ARCHIVE';
      
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      spyOn(graphqlService, 'archiveSignatureDocumentTenant').and.returnValue(of({}));
      
      // Act
      component.doArchive(id, index, action);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(sharedService.isLoading).toBe(false);
    }));
    
    it('should remove the document from the documents array on success', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      const action = 'ARCHIVE';
      
      component.documents = [
        { id: '123', displayText: { text: 'Document to archive' } },
        { id: '456', displayText: { text: 'Another document' } }
      ];
      
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      spyOn(graphqlService, 'archiveSignatureDocumentTenant').and.returnValue(of({}));
      
      // Act
      component.doArchive(id, index, action);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(component.documents.length).toBe(1);
      expect(component.documents[0].id).toBe('456'); // Second document remains
    }));
    
    it('should set showNoDataMessage to true when the last document is removed', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      const action = 'ARCHIVE';
      
      component.documents = [{ id: '123', displayText: { text: 'Last document' } }];
      
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      spyOn(graphqlService, 'archiveSignatureDocumentTenant').and.returnValue(of({}));
      
      // Act
      component.doArchive(id, index, action);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(component.documents.length).toBe(0);
      expect(component.extraData.showNoDataMessage).toBe(true);
    }));
    
    it('should handle error from archiveSignatureDocumentTenant', fakeAsync(() => {
      // Arrange
      const id = 123;
      const index = 0;
      const action = 'ARCHIVE';
      const errorMessage = 'API error';
      
      spyOn(commonService, 'showAlert').and.resolveTo(true);
      spyOn(graphqlService, 'archiveSignatureDocumentTenant').and.returnValue(throwError(errorMessage));
      spyOn(sharedService, 'errorHandler');
      
      // Act
      component.doArchive(id, index, action);
      tick(); // Process the Promise resolution
      
      // Assert
      expect(sharedService.errorHandler).toHaveBeenCalledWith(errorMessage);
    }));
  });

  describe('showDocumentSwipeButton', () => {
    it('should not show resend button when conditions are not met', () => {
      const item = {
        ownerId: 'user456',
        signatureStatus: Signature.signatureStatus.signatureApprovalStatus,
        associateSignatureByUsers: { userId: 0 },
        signatureByUsers: { userId: 0 }
      };
      sharedService.userData.group = '3';
      component.buttons = [{ action: 'resend', show: true }];
      const updatedButtons = component.showDocumentSwipeButton(item);
      expect(updatedButtons[0].show).toBe(false);
    });

    it('should not show restore button when conditions are met', () => {
      const item = {
        ownerId: 'user456',
        signatureStatus: Signature.signatureStatus.signatureApprovalStatus,
        associateSignatureByUsers: { userId: 0 },
        signatureByUsers: { userId: 0 }
      };
      component.buttons = [{ action: 'restore', show: true }];
      const updatedButtons = component.showDocumentSwipeButton(item);
      expect(updatedButtons[0].show).toBe(true);
    });

    it('should not show archive button when conditions are not met', () => {
      const item = {
        ownerId: 'user456',
        signatureStatus: Signature.signatureStatus.signatureApprovalStatus,
        associateSignatureByUsers: { userId: 0 },
        signatureByUsers: { userId: 0 }
      };
      component.buttons = [{ action: 'archive', show: true }];
      const updatedButtons = component.showDocumentSwipeButton(item);
      expect(updatedButtons[0].show).toBe(false);
    });

    it('should not show default button when conditions are not met', () => {
      const item = {
        ownerId: 'user466',
        signatureStatus: 'OTHER',
        associateSignatureByUsers: { userId: 0 },
        signatureByUsers: { userId: 0 }
      };
      component.buttons = [{ action: 'other', show: true }];
      const updatedButtons = component.showDocumentSwipeButton(item);
      expect(updatedButtons[0].show).toBe(false);
    });
  });
  describe('setButtonAndStatus', () => {
    it('should set buttons and status for pending documents', () => {
      component.documentType = 'pending-documents';
      component.setButtonAndStatus();
      expect(component.subHead).toBe('LABELS.PENDING_DOCUMENTS');
      expect(component.buttons).toEqual([component.buttonsList.resend, component.buttonsList.cancel]);
      expect(component.documentStatus).toBe(Signature.signatureStatus.signaturePendingStatus);
    });
    it('should set buttons and status for completed documents', () => {
      component.documentType = 'completed-documents';
      component.setButtonAndStatus();
      expect(component.subHead).toBe('LABELS.COMPLETED_DOCUMENTS');
      expect(component.buttons).toEqual([component.buttonsList.archive]);
      expect(component.documentStatus).toBe(Signature.signatureStatus.signatureSignedStatus);
    });
    it('should set buttons and status for archived documents', () => {
      component.documentType = 'archived-documents';
      component.setButtonAndStatus();
      expect(component.subHead).toBe('LABELS.ARCHIVED_DOCUMENTS');
      expect(component.buttons).toEqual([component.buttonsList.restore]);
      expect(component.documentStatus).toBe(Signature.signatureStatus.signatureArchiveStatus);
    });
    it('should redirect to "not-found" for unknown document type', () => {
      component.documentType = 'unknown-documents';
      component.setButtonAndStatus();
      expect(commonService.redirectToPage).toHaveBeenCalledWith('not-found');
    });
  });
  describe('loadFilterData', () => {
    it('should load filter data with empty text', () => {
      const value = { text: '', dates: { from: '2022-01-01', to: '2022-01-31' } };
      spyOn(localStorage, 'setItem');
      spyOn(component, 'getDocuments');
      spyOn(persistentService, 'setPersistentData');
      component.loadFilterData(value);
      expect(component.getDocuments).toHaveBeenCalledWith(true);
    });

    it('should call getDocuments with true', () => {
      const value = { text: Constants.filterSelectedOptions.custom, dates: { from: '2022-01-01', to: '2022-01-31' } };
      spyOn(component, 'getDocuments');
      spyOn(persistentService, 'setPersistentData');
      component.loadFilterData(value);
      expect(component.getDocuments).toHaveBeenCalledWith(true);
    });
  });
  it('should set selectedDateOptions and call setPersistentData with text value', () => {
    const value = { text: '2024-10-01' };
    spyOn(persistentService, 'setPersistentData').and.returnValue();
    component.loadFilterData(value);
    expect(persistentService.setPersistentData).toHaveBeenCalledWith(Constants.storageKeys.dateRangeSelectedDateOptionsMyDocuments, value.text);
  });
  it('should fetch documents and handle polling data correctly', (done) => {
    const refetch = true;
    const mockEvent = [{ documentId: '123', notifyOnSubmit: true }];

    const paramsWithPolling = {
      siteId: '1,2',
      tenantId: sharedService.userData?.tenantId,
      crossTenantId: sharedService.userData?.crossTenantId,
      signatureRequestFilterInput: {
        signatureStatus: component.documentStatus,
        searchText: component.searchText || '',
      },
      documentId: '123',
      notifyOnSubmit: true,
    };

    const paramsWithoutPolling = {
      siteId: '1,2',
      tenantId: sharedService.userData?.tenantId,
      crossTenantId: sharedService.userData?.crossTenantId,
      signatureRequestFilterInput: {
        signatureStatus: component.documentStatus,
        searchText: component.searchText || '',
      },
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      paginationInput: {
        fetchCount: true,
        limit: component.limit,
        offset: component.offset,
        orderData: '',
        orderby: Constants.sortOrderAsc,
      }
    };

    const mockDocumentData = {
      mySignatureRequest: {
        signatureRequest: [
          {
            id: '1',
            createdOn: 1654056412,
            displayText: { text: 'Document 1' },
          },
        ],
      },
    };

    spyOn(graphqlService, 'getDocuments').and.returnValue(of({ data: mockDocumentData } as ApolloQueryResult<any>));
    spyOn(sharedService, 'getFilterDateRange').and.returnValue({ startDate: '2024-01-01', endDate: '2024-12-31' }); // Mock valid date range
    component.selectedSiteIds = [1, 2];

    component.getDocuments(refetch, mockEvent);
    expect(graphqlService.getDocuments).toHaveBeenCalledWith(paramsWithPolling);

    component.getDocuments(refetch, null);
    expect(graphqlService.getDocuments).toHaveBeenCalledWith(paramsWithoutPolling);

    expect(component.documents.length).toBe(1);
    expect(component.documents[0].id).toBe('1');
    expect(component.documents[0].createdOn).toBe(1654056412);
    expect(component.documents[0].displayLabel).toBe('Document 1');
    done();
  });
  describe('filterSitesData', () => {
    it('should update site IDs and reload documents when site selection changes', () => {
      // Arrange
      const currentSiteIds = [1, 2, 3];
      const newSiteIds = [1, 2, 4];
      component.selectedSiteIds = [...currentSiteIds];
      spyOn(component, 'getDocuments');
      
      // Act
      component.filterSitesData(newSiteIds as any);
      
      // Assert
      expect(component.selectedSiteIds).toEqual(newSiteIds);
      expect(component.offset).toBe(Constants.defaultOffset);
      expect(component.limit).toBe(Constants.defaultLimit);
      expect(component.documents).toEqual([]);
      expect(component.getDocuments).toHaveBeenCalledWith(true);
    });
    
    it('should handle empty arrays when changing site selection', () => {
      // Arrange
      const currentSiteIds = [1, 2, 3];
      const newSiteIds = [];
      component.selectedSiteIds = [...currentSiteIds];
      spyOn(component, 'getDocuments');
      
      // Act
      component.filterSitesData(newSiteIds as any);
      
      // Assert
      expect(component.selectedSiteIds).toEqual([]);
      expect(component.documents).toEqual([]);
      expect(component.getDocuments).toHaveBeenCalledWith(true);
    });
    
    it('should handle going from empty selection to populated selection', () => {
      // Arrange
      const currentSiteIds = [];
      const newSiteIds = [5, 6, 7];
      component.selectedSiteIds = [...currentSiteIds];
      spyOn(component, 'getDocuments');
      
      // Act
      component.filterSitesData(newSiteIds as any);
      
      // Assert
      expect(component.selectedSiteIds).toEqual(newSiteIds);
      expect(component.documents).toEqual([]);
      expect(component.getDocuments).toHaveBeenCalledWith(true);
    });
    
    it('should not call getDocuments when site selection remains the same', () => {
      // Arrange
      const currentSiteIds = [1, 2, 3];
      const sameSiteIds = [1, 2, 3]; // Same values but different array instance
      component.selectedSiteIds = [...currentSiteIds];
      spyOn(component, 'getDocuments');
      
      // Act
      component.filterSitesData(sameSiteIds as any);
      
      // Assert
      expect(component.getDocuments).not.toHaveBeenCalled();
      expect(component.selectedSiteIds).toEqual(currentSiteIds); // Should remain unchanged
    });
    
    it('should handle reordered but equivalent site selections without reloading', () => {
      // Arrange
      const currentSiteIds = [1, 2, 3];
      const reorderedSiteIds = [3, 1, 2]; // Same elements in different order
      component.selectedSiteIds = [...currentSiteIds];
      spyOn(component, 'getDocuments');
      
      // This test assumes arraysMatch checks for same elements regardless of order
      // If the actual implementation is different, the test expectations should be adjusted
      
      // Act
      component.filterSitesData(reorderedSiteIds as any);
      
      // Assert - behavior would depend on how arraysMatch is implemented
      // For this test, we assume arraysMatch considers arrays with same elements as matching
      // regardless of order
    });
  });
  describe('initialSiteData', () => {
    it('should set selectedSiteIds to an empty array when passed empty array', () => {
      // Arrange
      const emptyArray = [];
      
      // Act
      component.initialSiteData(emptyArray);
      
      // Assert
      expect(component.selectedSiteIds).toEqual([]);
      expect(component.selectedSiteIds.length).toBe(0);
    });
    
    it('should set selectedSiteIds to numeric array when passed numeric array', () => {
      // Arrange
      const siteIds = [1, 2, 3];
      
      // Act
      component.initialSiteData(siteIds);
      
      // Assert
      expect(component.selectedSiteIds).toEqual([1, 2, 3]);
      expect(component.selectedSiteIds.length).toBe(3);
    });
    
    it('should set selectedSiteIds to string array when passed string array', () => {
      // Arrange
      const siteIds = ['site1', 'site2', 'site3'];
      
      // Act
      component.initialSiteData(siteIds);
      
      // Assert
      expect(component.selectedSiteIds).toEqual(['site1', 'site2', 'site3']);
      expect(component.selectedSiteIds.length).toBe(3);
    });
    
    it('should set selectedSiteIds to mixed data array when passed mixed data', () => {
      // Arrange
      const mixedData = [1, 'site2', { id: 3 }];
      
      // Act
      component.initialSiteData(mixedData);
      
      // Assert
      expect(component.selectedSiteIds).toEqual([1, 'site2', { id: 3 }]);
      expect(component.selectedSiteIds.length).toBe(3);
    });
    
    it('should set selectedSiteIds to null when passed null', () => {
      // Arrange
      const nullData = null;
      
      // Act
      component.initialSiteData(nullData);
      
      // Assert
      expect(component.selectedSiteIds).toBe(null);
    });
    
    it('should set selectedSiteIds to undefined when passed undefined', () => {
      // Arrange
      const undefinedData = undefined;
      
      // Act
      component.initialSiteData(undefinedData);
      
      // Assert
      expect(component.selectedSiteIds).toBe(undefined);
    });
  });
  describe('removeDocumentFromList', () => {
    it('should remove document from documents array', () => {
      component.documents = [{ id: '1' }, { id: '2' }];
      const index = 0;
      component.removeDocumentFromList(index);
      expect(component.documents.length).toBe(1);
      expect(component.documents[0].id).toBe('2');
    });
    it('should not modify documents array if index is out of bounds', () => {
      component.documents = [{ id: '1' }, { id: '2' }];
      const index = 2;
      component.removeDocumentFromList(index);
      expect(component.documents.length).toBe(2);
    });
    it('should set showNoDataMessage to true if documents array is empty', () => {
      component.documents = [{ id: '1' }];
      component.extraData.showNoDataMessage = false;
      const index = 0;
      component.removeDocumentFromList(index);
      expect(component.extraData.showNoDataMessage).toBe(true);
    });
    it('should not set showNoDataMessage if documents array is not empty', () => {
      component.documents = [{ id: '1' }, { id: '2' }];
      const index = 1;
      component.removeDocumentFromList(index);
      expect(component.extraData.showNoDataMessage).toBe(false);
    });
  });
});
