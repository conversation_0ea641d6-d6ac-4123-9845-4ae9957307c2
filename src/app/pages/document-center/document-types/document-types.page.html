<app-header headerTitle="MENU.DOCUMENT_CENTER" [subHead]="subHead"></app-header>
<div *ngIf="isEnableMultiSite && sharedService?.userData?.mySites?.length > 1" class="doc-types-site">
    <app-sites (filterSites)="filterSitesData($event)" (onLoadSites)="initialSiteData($event)" [siteLabel]="siteLabel"></app-sites>
</div>
<div class="site-header-padding" *ngIf="!(isEnableMultiSite && sharedService?.userData?.mySites?.length > 1)"></div>
<app-search-bar (seachAction)="searchDocuments($event)" id="search-bar" (closeSearch)="loadDocuments()"
  (closeDateFilter)="loadFilterData($event)"
  [selectedDateOptions]="selectedDateOptions"
  [isDateFilter]="true"
  [searchText]="searchText"
  (closeSearch)="closeSearch()"
  [dateRange]="dateRange">
</app-search-bar>
<app-select-list-grid [subHead]="subHead" (viewType)="isList=$event"
    [showIcons]="documents?.length"></app-select-list-grid>
<ion-content>
    <ion-refresher slot="fixed" (ionRefresh)="pullRefresh($event)">
        <ion-refresher-content></ion-refresher-content>
    </ion-refresher>
    <app-data-list *ngIf="isList" [extraData]="extraData" [listData]="documents" (itemBtnClick)="selectAction($event)">
    </app-data-list>
    <app-data-grid (itemBtnClick)="selectAction($event)" *ngIf="!isList" [gridData]="documents" [extraData]="extraData">
    </app-data-grid>
    <!-- TODO! CHP-3598-->
    <ng-container *ngIf="showLoadMore">
        <ion-infinite-scroll threshold="100px" (ionInfinite)="loadData($event)">
            <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{'BUTTONS.LOAD_MORE' | translate}}">
            </ion-infinite-scroll-content>
        </ion-infinite-scroll>
    </ng-container>

</ion-content>
<!-- TODO! CHP-3597 -->
<app-action-button [button]="button" (buttonClick)="doAction()"></app-action-button>
<app-footer backButtonLink="document-center"></app-footer>