import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { DocumentTypesPageRoutingModule } from './document-types.routing.module';
import { DocumentTypesPage } from './document-types.page';
import { SharedModule } from 'src/app/shared.module';
import { DataListModule } from 'src/app/components/data-list/data-list.module';
import { SearchBarModule } from 'src/app/components/search-bar/search-bar.module';
import { SelectListGridModule } from 'src/app/components/select-list-grid/select-list-grid.module';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';
import { DataGridModule } from 'src/app/components/data-grid/data-grid.module';
import { SitesModule } from 'src/app/components/sites/sites.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        DocumentTypesPageRoutingModule,
        SharedModule,
        SearchBarModule,
        DataListModule,
        SelectListGridModule,
        ActionButtonModule,
        DataGridModule,
        SitesModule
    ],
    declarations: [DocumentTypesPage]
})
export class DocumentTypesPageModule { }
