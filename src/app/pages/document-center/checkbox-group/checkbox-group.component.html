<ion-header class="common-plain-header">
  <ion-toolbar>
    <ion-buttons slot="start" class="header-menu left">
      <ion-button class="ion-text-capitalize" id="cancel" (click)="cancel(checkboxGroupObject)"
        *ngIf="!checkboxGroupObject?.addToExistingGroup">
        {{ 'BUTTONS.CANCEL' | translate }}</ion-button>
    </ion-buttons>
    <ion-buttons slot="end" class="header-menu right">
      <ion-button class="ion-text-capitalize" id="save" (click)="save()"
        *ngIf="checkboxGroupObject?.showCheckboxGroup || checkboxGroupObject?.addToExistingGroup">
        {{ 'BUTTONS.SAVE' | translate }}</ion-button>
    </ion-buttons>
    <h1 class="header-title" *ngIf="checkboxGroupObject?.showCheckboxGroup">{{ 'LABELS.CHECKBOX_GROUP' | translate }}
    </h1>
  </ion-toolbar>
</ion-header>
<ion-content *ngIf="checkboxGroupObject?.showCheckboxGroup">
  <div class="popup-list-group">
    <input type="text" id="checkbox-group" [placeholder]="'PLACEHOLDERS.CHECKBOX_GROUP_NAME' | translate"
      class="common-input group-name-input" [(ngModel)]="groupName" autocapitalize="on">
    <Label class="group-name-label" *ngIf="groupNameRequired">{{ 'MESSAGES.GROUP_NAME_REQUIRED' | translate
      }}</Label><br>
    <label class="checkbox-label">{{ 'LABELS.CHECKBOX_WITH_LABEL' | translate }}</label>
    <div class="popup-group">
      <ion-row>
        <ion-col>
          <button class="button-left" id="checkbox-withlabel-button-left" (click)="checkboxGroupCountDecrease(true)">
            <ion-icon name="remove"></ion-icon>
          </button>
        </ion-col>
        <ion-col>
          <input type="text" class="label-input common-input" readonly="true"
            [ngModel]="newCheckBoxWithLabelGroupNumber" autocapitalize="on">
        </ion-col>
        <ion-col>
          <button class="button-right" id="checkbox-withlabel-button-right" (click)="checkboxGroupCountIncrease(true)">
            <ion-icon name="add"></ion-icon>
          </button>
        </ion-col>
      </ion-row>
    </div>
    <label class="checkbox-label">{{ 'LABELS.CHECKBOX_WITHOUT_LABEL' | translate }}</label>
    <div class="popup-group">
      <ion-row>
        <ion-col>
          <button class="button-left" id="checkbox-withoutlabel-button-left"
            (click)="checkboxGroupCountDecrease(false)">
            <ion-icon name="remove"></ion-icon>
          </button>
        </ion-col>
        <ion-col>
          <input type="text" class="label-input common-input" readonly="true"
            [ngModel]="newCheckBoxWithOutLabelGroupNumber" autocapitalize="on">
        </ion-col>
        <ion-col>
          <button class="button-right" id="checkbox-withoutlabel-button-right"
            (click)="checkboxGroupCountIncrease(false)">
            <ion-icon name="add"></ion-icon>
          </button>
        </ion-col>
      </ion-row>
    </div>
  </div>
</ion-content>
<ion-content *ngIf="!checkboxGroupObject?.showCheckboxGroup">
  <div class="group-item" *ngIf="!checkboxGroupObject?.addToExistingGroup">
    <div *ngFor="let data of checkFieldDetails">
      <label class="item-label">{{data.groupName}}</label>
    </div>
    <label class="item-label" id="new-checkbox-group" (click)="newCheckboxGroup()">{{ 'LABELS.NEW_CHECKBOX_GROUP' |
      translate }}</label>
  </div>
  <div *ngIf="checkboxGroupObject?.addToExistingGroup" class="existing-group-item">
    <h6>{{ 'MESSAGES.ADD_TO_EXISTING_CHECKBOX_GROUP' | translate }}</h6>
    <input type="radio" id="radio-button-no" name="groupList" checked="true" (click)="showGroupList(false)">
    {{ 'BUTTONS.NO' | translate }}
    <input type="radio" id="radio-button-yes" name="groupList" class="radio-button-space" (click)="showGroupList(true)">
    {{ 'BUTTONS.YES' | translate }}
    <div *ngIf="showGroup">
      <div *ngFor="let data of checkFieldDetails;let d=index">
        <input type="radio" id="add-item-{{d}}" (click)="addItemToGroup(data)">
        <label class="item-label">{{data.groupName}}</label>
      </div>
    </div>

  </div>
</ion-content>