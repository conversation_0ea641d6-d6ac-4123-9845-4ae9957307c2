import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { AuthGuard } from 'src/app/services/auth-guard/auth.guard';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ReactiveFormsModule } from '@angular/forms';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';

import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';
import { CheckboxGroupComponent } from './checkbox-group.component';

describe('CheckboxGroupComponent', () => {
  let component: CheckboxGroupComponent;
  let fixture: ComponentFixture<CheckboxGroupComponent>;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [CheckboxGroupComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule,
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [
        NavParams,
        AuthGuard,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(CheckboxGroupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('cancel', () => {
    it('should hide the checkbox group if checkboxGroupObject.isNewCheckboxGroup,showCheckboxGroup,showCheckboxGroup is false', () => {
      const checkboxGroupObject = { isNewCheckboxGroup: false, showCheckboxGroup: false, addToExistingGroup: false };
      component.cancel(checkboxGroupObject);
      expect(component.cancel).toBeDefined();
    });

    it('should hide the checkbox group if checkboxGroupObject.isNewCheckboxGroup true', () => {
      const checkboxGroupObject = { isNewCheckboxGroup: true };
      component.checkboxGroupObject = {
        showCheckboxGroup: ''
      };
      fixture.detectChanges();
      component.cancel(checkboxGroupObject);
      expect(component.checkboxGroupObject.showCheckboxGroup).toBeFalse();
    });

    it('should hide the checkbox group and reset addToExistingGroup if showCheckboxGroup and addToExistingGroup are true', () => {
      const checkboxGroupObject = { showCheckboxGroup: true, addToExistingGroup: true };
      component.checkboxGroupObject = {
        showCheckboxGroup: '',
        addToExistingGroup: ''
      };
      fixture.detectChanges();
      component.cancel(checkboxGroupObject);

      expect(component.checkboxGroupObject.showCheckboxGroup).toBeFalse();
      expect(component.checkboxGroupObject.addToExistingGroup).toBeFalse();
    });

    it('should dismiss the modal controller', () => {
      component.cancel({});
      expect(modalController.dismiss).toHaveBeenCalled();
    });
  });
  describe('checkboxGroupCountIncrease', () => {
    it('should increase newCheckBoxWithLabelGroupNumber by 1 when condition is true', () => {
      component.newCheckBoxWithLabelGroupNumber = 5;
      const condition = true;
      component.checkboxGroupCountIncrease(condition);
      expect(component.newCheckBoxWithLabelGroupNumber).toBe(6);
    });
    it('should increase newCheckBoxWithOutLabelGroupNumber by 1 when condition is false', () => {
      component.newCheckBoxWithOutLabelGroupNumber = 3;
      const condition = false;
      component.checkboxGroupCountIncrease(condition);
      expect(component.newCheckBoxWithOutLabelGroupNumber).toBe(4);
    });

    it('should not modify newCheckBoxWithLabelGroupNumber when condition is false', () => {
      component.newCheckBoxWithLabelGroupNumber = 5;
      const condition = false;
      component.checkboxGroupCountIncrease(condition);
      expect(component.newCheckBoxWithLabelGroupNumber).toBe(5);
    });

    it('should not modify newCheckBoxWithOutLabelGroupNumber when condition is true', () => {
      component.newCheckBoxWithOutLabelGroupNumber = 3;
      const condition = true;
      component.checkboxGroupCountIncrease(condition);
      expect(component.newCheckBoxWithOutLabelGroupNumber).toBe(3);
    });
  });
  describe('checkboxGroupCountDecrease', () => {
    it('should decrease newCheckBoxWithLabelGroupNumber by 1 when condition is true and newCheckBoxWithLabelGroupNumber > 0', () => {
      component.newCheckBoxWithLabelGroupNumber = 5;
      const condition = true;
      component.checkboxGroupCountDecrease(condition);
      expect(component.newCheckBoxWithLabelGroupNumber).toBe(4);
    });
    it('should decrease newCheckBoxWithOutLabelGroupNumber by 1 when condition is false and newCheckBoxWithOutLabelGroupNumber > 0', () => {
      component.newCheckBoxWithOutLabelGroupNumber = 3;
      const condition = false;
      component.checkboxGroupCountDecrease(condition);
      expect(component.newCheckBoxWithOutLabelGroupNumber).toBe(2);
    });
    it('should not modify newCheckBoxWithLabelGroupNumber when condition is false', () => {
      component.newCheckBoxWithLabelGroupNumber = 5;
      const condition = false;
      component.checkboxGroupCountDecrease(condition);
      expect(component.newCheckBoxWithLabelGroupNumber).toBe(5);
    });
    it('should not modify newCheckBoxWithOutLabelGroupNumber when condition is true', () => {
      component.newCheckBoxWithOutLabelGroupNumber = 3;
      const condition = true;
      component.checkboxGroupCountDecrease(condition);
      expect(component.newCheckBoxWithOutLabelGroupNumber).toBe(3);
    });
  });
  describe('checkGroupIsExists', () => {
    it('should return true if enteredGroupName exists in droppedArray', () => {
      const droppedArray = [{ groupName: 'Group 1' }, { groupName: 'Group 2' }, { groupName: 'Group 3' }];
      const enteredGroupName = 'Group 2';
      const result = component.checkGroupIsExists(droppedArray, enteredGroupName);
      expect(result).toBeTrue();
    });

    it('should return false if enteredGroupName does not exist in droppedArray', () => {
      const droppedArray = [{ groupName: 'Group 1' }, { groupName: 'Group 2' }, { groupName: 'Group 3' }];
      const enteredGroupName = 'Group 4';
      const result = component.checkGroupIsExists(droppedArray, enteredGroupName);
      expect(result).toBeFalse();
    });
  });
  describe('newCheckboxGroup', () => {
    it('should set showCheckboxGroup and isNewCheckboxGroup to true when newCheckboxGroup is called', () => {
      component.checkboxGroupObject = {
        showCheckboxGroup: '',
        isNewCheckboxGroup: ''
      };
      fixture.detectChanges();
      component.newCheckboxGroup();
      expect(component.checkboxGroupObject.showCheckboxGroup).toBeTrue();
      expect(component.checkboxGroupObject.isNewCheckboxGroup).toBeTrue();
    });
  });
  describe('showGroupList', () => {
    it('should set showGroup when condition is true', () => {
      component.showGroupList(true);
      expect(component.showGroup).toEqual(true);
    });

    it('should set showGroup when condition is false', () => {
      component.showGroupList(false);
      expect(component.showGroup).toEqual(false);
    });
  });
  describe('addItemToGroup', () => {
    it('should set newCheckboxItem when addItemToGroup is called', () => {
      const groupData = { id: 1, name: 'Group 1' };
      component.addItemToGroup(groupData);
      expect(component.newCheckboxItem).toEqual(groupData);
    });
  });

  it('should call save', () => {
    component.checkFieldDetails = [];
    component.paletteSize = {
      top: '',
      left: '',
      width: '',
      height: ''
    };
    component.save();
    expect(component.save).toBeDefined();
  });
});
