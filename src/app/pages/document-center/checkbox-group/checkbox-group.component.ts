import { CommonService } from 'src/app/services/common-service/common.service';
import { Component, OnInit } from '@angular/core';
import { ModalController, NavParams } from '@ionic/angular';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { isBlank } from 'src/app/utils/utils';
import { Signature } from 'src/app/constants/signature';

@Component({
  selector: 'app-checkbox-group',
  templateUrl: './checkbox-group.component.html',
  styleUrls: ['./checkbox-group.component.scss']
})
export class CheckboxGroupComponent implements OnInit {
  signatureTypeTagData: any;
  newCheckBoxWithLabelGroupNumber: number;
  newCheckBoxWithOutLabelGroupNumber: number;
  groupName: string;
  checkboxGroupDetails: any = [];
  addObjects: any;
  groupNameRequired = false;
  checkFieldDetails: any;
  data: any;
  checkboxGroupObject: any;
  showGroup = false;
  newCheckboxItem: any;
  documentSignersDetails: any;
  paletteSize: any;
  documentFileSizeInfo: any;
  pageCount = 1;
  userDetails: any;
  constructor(
    private readonly modalController: ModalController,
    private readonly navParams: NavParams,
    private readonly sharedService: SharedService,
    private readonly common: CommonService
  ) {
    this.data = {
      showCheckboxGroup: true,
      addToExistingGroup: false,
      isNewCheckboxGroup: false,
      checkboxLabel: true
    };
    this.signatureTypeTagData = this.navParams.get('signatureTypeTagData');
    this.addObjects = this.navParams.get('addObjects');
    this.checkFieldDetails = this.navParams.get('checkFieldDetails');
    this.checkboxGroupObject = this.navParams.get('checkboxGroupObject');
    this.documentSignersDetails = this.navParams.get('documentSignersDetails');
    this.paletteSize = this.navParams.get('paletteSize');
    this.documentFileSizeInfo = this.navParams.get('documentFileSizeInfo');
    this.userDetails = this.navParams.get('userDetails');
    this.pageCount = this.navParams.get('pageCount');
  }

  ngOnInit(): void {
    this.newCheckBoxWithLabelGroupNumber = 1;
    this.newCheckBoxWithOutLabelGroupNumber = !this.signatureTypeTagData?.multipleCheckBoxAllowed ? 0 : 1;
  }
  cancel(checkboxGroupObject: any): void {
    if (checkboxGroupObject.isNewCheckboxGroup) {
      this.checkboxGroupObject.showCheckboxGroup = false;
    } else if (checkboxGroupObject.showCheckboxGroup && checkboxGroupObject?.addToExistingGroup) {
      this.checkboxGroupObject.showCheckboxGroup = false;
      this.checkboxGroupObject.addToExistingGroup = false;
    }
    this.modalController.dismiss();
  }
  checkboxGroupCountIncrease(condition: boolean): void {
    if (condition) {
      this.newCheckBoxWithLabelGroupNumber = this.newCheckBoxWithLabelGroupNumber + 1;
    } else {
      this.newCheckBoxWithOutLabelGroupNumber = this.newCheckBoxWithOutLabelGroupNumber + 1;
    }
  }
  checkboxGroupCountDecrease(condition: boolean): void {
    if (condition && this.newCheckBoxWithLabelGroupNumber > 0) {
      this.newCheckBoxWithLabelGroupNumber = this.newCheckBoxWithLabelGroupNumber - 1;
    } else if (!condition && this.newCheckBoxWithOutLabelGroupNumber > 0) {
      this.newCheckBoxWithOutLabelGroupNumber = this.newCheckBoxWithOutLabelGroupNumber - 1;
    }
  }

  checkGroupIsExists(droppedArray, enteredGroupName: string): boolean {
    return droppedArray.some((result) => {
      return result.groupName === enteredGroupName;
    });
  }

  save() {
    let checkGroupName = false;

    if (this.checkFieldDetails.length > 0) {
      checkGroupName = this.checkGroupIsExists(this.checkFieldDetails, this.groupName);
    }

    if (checkGroupName) {
      this.common.showToast({
        message: this.common.getTranslateData('VALIDATION_MESSAGES.GROUP_NAME_EXISTS'),
        color: 'warning'
      });
      return false;
    }

    const scrollTop = this.documentFileSizeInfo?.documentSizeDetails?.nativeElement?.scrollTop;
    const scrollLeft = this.documentFileSizeInfo?.documentSizeDetails?.nativeElement?.scrollLeft;
    const top = scrollTop + Signature.scrollDefaultValue;
    const left = scrollLeft + Signature.scrollDefaultValue;
    this.paletteSize.top = top;
    this.paletteSize.left = left;
    const paletteSizeWidth = this.paletteSize.width;
    const paletteSizeHeight = this.paletteSize.height;
    const orginalFileSizeInfo = this.sharedService?.getDocumentWidthAndHeight(
      left,
      top,
      parseFloat(paletteSizeWidth),
      parseFloat(paletteSizeHeight),
      false,
      '',
      this.documentFileSizeInfo?.imageSizeDetails,
      this.documentFileSizeInfo?.documentSizeDetails
    );
    if (this.checkboxGroupObject?.addToExistingGroup) {
      if (!isBlank(this.newCheckboxItem)) {
        if (!isBlank(this.addObjects)) {
          let count = 0;
          this.addObjects.forEach((element) => {
            if (count === 0) {
              if (element.groupName === this.newCheckboxItem.groupName) {
                const field = Signature.paletteType.checkboxField;
                const checkboxCondition = this.checkboxGroupObject.checkboxLabel;
                const groupObj = { random: true, groupName: element.groupName };
                const params = {
                  field,
                  checkboxLabelCondition: checkboxCondition,
                  groupObj,
                  signatureTypeTagData: this.signatureTypeTagData,
                  addObjects: this.addObjects,
                  documentSignersDetails: this.documentSignersDetails,
                  paletteSize: this.paletteSize,
                  orginalFileSizeInfo,
                  pageCount: this.pageCount,
                  data: this.userDetails
                };
                this.sharedService.createPaletteButton(params, this.documentFileSizeInfo?.imageSizeDetails?.offsetWidth);
                count++;
                this.checkboxGroupObject.addToExistingGroup = false;
                this.modalController.dismiss();
              }
            }
          });
        }
      } else {
        const field = Signature.paletteType.checkboxField;
        const checkboxCondition = this.checkboxGroupObject.checkboxLabel;
        const groupObj = '';
        const params = {
          field,
          checkboxLabelCondition: checkboxCondition,
          groupObj,
          signatureTypeTagData: this.signatureTypeTagData,
          addObjects: this.addObjects,
          documentSignersDetails: this.documentSignersDetails,
          paletteSize: this.paletteSize,
          orginalFileSizeInfo,
          pageCount: this.pageCount,
          data: this.userDetails
        };
        this.sharedService.createPaletteButton(params, this.documentFileSizeInfo?.imageSizeDetails?.offsetWidth);
        this.checkboxGroupObject.addToExistingGroup = false;
        this.modalController.dismiss();
      }
    } else {
      if (!isBlank(this.groupName)) {
        this.data.showCheckboxGroup = false;
        this.data.addToExistingGroup = false;
        this.checkboxGroupDetails.push({
          groupName: this.groupName,
          checked: false,
          condition: this.signatureTypeTagData.checkBoxGroupAreMandatory,
          userId: this.documentSignersDetails.id || 0,
          approver: this.documentSignersDetails?.pendingApproveUser || false
        });
        const field = Signature.paletteType.checkboxField,
          checkboxCondition = true,
          groupObj = { random: true, groupName: this.groupName };
        if (this.newCheckBoxWithLabelGroupNumber > 0) {
          const params = {
            field,
            checkboxLabelCondition: checkboxCondition,
            groupObj,
            signatureTypeTagData: this.signatureTypeTagData,
            addObjects: this.addObjects,
            documentSignersDetails: this.documentSignersDetails,
            paletteSize: this.paletteSize,
            orginalFileSizeInfo,
            pageCount: this.pageCount,
            data: this.userDetails
          };
          for (let i = 0; i < this.newCheckBoxWithLabelGroupNumber; i++) {
            this.sharedService.createPaletteButton(params, this.documentFileSizeInfo?.imageSizeDetails?.offsetWidth);
          }
        }
        if (this.newCheckBoxWithOutLabelGroupNumber > 0) {
          const checkboxLabel = false;
          const params = {
            field,
            checkboxLabelCondition: checkboxLabel,
            groupObj,
            signatureTypeTagData: this.signatureTypeTagData,
            addObjects: this.addObjects,
            documentSignersDetails: this.documentSignersDetails,
            paletteSize: this.paletteSize,
            orginalFileSizeInfo,
            pageCount: this.pageCount,
            data: this.userDetails
          };
          for (let j = 0; j < this.newCheckBoxWithOutLabelGroupNumber; j++) {
            this.sharedService.createPaletteButton(params, this.documentFileSizeInfo?.imageSizeDetails?.offsetWidth);
          }
        }
        this.modalController.dismiss({
          checkboxGroupDetails: this.checkboxGroupDetails,
          checkboxGroupObject: this.data
        });
      } else {
        this.groupNameRequired = true;
      }
    }
  }
  newCheckboxGroup(): void {
    this.checkboxGroupObject.showCheckboxGroup = true;
    this.checkboxGroupObject.isNewCheckboxGroup = true;
  }
  showGroupList(condition: boolean): void {
    this.showGroup = condition ? true : false;
  }
  addItemToGroup(groupData: any): void {
    this.newCheckboxItem = groupData;
  }
}
