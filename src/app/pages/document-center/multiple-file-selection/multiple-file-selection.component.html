<ion-header mode="ios">
  <ion-toolbar mode="ios">
    <ion-buttons slot="start" mode="ios">
      <ion-button (click)="cancel()" class="set-button-color">
        {{ 'BUTTONS.BACK' | translate }}
      </ion-button>
    </ion-buttons>
    <ion-title mode="ios" class="set-title-color">{{ 'LABELS.DOCUMENTS' | translate }}</ion-title>
    <ion-buttons slot="end" mode="ios">
      <ion-button (click)="finish()" class="set-button-color ion-text-capitalize">
        {{ 'BUTTONS.FINISH' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="multiple-file-selection">
  <ion-row>
    <ion-col size="12" class="file-form">
      <input #docInput type="file" id="file-choose" (change)="loadDocument($event)" (click)="docInput.value = null">
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col size="12">
      <ion-item lines="full" class="ion-no-padding" *ngFor="let item of selectedFilesList; let i = index">
        <ion-thumbnail slot="start" class="set-image-size">
          <img src="assets/images/doc-types/{{ item.extension }}.png" *ngIf="item?.imageURL === ''; else imageType" [alt]="item?.name" />
          <ng-template #imageType>
            <ion-skeleton-text [animated]="true" *ngIf="!item?.imageURL"></ion-skeleton-text>
            <img [src]="item?.imageURL" [alt]="item?.name" *ngIf="item?.imageURL" />
          </ng-template>
        </ion-thumbnail>
        <ion-label>
          <span class="set-title-color">{{ item?.name }} </span>
          <p>
            <ion-row>
              <ion-col size="6" class="remove-start-padding ion-text-capitalize"
                >{{ 'LABELS.TYPE' | translate }}: {{ item?.extension.includes('image') ? imageText : item?.extension }}</ion-col
              >
              <ion-col size="6">{{ 'LABELS.SIZE' | translate }}: {{ item?.size | fileSize }}</ion-col>
            </ion-row>
          </p>
        </ion-label>
        <ion-icon name="trash" slot="end" class="set-title-color" (click)="showConfirmToRemoveFile(i)"></ion-icon>
      </ion-item>
    </ion-col>
  </ion-row>
</ion-content>
<ion-footer>
  <ion-row>
    <ion-col size="9.5" *ngIf="selectedFilesList?.length > 0" class="ion-no-padding ion-padding-start ion-align-self-center set-title-color">
      <b> {{ selectedFilesList?.length }} {{ 'MESSAGES.ITEMS_SELECTED' | translate }} </b>
      <b>({{ totalFileSize | fileSize }})</b>
    </ion-col>
    <ion-col
      [size]="selectedFilesList?.length > 0 ? '2.5' : '12'"
      class="ion-no-padding"
      [ngClass]="selectedFilesList?.length > 0 ? 'ion-text-center' : 'ion-text-end'"
    >
      <ion-fab-button (click)="selectFileFromType(selectFilesFrom)" class="set-fab-button-style" size="small">
        <ion-icon name="add"></ion-icon>
      </ion-fab-button>
    </ion-col>
  </ion-row>
</ion-footer>
