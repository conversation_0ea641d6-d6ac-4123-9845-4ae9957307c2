import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants } from 'src/app/constants/constants';
import { DocumentPicker } from '@awesome-cordova-plugins/document-picker/ngx';
import { ModalController, Platform, NavParams } from '@ionic/angular';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { Camera, CameraResultType, CameraSource, PermissionStatus, Photo } from '@capacitor/camera';
import * as moment from 'moment';
import { Filesystem } from '@capacitor/filesystem';
import { DocumentScanner, ResponseType, ScanDocumentOptions, ScanDocumentResponse, ScanDocumentResponseStatus } from 'capacitor-document-scanner';
import { Capacitor } from '@capacitor/core';
import { getFileReader, isHTMLTagAvailable, resizeBase64Image } from 'src/app/utils/utils';

@Component({
  selector: 'app-multiple-file-selection',
  templateUrl: './multiple-file-selection.component.html',
  styleUrls: ['./multiple-file-selection.component.scss']
})
export class MultipleFileSelectionComponent {
  @ViewChild('docInput') docInputViewChild: ElementRef;
  docInputElement: HTMLInputElement;

  imageText: string = this.commonService.getTranslateData('LABELS.IMAGE');
  multipleFileUpload: any = {
    target: {
      files: []
    }
  };

  selectedFilesList: any = [];
  selectFilesFrom: string;
  allowedFileTypes: any;
  constructor(
    private readonly modalController: ModalController,
    private readonly platform: Platform,
    private readonly docPicker: DocumentPicker,
    private readonly commonService: CommonService,
    private readonly sharedService: SharedService,
    private readonly navParams: NavParams
  ) {
    this.selectFilesFrom = this.navParams.get('sourceType');
  }

  ionViewDidEnter(): void {
    this.docInputElement = this.docInputViewChild.nativeElement;
    if (this.sharedService.appConfig) {
      const fileTypes = this.sharedService.appConfig;
      this.allowedFileTypes = fileTypes.configurations.allowedFileFormat;
    }
    this.selectFileFromType(this.selectFilesFrom);
  }

  finish(): void {
    if (this.multipleFileUpload.target.files.length > Constants.maxFileLength) {
      const fileLengthErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.MAX_FILE_LENGTH_FINISH', {
        maxFileLength: Constants.maxFileLength,
        removeCount: this.multipleFileUpload.target.files.length - Constants.maxFileLength
      });
      this.commonService.showMessage(fileLengthErrorMessage);
    } else if (this.totalFileSize > Constants.maxFileUploadSize) {
      const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', {
        maxFileSize: Constants.maxFileSize
      });
      this.commonService.showMessage(sizeErrorMessage);
    } else {
      this.modalController.dismiss({
        multipleFileUploadData: this.multipleFileUpload,
        selectedFilesListData: this.selectedFilesList
      });
    }
  }

  cancel(): void {
    this.modalController.dismiss();
  }

  selectFileFromType(type: string): void {
    if (this.multipleFileUpload.target.files.length >= Constants.maxFileLength) {
      const fileLengthErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.MAX_FILE_LENGTH', {
        maxFileLength: Constants.maxFileLength
      });
      this.commonService.showMessage(fileLengthErrorMessage);
      return;
    }
    if (type === Constants.docSourceCam) {
      this.takePicture(Constants.docSourceCam);
    } else if (type === Constants.docSourceScan) {
      this.openScanner();
    } else if (type === Constants.docSourceiCloud) {
      this.uploadFileFromiCloud();
    } else if (type === Constants.docSourceGallery) {
      if (this.platform.is('ios')) {
        this.takePicture(Constants.docSourceGallery);
      } else {
        this.docInputElement?.click();
      }
    }
  }

  convertImageUriToPhoto(imageUri: string): Photo {
    const photo: Photo = {
      dataUrl: `${Constants.base64Prefix}${imageUri}`,
      webPath: Capacitor.convertFileSrc(imageUri),
      format: 'jpeg',
      saved: false
    };
    return photo;
  }
  getCameraPermission(): Promise<PermissionStatus> {
    return Camera.requestPermissions({ permissions: ['camera'] });
  }
  scanDocument(): Promise<ScanDocumentResponse> {
    const options: ScanDocumentOptions = { responseType: ResponseType.Base64, maxNumDocuments: 5 };
    return DocumentScanner.scanDocument(options);
  }

  async openScanner(): Promise<void> {
    const { camera } = await this.getCameraPermission();
    if (camera !== 'granted') {
      this.commonService.showToast({ message: 'MESSAGES.NO_PERMISSION' });
      return;
    }
    try {
      const { status, scannedImages } = await this.scanDocument();
      if (status === ScanDocumentResponseStatus.Success && scannedImages.length > 0) {
        scannedImages.forEach(async (doc, index) => {
          resizeBase64Image({ base64: doc, maxHeight: 1100, maxWidth: 800, quality: 80 }).then(async (image) => {
            const filteredImage = this.convertImageUriToPhoto(image);
            await this.getFileFromCap(filteredImage, index);
          });
        });
      }
    } catch (error) {
      this.commonService.showToast({ message: 'ERROR_MESSAGES.SOMETHING_WENT_WRONG', color: 'dark' });
    }
  }

  uploadFileFromiCloud(): void {
    this.docPicker.getFile('all').then((uri) => {
      this.convertFileURIToFileObj(uri);
    });
  }

  async takePicture(type: string): Promise<void> {
    const image = await Camera.getPhoto({
      quality: 80,
      allowEditing: false,
      resultType: CameraResultType.DataUrl,
      source: type === Constants.docSourceGallery ? CameraSource.Photos : CameraSource.Camera,
      height: 1100,
      width: 800
    });

    await this.getFileFromCap(image);
  }

  async getFileFromCap(imageData: Photo, index = 0): Promise<void> {
    const fileName = `${moment.utc().unix() * 100 + index}.${imageData.format}`;
    const fileEntry = await this.convertURLToFile(imageData.dataUrl, fileName, Constants.extToMimes[imageData.format]);
    this.loadDocument(fileEntry);
  }

  async convertFileURIToFileObj(uri: string): Promise<void> {
    const fileName = uri.substring(uri.lastIndexOf('/') + 1).toLowerCase();
    const fileType = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

    const setMimeType = `data:${Constants.extToMimes[fileType]};base64,`;

    Filesystem.readFile({
      path: uri
    }).then(async (base64Data: any) => {
      const fullData = `${setMimeType} ${base64Data.data}`;
      const fileEntry = await this.convertURLToFile(fullData, fileName, Constants.extToMimes[fileType]);
      this.loadDocument(fileEntry);
    });
  }
  get totalFileSize(): number {
    return this.selectedFilesList.reduce((sum, item) => sum + item.size, 0);
  }
  async convertURLToFile(url, filename, mimeType): Promise<File> {
    const res = await fetch(url);
    const buf = await res.arrayBuffer();
    return new File([buf], filename, { type: mimeType });
  }

  loadDocument(data: any): void {
    let selectedFile;
    if (this.platform.is('android') && this.selectFilesFrom === Constants.docSourceGallery) {
      selectedFile = data.target.files[0];
    } else {
      selectedFile = data;
    }
    const fileName: string = selectedFile?.name?.includes(Constants.checkSpace) ? decodeURIComponent(selectedFile.name) : selectedFile.name;
    const ext = fileName?.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    const pos = this.allowedFileTypes?.indexOf(ext);
    if (pos < 0) {
      this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.FILE_FORMAT'));
    } else if (selectedFile.size > Constants.maxFileUploadSize) {
      const sizeErrorMessage = this.commonService.getTranslateDataWithParam('ERROR_MESSAGES.FILE_UPLOADSIZE', {
        maxFileSize: Constants.maxFileSize
      });
      this.commonService.showMessage(sizeErrorMessage);
    } else if (isHTMLTagAvailable(fileName)) {
      this.commonService.showMessage(this.commonService.getTranslateData('ERROR_MESSAGES.ERROR_MSG_INVALID_INPUT'));
    } else {
      this.multipleFileUpload.target.files.push(selectedFile);
      this.selectedFilesList.push({
        name: fileName,
        size: selectedFile.size,
        type: selectedFile.type,
        extension: this.commonService.getFileType(selectedFile.type),
        imageURL: null
      });
      const getLastIndex = this.selectedFilesList.length - 1;
      if (Constants.checkFileExtensionType.includes(this.commonService.getFileType(selectedFile.type))) {
        const newInstance = getFileReader();
        newInstance.readAsDataURL(selectedFile);
        newInstance.onload = () => {
          this.selectedFilesList[getLastIndex].imageURL = newInstance.result as string;
        };
      } else {
        this.selectedFilesList[getLastIndex].imageURL = '';
      }
    }
  }

  deleteFile(index: number): void {
    this.selectedFilesList.splice(index, 1);
    this.multipleFileUpload.target.files.splice(index, 1);
  }

  showConfirmToRemoveFile(index: number): void {
    const buttons = [
      {
        text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
        confirm: false
      },
      {
        text: this.commonService.getTranslateData('BUTTONS.OK'),
        confirm: true
      }
    ];
    const alertData = {
      message: 'MESSAGES.DO_YOU_WANT_TO_REMOVE_FILE',
      header: 'TITLES.SIGNATURE_DOCUMENTS',
      buttons,
      backDrop: false
    };

    this.commonService.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        this.deleteFile(index);
      }
    });
  }
}
