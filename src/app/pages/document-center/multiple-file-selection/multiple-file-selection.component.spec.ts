import { TranslateModule } from '@ngx-translate/core';
import { FileSizePipe } from 'src/app/pipes/file-size.pipe';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { USE_PERMISSIONS_STORE, NgxPermissionsService, NgxPermissionsStore } from 'ngx-permissions';
import { DocumentPicker } from '@awesome-cordova-plugins/document-picker/ngx';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestConstants } from 'src/app/constants/test-constants';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MultipleFileSelectionComponent } from 'src/app/pages/document-center/multiple-file-selection/multiple-file-selection.component';
import { Alert<PERSON><PERSON>roller, IonicModule, ModalController, NavParams } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { PermissionStatus } from '@capacitor/camera';
import { ScanDocumentResponse, ScanDocumentResponseStatus } from 'capacitor-document-scanner';
import { Constants } from 'src/app/constants/constants';

describe('MultipleFileSelectionComponent', () => {
  let component: MultipleFileSelectionComponent;
  let fixture: ComponentFixture<MultipleFileSelectionComponent>;
  let modalController: ModalController;
  let sharedService: SharedService;
  let commonService: CommonService;
  const { modalSpy } = TestConstants;
  let docPicker: DocumentPicker;
  let alertController: AlertController;
  const alertSpy = TestConstants.alertSpy;
  const mockData = {
    target: {
      files: [
        {
          name: 'file1.jpg',
          lastModifiedDate: 'Wed Mar 24 2022 01:39:31 GMT+0530 (India Standard Time) {}',
          size: 1445,
          type: 'image/jpeg'
        }
      ]
    }
  };
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [MultipleFileSelectionComponent],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        NavParams,
        ModalController,
        SharedService,
        CommonService,
        DocumentPicker,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        FileSizePipe,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(MultipleFileSelectionComponent);
    component = fixture.componentInstance;
    modalController = TestBed.inject(ModalController);
    sharedService = TestBed.inject(SharedService);
    commonService = TestBed.inject(CommonService);
    spyOn(commonService, 'showToast').and.stub();
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });

    alertController = TestBed.inject(AlertController);
    spyOn(alertController, 'create').and.callFake(() => {
      return alertSpy;
    });
    alertSpy.present.and.stub();
    spyOn(alertController, 'dismiss').and.stub();

    docPicker = TestBed.inject(DocumentPicker);
    spyOn(docPicker, 'getFile').and.resolveTo('');
    fixture.detectChanges();
  });

  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call uploadFileFromiCloud', () => {
    component.uploadFileFromiCloud();
    expect(component.uploadFileFromiCloud).toBeDefined();
  });

  it('should call convertFileURIToFileObj', () => {
    component.convertFileURIToFileObj('file://example.com/image1.jpg');
    expect(component.convertFileURIToFileObj).toBeDefined();
  });

  it('should call loadDocument', () => {
    component.loadDocument(mockData);
    expect(component.loadDocument).toBeDefined();
  });

  it('should call takePicture Gallery', () => {
    component.takePicture('Gallery');
    expect(component.takePicture).toBeDefined();
  });

  it('should call takePicture Camera', () => {
    component.takePicture('Camera');
    expect(component.takePicture).toBeDefined();
  });

  it('should call selectFileFromType Camera', () => {
    component.selectFileFromType('Camera');
    expect(component.selectFileFromType).toBeDefined();
  });

  it('should call selectFileFromType Gallery', () => {
    component.selectFileFromType('Gallery');
    expect(component.selectFileFromType).toBeDefined();
  });

  it('should call selectFileFromType iCloud', () => {
    component.selectFileFromType('iCloud');
    expect(component.selectFileFromType).toBeDefined();
  });

  it('should call cancel', () => {
    component.cancel();
    expect(component.cancel).toBeDefined();
  });

  it('should call finish', () => {
    component.finish();
    expect(component.finish).toBeDefined();
  });

  it('should call deleteFile', () => {
    component.deleteFile(1);
    expect(component.deleteFile).toBeDefined();
  });

  it('should call ionViewDidEnter', () => {
    component.ionViewDidEnter();
    expect(component.ionViewDidEnter).toBeDefined();
  });

  it('execute openScanner: no permission', async () => {
    const permissionStatus: PermissionStatus = { camera: 'denied', photos: 'granted' };
    spyOn(component, 'getCameraPermission').and.resolveTo(permissionStatus);
    const scanDocumentResponse: ScanDocumentResponse = { scannedImages: [] };
    spyOn(component, 'scanDocument').and.resolveTo(scanDocumentResponse);
    await component.openScanner();
    expect(component.openScanner).toBeTruthy();
  });

  it('execute openScanner: scan image completed', async () => {
    const permissionStatus: PermissionStatus = { camera: 'granted', photos: 'granted' };
    spyOn(component, 'getCameraPermission').and.resolveTo(permissionStatus);
    const scanDocumentResponse: ScanDocumentResponse = { scannedImages: ['fkdhskjfs'], status: ScanDocumentResponseStatus.Success };
    spyOn(component, 'scanDocument').and.resolveTo(scanDocumentResponse);
    await component.openScanner();
    expect(component.openScanner).toBeTruthy();
  });

  it('execute openScanner: scan image throws error', async () => {
    const permissionStatus: PermissionStatus = { camera: 'granted', photos: 'granted' };
    spyOn(component, 'getCameraPermission').and.resolveTo(permissionStatus);
    spyOn(component, 'scanDocument').and.throwError('');
    await component.openScanner();
    expect(component.openScanner).toBeTruthy();
  });

  it('execute ionViewDidEnter: contains file formats', () => {
    sharedService.appConfig = { configurations: { allowedFileFormat: '' } };
    component.ionViewDidEnter();
    expect(component.ionViewDidEnter).toBeTruthy();
  });

  it('execute selectFileFromType: file count exceeded', () => {
    component.multipleFileUpload = { target: { files: new Array(11).fill({ size: 3242 }) } };
    component.selectFileFromType(Constants.docSourceCam);
    expect(component.selectFileFromType).toBeTruthy();
  });

  it('execute selectFileFromType: source scan', () => {
    component.selectFileFromType(Constants.docSourceScan);
    expect(component.selectFileFromType).toBeTruthy();
  });

  it('execute finish: file count exceeded: multipleFileUpload', () => {
    component.multipleFileUpload = { target: { files: new Array(11).fill({ size: 3242 }) } };
    component.finish();
    expect(component.finish).toBeTruthy();
  });

  it('execute finish: file count exceeded: selectedFilesList', () => {
    component.selectedFilesList = Array(9).fill({ size: 9999999 });
    component.finish();
    expect(component.finish).toBeTruthy();
  });

  it('execute showConfirmToRemoveFile', () => {
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    component.showConfirmToRemoveFile(0);
    expect(component.showConfirmToRemoveFile).toBeTruthy();
  });

  it('execute loadDocument: format not allowed', () => {
    const data = { name: 'test' };
    component.allowedFileTypes = 'jpg';
    component.loadDocument(data);
    expect(component.loadDocument).toBeTruthy();
  });

  it('execute loadDocument', () => {
    const data = { name: 'test.jpg', size: Constants.maxFileUploadSize + 2 };
    component.allowedFileTypes = 'jpg';
    component.loadDocument(data);
    expect(component.loadDocument).toBeTruthy();
  });

  it('execute loadDocument', () => {
    const data = { name: 'test<a />.jpg', size: Constants.maxFileUploadSize };
    component.allowedFileTypes = 'jpg';
    component.loadDocument(data);
    expect(component.loadDocument).toBeTruthy();
  });

  it('should call getFileFromCap', () => {
    const data = {
      base64String: '',
      dataUrl: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4QQ4RXhpZgAATU0AKgAAAAgADAEPAAIAAAASAAAAngEQAAIAAAAMAA',
      path: '',
      webPath: '',
      exif: { DateTimeDigitized: '2012:08:08 14:55:30' },
      format: 'jpeg',
      saved: false
    };
    component.getFileFromCap(data);
    expect(component.getFileFromCap).toBeDefined();
  });

  it('should call convertImageUriToPhoto', () => {
    component.convertImageUriToPhoto('/9j/4AAQSkZJRgABAQAASABIAAD/4QQ4RXhpZgAATU0AKgAAAAgADAEPAAIAAAASAAAAngEQAAIAAAAMAA');
    expect(component.convertImageUriToPhoto).toBeDefined();
  });

  it('should call showConfirmToRemoveFile', () => {
    component.showConfirmToRemoveFile(1);
    expect(component.showConfirmToRemoveFile).toBeDefined();
  });
});
