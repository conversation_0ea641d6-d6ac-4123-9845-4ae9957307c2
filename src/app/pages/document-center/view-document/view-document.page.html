<ion-header class="common-plain-header">
    <ion-toolbar>
        <ion-row *ngIf="!applessDocumentWorkFlowComplete" class="set-row-height">
            <ion-col size="3">
                <ion-icon tappable (click)="back()" class="common-back-btn-icon"
                    *ngIf="sharedService.isAppLessHomeLoggedIn() || (!showSignDoc  && !applessDocumentWorkflow)"
                    src="assets/icon/material-svg/arrow-left-bold.svg" id="back-button">
                </ion-icon>
                <a tappable id="cancel-request" (click)="cancel()" *ngIf="showSignDoc && !applessDocumentWorkflow"
                    class="button-download">
                    {{ 'BUTTONS.CANCEL' | translate }}
                </a>
            </ion-col>
            <ion-col size="6">
                <div class="tab-contents" *ngIf="showSignDoc">
                    @if (
                    !myPaletteSign &&
                    (documentSignersDetails?.length === 3 || (documentSignersDetails?.length === 2 &&
                    signatureTypeTagData.allowPendingApproveSignature)) &&
                    platformValue !== constants?.platform.web
                    ) {
                    <ion-select id="selected-signer" class="text-colour select-option" fill="outline"
                        labelPlacement="floating" (ionChange)="signerSelected($event, true)"
                        [interfaceOptions]="customAlertOptions" color="none" shape="round"
                        [ngStyle]="{ '--background': selectedDocumentSignerDetails.borderColor }"
                        [value]="selectedDocumentSignerDetails.identifier">
                        <ion-select-option *ngFor="let item of documentSignersDetails" [value]="item.identifier">{{
                            item.name }}</ion-select-option>
                    </ion-select>
                    }
                    <ul class="ul-contents"
                        *ngIf="!myPaletteSign && ((documentSignersDetails.length ===2 && !signatureTypeTagData.allowPendingApproveSignature) || (documentSignersDetails.length !== 1 && platformValue === constants.platform.web))">
                        <ion-row>
                            <ion-col class="li-contents" [id]="'signer-tab-'+d" (click)="signerSelected(item,false)"
                                *ngFor="let item of documentSignersDetails;let d=index"
                                [ngClass]="{'text-colour' : item.selected,'border-left': d === 0,'border-right': d === documentSignersDetails.length - 1}"
                                [ngStyle]="{'background': item.selected ? item.borderColor : ''}">
                                <li>{{item.name}}</li>
                            </ion-col>
                        </ion-row>
                    </ul>
                    <ul class="ul-contents" *ngIf="documentSignersDetails.length === 1 && !myPaletteSign">
                        <ion-row>
                            <ion-col>
                                <li>{{selectedDocumentSignerDetails.name}}</li>
                            </ion-col>
                        </ion-row>
                    </ul>
                </div>
                <div class="tab-contents"
                    *ngIf="!showSignDoc && documentRootInfo && documentRootInfo?.document?.associatePatientDetails">
                    <ul class="ul-contents">
                        <ion-row>
                            <ion-col>
                                {{documentRootInfo?.document?.associatePatient}}
                            </ion-col>
                        </ion-row>
                    </ul>
                </div>
            </ion-col>
            <ion-col size="3">
                <ion-button color="success" mode="md" class="doc-download-button ion-text-capitalize"
                    href={{downloadUrl}} target="_blank" id="download-button"
                    *ngIf="isSigned && downloadUrl && platform" (click)="downloadDoc()">
                    {{ 'BUTTONS.DOWNLOAD' | translate }}
                </ion-button>
                <ion-buttons class="header-menu right">
                    <ion-button id="start" fill="solid"
                        *ngIf="!isSigned && !startSign && !finishSign && 
                        (displayTypeConditions?.obtainSignature || (displayTypeConditions?.allowAssociateRoles && associatePhysicaianSignature) || mySignatureNeeded)"
                        (click)="startSigningProcess(true)" class="ion-text-capitalize set-as-button">{{ 'BUTTONS.START'
                        | translate
                        }}
                    </ion-button>
                    <!-- Hide next button -->
                    <ion-button id="findNext" fill="solid"
                        *ngIf="!approvePending && !isSigned && startSign && !finishSign  && (paletteStatus===paletteStatuses.signing ||paletteStatus===paletteStatuses.notSigned || paletteStatus===paletteStatuses.confirmed) &&
                         (displayTypeConditions?.obtainSignature||(displayTypeConditions?.allowAssociateRoles && associatePhysicaianSignature) || mySignatureNeeded)"
                        (click)="findNext(true)" class="ion-text-capitalize set-as-button">{{ 'BUTTONS.NEXT' | translate
                        }}
                    </ion-button>
                    <!-- Hide next button -->
                    <ion-button id="finishbutton" class="ion-text-capitalize set-as-button" fill="solid"
                        *ngIf="!represntativeConfirmSignature && !approvePending &&!isSigned && (paletteStatus===paletteStatuses.signed || paletteStatus===paletteStatuses.notConfirmed) &&
                     (displayTypeConditions?.obtainSignature || (displayTypeConditions?.allowAssociateRoles && associatePhysicaianSignature) || mySignatureNeeded)"
                        (click)="presentResignActionSheet()">{{finishButtonName}}</ion-button>
                </ion-buttons>
                <a tappable id="send-document" class="button-send" *ngIf="showSignDoc" (click)="documentSend()">
                    {{sendButtonText}}
                </a>
            </ion-col>
        </ion-row>

    </ion-toolbar>
</ion-header>

<ion-content [scrollEvents]="true" (ionScroll)="getScrollPosition($event)" class="view-document-page-ion-content"
    [class.unset-max-width]="isiPad || isMobileWeb">
    <div #signDocumentWrapper class="sign-document-wrapper" [class.set-padding-end-start-sign]="showSignDoc"
        [hidden]="applessDocumentWorkFlowComplete || consoloWorkFlow">
        <div class="drag-boundary" *ngIf="currentImagePath" #myBounds>
            <pinch-zoom [autoHeight]="true" [backgroundColor]="'rgba(0, 0, 255, 0)'" [double-tap]="isMobilePlatform"
                [wheel]="isMobilePlatform" [disableZoomControl]="isMobilePlatform ? 'auto' : 'disable'"
                class="set-height" *ngIf="!isPlaceHolderImage" [class.remove-position-relative]="!isMobilePlatform">
                <img #signDocument (load)="imageLoaded(signDocument)" class="sign-document" alt="sign-document"
                    loading="lazy" [src]="currentImagePath | queryURI">
                <ng-container *ngIf="showSignDoc">
                    <ng-container *ngFor="let obj of addObjects;let d=index">
                        <div *ngIf="obj.page===currentDocPage && !obj.signaturePaletteLock"
                            [ngResizable]=obj.isResizable [rzMinWidth]=obj.resizeMinWidth
                            [rzMaxWidth]=obj.resizeMaxWidth [rzMinHeight]=obj.resizeMinHeight
                            [rzMaxHeight]=obj.resizeMaxHeight (rzStop)="onResizing($event,d)" id="drop-{{obj.id}}"
                            [style.top]="obj.myLocation.fixTop" [style.border]="obj.borderColor"
                            [style.left]="obj.myLocation.fixLeft"
                            [ngStyle]="{width:obj.myLocation.width,height:obj.myLocation.height}"
                            [ngDraggable]=obj.isDraggable [bounds]="myBounds" [inBounds]="inBounds"
                            [outOfBounds]="myOutOfBounds" (stopped)="onDragEnd($event,obj, d)"
                            [hidden]="obj.hidePalette"
                            [class.only-checkbox-responsive]="!obj.checkboxLabel && obj?.name === signature.paletteType.checkboxValue"
                            class="text-block" (started)="onDragStart(true)">
                            <label
                                *ngIf="!obj.signpad && !obj.signature && !obj.checkbox && !enableSign && !myPaletteSign"
                                class="set-text-color responsive-font set-label-full-width"
                                [ngStyle]="obj?.myLocation?.fontSize <= 14 ? {'font-size':obj?.myLocation?.fontSize+'px'}: {}">{{obj.textPlaceholder
                                ?
                                obj.textPlaceholder :
                                obj.name}}</label>
                            <textarea character-input-limit class="textarea-resize responsive-font text_fill_{{obj.id}}"
                                [id]="'text-palette-'+d"
                                [ngStyle]="{'font-size': obj.myLocation.fontSize <= 14 ? obj.myLocation.fontSize+'px':{}}"
                                *ngIf="obj.name === signature.paletteType.textValue && myPaletteSign && obj.userId===userData.userId && !obj.pendingApproveUser"
                                [placeholder]="obj?.textPlaceholder ? obj?.textPlaceholder : 'PLACEHOLDERS.TEXT' | translate"
                                (focusout)="openTextPad(d)" [(ngModel)]="textValue[obj.id]" (blur)="openTextPad(d)"
                                (keyup)="openTextPad(d)" autocapitalize="on"
                                [attr.data-font]="obj?.myLocation?.fontSize"></textarea>
                            <label [id]="'signature-palette-'+d"
                                *ngIf="obj.signpad && !obj.signature && !obj.checkbox && !myPaletteSign"
                                class="set-label-full-width">
                                <img class="sign-picture" src="assets/images/shc-logo.png" alt="sign-picture">
                            </label>
                            <label id="signature-{{d}}" *ngIf="obj.name === signature.paletteType.signValue && !obj.signature&&
                    myPaletteSign && obj.userId===userData.userId && !obj.pendingApproveUser"
                                (click)="enableSign ? openSignPad(obj) :''" class="set-label-full-width">
                                <img class="sign-picture" src="assets/images/shc-logo.png" alt="sign-picture" />
                            </label>
                            <img class="signature-data {{obj.name}}"
                                *ngIf="obj.signature && obj.name === signature.paletteType.signValue && myPaletteSign && obj.userId==userData.userId && !obj.pendingApproveUser"
                                src={{obj.signature}} alt="signature-data" loading="lazy" />
                            <label *ngIf="!obj.signpad && obj.checkbox && !myPaletteSign">
                                <ion-checkbox id="checkbox-palette-{{d}}" class="checkbox-icon set-checkbox-align-left"
                                    (click)="checkboxClick()" color="blumine" mode="md" disabled="true"
                                    [class.set-only-checkbox-responsive]="!obj.checkboxLabel"
                                    [ngClass]="{'ion-checkbox-custom': isCheckboxCross}">
                                </ion-checkbox>
                                <textarea *ngIf="obj.checkboxLabel" character-input-limit
                                    [attr.data-font]="obj?.myLocation?.fontSize"
                                    [placeholder]="'PLACEHOLDERS.CHECK_BOX_PLACEHOLDER' | translate"
                                    class="checkbox-input-label checkbox-input-label-set responsive-font text_fill_{{obj.id}}"
                                    [(ngModel)]="checkboxLabelValue[obj.id]" (focusout)="openTextPad(d)"
                                    (keyup)="openTextPad(d)" (blur)="openTextPad(d)" id="check-box-text-{{d}}"
                                    autocapitalize="on"
                                    [ngStyle]="obj?.myLocation?.fontSize <= 14 ? {'font-size':obj?.myLocation?.fontSize+'px'}: {}"></textarea>
                            </label>
                            <label
                                *ngIf="!obj.signpad && obj.checkbox && myPaletteSign && obj.userId===userData.userId && !obj.pendingApproveUser"
                                class="checkbox-wrapper">
                                <ion-checkbox id="status-change-{{d}}" color="blumine" mode="md"
                                    (click)="checkboxStatusChange($event,d)" [checked]="obj?.signature"
                                    class="checkbox-icon" [class.set-only-checkbox-responsive]="!obj.checkboxLabel"
                                    [ngClass]="{'ion-checkbox-custom': isCheckboxCross}"></ion-checkbox>
                                <label *ngIf="obj.checkboxLabel"
                                    class="checkbox-input-label responsive-font">{{obj.checkboxLabelValue}}</label>
                            </label>
                            <span class="close-option" [ngStyle]="{color:obj.iconColor}" *ngIf="!enableSign"
                                id="remove-palette-{{d}}" (click)="removePaletteOption(obj,d)">
                                <ion-icon name="close-circle"></ion-icon>
                            </span>
                        </div>
                    </ng-container>
                </ng-container>

                <!-- document view to be signed start -->
                <ng-container *ngIf="!isSigned">
                    <ng-container *ngFor="let obj of dummyDroppedObjects;let d=index">
                        <div class="palette-style {{obj?.groupName}} {{obj?.paletteType}} " *ngIf="(snapAndSignApproval || !obj.signaturePaletteLock) && !approvePending &&
                        obj.signatureDocumentPageNumber === currentDocPage"
                            [ngStyle]="{top:obj.verticalSpaceTop,left:obj.horizontalSpaceLeft,width:obj.signaturePaletteWidth,height:obj.signaturePaletteHeight,border:obj.border,'box-shadow':obj.boxShadow,'cursor':obj?.setCursor}"
                            [class.only-checkbox]="obj?.paletteType === signature?.paletteType?.checkboxValue && !obj?.signature?.signatureImage && !obj?.checkboxLabel"
                            [class.field-hide]="!obj?.mySignNeed"
                            [class.sign-field-signed]="obj?.paletteType === signature?.paletteType?.signValue && obj?.signaturePaletteLock || finishSign && paletteStatus === paletteStatuses.confirmed"
                            [class.text-field-signed]="(obj?.paletteType === signature?.paletteType?.textValue || obj?.paletteType === signature?.paletteType?.checkboxField) && obj?.mySignNeed && finishSign && paletteStatus === paletteStatuses.confirmed"
                            [class.only-checkbox-responsive]="!obj.checkboxLabel && obj?.paletteType === signature.paletteType.checkboxValue"
                            [class.remove-bg-color-snapsign]="!obj?.pendingApproveUser && snapAndSignApproval">
                            <label *ngIf="obj.paletteType === signature.paletteType.signValue && !obj.signature"
                                id="text-label-{{obj.id}}" (click)="obj.mySignNeed ? openSignPad(obj) :''">
                                <img class="sign-picture" src="assets/images/shc-logo.png" alt="sign-picture" />
                            </label>
                            <img class="signature-data {{obj?.paletteType}}" id="text-label-{{obj.id}}"
                                *ngIf="obj?.signature && !obj?.signaturePaletteLock && obj?.paletteType === signature.paletteType.signValue"
                                (click)="!isSigned && !obj?.signaturePaletteLock && obj?.mySignNeed ? openSignPad(obj) :''"
                                src={{obj?.signature}} alt="signature-data" loading="lazy" />

                            <img class="signature-data {{obj?.paletteType}}" id="text-label-{{obj.id}}"
                                *ngIf="obj?.signaturePaletteLock" src={{obj?.signature?.signatureImage}}
                                alt="signature-data" loading="lazy"
                                [class.snapsign-text]="snapAndSignApproval && obj.paletteType === signature.paletteType.textValue"
                                [class.snapsign-sign]="snapAndSignApproval && obj.paletteType === signature.paletteType.signValue"
                                [class.snapsign-checkbox-with-label]="snapAndSignApproval && obj.paletteType === signature.paletteType.checkboxValue  && obj?.checkboxLabel"
                                [class.snapsign-checkbox-without-label]="snapAndSignApproval && obj.paletteType === signature.paletteType.checkboxValue  && !obj?.checkboxLabel" />

                            <textarea character-input-limit [attr.data-font]="obj?.fontSize"
                                class="textarea-resize responsive-font text_fill_{{obj.id}}" id="text-{{obj.id}}"
                                *ngIf="obj?.paletteType === signature.paletteType.textValue && !obj.signaturePaletteLock"
                                [ngStyle]="{'font-size': obj.fontSize  <= 14 ? obj.fontSize+'px':{}}"
                                (focusout)="openTextPad(d)" [(ngModel)]="textValue[obj.id]" (blur)="openTextPad(d)"
                                (keyup)="openTextPad(d);checkStartSignWithTextPad();"
                                [disabled]="!obj.mySignNeed && (obj?.pendingApproveUser || !mySignatureNeeded) || obj?.isDisableForPatient"
                                [placeholder]="obj?.textPlaceholder || ''"
                                [readonly]="!obj.mySignNeed || obj?.isDisableForPatient" autocapitalize="on"></textarea>
                            <label
                                *ngIf="obj.paletteType === signature.paletteType.checkboxValue && !obj.signature.signatureImage"
                                id="text{{obj.id}}">
                                <ion-checkbox id="checkbox-palette-{{d}}" color="blumine" mode="md"
                                    class="checkbox-icon set-checkbox-left" (click)="checkboxStatusChange($event,d)"
                                    [checked]="obj?.signature" [disabled]="!obj.mySignNeed && !mySignatureNeeded"
                                    [class.set-only-checkbox-responsive]="!obj.checkboxLabel"
                                    [ngClass]="{'ion-checkbox-custom': isCheckboxCross}">
                                </ion-checkbox>

                                <label class="label-position responsive-font"
                                    *ngIf="obj?.checkboxLabel">{{obj?.checkBoxPaletteLabel}}</label>
                            </label>
                        </div>
                    </ng-container>
                </ng-container>
            </pinch-zoom>
        </div>
    </div>
    <ion-row *ngIf="applessDocumentWorkFlowComplete || consoloWorkFlow" class="ion-align-items-center">
        <ion-col size="12" class="text-center">
            <ion-label class="ion-text-wrap"> {{applessDocumentWorkFlowCompletedMessage}}</ion-label>
        </ion-col>
    </ion-row>
</ion-content>

<div class="palette-options" *ngIf="paletteButtons">

    <div class="palette text-button" id="add-sign-box" (click)="createPaletteButton('sign',true,'')"
        *ngIf="signatureTypeTagData.signaturepaletteAllowed">
        <span class="palette-button-span"><label class="palette-button-label">{{ 'BUTTONS.ADD_SIGN_BOX' | translate
                }}</label></span>
    </div>
    <div class="palette palette-left text-button" id="add-text-box" (click)="createPaletteButton('text',true,'')"
        *ngIf="signatureTypeTagData.textBoxpaletteAllowed">
        <span class="palette-button-span">
            <label class="palette-button-label">{{ 'BUTTONS.ADD_TEXT_BOX' | translate }}</label></span>
    </div>
    <div class=" palette palette-left palette-more" id="more-paltte-option"
        *ngIf="signatureTypeTagData.checkBoxpaletteAllowed" (click)="morePaletteButton()">
        <i *ngIf="plusButton" class="palette-more-icon icon ">+</i>
        <span id="add-check-box" *ngIf="!plusButton" class="palette palette-button-span"><label
                class="palette-button-label">{{ 'BUTTONS.ADD_CHECK_BOX' | translate }}</label></span>
    </div>

</div>
<div class="canvas-template">
    <canvas id="dateCanvas" width="201" height="25"></canvas>
    <canvas id="nameCanvas"></canvas>
    <canvas id="combineCanvas" width="490"></canvas>
</div>
<div class="palette-options"
    *ngIf="(represntativeConfirmSignature || finishSign) && !initiateResign && (paletteStatus===paletteStatuses.signing || paletteStatus===paletteStatuses.confirmed || paletteStatus===paletteStatuses.signed) && !applessDocumentWorkFlowCompletedMessage">
    <div class="palette blue-button" *ngIf="!snapAndSignApproval" (click)="reSignDocument();">
        <span class="palette-button-span">
            <label class="palette-button-label">{{ 'BUTTONS.RE_SIGN' | translate }}</label>
        </span>
    </div>
    <div class="palette palette-left blue-button"
        *ngIf="!approvePending && !isSigned && (represntativeConfirmSignature || finishSign) && (paletteStatus===paletteStatuses.signing || paletteStatus===paletteStatuses.signed) &&
    (displayTypeConditions.obtainSignature || (displayTypeConditions.allowAssociateRoles && associatePhysicaianSignature) || mySignatureNeeded)"
        (click)="submitDocument();">
        <span class="palette-button-span">
            <label class="palette-button-label">{{ 'BUTTONS.SUBMIT_SIGNED_DOCUMENT' | translate }}</label>
        </span>
    </div>
</div>
<ion-footer class="common-footer">
    <ion-toolbar>
        <div class="pagination-container" [hidden]="applessDocumentWorkFlowComplete  || consoloWorkFlow">
            <ion-grid>
                <ion-row>
                    <ion-col>
                        <button [disabled]="currentDocPage<=1" class="left-arrow" (click)="previous()" id="previous">
                            <ion-icon name="chevron-up-outline">
                            </ion-icon>
                        </button>
                    </ion-col>
                    <ion-col>
                        <div class="page-info">{{currentDocPage}}/{{totalDocsCount}}</div>
                    </ion-col>
                    <ion-col>
                        <button [disabled]="currentDocPage>=totalDocsCount" class="right-arrow" (click)="next()"
                            id="next">
                            <ion-icon name="chevron-down-outline"></ion-icon>
                        </button>
                    </ion-col>
                </ion-row>
            </ion-grid>
        </div>
    </ion-toolbar>
</ion-footer>