.pagination-container {
    color: var(--ion-color-skin-secondary-bright);
    height: 100%;
    padding-bottom: 5px;

    .page-info {
        text-align: center;
    }

    button {
        background: transparent;

        ion-icon {
            font-size: 25px;
            color: var(--ion-color-skin-secondary-bright);
        }

        &:disabled {
            opacity: 0.5;
        }

        &.left-arrow {
            float: right;
        }

        &.right-arrow {
            float: left;
        }
    }
}

.button-download {
    color: rgb(108, 193, 136);
    padding: 12px;
    float: left;
}

.doc-download-button {
    --ion-color-base: var(--ion-color-skin-secondary-bright) !important;
    text-transform: capitalize;
    float: right;
}

.button-send {
    background-color: #ffffff;
    color: var(--ion-color-skin-main) !important;
    padding: 12px;
    float: right;
    border-radius: 2px;
}
.signature-data {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0px;
    right: 0px;
    object-fit: contain;
    @media screen and (max-width: 500px) {
        object-fit: fill;
    }
}

.snapsign {
    &-text {
        @extend .sanpsign-common-style;
        width: 36%;
    }
    &-sign {
        @extend .sanpsign-common-style;
        width: 56%;
    }
    &-checkbox-with-label {
        @extend .sanpsign-common-style;
        @media screen and (max-width: 1024px) {
            width: 30%;
        }
        width: 35%;
    }
    &-checkbox-without-label {
        @extend .sanpsign-common-style;
        width: 70%;
    }
}
.sanpsign-common-style {
    position: relative;
    vertical-align: top;
    right: auto;
    float: left;
    left: 0px;
    top: 0px;
}
.remove-bg-color-snapsign {
    border: none !important;
    box-shadow: none !important;
    color: transparent !important;
    background: transparent !important;
}
.canvas-template {
    visibility: hidden;
    width: 0px;
    height: 0px;
}

.unset-max-width {
    .drag-boundary {
        max-width: unset !important;
    }
}
.set-padding-end-start-sign {
    @media screen and (max-width: 480px) {
        padding-inline-end: var(--ion-padding, 6px);
    }
    padding-inline-end: var(--ion-padding, 12px);
}
.sign-document-wrapper {
    background: var(--ion-color-white);
    text-align: center;

    .placeholder-image-style {
        position: fixed;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
    }
    .sign-document {
        width: 100%;
        height: auto;
        position: relative;
        top: 0px;
        @media screen and (min-width: 768px) and (max-width: 1400px) {
            max-width: 800px !important;
        }
        max-width: 800px;
        border: dotted 1px black;
    }
    .drag-boundary {
        max-width: 800px;
        height: auto;
        text-align: center;
        vertical-align: baseline;
        width: 100%;
        display: inline-block;
        .set-image-loader {
            position: fixed;
            width: 100%;
            height: calc(100% - 10px);
            top: 0;
        }
        .text-block {
            position: absolute;
            text-align: left;
            cursor: pointer;
            display: flex;
            margin: auto;
            background-color: var(--ion-color-white);
            .set-text-color {
                color: var(--ion-color-black);
            }
            .set-label-full-width {
                width: 100%;
            }
        }
        .sign-field-signed,
        .text-field-signed {
            box-shadow: none !important;
            border: none !important;
            background-color: transparent !important;
            textarea {
                border: none;
            }
        }
        .palette-style {
            position: absolute;
            text-align: center;
            cursor: not-allowed;
            background-color: var(--ion-color-white);
            min-height: 8px;
            &.field-hide {
                opacity: 0.5;
            }
            textarea {
                width: 100%;
                height: 100%;
                background-color: var(--ion-color-white);
                border-width: 0;
                padding: 1px 0 0 1px;
            }
        }

        .only-checkbox {
            @media screen and (max-width: 480px) {
                width: 10px !important;
                height: 10px !important;
                --size: 10px;
            }
        }
        .label-position {
            float: left;
        }

        .textarea-resize {
            resize: none;
            width: 100%;
            height: 100%;
            background-color: var(--ion-color-white);
            color: var(--ion-color-black);
            min-height: 8px;
            position: absolute;
            left: 0px !important;
            top: 0px;
            bottom: 0px;
            padding: 1px 0 0 1px;
            font-family: sans-serif;
        }

        .sign-picture {
            width: 100%;
            vertical-align: top;
            height: 100%;
            padding: 2px;
            position: relative;
            background-color: var(--ion-color-white);
        }

        .checkbox-input-label {
            top: 1px;
            color: #000;
            text-align: left;
            margin-top: 0px;
            background-color: var(--ion-color-white);
        }

        .close-option {
            font-size: 20px;
            position: absolute;
            top: -12px;
            right: -14px;
            background: none;
            border: none;
            width: 20px;
            height: 20px;
        }

        .responsive-font {
            @media screen and (min-width: 320px) and (max-width: 440px) {
                font-size: 1.6vw;
            }
            @media screen and (min-width: 441px) and (max-width: 767px) and (-webkit-min-device-pixel-ratio: 2) {
                font-size: 1.4vw;
            }
            @media screen and (min-width: 768px) and (max-width: 1223px) and (-webkit-min-device-pixel-ratio: 2) {
                font-size: 1.5vw;
            }
            @media screen and (min-width: 1224px) and (-webkit-min-device-pixel-ratio: 2) {
                font-size: 13px;
            }
        }
    }
}
.palette-options {
    width: 100%;
    height: 47px;
    background: #f8f8f8;
    padding: 5px 5px 5px;
    border-top: 1px solid #e2e2e2;

    .text-button {
        background: var(--ion-color-skin-secondary-bright) url(/assets/images/paletteButtons/signing--icon.png)
            no-repeat 10px 10px;
        background-size: 14px auto;
    }
    .blue-button {
        background: var(--ion-color-document-submit);
        background-size: 14px auto;
    }
    .palette {
        width: auto;
        float: left;
        height: 36px;
        text-align: center;
        line-height: 2;
        color: var(--ion-color-white);
        padding: 0px 18px;
        min-width: 105px;
        border-radius: 3px;
        cursor: pointer;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.25);
    }

    .palette-left {
        margin-left: 5px;
    }

    .palette-button-span {
        float: left;
        margin-left: 10px;
    }

    .palette-button-label {
        cursor: pointer;
        font-weight: normal;
        font-size: 13px;
        line-height: 1.42857;
    }

    .palette-more {
        min-width: 40px;
        background: var(--ion-color-skin-secondary-bright);
        text-align: center;
        float: right;
    }

    .palette-more-icon {
        font-size: 25px;
        right: 0px;
        position: initial;
        top: 0px;
        line-height: 35px;
    }
}

.tab-contents {
    color: var(--ion-color-white);
    text-align: center;

    .ul-contents {
        padding: 0px;
        background: var(--ion-color-white);
        border: 1px solid var(--ion-color-skin-secondary-bright);
        color: var(--ion-color-skin-main);
        border-radius: 10px;
        list-style-type: none;
        margin: 6px;
    }

    .li-contents {
        display: inline;
        cursor: pointer;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .border-left {
        border-bottom-left-radius: 9px;
        border-top-left-radius: 9px;
    }

    .border-right {
        border-bottom-right-radius: 9px;
        border-top-right-radius: 9px;
    }

    .text-colour {
        color: var(--ion-color-white);
    }

    .send-button {
        color: var(--ion-color-skin-secondary-bright);
    }

    .select-option {
        --border-radius: 10px;
        min-height: 40px;
    }
}
.checkbox-input-label-set {
    border: none;
    resize: none;
    position: absolute;
    font-size: 14px;
    width: calc(100% - 14px);
    height: -webkit-fill-available;
    @media screen and (min-width: 767px) {
        min-height: 11px;
    }
    @media screen and (max-width: 480px) {
        left: 10px;
        width: calc(100% - 10px);
    }
}
.md {
    .checkbox-input-label-set,
    .textarea-resize {
        @media screen and (min-width: 320px) and (max-width: 1400px) {
            line-height: 0.7;
        }
        line-height: 1;
    }
}
.ios {
    .checkbox-input-label-set,
    .textarea-resize {
        @media screen and (min-width: 320px) and (max-width: 1400px) {
            line-height: 0.8;
        }
        line-height: 1;
    }
}
.checkbox-icon {
    width: 100%;
    height: 100%;
    max-width: 13px;
    max-height: 13px;
    --size: 13px;
    @media screen and (min-width: 500px) and (max-width: 1366px) {
        width: 10px;
        height: 10px;
        --size: 10px;
        vertical-align: text-top;
    }
    @media screen and (max-width: 480px) {
        width: 8px;
        height: 8px;
        --size: 10px;
        vertical-align: text-top;
    }
    --border-width: 1px;
}

.ion-checkbox-custom::part(mark) {
    stroke: rgb(255, 255, 255);
    stroke-width: 4;
    d: path("M 4,4 L20,20 M 20,4 L 4,20");
}
.set-only-checkbox-responsive {
    max-width: 13px;
    max-height: 13px;
    vertical-align: text-top;
    --size: 13px;
    @media screen and (max-width: 480px) {
        width: 8px;
        height: 8px;
        --size: 8px;
    }
}
.set-height {
    height: 100%;
}
.remove-position-relative {
    position: unset !important;
}
.view-document-page-ion-content {
    &.disabled-scroll-on-drag-start {
        --overflow: hidden;
    }
}

.only-checkbox-responsive {
    @media screen and (max-width: 480px) {
        width: 10px !important;
        height: 10px !important;
        --size: 10px;
        .checkbox-icon {
            position: absolute;
            top: 0;
        }
    }
    @media screen and (min-width: 720px) {
        width: 13px !important;
        height: 13px !important;
        --size: 13px;
        transform: translate(2px, 3px) !important;
    }
}

.set-checkbox-align-left {
    @media screen and (max-width: 480px) {
        position: absolute;
    }
}
.checkbox-wrapper {
    @media screen and (max-width: 480px) {
        display: flex;
    }
}

.set-checkbox-left {
    float: left;
    margin-right: 2px;
    left: 0;
}

.set-as-button {
    background-color: #ffffff;
    --background: #ffffff;
    color: var(--ion-color-skin-main) !important;
    margin-top: 7px;
}
.set-row-height{
    min-height: 56px;
}