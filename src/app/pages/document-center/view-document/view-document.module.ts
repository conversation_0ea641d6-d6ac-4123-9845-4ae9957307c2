import { PinchZoomModule } from '@meddv/ngx-pinch-zoom';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ViewDocumentPageRoutingModule } from 'src/app/pages/document-center/view-document/view-document-routing.module';
import { ViewDocumentPage } from 'src/app/pages/document-center/view-document/view-document.page';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { SharedModule } from 'src/app/shared.module';
import { AngularDraggableModule } from 'angular2-draggable';
import { SignatureMorePalettesModule } from 'src/app/pages/document-center/signature-more-palettes/signature-more-palettes.module';
import { CheckboxGroupComponentModule } from 'src/app/pages/document-center/checkbox-group/checkbox-group.module';
import { SignatureComponentModule } from 'src/app/components/signature/signature.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ViewDocumentPageRoutingModule,
    HeaderPlainModule,
    SharedModule,
    AngularDraggableModule,
    SignatureMorePalettesModule,
    CheckboxGroupComponentModule,
    SignatureComponentModule,
    PinchZoomModule
  ],
  declarations: [ViewDocumentPage]
})
export class ViewDocumentPageModule {}
