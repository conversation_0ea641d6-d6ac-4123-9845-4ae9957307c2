import { ExistingFileListPageModule } from './../existing-file-list/existing-file-list.module';
import { SitesModule } from './../../../components/sites/sites.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FilingCenterPage } from './filing-center.page';
import { SharedModule } from 'src/app/shared.module';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SharedModule,
    HeaderPlainModule,
    SearchBarRecipientsModule,
    SitesModule,
    ExistingFileListPageModule
  ],
  declarations: [FilingCenterPage],
  exports: [FilingCenterPage]
})
export class FilingCenterPageModule {}
