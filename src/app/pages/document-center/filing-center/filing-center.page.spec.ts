import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Apollo } from 'apollo-angular';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { IonicModule, NavParams, ModalController } from '@ionic/angular';
import { FilingCenterPage } from './filing-center.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';
import { UntypedFormBuilder } from '@angular/forms';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { of, throwError } from 'rxjs';
import { Constants, FolderType } from 'src/app/constants/constants';
import { Config } from 'src/app/constants/config';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SiteDetailsResponse } from 'src/app/interfaces/document-request-signature';

describe('FilingCenterPage', () => {
  let component: FilingCenterPage;
  let fixture: ComponentFixture<FilingCenterPage>;
  let sharedService: SharedService;
  const modalSpy = TestConstants.modalSpy;
  let modalController: ModalController;
  let graphqlService: GraphqlService;
  let common: CommonService;
  const selectedItemData = {
    id: '1110766',
    userId: 1110766,
    displayName: 'arj atla pa',
    isVirtual: false,
    userEMverification: {
      mobileVerified: 1,
      emailVerified: 1,
      __typename: 'mobileOrEmailVerification'
    },
    firstName: 'arj',
    lastName: 'atla pa',
    dateOfBirth: '1991-05-25',
    patientIdentity: {
      IdentityValue: 'MultiON',
      __typename: 'PatientIdentity'
    },
    cmisId: 356414,
    userName: '<EMAIL>',
    mobile: '',
    countryCode: '',
    role: 3,
    careGiver: null,
    __typename: 'SignatureRequestTextDisplay'
  };
  const fcMapping = {
    registrationId: 'yo640mrd9gd5wyafg6c9mqux86eu67sa'
  };
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [FilingCenterPage],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        NavParams,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        { provide: UntypedFormBuilder, useValue: new UntypedFormBuilder() },
        GraphqlService,
        ModalController
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    graphqlService = TestBed.inject(GraphqlService);
    sharedService = TestBed.inject(SharedService);
    common = TestBed.inject(CommonService);

  
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    
    modalSpy.onDidDismiss.and.resolveTo({ data: { recipient: { role: 1 } } });
    fixture = TestBed.createComponent(FilingCenterPage);
    component = fixture.componentInstance;
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call openDisplayTextModal', () => {
    component.openDisplayTextModal();
    expect(component.openDisplayTextModal).toBeDefined();
  });

  it('should call openDisplayText', () => {
    component.openDisplayText('recipient');
    component.openDisplayText('patient');
    component.openDisplayText('associate');
    component.title = '';
    expect(component.openDisplayText).toBeDefined();
  });

  it('should call fcmMapping', () => {
    component.fcmMapping(fcMapping);
    expect(component.fcmMapping).toBeDefined();
  });

  it('should call getPatientData', () => {
    component.getPatientData();
    expect(component.getPatientData).toBeDefined();
  });

  it('should call commonSignatureRequestTextsDisplaySearchFunc', () => {
    component.commonSignatureRequestTextsDisplaySearchFunc('Recipients');
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
  });

  it('should call searchOperations', () => {
    component.searchOperations({ do: 'reset', value: '' });
    expect(component.searchOperations).toBeDefined();
  });

  it('should call showAddPatient', () => {
    component.showAddPatient();
    fixture.detectChanges();
    expect(component.showAddPatient).toBeDefined();
  });

  it('should call cancel', () => {
    component.cancel();
    fixture.detectChanges();
    expect(component.cancel).toBeTruthy();
  });

  it('should call loadData', () => {
    component.loadData();
    component.pageCount = component.pageCount + 1;
    fixture.detectChanges();
    expect(component.loadData).toBeDefined();
  });

  it('should call commonFunction', () => {
    component.commonFunction();
    expect(component.commonFunction).toBeDefined();
  });

  it('should call selectItem', () => {
    component.selectItem(selectedItemData);
    expect(component.selectItem).toBeDefined();
  });

  it('should call siteTenantFilingCenterContentFunc', () => {
    component.siteTenantFilingCenterContentFunc();
    expect(component.siteTenantFilingCenterContentFunc).toBeDefined();
  });

  it('should call filterSitesData', () => {
    component.filterSitesData([]);
    expect(component.filterSitesData).toBeDefined();
  });

  it('should call openExistingFileList', () => {
    component.openExistingFileList();
    expect(component.openExistingFileList).toBeDefined();
  });

  it('should call showExistingFileList', () => {
    const itemData = {
      name: '100720R ,SreedeviABN-converted-converted.pdf',
      folder:
        'CHA_CIQARBQA4/Atlanta/CPR+ to Citus Health/Advanced Beneficiary Notice (ABN)/100720R ,SreedeviABN-converted-converted.pdf',
      size: 19527,
      type: 'application/pdf',
      preview:
        '/mnt/dav/CHA_CIQARBQA4/Atlanta/CPR+ to Citus Health/Advanced Beneficiary Notice (ABN)/100720R ,SreedeviABN-converted-converted.pdf',
      addedOn: **********.558,
      fileId: null,
      __typename: 'SyncContent'
    };
    component.showExistingFileList(itemData, '');
    fixture.detectChanges();
    expect(component.showExistingFileList).toBeDefined();
  });

  it('commonSignatureRequestTextsDisplaySearchFunc function: Throw error', () => {
    const response = {};
    spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(throwError(() => response));
    component.commonSignatureRequestTextsDisplaySearchFunc();
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeTruthy();
  });

  it('commonSignatureRequestTextsDisplaySearchFunc function: enrolled user', () => {
    const response = { data: { signatureRequestTextsDisplaySearch: [{ isVirtual: false }] } };
    spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of(response));
    component.commonSignatureRequestTextsDisplaySearchFunc();
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeTruthy();
  });

  it('commonSignatureRequestTextsDisplaySearchFunc function: virtual user, verification enabled', () => {
    sharedService.userData.config.enable_verification_of_cell_and_mobile = '1';
    const response = { data: { signatureRequestTextsDisplaySearch: [{ isVirtual: true }] } };
    spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of(response));
    component.commonSignatureRequestTextsDisplaySearchFunc();
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeTruthy();
  });

  it('commonSignatureRequestTextsDisplaySearchFunc function: virtual user, verification disabled', () => {
    sharedService.userData.config.enable_verification_of_cell_and_mobile = '0';
    const response = { data: { signatureRequestTextsDisplaySearch: [{ isVirtual: true }] } };
    spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of(response));
    component.commonSignatureRequestTextsDisplaySearchFunc();
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeTruthy();
  });

  it('commonSignatureRequestTextsDisplaySearchFunc function: virtual user, alternate contact workflow', () => {
    component.pageCount = 1;
    sharedService.userData.config.enable_verification_of_cell_and_mobile = '0';
    sharedService.userData.config.default_patients_workflow = Constants.workflowAlternateContact;
    const response = { data: { signatureRequestTextsDisplaySearch: [{ isVirtual: true, alternateContacts: [] }] } };
    spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of(response));
    component.commonSignatureRequestTextsDisplaySearchFunc();
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeTruthy();
  });

  it('searchOperations: add patient', () => {
    component.searchOperations({ do: 'add' });
    expect(component.searchOperations).toBeDefined();
  });

  it('searchOperations: select site', () => {
    component.selectedSiteIds = [];
    component.searchOperations({ do: 'reset' });
    expect(component.searchOperations).toBeDefined();
  });

  it('getPatientData: execute API return no data', () => {
    const response = [];
    spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of(response));
    component.getPatientData();
    expect(component.getPatientData).toBeTruthy();
  });

  it('getPatientData: execute API return data', () => {
    const response = [{ a: 1 }];
    spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of(response));
    component.getPatientData();
    expect(component.getPatientData).toBeTruthy();
  });

  it('getPatientData: execute API return data,page 1', () => {
    component.pageCount = 1;
    const response = [{ a: 1 }];
    spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of(response));
    component.getPatientData();
    expect(component.getPatientData).toBeTruthy();
  });

  it('getPatientData: execute API throw error', () => {
    const response = [];
    spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(throwError(response));
    component.getPatientData();
    expect(component.getPatientData).toBeTruthy();
  });

  it('execute isDefaultDataShowBehaviour', () => {
    component.isDefaultDataShowBehaviour;
    expect(component.isDefaultDataShowBehaviour).toBeDefined();
  });
  it('execute loadData', fakeAsync(() => {
    component.loadData();
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(component.loadData).toBeTruthy();
    });
  }));
  it('should open patient display text if allowAssociatePatient is true', () => {
    spyOn(component, 'openDisplayText');
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    component.selectedData = { allowAssociatePatient: true };

    component.openDisplayTextModal();

    expect(component.isSingleSelection).toBeFalse();
    expect(component.hideRecipientSearchSection).toBeFalse();
    expect(component.openDisplayText).toHaveBeenCalledWith(component.constData.patientValue);
  });

  it('should open associate display text if allowAssociateRoles is true and allowRecipientRoles is false', () => {
    spyOn(component, 'openDisplayText');
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    component.selectedData = { allowAssociateRoles: true, allowRecipientRoles: false };
    component.userData = { mySites: ['Site1'] };

    component.openDisplayTextModal();

    expect(component.hideRecipientSearchSection).toBeTrue();
    expect(component.isSingleSelection).toBeTrue();
    expect(component.openDisplayText).toHaveBeenCalledWith(component.constData.associate);
  });

  it('should open recipient display text if allowRecipientRoles is true', () => {
    spyOn(component, 'openDisplayText');
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    component.selectedData = { allowRecipientRoles: true };
    component.userData = { mySites: ['Site1'] };

    component.openDisplayTextModal();

    expect(component.hideRecipientSearchSection).toBeTrue();
    expect(component.isSingleSelection).toBeTrue();
    expect(component.openDisplayText).toHaveBeenCalledWith(component.constData.recipient);
  });

  it('should call getFileDetailsBySite if mySites has exactly one site', () => {
    spyOn(component, 'getFileDetailsBySite');
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    component.selectedData = { allowRecipientRoles: false, allowAssociatePatient: false };
    component.userData = { mySites: ['Site1'] };

    component.openDisplayTextModal();

    expect(component.hideRecipientSearchSection).toBeTrue();
    expect(component.siteFilterShow).toBeFalse();
    expect(component.getFileDetailsBySite).toHaveBeenCalled();
  });

  it('should open recipient display text if mySites has more than one site', () => {
    spyOn(component, 'openDisplayText');
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    component.selectedData = { allowRecipientRoles: false, allowAssociatePatient: false };
    component.userData = { mySites: ['Site1', 'Site2'] };

    component.openDisplayTextModal();

    expect(component.isSingleSelection).toBeTrue();
    expect(component.openDisplayText).toHaveBeenCalledWith(component.constData.recipient);
  });

  it('should call openExistingFileList if none of the conditions match', () => {
    spyOn(component, 'openExistingFileList');
    spyOn(sharedService, 'isEnableConfig').and.returnValue(false);

    component.openDisplayTextModal();

    expect(component.openExistingFileList).toHaveBeenCalled();
  });
  it('should handle multi-site enabled and call siteTenantFilingCenterContentFunc', () => {
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    component.fcmMappingData = { folderBySite: 'testFolder', responseData: {} };
    component.selectedData = { toFilingCenter: 'testFilingCenter' };
    spyOn(component, 'siteTenantFilingCenterContentFunc');

    component.openExistingFileList();

    expect(component.fcmMappingData.responseData.folder).toBe('testFilingCenter');
    expect(component.fcmMappingData.responseData.siteRegistrationId).toBe('');
    expect(component.siteTenantFilingCenterContentFunc).toHaveBeenCalled();
  });

  it('should handle single-site case and call showExistingFileList', (done) => {
    spyOn(sharedService, 'isEnableConfig').and.returnValue(false);
    component.selectedData = { toFilingCenter: 'testFilingCenter' };
    const mockResponse = {
      data: { getTenantFilingCenterContent: 'testContent' },
    };
    spyOn(graphqlService, 'folderFilingCenterContent').and.returnValue({
      valueChanges: {
        subscribe: (callback: any) => {
          callback(mockResponse);
        },
      },
    });
    spyOn(component, 'showExistingFileList');

    component.openExistingFileList();

    setTimeout(() => {
      expect(component.showExistingFileList).toHaveBeenCalledWith(
        [],
        'getTenantFilingCenterContent'
      );
      done();
    });
  });

  it('should do nothing if no folder is available', () => {
    spyOn(sharedService, 'isEnableConfig').and.returnValue(false);
    component.selectedData = null;

    component.openExistingFileList();

    expect(component.fcmMappingData?.responseData?.folder).toBeUndefined();
  });
  it('should handle "recipient" case with multi-site enabled and no recipient roles', () => {
    spyOn(sharedService, 'isEnableConfig').and.returnValue(true);
    component.selectedData = { allowAssociatePatient: false, allowRecipientRoles: false };

    component.openDisplayText('recipient');

    expect(component.title).toBe('');
    expect(component.siteFilterShow).toBeTrue();
  });

  it('should handle "recipient" case with one site', () => {
    spyOn(sharedService, 'isEnableConfig').and.returnValue(false);
    spyOn(common, 'getTranslateData').and.returnValue('Translated Title');
    component.userData = { mySites: [{ id: '123' }] };
    component.selectedData = { allowAssociatePatient: true, allowRecipientRoles: true };
    spyOn(component, 'commonFunction');

    component.openDisplayText('recipient');

    expect(component.title).toBe('Translated Title');
    expect(component.isRecipient).toBeTrue();
    expect(component.siteFilterShow).toBeFalse();
    expect(component.selectedSiteIds).toEqual(['123']);
    expect(component.commonFunction).toHaveBeenCalled();
  });

  it('should handle "patient" case', () => {
    spyOn(sharedService, 'getSiteLabelBasedOnMultiAdmission').and.returnValue('Custom Label');
    spyOn(common, 'getTranslateData').and.returnValue('Patient Title');
    spyOn(component, 'getPatientData');

    component.openDisplayText('patient');

    expect(component.title).toBe('Patient Title');
    expect(component.siteFilterShow).toBeTrue();
    expect(component.addPatient).toBeTrue();
    expect(component.hideRecipientSearchSection).toBeTrue();
    expect(component.siteLabel).toBe('Custom Label');
    expect(component.getPatientData).toHaveBeenCalled();
  });

  it('should handle "associate" case', () => {
    spyOn(common, 'getTranslateData').and.returnValue('Associate Title');
    spyOn(component, 'commonSignatureRequestTextsDisplaySearchFunc');

    component.openDisplayText('associate');

    expect(component.title).toBe('Associate Title');
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toHaveBeenCalledWith(
      component.constData.signatureRequestActionType.onBehalf
    );
  });
  describe('getFileDetailsBySite', () => {
    it('should call getFileDetailsBySite', () => {
      component.selectedFolderType = FolderType.fillingCenter;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const response: SiteDetailsResponse = { data: { getSiteDetails: {} as any } };
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      spyOn(graphqlService, 'siteDetails').and.returnValue(of(response as any));
      spyOn(component, 'fcmMapping');
      component.getFileDetailsBySite(selectedItemData);
      expect(component.getFileDetailsBySite).toBeDefined();
    });
    it('should call getFileDetailsBySite: no data returned', () => {
      component.selectedFolderType = FolderType.fillingCenter;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const response: SiteDetailsResponse = { data: {} as any };
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      spyOn(graphqlService, 'siteDetails').and.returnValue(of(response as any));
      spyOn(common, 'showMessage');
      spyOn(component, 'fcmMapping');
      component.getFileDetailsBySite(selectedItemData);
      expect(component.getFileDetailsBySite).toBeDefined();
      expect(common.showMessage).toHaveBeenCalledWith('LABELS.SELECT_SITES');
    });
    it('should call getFileDetailsBySite: document folder', () => {
      component.selectedFolderType = FolderType.documentFolder;
      spyOn(component, 'showExistingFileList');
      component.getFileDetailsBySite(selectedItemData);
      expect(component.getFileDetailsBySite).toBeDefined();
      expect(component.showExistingFileList).toHaveBeenCalled();
    });
    it('should set site based on user data if only one site is available', () => {
      component.userData = { mySites: [{ id: '1' }] };
      component.getFileDetailsBySite();
      expect(component.selectedSiteId).toBe('1');
    });
    it('should select virtualPatientSiteId if available', () => {
      component.fillingData = {
        ...component.fillingData,
        virtualPatientSiteId: component.fillingData.virtualPatientSiteId
      };
      component.getFileDetailsBySite();
      expect(component.selectedSiteId).toBe('0');
    });
  });

  it('should fall back to selectedSiteIds if no other site information is available', () => {
    component.selectedSiteIds = ['6'];
    component.getFileDetailsBySite();

    expect(component.selectedSiteId).toBe('6');
  });
  it('should handle FCM mappings correctly', () => {
    const mockResponse = {
      data: {
        siteFcMappings: [{
          registrationId: '123',
          folder: 'folderPath',
          folderPath: 'path/to/folder',
          siteRegistrationId: '123',
          filingCenterSiteConfig: { fromFilingCenter: 'defaultFolder' },
        }]
      }
    };

    const mockApolloQueryResult = {
      data: mockResponse.data,
      loading: false,
      networkStatus: 7, 
    };

    spyOn(graphqlService, 'siteFCMappings').and.returnValue(of(mockApolloQueryResult));
    let passParams = {
      docRefType: 1, 
      docRefTypeId: parseInt(component.selectedData?.id), 
      siteRegistrationId: mockResponse.data.siteFcMappings[0].registrationId 
    };

    if (
      sharedService.getConfigValue('documentExchangeMode') === 'whValue' &&
      parseInt(component.selectedData?.tagTypeId) !== 0
    ) {
      passParams.docRefType = 2;
      passParams.docRefTypeId = parseInt(component.selectedData?.tagTypeId);
    }

    component.fcmMapping(mockResponse.data);

    expect(sharedService.isLoading).toBe(false);
    const receivedData = mockResponse.data.siteFcMappings[0];
    expect(component.fcmMappingData.responseData).toEqual(receivedData);
    expect(component.fcmMappingData.folderBySite).toBe(receivedData.folder);
    expect(component.fcmMappingData.folderPath).toBe(receivedData.folderPath);
    expect(component.fcmMappingData.siteRegId).toBe(receivedData.siteRegistrationId);
    expect(component.fcmMappingData.defaultFolderFromFilingCenter).toBe(receivedData.filingCenterSiteConfig.fromFilingCenter);
  });
  it('should call getPatientData when title is "TITLES.CHOOSE_ASSOCIATED_PATIENT"', () => {
    component.title = (component as any).common.getTranslateData('TITLES.CHOOSE_ASSOCIATED_PATIENT');
    spyOn(component, 'getPatientData');
    component.commonFunction();
    expect(component.getPatientData).toHaveBeenCalled();
  });
  it('should call commonSignatureRequestTextsDisplaySearchFunc when title is "TITLES.CHOOSE_RECIPIENTS" or "TITLES.CHOOSE_DELEGATED_STAFF" and there are selected site IDs and search key', () => {
    component.title =  (component as any).common.getTranslateData('TITLES.CHOOSE_RECIPIENTS');
    component.selectedSiteIds = [1, 2];
    component.searchKey = 'testKey';
    spyOn(component, 'commonSignatureRequestTextsDisplaySearchFunc');
    component.commonFunction();
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toHaveBeenCalledWith(component.constData.signatureRequestActionType.recipients);
  
    component.title =  (component as any).common.getTranslateData('TITLES.CHOOSE_DELEGATED_STAFF');
    component.commonFunction();
    expect(component.commonSignatureRequestTextsDisplaySearchFunc).toHaveBeenCalledWith(component.constData.signatureRequestActionType.onBehalf);
      component.searchKey = '';
    spyOn(component, 'showCommonMessage');
    component.commonFunction();
    expect(component.sharedService.isLoading).toBeFalse();
    expect(component.noData).toBeTrue();
    expect(component.showCommonMessage).toHaveBeenCalled();
  });

  it('should call graphqlService.siteDetails and handle response correctly when title does not match "TITLES.CHOOSE_ASSOCIATED_PATIENT" or "TITLES.CHOOSE_RECIPIENTS"/"TITLES.CHOOSE_DELEGATED_STAFF"', () => {
    component.title =  (component as any).common.getTranslateData('TITLES.OTHER');
    component.selectedSiteIds = [1];
    spyOn( (component as any).graphqlService, 'siteDetails').and.returnValue(of({ data: { getSiteDetails: { /* mock data */ } } }));
    spyOn(component, 'fcmMapping');
    component.commonFunction();
    expect(component.sharedService.isLoading).toBeFalse();
    expect(component.fcmMapping).toHaveBeenCalledWith({ /* mock site details */ });
      (component as any).graphqlService.siteDetails.and.returnValue(throwError('error'));
    spyOn(component, 'showCommonMessage');
    component.commonFunction();
    expect(component.sharedService.isLoading).toBeFalse();
  });
  it('should handle no search key correctly', () => {
    component.title = (component as any).common.getTranslateData('TITLES.CHOOSE_RECIPIENTS');
    component.selectedSiteIds = [1, 2];
    component.searchKey = ''; 
  
    component.sharedService.isLoading = false; 
    spyOn(component, 'showCommonMessage'); 
  
    component.commonFunction();
  
    expect(component.sharedService.isLoading).toBeFalse();
    expect(component.noData).toBeTrue();
    expect(component.showCommonMessage).toHaveBeenCalled();
  });
  it('should set userType to "Recipient" and select site if TITLES.CHOOSE_RECIPIENTS is chosen', () => {
    component.title = (component as any).common.getTranslateData('TITLES.CHOOSE_RECIPIENTS');
    const item = { role: 'someRole', careGiver: true, site_id: '1' };
    component.selectedData = { allowRecipientRoles: true, allowAssociatePatient: true };
    spyOn(component.sharedService, 'isEnableConfig').and.returnValue(true);
    spyOn(component, 'getFileDetailsBySite');
  
    component.selectItem(item);
  
    expect(component.fillingData.userType).toBe('Recipient');
    expect(component.fillingData.selectedRecipientRole).toBe(item.role);
    expect(component.fillingData.isRecipientCaregiver).toBe(true);
    expect(component.fillingData.selectedPatientSiteForRecipient).toBe(item.site_id);
    expect(component.fillingData.disableSiteFilter).toBe(true);
    expect(component.getFileDetailsBySite).toHaveBeenCalledWith(item);
  });
  it('should set userType to "Associated Patient" if TITLES.CHOOSE_ASSOCIATED_PATIENT is chosen', () => {
    component.title = (component as any).common.getTranslateData('TITLES.CHOOSE_ASSOCIATED_PATIENT');
    const item = { role: 'someRole', careGiver: false, site_id: '2' };
    component.selectedData = { allowAssociateRoles: true };
  
    component.selectItem(item);
  
    expect(component.fillingData.userType).toBe('Associated Patient');
    expect(component.fillingData.selectedRecipientRole).toBe(item.role);
    expect(component.fillingData.isRecipientCaregiver).toBe(false);
    expect(component.fillingData.selectedPatientSiteForRecipient).toBe(item.site_id);
  });
  it('should set userType to "Delegated Staff" if TITLES.CHOOSE_DELEGATED_STAFF is chosen', () => {
    component.title = (component as any).common.getTranslateData('TITLES.CHOOSE_DELEGATED_STAFF');
    const item = { role: 'someRole', careGiver: true, site_id: '3' };
    component.selectedData = { allowAssociateRoles: true };
  
    component.selectItem(item);
  
    expect(component.fillingData.userType).toBe('Delegated Staff');
    expect(component.fillingData.selectedRecipientRole).toBe(item.role);
    expect(component.fillingData.isRecipientCaregiver).toBe(true);
    expect(component.fillingData.selectedPatientSiteForRecipient).toBe(item.site_id);
  });
  describe('CommonFunctionSignatureRequestTextsDisplaySearch', () => {
    it('should call commonSignatureRequestTextsDisplaySearchFunc call signatureRequestTextsDisplaySearch graphQL API with success', () => {
      spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of({ data: { signatureRequestTextsDisplaySearch: [{}] } }));
      component.commonSignatureRequestTextsDisplaySearchFunc('OnBehalf');
      fixture.detectChanges();
      expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
    });
    it('should call commonSignatureRequestTextsDisplaySearchFunc call signatureRequestTextsDisplaySearch return empty', () => {
      spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of({ data: { signatureRequestTextsDisplaySearch: [] } }));
      component.selectedData = { allowAssociatePatient: true };
      component.fillingData = {
        userType: 'Recipient',
        selectedRecipientRole: '3',
        isRecipientCaregiver: false,
        selectedPatientSiteForRecipient: '1',
        disableSiteFilter: false,
        virtualPatientSiteId: '0',
        selectedPatientSiteId: '1',
        selectedAssociatePatientId: '123',
        patient: { id: '123' },
        admission: null
      };
      component.commonSignatureRequestTextsDisplaySearchFunc('Recipients');
      fixture.detectChanges();
      expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
      expect(component.createPatientLinkMessage).toBe('');
    });
  });
});