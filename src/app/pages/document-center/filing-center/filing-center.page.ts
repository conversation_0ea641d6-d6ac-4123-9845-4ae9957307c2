import {
  SiteDetailsResponse,
  FCMappingsResponse,
  SignatureRequestRecipients
} from 'src/app/interfaces/document-request-signature';
import { ExistingFileListPage } from './../existing-file-list/existing-file-list.page';
import { AddVirtualPatientComponent } from './../../user/add-virtual-patient/add-virtual-patient.component';
import { Config } from 'src/app/constants/config';
import { Component } from '@angular/core';
import { ModalController, NavParams } from '@ionic/angular';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Constants, FolderType, UserGroup } from 'src/app/constants/constants';
import { isBlank, isObject, isPresent } from 'src/app/utils/utils';
import { SyncFolderType } from 'src/app/interfaces/messages';
import { getValueFromLocalStorage } from 'src/app/utils/storage-utils';
import { Admission } from 'src/app/interfaces/common-interface';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';
import { UserService } from 'src/app/services/user-service/user.service';

@Component({
  selector: 'app-filing-center',
  templateUrl: './filing-center.page.html',
  styleUrls: ['./filing-center.page.scss']
})
export class FilingCenterPage {
  isSingleSelection: boolean = false;
  title: string;
  searchKey: string;
  noData: boolean;
  noResult = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
  selectedData: any;
  selectedSiteIds: any;
  userData: any;
  pageCount = Constants.defaultPageCount;
  displayData: any = [];
  hideRecipientSearchSection: boolean = false;
  siteFilterShow: boolean = true;
  constData: any;
  addPatient: boolean = false;
  virtualPatient: any;
  fcmMappingData: any = {
    folderBySite: '',
    folderPath: '',
    siteRegId: '',
    defaultFolderFromFilingCenter: '',
    responseData: {}
  };
  fillingData = {
    userType: '',
    selectedRecipientRole: '',
    isRecipientCaregiver: false,
    selectedPatientSiteForRecipient: '',
    disableSiteFilter: false,
    virtualPatientSiteId: '',
    selectedPatientSiteId: '',
    selectedAssociatePatientId: '',
    patient: {} as any,
    admission: {} as any
  };
  filingCenterContentData: any = [];
  isShowLoadMore: boolean = false;
  loadLimit: number;
  siteFilter = true;
  isRecipient = false;
  createPatientLinkMessage = '';
  selectedSiteId: string;
  siteLabel = 'LABELS.SITES';
  selectedFolderType: FolderType;
  constructor(
    private readonly modalController: ModalController,
    private readonly navParams: NavParams,
    private readonly graphqlService: GraphqlService,
    private readonly common: CommonService,
    private readonly userService: UserService,
    public readonly sharedService: SharedService
  ) {
    this.userData = this.sharedService.userData;
    this.constData = Constants;
    this.selectedData = this.navParams.get('selectedItem');
    this.selectedFolderType = this.navParams.get('selectedFolderType');
    this.openDisplayTextModal();
    this.siteFilter = this.sharedService.isEnableConfig(Config.enableMultiSite);
  }

  openDisplayTextModal() {
    if (this.sharedService.isEnableConfig(Config.enableMultiSite) && this.selectedData?.allowAssociatePatient) {
      // TODO: patient
      this.isSingleSelection = this.hideRecipientSearchSection = false;
      this.openDisplayText(this.constData.patientValue);
    } else if (
      this.sharedService.isEnableConfig(Config.enableMultiSite) &&
      (this.selectedData?.allowRecipientRoles || this.selectedData?.allowAssociateRoles)
    ) {
      if (this.userData?.mySites && this.userData?.mySites?.length > 0) {
        this.hideRecipientSearchSection = true;
      }
      this.isSingleSelection = true;
      if (this.selectedData.allowAssociateRoles && !this.selectedData.allowRecipientRoles) {
        // TODO: associate
        this.openDisplayText(this.constData.associate);
      } else {
        // TODO: recipient
        this.openDisplayText(this.constData.recipient);
      }
    } else if (
      this.sharedService.isEnableConfig(Config.enableMultiSite) &&
      !this.selectedData?.allowRecipientRoles &&
      !this.selectedData?.allowAssociatePatient
    ) {
      if (this.userData?.mySites && this.userData?.mySites?.length === 1) {
        this.hideRecipientSearchSection = true;
        this.siteFilterShow = false;
        this.getFileDetailsBySite();
      } else {
        // TODO: recipient
        this.isSingleSelection = true;
        this.openDisplayText(this.constData.recipient);
      }
    } else {
      this.openExistingFileList();
    }
  }

  openExistingFileList() {
    const folder = this.sharedService.isEnableConfig(Config.enableMultiSite)
      ? this.fcmMappingData.folderBySite // set dynamic if available
      : this.selectedData?.toFilingCenter;
    if (folder) {
      if (this.sharedService.isEnableConfig(Config.enableMultiSite)) {
        this.fcmMappingData.responseData.folder = this.selectedData?.toFilingCenter;
        this.fcmMappingData.responseData.siteRegistrationId = '';
        this.siteTenantFilingCenterContentFunc();
      } else {
        this.showExistingFileList([], 'getTenantFilingCenterContent');
      }
    }
  }

  openDisplayText(title: string) {
    this.selectedSiteIds = '0';
    this.siteLabel = 'LABELS.SITES';
    switch (title) {
      case 'recipient':
        if (
          this.sharedService.isEnableConfig(Config.enableMultiSite) &&
          !this.selectedData?.allowAssociatePatient &&
          !this.selectedData?.allowRecipientRoles
        ) {
          this.title = '';
          this.siteFilterShow = true;
        } else {
          this.title = this.common.getTranslateData('TITLES.CHOOSE_RECIPIENTS');
          this.isRecipient = true;
          if (this.userData?.mySites && this.userData?.mySites?.length === 1) {
            this.siteFilterShow = false;
            this.selectedSiteIds = [this.userData?.mySites[0].id];
            this.commonFunction();
          }
        }
        break;
      case 'patient':
        this.title = this.common.getTranslateData('TITLES.CHOOSE_ASSOCIATED_PATIENT');
        this.siteFilterShow = this.addPatient = this.hideRecipientSearchSection = true;
        this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITES');
        this.getPatientData();
        break;

      case 'associate':
        this.title = this.common.getTranslateData('TITLES.CHOOSE_DELEGATED_STAFF');
        this.commonSignatureRequestTextsDisplaySearchFunc(this.constData.signatureRequestActionType.onBehalf);
        break;
    }
  }

  /**
   *
   * @param selectedPatient selected item data
   */
  getFileDetailsBySite(selectedPatient?: any) {
    let site, selectPatientSite;
    if (this.userData?.mySites && this.userData?.mySites?.length === 1) {
      site = this.userData?.mySites[0].id.toString();
    } else {
      if (selectedPatient && selectedPatient.siteId) {
        selectPatientSite = selectedPatient.siteId;
      } else if (selectedPatient && selectedPatient.site_id) {
        selectPatientSite = selectedPatient.site_id;
      } else if (selectedPatient && selectedPatient?.createdPatientDetails?.siteIds) {
        selectPatientSite = selectedPatient?.createdPatientDetails?.siteIds;
      }
      if (!isBlank(this.fillingData.virtualPatientSiteId) && String(this.fillingData.virtualPatientSiteId) !== '0') {
        site = this.sharedService.isMultiAdmissionsEnabled
          ? this.fillingData?.admission?.siteId
          : this.fillingData.virtualPatientSiteId[0].toString();
      } else if (!isBlank(selectPatientSite) && String(selectPatientSite) !== '0') {
        site = this.sharedService.isMultiAdmissionsEnabled ? this.fillingData?.admission?.siteId : selectPatientSite;
      } else {
        site = this.selectedSiteIds && this.selectedSiteIds !== '0' && this.selectedSiteIds.length !== 0 ? this.selectedSiteIds[0].toString() : '0';
      }
    }
    this.selectedSiteId = site;
    this.sharedService.isLoading = true;
    const admissionId = this.sharedService.isMultiAdmissionsEnabled && !isBlank(this.fillingData?.admission) ? this.fillingData?.admission?.admissionId : '';
    if (this.selectedFolderType === FolderType.fillingCenter) {
      this.graphqlService.siteDetails(+site, admissionId)?.subscribe((response: SiteDetailsResponse) => {
        if (response && response.data.getSiteDetails) {
          this.fcmMapping(response.data.getSiteDetails);
        } else {
          this.sharedService.isLoading = false;
          this.common.showMessage(this.common.getTranslateData('LABELS.SELECT_SITES'));
        }
      });
    } else if (this.selectedFolderType === FolderType.documentFolder) {
      this.showExistingFileList([], 'documents', { siteIds: site, tagId: this.selectedData?.tagGuid });
    }
  }

  /**
   * After site details response call this graphQL service
   * @param response Site details response
   */
  fcmMapping(response: any) {
    const passParams = {
      docRefType: 0,
      docRefTypeId: 0,
      siteRegistrationId: response.registrationId
    };
    if (
      this.sharedService.getConfigValue(Config.documentExchangeMode) === Constants.whValue &&
      parseInt(this.selectedData?.tagTypeId) !== 0
    ) {
      passParams.docRefType = 2; // It is denoted as document type category
      passParams.docRefTypeId = parseInt(this.selectedData?.tagTypeId);
    } else {
      passParams.docRefType = 1; // It is denoted as document type category
      passParams.docRefTypeId = parseInt(this.selectedData?.id);
    }
    this.sharedService.isLoading = true;
    this.graphqlService.siteFCMappings(passParams)?.subscribe((response: FCMappingsResponse) => {
      this.sharedService.isLoading = false;
      const receivedData = response.data.siteFcMappings[0];
      this.fcmMappingData.responseData = receivedData;
      this.fcmMappingData.folderBySite = receivedData.folder;
      this.fcmMappingData.folderPath = receivedData.folderPath;
      this.fcmMappingData.siteRegId = receivedData.siteRegistrationId;
      this.fcmMappingData.defaultFolderFromFilingCenter =
        receivedData.filingCenterSiteConfig && receivedData.filingCenterSiteConfig.fromFilingCenter
          ? receivedData.filingCenterSiteConfig.fromFilingCenter
          : '';
      this.showExistingFileList([], 'getSiteTenantFilingCenterContent', passParams);
    });
  }

  /**
   * After complete siteFCMappings graphQL service call this graphQL service
   */
  siteTenantFilingCenterContentFunc() {
    let passParams = {
      sessionId: this.sharedService.userData?.authenticationToken,
      type: SyncFolderType.INCOMING,
      folder: this.fcmMappingData.responseData?.folder,
      guid: this.fcmMappingData.responseData?.siteRegistrationId,
      searchKey: '',
      fromCrud: false
    };
    this.showExistingFileList([], 'getSiteTenantFilingCenterContent', passParams);
  }
  /**
   * Open another popup to view existing-file-list
   * @param existingFileData siteTenantFilingCenterContent response
   */
  async showExistingFileList(existingFileData, query: string, payload?) {
    const modal: any = await this.modalController.create({
      component: ExistingFileListPage,
      componentProps: {
        existingFileData,
        payload,
        fcmMappingData: this.fcmMappingData.responseData,
        graphQLQuery: query,
        selectedFolderType: this.selectedFolderType
      },
      id: 'existing-file-list'
    });
    modal.present();
    modal.onDidDismiss().then(({ data }) => {
      if (data.dismissed) {
        this.modalController.dismiss({
          item: data?.item,
          dismissed: true,
          selectedRecipients: this.fillingData.patient,
          admission: this.fillingData.admission,
          selectedSite: this.selectedSiteId ? this.selectedSiteId : this.selectedSiteIds,
          selectedSites: this.selectedSiteIds,
          fcmMappingData: this.fcmMappingData,
          alternateIndex: this.alternateItemIndex
        });
      } else {
        this.modalController.dismiss({ item: undefined, dismissed: false });
      }
    });
  }

  getPatientData() {
    this.loadLimit = 20;
    this.isShowLoadMore = false;
    const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    if (this.siteFilterShow && !this.isSingleSelection && isPresent(localSelectedSites) && this.selectedSiteIds === Constants.defaultSiteId) {
      this.selectedSiteIds = JSON.parse(localSelectedSites);
    } else if (!this.selectedSiteIds.length) {
      this.selectedSiteIds = Constants.defaultSiteId;
    }
    const payload = {
      siteIds: this.selectedSiteIds ? this.selectedSiteIds.toString() : '0',
      isTenantRoles: '',
      roleId: Constants.patientGroupId,
      status: Constants.notRejected,
      needVirtualPatients: true,
      pageCount: this.pageCount,
      searchKeyword: this.searchKey,
      nursingAgencies: this.sharedService.isEnableConfig(Config.enableNursingAgencies)
        ? this.userData?.nursing_agencies
        : ''
    };
    if (this.pageCount === Constants.defaultPageCount) {
      this.displayData = [];
    }
    this.sharedService.isLoading = true;
    this.sharedService.getAssociatedPatientsByTagId(payload).subscribe(
      (recipients) => {
        this.sharedService.isLoading = false;
        if (!isBlank(recipients)) {
          this.noData = false;
          this.isShowLoadMore = recipients?.length === this.loadLimit;
          const recipientsData = recipients.map((r) => ({
            ...r,
            displayText: `${this.common.getPatientDisplayName(
              r,
              Constants.patientValue,
              Constants.displayNamePatientCaregiverFormat
            )} ${r.passwordStatus === 'true' ? `(${Constants.enrolledUser})` : `(${Constants.showVirtualUser})`}`
          }));
          if (this.pageCount === Constants.defaultPageCount) {
            this.displayData = recipientsData;
          } else {
            this.displayData = [...this.displayData, ...recipientsData];
          }
        } else {
          this.noData = true;
          this.showCommonMessage();
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }

  // GraphQL api changes
  commonSignatureRequestTextsDisplaySearchFunc(action = this.constData.signatureRequestActionType.recipients) {
    // TODO Pagination support in commonSignatureRequestTextsDisplaySearchFunc
    const params: { signatureRequestRecipients: SignatureRequestRecipients } = {
      signatureRequestRecipients: {
        roles: this.selectedData?.signatureByRoles ? this.selectedData?.signatureByRoles?.toString() : '',
        searchValue: this.searchKey ? this.searchKey : '',
        siteId: this.selectedSiteIds?.toString()
      }
    };
    this.sharedService.isLoading = true;
    this.noData = false;
    if (this.fillingData.selectedAssociatePatientId) {
      params.signatureRequestRecipients.filterByPatientId = +this.fillingData.selectedAssociatePatientId;
    }
    this.graphqlService.signatureRequestTextsDisplaySearch(params, action)?.subscribe({
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      next: (response: any) => {
        this.displayData = [];
        this.sharedService.isLoading = false;
        const responseRecipients = response.data.signatureRequestTextsDisplaySearch;
        this.userService.transformUserList(responseRecipients, action, this.selectedData).then((displayData) => {
          this.displayData = displayData;
          if (this.displayData.length === 0) {
            this.noData = true;
            this.showCommonMessage();
          }
        });
      },
      error: () => {
        this.displayData = [];
        this.noData = true;
        this.sharedService.isLoading = false;
        this.showCommonMessage();
      }
    });
  }

  /**
   *
   * @param data data.do = search | reset | add, data.value = string
   */
  searchOperations(data: any): void {
    if (data.do !== 'add') {
      this.searchKey = data.value || '';
      this.pageCount = Constants.defaultPageCount;
      if (isBlank(this.selectedSiteIds) || this.selectedSiteIds?.length === 0) {
        this.sharedService.isLoading = false;
        this.common.showMessage(this.common.getTranslateData('LABELS.SELECT_SITES'));
      } else {
        if (data.do === 'reset') {
          this.displayData = [];
        }
        // TODO Pagination support in commonSignatureRequestTextsDisplaySearchFunc
        this.commonFunction();
      }
    } else {
      this.showAddPatient();
    }
  }

  async showAddPatient(): Promise<void> {
    const page = 'docCenter';
    const modal: any = await this.modalController.create({
      component: AddVirtualPatientComponent,
      componentProps: { page },
      id: 'add-patient'
    });
    await modal.present();
    await modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.virtualPatient = data.recipient;
        this.selectItem(data.recipient);
      }
    });
  }

  cancel(): void {
    this.modalController.dismiss({
      dismissed: false
    });
  }

  filterSitesData(data: []): void {
    this.selectedSiteIds = data;
    this.pageCount = Constants.defaultPageCount;
    this.commonFunction();
  }

  loadData() {
    setTimeout(() => {
      this.pageCount++;
      this.commonFunction();
    }, 1500);
  }

  commonFunction() {
    if (this.title === this.common.getTranslateData('TITLES.CHOOSE_ASSOCIATED_PATIENT')) {
      this.getPatientData();
    } else if (
      this.title === this.common.getTranslateData('TITLES.CHOOSE_RECIPIENTS') ||
      this.title === this.common.getTranslateData('TITLES.CHOOSE_DELEGATED_STAFF')
    ) {
      this.selectedSiteIds.length > 0 &&
        this.searchKey &&
        this.commonSignatureRequestTextsDisplaySearchFunc(
          this.title === this.common.getTranslateData('TITLES.CHOOSE_DELEGATED_STAFF')
            ? this.constData.signatureRequestActionType.onBehalf
            : this.constData.signatureRequestActionType.recipients
        );
      if (!this.searchKey) {
        this.sharedService.isLoading = false;
        this.noData = true;
        // TODO Pagination support in commonSignatureRequestTextsDisplaySearchFunc
        this.showCommonMessage();
      }
    } else {
      this.sharedService.isLoading = true;
      if (this.selectedFolderType === FolderType.documentFolder) {
        this.showExistingFileList([], 'documents', { siteIds: +this.selectedSiteIds[0], tagId: this.selectedData.tagGuid });
        return;
      }
      this.graphqlService.siteDetails(parseInt(this.selectedSiteIds[0]))?.subscribe(
        (response: SiteDetailsResponse) => {
          this.sharedService.isLoading = false;
          if (response && response.data.getSiteDetails) {
            this.fcmMapping(response.data.getSiteDetails);
          } else {
            this.noData = true;
            this.showCommonMessage();
          }
        },
        () => {
          this.sharedService.isLoading = false;
        }
      );
    }
  }

  /**
   *
   * @param item Call when Click on item list
   */
  alternateItemIndex = -1;
  selectItem(item: any, alternateIndex = -1) {
    this.alternateItemIndex = alternateIndex;
    this.fillingData.patient = item;
    if (this.title === this.common.getTranslateData('TITLES.CHOOSE_RECIPIENTS')) {
      this.fillingData.userType = 'Recipient';
    } else if (this.title === this.common.getTranslateData('TITLES.CHOOSE_ASSOCIATED_PATIENT')) {
      this.fillingData.userType = 'Associated Patient';
    } else if (this.title === this.common.getTranslateData('TITLES.CHOOSE_DELEGATED_STAFF')) {
      this.fillingData.userType = 'Delegated Staff';
    }
    this.fillingData.selectedRecipientRole = item.role;
    this.fillingData.isRecipientCaregiver = item.careGiver ? true : false;
    if (
      (this.sharedService.isEnableConfig(Config.enableMultiSite) &&
        this.selectedData?.allowRecipientRoles &&
        this.selectedData?.allowAssociatePatient) ||
      (this.sharedService.isEnableConfig(Config.enableMultiSite) &&
        this.selectedData?.allowAssociateRoles &&
        this.selectedData?.allowAssociateRoles)
    ) {
      this.fillingData.selectedPatientSiteForRecipient = item.site_id
        ? item.site_id
        : item.siteId
          ? item.siteId
          : this.fillingData.selectedPatientSiteForRecipient
            ? this.fillingData.selectedPatientSiteForRecipient
            : '0';
      this.fillingData.disableSiteFilter =
        this.fillingData.selectedPatientSiteForRecipient && this.fillingData.selectedPatientSiteForRecipient != '0'
          ? true
          : false;
    }

    if (this.sharedService.isEnableConfig(Config.enableMultiSite)) {
      this.fillingData.virtualPatientSiteId = '0';
      this.fillingData.selectedPatientSiteId = item.site_id ? item.site_id : item.siteId;
      this.fillingData.selectedAssociatePatientId = item.userId || item.userid;
      let isRolePatient = false;
      if (!isBlank(item.role)) {
        const role = Number.isNaN(Number(item.role)) ? item.role.toUpperCase() : Number(item.role);
        isRolePatient = role === UserGroup.PATIENT || role === Constants.roleName.patient.toUpperCase();
      }
      if (!this.sharedService.isMultiAdmissionsEnabled || !isRolePatient) {
        this.getFileDetailsBySite(item);
      } else {
        let siteIds = this.selectedSiteIds;
        if (siteIds === Constants.defaultSiteId) {
          siteIds = this.sharedService.userData.mySites.map((site) => site.id);
        }
        const params = {
          userId: this.fillingData.patient?.userId || this.fillingData.patient?.userid || '',
          siteIds: isPresent(siteIds) && siteIds.length === 1 ? siteIds : undefined
        };
        this.sharedService.selectAdmission(params, undefined, AdmissionComponent, (admission: Admission) => {
          if (isObject(admission)) {
            this.fillingData.admission = admission;
            this.getFileDetailsBySite(item);
          }
        });
      }
    }
  }
  get isDefaultDataShowBehaviour(): boolean {
    return (
      this.title === this.common.getTranslateData('TITLES.CHOOSE_RECIPIENTS') &&
      (!this.siteFilter ||
        (this.siteFilter &&
          this.isSingleSelection &&
          this.selectedSiteIds?.length > 0 &&
          this.selectedSiteIds !== Constants.defaultSiteId))
    );
  }
  showCommonMessage(): void {
    this.noResult = this.userService.showCommonMessage(this.addPatient, this.searchKey);
    this.createPatientLinkMessage = this.addPatient ? this.common.getTranslateData('MESSAGES.TO_CREATE_NEW_PATIENT') : '';
  }
}
