<app-header-plain [headerTitle]="title" (close)="cancel()"></app-header-plain>
<div *ngIf="siteFilterShow">
    <app-sites (filterSites)="filterSitesData($event)"
        [mandatory]="title === ('TITLES.CHOOSE_ASSOCIATED_PATIENT' | translate) ? false : true"
        [singleSiteSelection]="isSingleSelection" [siteLabel]="siteLabel"></app-sites>
</div>
<app-search-bar-recipients (searchAction)="searchOperations($event)"
    *ngIf="hideRecipientSearchSection && (isDefaultDataShowBehaviour || !isRecipient)" [addPatient]="addPatient">
</app-search-bar-recipients>

<ion-content>
    <ion-list class="recipient-list">
        <div *ngIf="noData" class="common-no-items">
            <ion-label class="ion-text-center">{{ noResult }}</ion-label>
            <span *ngIf="createPatientLinkMessage !== ''" (click)="showAddPatient()">
                <u class="show-cursor-pointer"><b>{{'LABELS.CLICK_HERE'| translate}}</b></u>
                {{createPatientLinkMessage}}
            </span>
        </div>
        <ng-container *ngIf="isDefaultDataShowBehaviour">
            <ng-container *ngFor="let item of displayData; let i = index">
                <ion-item (click)="!item?.patientClick && selectItem(item)" [id]="'item-'+i" [ngClass]="!item?.patientClick ? 'set-mouse-hover' : ''"
                    lines="none">
                    <ion-label class="ion-text-wrap">{{
                        item.careGiver && item.careGiver.displayName ? item.careGiver.displayName + ' (' +
                        item.displayName
                        +
                        ')' :
                        item?.displayName}}
                        <span *ngIf="item?.dateOfBirth"> - {{item?.dateOfBirth |
                            date:constData.dateFormat.mmddyyyy}}</span>
                        <span *ngIf="item?.patientIdentity"> [{{'GENERAL.MRN' | translate}} :
                            {{item?.patientIdentity?.IdentityValue}}]</span>
                        <span> ({{item?.isVirtual ? ('GENERAL.VIRTUAL' | translate) : ('GENERAL.ENROLLED' |
                            translate)}})</span>
                    </ion-label>
                    <ion-checkbox class="common-checkbox" slot="end" mode='ios' id="select-recipient-{{i}}"
                        *ngIf="!item?.patientClick">
                    </ion-checkbox>
                </ion-item>

                <ion-item *ngFor="let alternate of item?.alternateContacts; let idAlt=index" class="recipient-child"
                    (click)="selectItem(item, idAlt)" id="recipient-child-{{idAlt}}">
                    <ion-label class="recipient-label-wrap">
                        <span>{{alternate?.patientDisplayName}}</span>
                        <span>({{alternate?.displayName}}
                            <span *ngIf="alternate?.relation"> - {{alternate?.relation}}</span>)</span>
                        <span *ngIf="alternate?.patientDob"> - {{alternate?.patientDob|
                            date:constData?.dateFormat?.mmddyyyy}}&nbsp;</span>
                        <span *ngIf="alternate?.patientMrn">[{{'GENERAL.MRN' | translate}}:
                            {{alternate?.patientMrn}}]&nbsp;</span>
                        <span *ngIf="alternate?.password as password">
                            @if (password === '1') {
                                ({{ 'GENERAL.ENROLLED' | translate }})
                            } @else if (password === '0' && !alternate.tag_type_id) {
                                ({{ 'GENERAL.VIRTUAL' | translate }})
                            }
                        </span>
                    </ion-label>
                    <ion-checkbox class="common-checkbox" slot="end" mode='ios'
                        id="select-recipient-alternate-{{idAlt}}">
                    </ion-checkbox>
                </ion-item>
            </ng-container>
        </ng-container>
        <ng-container *ngIf="!isRecipient">
            <ion-item *ngFor="let item of displayData; let i = index" (click)="selectItem(item)" class="set-mouse-hover"
                lines="none">
                <ion-label class="ion-text-wrap">{{item?.displayText}}
                    <span *ngIf="item?.sitename">{{item?.sitename }}</span>
                </ion-label>
                <ion-checkbox class="common-checkbox" slot="end" mode='ios' id="select-associated-recipient-{{i}}">
                </ion-checkbox>
            </ion-item>
        </ng-container>
        <ion-row class="ion-padding-top"
            *ngIf="!noData && isShowLoadMore && (isDefaultDataShowBehaviour|| !isRecipient)">
            <ion-col size="12" class="ion-text-center">
                <ion-badge color="blumine" mode="md" (click)="loadData()">{{'BUTTONS.LOAD_MORE' | translate}}
                </ion-badge>
            </ion-col>
        </ion-row>
    </ion-list>
</ion-content>