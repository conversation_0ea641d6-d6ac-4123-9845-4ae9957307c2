import { Component, OnInit } from '@angular/core';
import { NavParams, PopoverController, ModalController } from '@ionic/angular';
import { CheckboxGroupComponent } from '../checkbox-group/checkbox-group.component';
import { isBlank } from 'src/app/utils/utils';

@Component({
  selector: 'app-signature-more-palettes',
  templateUrl: './signature-more-palettes.component.html',
  styleUrls: ['./signature-more-palettes.component.scss']
})
export class SignatureMorePalettesComponent {
  signatureTypeTagData: any;
  addObjects: any;
  checkboxGroupDetails: any;
  checkFieldDetails: any;
  checkboxGroupObject: any;
  documentSignersDetails: any;
  paletteSize: any;
  documentFileSizeInfo: any;
  data: any;
  currentDocPage = 1;
  constructor(
    private readonly navParams: NavParams,
    private readonly popoverController: PopoverController,
    private readonly modalController: ModalController
  ) {
    this.signatureTypeTagData = this.navParams.get('signatureTypeTagData');
    this.addObjects = this.navParams.get('addObjects');
    this.checkFieldDetails = this.navParams.get('checkFieldDetails');
    this.checkboxGroupObject = this.navParams.get('checkboxGroupObject');
    this.documentSignersDetails = this.navParams.get('documentSignersDetails');
    this.paletteSize = this.navParams.get('paletteSize');
    this.documentFileSizeInfo = this.navParams.get('documentFileSizeInfo');
    this.data = this.navParams.get('userDetails');
    this.currentDocPage = this.navParams.get('pageCount');
  }

  async addCheckboxGroup(checkboxLabel: any): Promise<any> {
    this.checkboxGroupObject.checkboxLabel = checkboxLabel;
    if (
      !isBlank(this.checkFieldDetails) &&
      !this.checkboxGroupObject.isNewCheckboxGroup &&
      this.checkboxGroupObject.showCheckboxGroup
    ) {
      this.checkboxGroupObject.showCheckboxGroup = true;
    } else if (!isBlank(this.checkFieldDetails) && !this.checkboxGroupObject.isNewCheckboxGroup) {
      this.checkboxGroupObject.showCheckboxGroup = false;
    } else if (
      !isBlank(this.checkFieldDetails) &&
      this.checkboxGroupObject.isNewCheckboxGroup &&
      !this.checkboxGroupObject.addToExistingGroup
    ) {
      this.checkboxGroupObject.showCheckboxGroup = false;
    }
    const modal = await this.modalController.create({
      component: CheckboxGroupComponent,
      componentProps: {
        signatureTypeTagData: this.signatureTypeTagData,
        documentFileSizeInfo: this.documentFileSizeInfo,
        documentSignersDetails: this.documentSignersDetails,
        paletteSize: this.paletteSize,
        userDetails: this.data,
        addObjects: this.addObjects,
        checkFieldDetails: this.checkFieldDetails,
        checkboxGroupObject: this.checkboxGroupObject,
        pageCount: this.currentDocPage
      }
    });
    modal.onDidDismiss().then(({ data }) => {
      if (!isBlank(data)) {
        this.checkboxGroupDetails = data.checkboxGroupDetails;
        this.checkboxGroupObject = data.checkboxGroupObject;
      }
      this.popoverController.dismiss({
        checkboxGroupDetails: this.checkboxGroupDetails,
        checkboxGroupObject: this.checkboxGroupObject
      });
    });
    return await modal.present();
  }
  createPaletteButton(field: any, checkboxCondition: boolean = true, groupObj: any): void {
    if (!isBlank(this.checkboxGroupObject) && !this.checkboxGroupObject.showCheckboxGroup) {
      this.checkboxGroupObject.showCheckboxGroup = false;
      this.checkboxGroupObject.addToExistingGroup = true;
      this.addCheckboxGroup(checkboxCondition);
    } else {
      this.checkboxGroupObject.showCheckboxGroup = true;
      this.checkboxGroupObject.addToExistingGroup = false;
      this.popoverController.dismiss({
        field,
        checkboxCondition,
        groupObj,
        checkboxGroupObject: this.checkboxGroupObject
      });
    }
  }
}
