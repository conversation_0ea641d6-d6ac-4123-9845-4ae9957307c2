import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams, PopoverController } from '@ionic/angular';

import { SignatureMorePalettesComponent } from './signature-more-palettes.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';

describe('SignatureMorePalettesComponent', () => {
  let component: SignatureMorePalettesComponent;
  let fixture: ComponentFixture<SignatureMorePalettesComponent>;
  const modalSpy = TestConstants.modalSpy;
  let modalController: ModalController;
  const popupSpy = TestConstants.popoverSpy;
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SignatureMorePalettesComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot()],
      providers: [NavParams],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: { imageData: 'fakeData' } });
    popupSpy.present.and.stub();

    popupSpy.onWillDismiss.and.resolveTo({ data: { subject: '', file: [] } });
    fixture = TestBed.createComponent(SignatureMorePalettesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  describe('createPaletteButton', () => {
    it('should set checkboxGroupObject properties and call addCheckboxGroup when checkboxGroupObject is not null and showCheckboxGroup is false', () => {
      component.checkboxGroupObject = {
        showCheckboxGroup: false,
        addToExistingGroup: false
      };
      spyOn(component, 'addCheckboxGroup');
      component.createPaletteButton('field', true, 'groupObj');
      expect(component.checkboxGroupObject.showCheckboxGroup).toBe(false);
      expect(component.checkboxGroupObject.addToExistingGroup).toBe(true);
      expect(component.addCheckboxGroup).toHaveBeenCalledWith(true);
    });

    it('should set checkboxGroupObject properties and dismiss popoverController when checkboxGroupObject is null or showCheckboxGroup is true', () => {
      component.checkboxGroupObject = null;
      component.checkboxGroupObject = {
        showCheckboxGroup: true,
        addToExistingGroup: false
      };
      fixture.detectChanges();
      component.createPaletteButton('field', true, 'groupObj');
      expect(component.checkboxGroupObject.showCheckboxGroup).toBe(true);
      expect(component.checkboxGroupObject.addToExistingGroup).toBe(false);
    });
  });
  describe('addCheckboxGroup', () => {
    it('should set checkboxGroupObject properties and open modal when checkFieldDetails is not blank and isNewCheckboxGroup is true', async () => {
      component.checkFieldDetails = 'checkFieldDetails';
      component.checkboxGroupObject = {
        isNewCheckboxGroup: true,
        addToExistingGroup: false,
        showCheckboxGroup: false,
        checkboxLabel: ''
      };
      fixture.detectChanges();

      await component.addCheckboxGroup('checkboxLabel');
      expect(component.addCheckboxGroup).toBeDefined();
    });

    it('should set checkboxGroupObject properties and open modal when checkFieldDetails is not blank, isNewCheckboxGroup is false, and showCheckboxGroup is true', async () => {
      component.checkFieldDetails = 'checkFieldDetails';
      component.checkboxGroupObject = {
        isNewCheckboxGroup: false,
        addToExistingGroup: false,
        showCheckboxGroup: true,
        checkboxLabel: ''
      };
      fixture.detectChanges();

      await component.addCheckboxGroup('checkboxLabel');
      expect(component.addCheckboxGroup).toBeDefined();
    });

    it('should set checkboxGroupObject properties and open modal when checkFieldDetails is not blank, isNewCheckboxGroup is false, and showCheckboxGroup is false', async () => {
      component.checkFieldDetails = 'checkFieldDetails';
      component.checkboxGroupObject = {
        isNewCheckboxGroup: false,
        addToExistingGroup: false,
        showCheckboxGroup: false,
        checkboxLabel: ''
      };
      fixture.detectChanges();

      await component.addCheckboxGroup('checkboxLabel');
      expect(component.addCheckboxGroup).toBeDefined();
    });
  });
});
