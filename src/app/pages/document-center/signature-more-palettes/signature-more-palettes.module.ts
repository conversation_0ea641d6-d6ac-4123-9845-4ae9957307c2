import { NgModule } from "@angular/core";
import { SignatureMorePalettesComponent } from "./signature-more-palettes.component";
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { CheckboxGroupComponentModule } from '../checkbox-group/checkbox-group.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        TranslateModule,
        CheckboxGroupComponentModule
    ],
    declarations: [
        SignatureMorePalettesComponent
    ],
    exports: [SignatureMorePalettesComponent]
})
export class SignatureMorePalettesModule { }