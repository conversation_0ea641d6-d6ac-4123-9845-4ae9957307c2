<div class="popover-backdrop">
  <div class="palette palette-left text-button" id="checkbox-group" (click)="addCheckboxGroup(true)"
    *ngIf="signatureTypeTagData?.checkBoxGroupAllowed">
    <span class="palette-button-span">
      <label class="palette-button-label">{{ 'LABELS.CHECKBOX_GROUP' | translate }}</label></span>
  </div>
  <div class="palette palette-left text-button" id="checkbox-with-label"
    (click)="createPaletteButton('checkbox',true,'')">
    <span class="palette-button-span">
      <label class="palette-button-label">{{ 'LABELS.ADD_CHECKBOX_WITH_LABEL' | translate }}</label></span>
  </div>
  <div class="palette palette-left text-button" id="checkbox-without-label"
    (click)="createPaletteButton('checkbox',false,'')">
    <span class="palette-button-span">
      <label class="palette-button-label">{{ 'LABELS.ADD_CHECKBOX_WITHOUT_LABEL' | translate }}</label></span>
  </div>
</div>