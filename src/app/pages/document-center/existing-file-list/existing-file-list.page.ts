import { TenantFilingCenterContent } from 'src/app/interfaces/document-request-signature';
import { SyncFolderType } from 'src/app/interfaces/messages';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { NavParams, ModalController } from '@ionic/angular';
import { Component } from '@angular/core';
import { Constants, FolderType } from 'src/app/constants/constants';
import { isBlank } from 'src/app/utils/utils';
import { Activity } from 'src/app/constants/activity';

@Component({
  selector: 'app-existing-file-list',
  templateUrl: './existing-file-list.page.html',
  styleUrls: ['./existing-file-list.page.scss']
})
export class ExistingFileListPage {
  title = '';
  existingData: any = [];
  fcmMappingData: any = {};
  searchText: string = '';
  tempStoreData: any = [];
  previousQuery: string;
  selectedFolderType: FolderType;
  payload;
  isLoading = false;
  constants = Constants;
  folderType = FolderType;
  constructor(
    private readonly modalController: ModalController,
    private readonly navParams: NavParams,
    private readonly graphqlService: GraphqlService,
    private readonly common: CommonService,
    public readonly sharedService: SharedService
  ) {
    this.sharedService.isLoading = false;
    this.fcmMappingData = this.navParams.get('fcmMappingData');
    this.previousQuery = this.navParams.get('graphQLQuery');
    this.selectedFolderType = this.navParams.get('selectedFolderType');
    this.payload = this.navParams.get('payload');
    this.init(navParams.get('existingFileData'));
  }
  init(existingData) {
    if (this.selectedFolderType === FolderType.fillingCenter) {
      this.title = this.fcmMappingData?.folder || this.common.getTranslateData('TITLES.CHOOSE_FILLING_CENTER');
      this.fillExtraField(existingData);
    } else if (this.selectedFolderType === FolderType.documentFolder) {
      this.title = this.common.getTranslateData('TITLES.SELECT_DOCUMENT');
    }
    this.searchOperations({ value: '' });
  }

  cancel(item?: any) {
    if (item) {
      // track activity
      this.sharedService.trackActivity({
        type: Activity.selectDocument,
        name: Activity.selectDocument,
        des: {
          desConstant: Activity.selectDocumentDes,
          data: {
            displayName: this.sharedService.userData?.displayName,
            documentName: item?.name,
            documentId: item[this.identifier],
            fromName: this.selectedFolderType
          }
        }
      });
    }
    this.modalController.dismiss({
      item,
      dismissed: !!item
    });
  }
  get identifier() {
    return this.selectedFolderType === FolderType.documentFolder ? 'fileId' : 'folder';
  }

  searchOperations(data: any): void {
    this.searchText = data.value;
    this.existingData = [];
    if (this.previousQuery === 'getTenantFilingCenterContent') {
      this.getTenantFilingCenterContent();
    } else if (this.previousQuery === 'documents') {
      this.getDocumentsFromDocumentFolder(this.payload?.siteIds, this.payload?.tagId, this.searchText);
    } else {
      this.siteTenantFilingCenterContentFunc();
    }
  }
  getDocumentsFromDocumentFolder(siteIds, tagId, searchText = '', page = 1) {
    const passParams = {
      limit: Constants.messageListLoadLimit,
      page,
      searchText,
      sessionId: this.sharedService.userData?.authenticationToken,
      siteIds,
      tagId
    };
    this.isLoading = true;
    if (page === 1) {
      // TODO Currently full data is returned from API when called with tagId even if limit is passed
      this.existingData = [];
    }
    this.graphqlService.getFileList(passParams)?.subscribe({
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      next: (response: any) => {
        if (response?.data?.documents) {
          const updatedFileData = response.data.documents.data.map((item) => {
            const updatedItem = {
              name: item?.documentName,
              folder: null,
              size: +item.fileSize,
              type: item.fileType,
              addedOn: item.createdOn,
              fileId: item.id
            };
            return updatedItem;
          });
          this.existingData = this.existingData.concat(updatedFileData);
          this.fillExtraField(updatedFileData);
        }
      },
      error: () => {
        this.fillExtraField([]);
      }
    });
  }

  getTenantFilingCenterContent() {
    this.isLoading = true;
    this.graphqlService.folderFilingCenterContent(this.title, this.searchText)?.valueChanges.subscribe(
      (response: any) => {
        this.isLoading = false;
        if (response && response.data.getTenantFilingCenterContent) {
          this.fillExtraField(response.data.getTenantFilingCenterContent);
        }
      },
      () => {
        this.isLoading = false;
      }
    );
  }

  siteTenantFilingCenterContentFunc() {
    if (isBlank(this.fcmMappingData?.folder)) {
      this.existingData = [];
      this.isLoading = false;
      return;
    }
    const passParams = {
      sessionId: this.sharedService.userData?.authenticationToken,
      type: SyncFolderType.INCOMING,
      folder: this.fcmMappingData?.folder,
      guid: this.fcmMappingData?.siteRegistrationId,
      searchKey: this.searchText,
      fromCrud: false
    };
    this.isLoading = true;
    this.graphqlService.siteTenantFilingCenterContent(passParams)?.subscribe({
      next: (response: TenantFilingCenterContent) => {
        this.isLoading = false;
        if (response?.data?.getSiteTenantFilingCenterContent) {
          this.fillExtraField(response.data.getSiteTenantFilingCenterContent);
        }
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  /**
   * Added 'fileType' in response list
   * @param response siteTenantFilingCenterContent data
   * @returns
   */
  fillExtraField(response: any = []) {
    this.existingData = [];
    if (response?.length === 0) {
      this.isLoading = false;
      return;
    }

    for (const iterator of response) {
      const data = {
        ...iterator,
        fileType: this.common.getFileType(iterator.type)
      };
      this.existingData.push(data);
    }
  }
}
