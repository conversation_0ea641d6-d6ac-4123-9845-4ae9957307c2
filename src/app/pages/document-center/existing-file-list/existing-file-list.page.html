<app-header-plain [headerTitle]="title" (close)="cancel()" class="filing-center-header"></app-header-plain>
<app-search-bar-recipients (searchAction)="searchOperations($event)"></app-search-bar-recipients>
<ion-content>
  @for (item of existingData; track identifier; let i = $index) {
    <ion-item lines="full" (click)="cancel(item)" [id]="'item-' + i">
      <ion-thumbnail slot="start">
        <img src="assets/images/doc-types/{{ item?.fileType }}.png" alt="file" />
      </ion-thumbnail>
      <ion-label class="ion-text-wrap"
        >{{ item?.name }}
        @if (selectedFolderType !== folderType.documentFolder) {
          <p>{{ item?.size | fileSize }}</p>
        }
      </ion-label>
    </ion-item>
  }
  @if (existingData.length === 0) {
    @if (isLoading) {
      <app-skeleton-loader [count]="5" [skeletonWidths]="[80]" [avatar]="true"></app-skeleton-loader>
    } @else {
      <ion-row class="ion-margin-top">
        <ion-col size="12" class="ion-text-center">
          {{ 'MESSAGES.NO_ITEM_FOUND' | translate }}
        </ion-col>
      </ion-row>
    }
  }
</ion-content>
