/* eslint-disable @typescript-eslint/no-explicit-any */
import { Apollo } from 'apollo-angular';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavParams, ModalController } from '@ionic/angular';
import { TestConstants } from 'src/app/constants/test-constants';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { of, throwError } from 'rxjs';
import { Constants, FolderType } from 'src/app/constants/constants';
import { ExistingFileListPage } from './existing-file-list.page';

describe('ExistingFileListPage', () => {
  let component: ExistingFileListPage;
  let fixture: ComponentFixture<ExistingFileListPage>;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  let graphQlService: GraphqlService;
  const existingData = [
    {
      name: '100720R ,SreedeviABN-converted-converted.pdf',
      folder: 'CHA_CIQARBQA4/Atlanta/CPR+ to Citus Health/Advanced Beneficiary Notice (ABN)/100720R ,SreedeviABN-converted-converted.pdf',
      size: 19527,
      type: 'application/pdf',
      preview: '/mnt/dav/CHA_CIQARBQA4/Atlanta/CPR+ to Citus Health/Advanced Beneficiary Notice (ABN)/100720R ,SreedeviABN-converted-converted.pdf',
      addedOn: **********.558,
      fileId: null,
      __typename: 'SyncContent'
    }
  ];
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ExistingFileListPage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        Idle,
        IdleExpiry,
        Keepalive,
        NavParams,
        ModalController,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    graphQlService = TestBed.inject(GraphqlService);
    fixture = TestBed.createComponent(ExistingFileListPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call cancel', () => {
    component.cancel();
    fixture.detectChanges();
    expect(component.cancel).toBeDefined();
  });
  describe('searchOperations', () => {
    it('should call searchOperations', () => {
      const operationData = { do: 'reset', value: '' };
      component.searchOperations(operationData);
      expect(component.searchOperations).toBeDefined();
    });
    it('should call searchOperations with previousQuery=getTenantFilingCenterContent', () => {
      const operationData = { do: 'reset', value: '' };
      component.previousQuery = 'getTenantFilingCenterContent';
      spyOn(component, 'getTenantFilingCenterContent').and.stub();
      component.searchOperations(operationData);
      expect(component.searchOperations).toBeDefined();
      expect(component.getTenantFilingCenterContent).toHaveBeenCalled();
    });
    it('should call searchOperations with previousQuery=documents', () => {
      const operationData = { do: 'reset', value: '' };
      component.previousQuery = 'documents';
      spyOn(component, 'getDocumentsFromDocumentFolder').and.stub();
      component.searchOperations(operationData);
      expect(component.searchOperations).toBeDefined();
      expect(component.getDocumentsFromDocumentFolder).toHaveBeenCalled();
    });
  });

  it('should call fillExtraField', () => {
    component.fillExtraField(existingData);
    expect(component.fillExtraField).toBeDefined();
  });

  it('should call getTenantFilingCenterContent', () => {
    component.getTenantFilingCenterContent();
    expect(component.getTenantFilingCenterContent).toBeDefined();
  });

  describe('getDocumentsFromDocumentFolder', () => {
    it('should call GraphQL service and update data on successful response', () => {
      const response = {
        data: { documents: { data: [{ documentName: 'A', id: '24323', fileSize: 2131, fileType: 'pdf', createdOn: 23432423 }], totalCount: 1 } }
      };
      spyOn(graphQlService, 'getFileList').and.returnValue(of(response) as any);
      component.getDocumentsFromDocumentFolder(1, '52342');
      expect(graphQlService.getFileList).toHaveBeenCalledWith(
        jasmine.objectContaining({
          limit: Constants.messageListLoadLimit,
          page: 1,
          searchText: '',
          siteIds: 1,
          tagId: '52342'
        })
      );
    });
    it('should call GraphQL service and update data on successful response', () => {
      spyOn(graphQlService, 'getFileList').and.returnValue(throwError(() => null));
      component.getDocumentsFromDocumentFolder(1, '52342');
      expect(graphQlService.getFileList).toHaveBeenCalledWith(
        jasmine.objectContaining({
          limit: Constants.messageListLoadLimit,
          page: 1,
          searchText: '',
          siteIds: 1,
          tagId: '52342'
        })
      );
    });
  });
  describe('getIdentifier', () => {
    it('identifier for document folder to be fileId', () => {
      component.selectedFolderType = FolderType.documentFolder;
      expect(component.identifier).toEqual('fileId');
    });
    it('identifier for filing center to be folder', () => {
      component.selectedFolderType = FolderType.fillingCenter;
      expect(component.identifier).toEqual('folder');
    });
  });
  describe('getTenantFilingCenterContent', () => {
    it('should call getTenantFilingCenterContent', () => {
      const response = {
        data: {
          getTenantFilingCenterContent: []
        }
      };
      spyOn(graphQlService, 'folderFilingCenterContent').and.returnValue({ valueChanges: of(response) });
      component.getTenantFilingCenterContent();
      expect(component.getTenantFilingCenterContent).toBeDefined();
    });

    it('execute getTenantFilingCenterContent : throw error', () => {
      spyOn(graphQlService, 'folderFilingCenterContent').and.returnValue({ valueChanges: throwError('') });
      component.getTenantFilingCenterContent();
      expect(component.getTenantFilingCenterContent).toBeDefined();
    });
  });

  describe('siteTenantFilingCenterContentFunc', () => {
    it('should call GraphQL service and update data on successful response', () => {
      component.fcmMappingData = { folder: 'folder' };
      spyOn(graphQlService, 'siteTenantFilingCenterContent').and.returnValue(of({ data: { getSiteTenantFilingCenterContent: [] } }) as any);
      component.siteTenantFilingCenterContentFunc();
      expect(graphQlService.siteTenantFilingCenterContent).toHaveBeenCalled();
      expect(component.existingData).toEqual([]);
    });
    it('execute siteTenantFilingCenterContentFunc : throw error', () => {
      component.fcmMappingData = { folder: 'folder' };
      spyOn(graphQlService, 'siteTenantFilingCenterContent').and.returnValue(throwError(() => null));
      component.siteTenantFilingCenterContentFunc();
      expect(component.siteTenantFilingCenterContentFunc).toBeDefined();
    });
  });
  describe('init', () => {
    it('should call init: filing center', () => {
      component.selectedFolderType = FolderType.fillingCenter;
      component.fcmMappingData = { folder: 'folder' };
      spyOn(component, 'searchOperations').and.stub();
      component.init([]);
      expect(component.init).toBeDefined();
      expect(component.searchOperations).toHaveBeenCalled();
      expect(component.title).toEqual('folder');
    });
    it('should call init: filing center: show title if folder not exists', () => {
      component.selectedFolderType = FolderType.fillingCenter;
      spyOn(component, 'searchOperations').and.stub();
      component.init([]);
      expect(component.init).toBeDefined();
      expect(component.searchOperations).toHaveBeenCalled();
      expect(component.title).toEqual('TITLES.CHOOSE_FILLING_CENTER');
    });
    it('should call init: document folder', () => {
      component.selectedFolderType = FolderType.documentFolder;
      spyOn(component, 'searchOperations').and.stub();
      component.init([]);
      expect(component.init).toBeDefined();
      expect(component.searchOperations).toHaveBeenCalled();
    });
  });
});
