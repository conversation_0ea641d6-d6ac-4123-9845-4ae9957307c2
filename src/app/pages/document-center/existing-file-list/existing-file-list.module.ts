import { SharedModule } from './../../../shared.module';
import { SearchBarRecipientsModule } from './../../../components/search-bar-recipients/search-bar-recipients.module';
import { HeaderPlainModule } from './../../../components/header-plain/header-plain.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ExistingFileListPageRoutingModule } from './existing-file-list-routing.module';

import { ExistingFileListPage } from './existing-file-list.page';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ExistingFileListPageRoutingModule,
    HeaderPlainModule,
    SearchBarRecipientsModule,
    SharedModule
  ],
  declarations: [ExistingFileListPage]
})
export class ExistingFileListPageModule {}
