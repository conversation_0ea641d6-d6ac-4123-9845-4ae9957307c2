import { Component, OnInit } from '@angular/core';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { NavParams, ModalController } from '@ionic/angular';
import { UntypedFormBuilder, UntypedFormGroup, UntypedFormArray, UntypedFormControl } from '@angular/forms';
import { isBlank, isPresent } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Config } from 'src/app/constants/config';
import { Activity } from 'src/app/constants/activity';
import { AddVirtualPatientComponent } from 'src/app/pages/user/add-virtual-patient/add-virtual-patient.component';
import { getValueFromLocalStorage } from 'src/app/utils/storage-utils';
import { UserService } from 'src/app/services/user-service/user.service';

@Component({
  selector: 'app-document-center-recipients',
  templateUrl: './document-center-recipients.component.html',
  styleUrls: ['./document-center-recipients.component.scss']
})
export class DocumentCenterRecipientsComponent implements OnInit {
  recipients: any = [];
  selectRecipientForm: UntypedFormGroup;
  docType: any;
  chooseRecipients: any;
  searchKeyword: any = '';
  showLoadMore: boolean;
  pageCount = Constants.defaultPageCount;
  userData: any;
  mrn: string;
  patientGroupId: number;
  isBlank: any;
  virtualUser = this.common.getTranslateData('GENERAL.VIRTUAL');
  headerTitle = '';
  errorMessage = '';
  recipientType: string;
  addPatient = false;
  virtualPatient: any;
  constants: any = {};
  associateUsers: any;
  selectedSiteIds: any = [];
  siteSingleSelection: boolean = false;
  isDisabledSite: boolean = false;
  isEnableMultiSite: boolean = false;
  createPatientLinkMessage = '';
  isRecipient = false;
  siteFilterShow = true;
  siteFilterLabel = 'LABELS.SITES';
  selectedAssociatePatient;
  constructor(
    private readonly graphqlService: GraphqlService,
    private readonly navParams: NavParams,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly modalController: ModalController,
    private readonly sharedService: SharedService,
    private readonly userService: UserService,
    private readonly common: CommonService
  ) {
    this.isBlank = isBlank;
    this.docType = this.navParams.get('documentType');
    this.recipientType = this.navParams.get('recipientType');
    this.selectedAssociatePatient = this.navParams.get('selectedAssociatePatient');
    this.userData = this.sharedService.userData;
    this.mrn = this.common.getTranslateData('GENERAL.MRN');
    if (this.recipientType === Constants.associate) {
      this.siteSingleSelection = this.isDisabledSite = true;
      const associateSiteSelected = this.navParams.get('sites');
      this.selectedSiteIds = associateSiteSelected || [+this.userData?.tenantId];
    }
    this.selectRecipientForm = this.formBuilder.group({
      recipientList: new UntypedFormArray([])
    });
    this.isEnableMultiSite = this.sharedService.isEnableConfig(Config.enableMultiSite);
  }
  ngOnInit(): void {
    this.constants = Constants;
    if (this.recipientType === Constants.patientValue) {
      this.siteFilterLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITES');
      this.addPatient = true;
      this.headerTitle = 'TITLES.CHOOSE_ASSOCIATED_PATIENT';
      const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
      if (isPresent(localSelectedSites)) {
        this.selectedSiteIds = JSON.parse(localSelectedSites);
      }

      this.getAssociatedPatients();
    } else if (this.recipientType === Constants.associate) {
      this.headerTitle = 'TITLES.CHOOSE_DELEGATED_STAFF';

      this.loadList();
      this.siteFilterLabel = 'LABELS.SITES';
    } else {
      this.isRecipient = true;
      this.headerTitle = 'TITLES.CHOOSE_RECIPIENTS';
      this.siteSingleSelection = true;
      if (this.docType.allowAssociatePatient && this.sharedService.isEnableConfig(Config.enableMultiSite)) {
        this.siteSingleSelection = true;
        this.isDisabledSite = true;
        const associateSiteSelected = this.navParams.get('sites');
        this.selectedSiteIds = [+associateSiteSelected];
      }
      if (!this.sharedService.isEnableConfig(Config.enableMultiSite)) {
        this.loadList();
      }
      if (this.userData?.mySites && this.userData?.mySites?.length === 1) {
        this.siteFilterShow = false;
        this.selectedSiteIds = [this.userData?.mySites[0].id];
        this.loadList();
      }
    }
    this.patientGroupId = Constants.patientGroupId;
  }

  getAssociatedPatients(): void {
    this.showLoadMore = false;
    const payload = {
      siteIds: this.selectedSiteIds ? this.selectedSiteIds.toString() : '0',
      isTenantRoles: '',
      roleId: Constants.patientGroupId,
      status: Constants.notRejected,
      needVirtualPatients: true,
      pageCount: this.pageCount,
      searchKeyword: this.searchKeyword,
      nursingAgencies: this.sharedService.isEnableConfig(Config.enableNursingAgencies) ? this.userData?.nursing_agencies : ''
    };
    if (isBlank(payload.siteIds)) {
      payload.siteIds = Constants.defaultSiteId;
    }
    this.sharedService.getAssociatedPatientsByTagId(payload).subscribe(
      (recipients) => {
        if (this.pageCount === Constants.defaultPageCount) {
          this.recipients = [];
        }
        if (!isBlank(recipients)) {
          this.showLoadMore = recipients.length >= Constants.defaultLimit;
          const recipientsData = recipients.map((r) => ({
            ...r,
            displayText: `${this.common.getPatientDisplayName(
              r,
              Constants.patientValue,
              Constants.displayNamePatientCaregiverFormat
            )} ${r.passwordStatus === 'true' ? `(${Constants.enrolledUser})` : `(${Constants.showVirtualUser})`}`
          }));
          this.recipients = this.recipients.concat(recipientsData);
          this.associateUsers = this.recipients;
          this.createForm(this.recipients);
        } else {
          this.showCommonMessage();
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
        this.showCommonMessage();
      }
    );
  }

  loadList(): void {
    if (this.recipientType === Constants.patientValue) {
      this.getAssociatedPatients();
    } else if (this.recipientType === Constants.associate) {
      const params = {
        roles: this.docType?.associateUserSignatureByRoles
          ? this.docType?.associateUserSignatureByRoles
          : this.docType.signatureByRoles,
        siteId: this.userData.tenantId,
        admissionId: this.sharedService.isMultiAdmissionsEnabled ? this.navParams.get('admissionId') : ''
      };
      this.getAssociateData(params);
    } else {
      this.commonSignatureRequestTextsDisplaySearchFunc(this.constants.signatureRequestActionType.recipients);
    }
  }

  getAssociateData(params: any) {
    this.sharedService.isLoading = true;
    this.graphqlService.signatureRequestTextsDisplay(params)?.subscribe(
      ({ data }) => {
        this.sharedService.isLoading = false;
        this.showLoadMore = false;
        if (!isBlank(data.signatureRequestTextsDisplay)) {
          this.recipients = data.signatureRequestTextsDisplay;
          this.chooseRecipients = data.signatureRequestTextsDisplay;
          this.associateUsers = this.recipients;
          this.createForm(this.recipients);
        } else {
          this.showCommonMessage();
        }
      },
      (error) => {
        this.showLoadMore = false;
        this.sharedService.errorHandler(error);
        this.showCommonMessage();
      }
    );
  }

  // GraphQL api changes
  commonSignatureRequestTextsDisplaySearchFunc(action) {
    this.errorMessage = '';
    // TODO Pagination support in commonSignatureRequestTextsDisplaySearchFunc
    const params: {
      signatureRequestRecipients: {
        roles: string;
        searchValue: string;
        siteId: string;
        admissionId: string;
        filterByPatientId?: number;
      };
    } = {
      signatureRequestRecipients: {
        roles: this.docType?.signatureByRoles ? this.docType?.signatureByRoles : undefined,
        searchValue: this.searchKeyword ? this.searchKeyword : '',
        siteId: this.selectedSiteIds.toString(),
        admissionId: this.sharedService.isMultiAdmissionsEnabled ? this.navParams.get('admissionId') : ''
      }
    };
    if (this.selectedAssociatePatient?.patientId && this.docType?.allowAssociatePatient) {
      params.signatureRequestRecipients.filterByPatientId = +this.selectedAssociatePatient.patientId;
    }
    this.sharedService.isLoading = true;
    this.graphqlService.signatureRequestTextsDisplaySearch(params, action)?.subscribe({
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      next: (response: any) => {
        this.sharedService.isLoading = false;
        this.chooseRecipients = response.data.signatureRequestTextsDisplaySearch;
        const responseRecipients = response.data.signatureRequestTextsDisplaySearch;
        this.userService.transformUserList(responseRecipients, action, this.docType).then((recipients) => {
          this.recipients = recipients;
          this.createForm(this.recipients);
          this.associateUsers = this.recipients;
          if (this.recipients.length === 0) {
            this.showCommonMessage();
          }
        });
      },
      error: () => {
        this.sharedService.isLoading = false;
        this.showCommonMessage();
      }
    });
  }
  createForm(list: any): void {
    const controls = list.map((c) => new UntypedFormControl(false));
    this.selectRecipientForm = this.formBuilder.group({
      recipientList: new UntypedFormArray(controls)
    });
  }

  viewForm(data, alternateIndex = -1): void {
    let userSiteId = '';
    if (
      this.recipientType === Constants.patientValue &&
      this.docType.allowAssociatePatient &&
      this.sharedService.isEnableConfig(Config.enableMultiSite)
    ) {
      userSiteId = data.siteId.toString();
    } else {
      userSiteId = this.selectedSiteIds ? this.selectedSiteIds.toString() : '0';
    }
    this.modalController.dismiss({
      dismissed: true,
      alternateIndex,
      selectedSite: userSiteId,
      selectedSites: this.selectedSiteIds,
      data
    });
  }

  filterList(event) {
    this.recipients = this.associateUsers;
    if (this.recipientType === Constants.associate && this.recipients) {
      const searchTerm = event.srcElement.value;
      if (!searchTerm) {
        return;
      }
      this.recipients = this.recipients.filter((user: any) => {
        if (user.displayName && searchTerm) {
          if (user.displayName.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1) {
            return true;
          }
          return false;
        }
      });

      if (this.recipients.length === 0) {
        this.showCommonMessage();
      }
    }
  }

  searchOperations(action: any): void {
    if (action.do === 'search' || action.do === 'reset') {
      this.clearAll();
      this.recipients = [];
      this.searchKeyword = action.value;
      this.pageCount = Constants.defaultPageCount;
      if (
        this.headerTitle === 'TITLES.CHOOSE_RECIPIENTS' &&
        this.siteSingleSelection === true &&
        action.do === 'reset'
      ) {
        this.sharedService.isLoading = false;
        // TODO Pagination support in commonSignatureRequestTextsDisplaySearchFunc
        this.showCommonMessage();
      } else {
        if (
          this.headerTitle === 'TITLES.CHOOSE_RECIPIENTS' &&
          this.siteSingleSelection === true &&
          action.do === 'search'
        ) {
          this.commonSignatureRequestTextsDisplaySearchFunc(this.constants.signatureRequestActionType.recipients);
        } else {
          this.loadList();
        }
      }
      if (action.do === 'search') {
        this.sharedService.trackActivity({
          type: Activity.signatureRequest,
          name: Activity.searchRecipients,
          des: {
            data: {
              displayName: this.sharedService?.userData?.displayName,
              keyWord: this.searchKeyword
            },
            desConstant: Activity.searchRecipientsDes
          }
        });
      }
    } else if (action.do === 'add') {
      this.showAddPatient();
    }
  }
  async showAddPatient(): Promise<void> {
    const page = 'docCenter';
    const modal = await this.modalController.create({
      component: AddVirtualPatientComponent,
      componentProps: { page },
      id: 'add-patient'
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.virtualPatient = data;
        this.modalController.dismiss(data, null, 'choose-recipient');
      }
    });
    return await modal.present();
  }

  loadMore(): void {
    this.pageCount++;
    if (this.recipientType === Constants.patientValue) {
      this.getAssociatedPatients();
    } else {
      this.loadList();
    }
  }

  clearAll(): void {
    this.selectRecipientForm.reset();
    this.searchKeyword = '';
  }

  goBack(): void {
    this.modalController.dismiss();
  }

  loadData(event: any) {
    //TODO! CHP-3598
    setTimeout(() => {
      event.target.complete();
      this.loadMore();
    }, 1500);
  }

  filterSitesData(data: any): void {
    this.selectedSiteIds = data;
    this.pageCount = Constants.defaultPageCount;
    this.recipients = [];
    this.clearAll();

    if (this.headerTitle === 'TITLES.CHOOSE_RECIPIENTS' && this.siteSingleSelection === true) {
      this.showCommonMessage();
    } else {
      this.sharedService.isLoading = true;
      this.loadList();
    }
  }

  showCommonMessage() {
    // TODO After pagination support in commonSignatureRequestTextsDisplaySearchFunc, remove SEARCH_FOR_RECIPIENTS message
    this.errorMessage = this.userService.showCommonMessage(this.addPatient, this.searchKeyword);
    this.createPatientLinkMessage = this.addPatient ? this.common.getTranslateData('MESSAGES.TO_CREATE_NEW_PATIENT') : '';
  }

  get isRecipientDefaultDataShowBehaviour(): boolean {
    return (
      this.isRecipient && (!this.isEnableMultiSite || (this.isEnableMultiSite && this.selectedSiteIds?.length > 0))
    );
  }

  initialSiteData(data: any): void {
    if (!this.isDisabledSite && !this.siteSingleSelection) {
      this.selectedSiteIds = data;
    }
  }
}
