<app-header-plain [headerTitle]="headerTitle" (close)="goBack()"></app-header-plain>
<div *ngIf="siteFilterShow">
    <app-sites (filterSites)="filterSitesData($event)" [singleSiteSelection]="siteSingleSelection" [siteLabel]="siteFilterLabel"
        [mandatory]="isRecipient" [selectedSiteIds]="selectedSiteIds" [disableFilter]="isDisabledSite" (onLoadSites)="initialSiteData($event)"></app-sites>
</div>
<ion-searchbar *ngIf="recipientType === constants.associate" id="search-bar" (ionInput)="filterList($event)">
</ion-searchbar>
<app-search-bar-recipients
    *ngIf="(recipientType !== constants.associate && !isRecipient) || isRecipientDefaultDataShowBehaviour"
    [searchValue]="searchKeyword" [addPatient]=" addPatient" (searchAction)="searchOperations($event)">
</app-search-bar-recipients>
<ion-content>
    <form name="selectRecipientForm" id="select-recipient-form" [formGroup]="selectRecipientForm">
        <div class="common-list" *ngIf="!isRecipient || isRecipientDefaultDataShowBehaviour">
            <ion-list lines="none" *ngFor="let recipient of recipients; let id = index"
                class="recipient-list ion-no-padding">
                <ion-item class="recipient-parent" formArrayName="recipientList"
                    (click)="!recipient?.patientClick && viewForm(recipient)" id="recipient-{{id}}">
                    <ion-label class="ion-text-wrap">
                        <!-- TODO check all conditions and fix -->
                        <span class="common-capitalize-text"
                            *ngIf="recipientType === constants.patientValue || recipientType === constants.recipient">
                            {{recipient.displayText}}
                        </span>
                        <span class="common-capitalize-text" *ngIf="recipientType === constants.associate">
                            {{recipient.displayName}}
                        </span>

                        <span class="common-capitalize-text" *ngIf="recipient.sitename && isEnableMultiSite">
                            - {{recipient.sitename}}
                        </span>
                        <!-- TODO need to replace these conditions,once api gets standardized -->
                    </ion-label>
                    <ion-checkbox [formControlName]="id" class="common-checkbox" slot="end" mode='ios'
                        *ngIf="!recipient?.patientClick" id="select-recipient-{{id}}">
                    </ion-checkbox>
                </ion-item>
                <ion-item *ngFor="let alternate of recipient.alternateContacts; let idAlt=index" class="recipient-child"
                    (click)="viewForm(recipient,idAlt)" id="recipient-child-{{idAlt}}">
                    <ion-label class="recipient-label-wrap">
                        <span>{{alternate?.patientDisplayName}}</span>
                        <span>({{alternate?.displayName}}
                            <span *ngIf="alternate?.relation"> - {{alternate?.relation}}</span>)</span>
                        <span *ngIf="alternate?.patientDob"> - {{alternate?.patientDob|
                            date:constants?.dateFormat?.mmddyyyy}}&nbsp;</span>
                        <span *ngIf="alternate?.patientMrn">[{{'GENERAL.MRN' | translate}}:
                            {{alternate?.patientMrn}}]&nbsp;</span>
                        <span *ngIf="+alternate?.password" translate>({{ 'GENERAL.ENROLLED' | translate }})</span>
                        <span *ngIf="!+alternate?.password && !alternate.tag_type_id">({{ 'GENERAL.VIRTUAL' | translate }})</span>
                    </ion-label>
                    <ion-checkbox class="common-checkbox" slot="end" mode='ios'
                        id="select-recipient-alternate-{{idAlt}}">
                    </ion-checkbox>
                </ion-item>
            </ion-list>
        </div>
    </form>

    <div class="common-no-items" *ngIf="recipients?.length === 0">{{errorMessage}}
        <span *ngIf="createPatientLinkMessage !== ''" (click)="showAddPatient()">
            <u class="show-cursor-pointer"><b>{{'LABELS.CLICK_HERE'| translate}}</b></u>
            {{createPatientLinkMessage}}
        </span>
    </div>

    <!-- TODO! CHP-3598-->
    <ion-infinite-scroll *ngIf="showLoadMore" threshold="100px" (ionInfinite)="loadData($event)">
        <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{'BUTTONS.LOAD_MORE' | translate}}">
        </ion-infinite-scroll-content>
    </ion-infinite-scroll>
</ion-content>