import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { HeaderPlainModule } from '../../../components/header-plain/header-plain.module';
import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';
import { TranslateModule } from '@ngx-translate/core';
import { PatientModule } from '../../user/invite/patient/patient.module';
import { DocumentCenterRecipientsComponent } from './document-center-recipients.component';
import { AddVirtualPatientComponentModule } from '../../user/add-virtual-patient/add-virtual-patient.module';
import { SitesModule } from 'src/app/components/sites/sites.module';
@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        IonicModule,
        HeaderPlainModule,
        SearchBarRecipientsModule,
        TranslateModule,
        PatientModule,
        AddVirtualPatientComponentModule,
        SitesModule
    ],
    declarations: [DocumentCenterRecipientsComponent],
    exports: [DocumentCenterRecipientsComponent]
})
export class DocumentCenterRecipientsComponentModule { }
