import { Constants } from 'src/app/constants/constants';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Apollo } from 'apollo-angular';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { IonicModule, NavParams, ModalController } from '@ionic/angular';
import { DocumentCenterRecipientsComponent } from './document-center-recipients.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';
import { of, throwError } from 'rxjs';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { Config } from 'src/app/constants/config';

describe('DocumentCenterRecipientsComponent', () => {
  let component: DocumentCenterRecipientsComponent;
  let fixture: ComponentFixture<DocumentCenterRecipientsComponent>;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  let service: SharedService;
  const navData = {
    documentType: {
      associateUserSignatureByRoles: '[]',
      signatureByRoles: '[11,12]'
    },
    recipientType: 'associate'
  };
  let navParams = new NavParams(navData);
  let graphqlService: GraphqlService;

  const selectedItemData = {
    careGiver: null,
    cmisId: 362618,
    countryCode: '+1',
    dateOfBirth: '1993-06-21',
    displayName: 'Aani N',
    displayText: 'Aani N - 06/21/1993\n             (Enrolled)',
    firstName: 'Aani',
    id: '1738125',
    isVirtual: false,
    lastName: 'N',
    mobile: '**********',
    patientIdentity: { IdentityValue: '176r', __typename: 'PatientIdentity' },
    role: 3,
    userEMverification: { mobileVerified: 1, emailVerified: 1, __typename: 'mobileOrEmailVerification' },
    userId: 1738125,
    userName: '<EMAIL>',
    __typename: 'SignatureRequestTextDisplay'
  };
  const item = {
    sessionId: 'AQIC5wM2LY4Sfcx-sgsZCGzotRi-m8yu2NXhy8Km__uO_Ok.*AAJTSQACMDEAAlNLABM1MTI5MzU4NDE1MzQ3OTI5MDc2AAJTMQAA*',
    roles: '["7681"]',
    siteId: '558'
  };
  const mockRecipients = [{ id: 1, displayName: 'Test Recipient' }];
  const signatureRequestTextsMockResponse = {
    data: {
      signatureRequestTextsDisplaySearch: [
        {
          id: '2601534',
          userId: 2601534,
          displayName: 'Fenis AB',
          firstName: 'Fenis',
          lastName: 'AB',
          dateOfBirth: '2019-05-05',
          patientIdentity: { IdentityValue: '7856576', __typename: 'PatientIdentity' },
          avatar: '',
          cmisId: 2646995,
          role: 3,
          userName: '<EMAIL>',
          mobile: '**********',
          countryCode: '+91',
          careGiver: null,
          isVirtual: false,
          userEMverification: { mobileVerified: 1, emailVerified: 1, __typename: 'mobileOrEmailVerification' },
          alternateContacts: [],
          __typename: 'SignatureRequestTextDisplay'
        }
      ]
    }
  };
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DocumentCenterRecipientsComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        ReactiveFormsModule
      ],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        GraphqlService,
        Idle,
        IdleExpiry,
        ModalController,
        Keepalive,
        { provide: NavParams, useValue: navParams },
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    service = TestBed.inject(SharedService);
    graphqlService = TestBed.inject(GraphqlService);
    service.userData = {
      ...service.userData,
      tenantId: '1'
    };
    service.userData.config = {
      ...service.userData.config,
      enable_appless_model: 'true'
    };
    fixture = TestBed.createComponent(DocumentCenterRecipientsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('check patient recipient type', () => {
    navData.recipientType = 'patient';
    navParams = new NavParams(navData);
    expect(component).toBeTruthy();
  });

  it('should call loadList function', () => {
    component.loadList();
    expect(component.loadList).toBeDefined();
  });

  it('check recipient type exist or not', () => {
    navData.recipientType = 'recipient';
    navParams = new NavParams(navData);
    expect(component).toBeTruthy();
  });

  it('should call clear recipient from', () => {
    component.clearAll();
    component.selectRecipientForm.reset();
    expect(component.clearAll).toBeTruthy();
  });

  it('should call close modal', () => {
    component.goBack();
    fixture.detectChanges();
    expect(component.goBack).toBeTruthy();
  });

  it('should call filter data', () => {
    component.headerTitle = 'TITLES.CHOOSE_RECIPIENTS';
    component.siteSingleSelection = true;
    fixture.detectChanges();
    component.filterSitesData([767, 868]);
    expect(component.filterSitesData).toBeDefined();
  });

  it('should call filter data with different header and siteSingleSelection = false', () => {
    component.headerTitle = 'TITLES.CHOOSE_ASSOCIATED_PATIENT';
    component.siteSingleSelection = false;
    fixture.detectChanges();
    component.filterSitesData([767, 868]);
    expect(service.isLoading).toBe(true);
  });

  it('load more data', () => {
    component.loadMore();
    if (component.recipientType === Constants.patientValue) {
      component.getAssociatedPatients();
    } else {
      component.loadList();
    }
    expect(component.loadMore).toBeDefined();
  });

  it('should show add patient modal', () => {
    component.showAddPatient();
    fixture.detectChanges();
    expect(component.showAddPatient).toBeDefined();
  });

  it('search operations call do search', () => {
    const action = {
      do: 'search',
      value: 'test'
    };
    component.searchOperations(item);
    if (action.do === 'search' || action.do === 'reset') {
      component.clearAll();
      component.recipients = [];
      component.searchKeyword = action.value;
      component.loadList();
    }
    expect(item).toBeDefined();
    expect(component.searchOperations).toBeDefined();
  });

  it('search operations call do add', () => {
    const action = {
      do: 'add',
      value: 'test'
    };
    component.searchOperations(action);
    expect(action).toBeDefined();
    expect(component.searchOperations).toBeDefined();
  });

  it('search operations call do reset', () => {
    const action = {
      do: 'reset',
      value: 'test'
    };
    component.searchOperations(action);
    component.headerTitle = 'TITLES.CHOOSE_RECIPIENTS';
    component.siteSingleSelection = true;
    if (action.do === 'search' || action.do === 'reset') {
      component.clearAll();
      component.recipients = [];
      component.searchKeyword = action.value;
      component.loadList();
    }
    expect(item).toBeDefined();
    expect(component.searchOperations).toBeDefined();
  });

  it('pass selected item data and close modal', () => {
    component.viewForm(selectedItemData);
    fixture.detectChanges();
    expect(component.viewForm).toBeTruthy();
  });

  it('should call getAssociatedPatients function', () => {
    const mockResponse = [{ passwordStatus: true }];
    spyOn(service, 'getAssociatedPatientsByTagId').and.returnValue(of(mockResponse));
    component.getAssociatedPatients();
    expect(component.getAssociatedPatients).toBeDefined();
  });

  it('getAssociatedPatients function returns empty data', () => {
    spyOn(service, 'getAssociatedPatientsByTagId').and.returnValue(of({}));
    component.getAssociatedPatients();
    expect(component.getAssociatedPatients).toBeDefined();
  });
  it('getAssociatedPatients function handle error', () => {
    spyOn(service, 'getAssociatedPatientsByTagId').and.returnValue(throwError({}));
    component.getAssociatedPatients();
    expect(component.getAssociatedPatients).toBeDefined();
  });

  it('should call createForm function', () => {
    component.createForm([selectedItemData, selectedItemData]);
    expect(component.createForm).toBeDefined();
  });

  it('execute loadData', fakeAsync(() => {
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    component.loadData({ target: spy });
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(component.loadData).toBeTruthy();
    });
  }));

  it('get associate data call', () => {
    const mockResponse = {
      data: {
        signatureRequestTextsDisplay: [{ id: 1, name: 'Recipient1' }]
      }
    };
    spyOn(graphqlService, 'signatureRequestTextsDisplay').and.returnValue(of(mockResponse));
    component.getAssociateData(item);
    expect(component.getAssociateData).toBeDefined();
  });

  it('signatureRequestTextsDisplay call return empty data', () => {
    const mockResponse = {
      data: {
        signatureRequestTextsDisplay: []
      }
    };
    spyOn(graphqlService, 'signatureRequestTextsDisplay').and.returnValue(of(mockResponse));
    component.getAssociateData(item);
    expect(component.getAssociateData).toBeDefined();
  });

  it('signatureRequestTextsDisplay function return data without error ', () => {
    spyOn(graphqlService, 'signatureRequestTextsDisplay').and.returnValue(throwError({}));
    component.getAssociateData(item);
    expect(component.getAssociateData).toBeDefined();
  });

  it('should call filterList', () => {
    component.recipientType = 'associate';
    const mockEvent = {
      srcElement: { value: 'smv' }
    };
    component.associateUsers = mockRecipients;
    component.filterList(mockEvent);
    expect(component.filterList).toBeDefined();
  });
  it('check search term is empty', () => {
    component.recipientType = 'associate';
    const mockEvent = {
      srcElement: { value: '' }
    };
    component.associateUsers = mockRecipients;
    component.filterList(mockEvent);
    expect(component.filterList).toBeDefined();
  });
  it('loadMore function should check patient recipient type', () => {
    component.recipientType = 'patient';
    component.loadMore();
    expect(component.loadMore).toBeDefined();
  });

  it('execute showCommonMessage', () => {
    component.showCommonMessage();
    expect(component.showCommonMessage).toBeDefined();
  });

  it('should set error message correctly for addPatient=true', () => {
    component.addPatient = true;
    component.showCommonMessage();
    expect(component.errorMessage).toBe('MESSAGES.NO_MATCHES_FOUND');
    expect(component.createPatientLinkMessage).toBe('MESSAGES.TO_CREATE_NEW_PATIENT');
  });

  it('should set error message correctly for searchKeyword=true', () => {
    component.searchKeyword = true;
    component.showCommonMessage();
    expect(component.errorMessage).toBe('MESSAGES.NO_ITEM_FOUND');
    expect(component.createPatientLinkMessage).toBe('');
  });

  it('should set error message correctly for addPatient=false and searchKeyword=false', () => {
    component.addPatient = false;
    component.searchKeyword = false;
    component.showCommonMessage();
    expect(component.errorMessage).toBe('MESSAGES.SEARCH_FOR_RECIPIENTS');
    expect(component.createPatientLinkMessage).toBe('');
  });

  it('should return true when isRecipient is true and isEnableMultiSite is false', () => {
    component.isRecipient = true;
    component.isEnableMultiSite = false;
    expect(component.isRecipientDefaultDataShowBehaviour).toBe(true);
  });

  it('should return false when isRecipient is false', () => {
    component.isRecipient = false;
    component.isEnableMultiSite = true;
    expect(component.isRecipientDefaultDataShowBehaviour).toBe(false);
  });

  it('should return true when isRecipient is true, isEnableMultiSite is true, and selectedSiteIds has length > 0', () => {
    component.isRecipient = true;
    component.isEnableMultiSite = true;
    component.selectedSiteIds = [1, 2, 3];
    expect(component.isRecipientDefaultDataShowBehaviour).toBe(true);
  });

  it('should return false when isRecipient is true, isEnableMultiSite is true, and selectedSiteIds is empty', () => {
    component.isRecipient = true;
    component.isEnableMultiSite = true;
    component.selectedSiteIds = [];
    expect(component.isRecipientDefaultDataShowBehaviour).toBe(false);
  });

  it('should call ngOnInit with allowAssociatePatient=true and enableMultiSite=true', () => {
    component.recipientType = 'recipient';
    component.docType = {
      allowAssociatePatient: true
    };
    spyOn(service, 'isEnableConfig').and.returnValue(true);
    component.ngOnInit();
    expect(component.ngOnInit).toBeDefined();
    expect(component.siteSingleSelection).toBe(true);
    expect(component.isDisabledSite).toBe(true);
  });

  it('should call ngOnInit with user sites single selection', () => {
    service.userData.mySites = [{ id: '123', name: 'Demo' }];
    component.recipientType = 'recipient';
    fixture.detectChanges();
    component.ngOnInit();
    expect(component.siteFilterShow).toBe(false);
  });
  describe('CommonFunctionSignatureRequestTextsDisplaySearch', () => {

    it('should call commonSignatureRequestTextsDisplaySearchFunc call signatureRequestTextsDisplaySearch graphQL API with success', () => {
      spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of(signatureRequestTextsMockResponse));
      component.commonSignatureRequestTextsDisplaySearchFunc('OnBehalf');
      fixture.detectChanges();
      expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
    });
    it('should call commonSignatureRequestTextsDisplaySearchFunc call signatureRequestTextsDisplaySearch graphQL API with error', () => {
      spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(throwError({}));
      component.commonSignatureRequestTextsDisplaySearchFunc('OnBehalf');
      fixture.detectChanges();
      expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
    });
    it('should call commonSignatureRequestTextsDisplaySearchFunc call signatureRequestTextsDisplaySearch graphQL API with success', () => {
      spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of(signatureRequestTextsMockResponse));
      component.commonSignatureRequestTextsDisplaySearchFunc('Recipients');
      fixture.detectChanges();
      expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
    });
    it('should call commonSignatureRequestTextsDisplaySearchFunc call signatureRequestTextsDisplaySearch graphQL API with error', () => {
      spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(throwError({}));
      component.commonSignatureRequestTextsDisplaySearchFunc('Recipients');
      fixture.detectChanges();
      expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
    });
    it('should call commonSignatureRequestTextsDisplaySearchFunc call signatureRequestTextsDisplaySearch with filterByPatientId', () => {
      spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of(signatureRequestTextsMockResponse));
      component.selectedAssociatePatient = { patientId: 123 };
      component.docType = { allowAssociatePatient: true };
      component.commonSignatureRequestTextsDisplaySearchFunc('Recipients');
      fixture.detectChanges();
      expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
    });
    it('should call commonSignatureRequestTextsDisplaySearchFunc call signatureRequestTextsDisplaySearch return empty', () => {
      spyOn(graphqlService, 'signatureRequestTextsDisplaySearch').and.returnValue(of({ data: { signatureRequestTextsDisplaySearch: [] } }));
      component.selectedAssociatePatient = { patientId: 123 };
      component.docType = { allowAssociatePatient: true };
      component.commonSignatureRequestTextsDisplaySearchFunc('Recipients');
      fixture.detectChanges();
      expect(component.commonSignatureRequestTextsDisplaySearchFunc).toBeDefined();
      expect(component.createPatientLinkMessage).toBe('');
    });
  });

  it('should dismiss modal with correct parameters for non-patient view', () => {
    component.recipientType = Constants.recipient;
    component.docType = { allowAssociatePatient: true };
    spyOn(service, 'isEnableConfig').and.returnValue(false);
    const testData = { siteId: 123 };
    component.selectedSiteIds = [111];
    component.viewForm(testData);

    expect(modalController.dismiss).toHaveBeenCalledWith({
      dismissed: true,
      alternateIndex: -1,
      selectedSite: '111',
      selectedSites: [111],
      data: testData
    });
  });

  it('should dismiss modal with correct parameters for non-patient view without siteId', () => {
    component.recipientType = Constants.recipient;
    component.docType = { allowAssociatePatient: true };
    spyOn(service, 'isEnableConfig').and.returnValue(false);
    component.selectedSiteIds = '';
    const testData = { siteId: 123 };
    component.viewForm(testData);

    expect(modalController.dismiss).toHaveBeenCalledWith({
      dismissed: true,
      alternateIndex: -1,
      selectedSite: '0',
      selectedSites: '',
      data: testData
    });
  });

  it('should dismiss modal with correct parameters for patient view', () => {
    component.recipientType = Constants.patientValue;
    component.docType = { allowAssociatePatient: true };
    component.selectedSiteIds = '12';
    spyOn(service, 'isEnableConfig').and.returnValue(true);
    const testData = { siteId: 123 };
    component.viewForm(testData);

    expect(modalController.dismiss).toHaveBeenCalledWith({
      dismissed: true,
      alternateIndex: -1,
      selectedSite: '123',
      selectedSites: '12',
      data: testData
    });
  });
});
