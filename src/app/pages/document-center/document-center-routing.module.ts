import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { UserGroup } from 'src/app/constants/constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { PermissionGuard } from 'src/app/services/permission-guard/permission.guard';


const routes: Routes = [
    {
        path: '',
        loadChildren: () => import('./documents/documents.module').then((m) => m.DocumentsPageModule)
    },
    {
        path: 'request-signature',
        loadChildren: () => import('./request-signature/request-signature.module').then((m) => m.RequestSignaturePageModule),
        canActivate: [PermissionGuard],
        data: {
            permissions: {
                restrictedTo: {
                    userTypes: [UserGroup.PATIENT]
                },
                redirectTo: PageRoutes.accessDenied,
            }
        }
    },
    {
        path: 'select-document',
        loadChildren: () => import('./select-document/select-document.module').then((m) => m.SelectDocumentPageModule)
    },
    {
        path: 'view-document/:documentId/:senderTenant',
        loadChildren: () => import('./view-document/view-document.module').then((m) => m.ViewDocumentPageModule)
    },
    {
        path: 'view-document',
        loadChildren: () => import('./view-document/view-document.module').then((m) => m.ViewDocumentPageModule)
    },
    {
        path: ':type',
        loadChildren: () => import('./document-types/document-types.module').then((m) => m.DocumentTypesPageModule)
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class DocumentCenterRoutingModule { }
