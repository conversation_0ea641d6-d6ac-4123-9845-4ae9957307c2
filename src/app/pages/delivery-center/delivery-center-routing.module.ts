import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadChildren: () => import('./delivery/delivery.module').then((m) => m.DeliveryPageModule)
  },
  {
    path: ':type',
    loadChildren: () => import('./delivery-types/delivery-types.module').then((m) => m.DeliveryTypesPageModule)
  },
  {
    path: 'delivery-details/:courierTicketNumber/:id',
    loadChildren: () => import('./delivery-details/delivery-details.module').then((m) => m.DeliveryDetailsPageModule)
  },

  {
    path: 'delivery-details',
    loadChildren: () => import('./delivery-details/delivery-details.module').then((m) => m.DeliveryDetailsPageModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DeliveryCenterRoutingModule {}
