<app-header headerTitle="MENU.DELIVERY_CENTER"></app-header>
<ion-content class="delivery-page common-page-layout">
    <div class="common-page-sections">
        <div class="common-page-section row" (click)="gotoDeliveries(pageRoutes.pendingDeliveries)" tappable
            id="pending-delivery">
            <div class="common-page-btns common-btn1 col common-full-width" id="pending">
                <span>{{ 'LABELS.PENDING_DELIVERY' | translate }}</span>
                <i><img src="../../assets/icon/delivery/pending-delivery.png" alt="pending-delivery">
                </i>
            </div>
        </div>
        <div class="common-page-section row" (click)="gotoDeliveries(pageRoutes.completedDeliveries)" tappable
            id="completed-delivery">
            <div class="common-page-btns common-btn2 col common-full-width" id="completed">
                <span>{{ 'LABELS.COMPLETED_DELIVERY' | translate }}</span>
                <i><img src="../../assets/icon/delivery/completed-delivery.png" alt="completed-delivery">
                </i>
            </div>
        </div>
    </div>
</ion-content>
<app-action-button [button]="button"></app-action-button>
<app-footer></app-footer>