import { Component } from '@angular/core';
import { Activity } from 'src/app/constants/activity';
import { Constants } from 'src/app/constants/constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';

@Component({
  selector: 'app-delivery',
  templateUrl: './delivery.page.html',
  styleUrls: ['./delivery.page.scss']
})
export class DeliveryPage {
  button: any = {};
  pageRoutes: any;
  constructor(private readonly sharedService: SharedService, private readonly common: CommonService) {
    this.pageRoutes = PageRoutes;
    if (Number(this.sharedService.userData?.group) === Constants.patientGroupId) {
      this.button = {
        iconCustom: false,
        type: 'new-chat',
        buttonType: 'ion-button',
        buttonIcon: 'chatbox-ellipses',
        colorTheme: 'de-york'
      };
    }
  }
  gotoDeliveries(url): void {
    let activityData;
    if (url === PageRoutes.pendingDeliveries) {
      activityData = {
        type: Activity.pendingDelivery,
        name: Activity.viewPendingDelivery,
        des: Activity.viewPendingDeliveryDes
      };
    } else {
      activityData = {
        type: Activity.completedgDelivery,
        name: Activity.viewCompletedDelivery,
        des: Activity.viewCompletedDeliveryDes
      };
    }
    this.sharedService.trackActivity(activityData);
    this.common.redirectToPage(url);
  }
}
