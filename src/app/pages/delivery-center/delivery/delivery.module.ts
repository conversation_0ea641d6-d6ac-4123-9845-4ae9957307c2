import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { DeliveryPageRoutingModule } from './delivery-routing.module';

import { DeliveryPage } from './delivery.page';
import { SharedModule } from 'src/app/shared.module';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    DeliveryPageRoutingModule,
    CommonModule,
    FormsModule,
    IonicModule,
    SharedModule,
    ActionButtonModule
  ],
  declarations: [DeliveryPage]
})
export class DeliveryPageModule {}
