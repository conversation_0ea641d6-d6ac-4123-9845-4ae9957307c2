import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { PageRoutes } from 'src/app/constants/page-routes';
import { DeliveryPage } from './delivery.page';

describe('DeliveryPage', () => {
  let component: DeliveryPage;
  let fixture: ComponentFixture<DeliveryPage>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DeliveryPage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(DeliveryPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute gotoDeliveries', () => {
    component.gotoDeliveries(PageRoutes.pendingDeliveries);
    expect(component.gotoDeliveries).toBeDefined();
  });
  it('execute gotoDeliveries : else', () => {
    component.gotoDeliveries('');
    expect(component.gotoDeliveries).toBeDefined();
  });
});
