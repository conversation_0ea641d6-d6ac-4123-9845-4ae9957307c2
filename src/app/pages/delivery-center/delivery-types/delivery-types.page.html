<app-header headerTitle="MENU.DELIVERY_CENTER" [subHead]="subHead"></app-header>
<div class="site-header-padding"></div>
<app-search-bar (seachAction)="searchDelivery($event)" (closeSearch)="loadDelivery()"></app-search-bar>
<app-select-list-grid [subHead]="subHead" (viewType)="isList=$event" [showIcons]="deliveries?.length"></app-select-list-grid>
<ion-content class="delivery-type">
  <ion-refresher slot="fixed" (ionRefresh)="pullRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <app-data-list *ngIf="isList" [extraData]="extraData" [listData]="deliveries" (itemBtnClick)="selectAction($event)"></app-data-list>
  <app-data-grid *ngIf="!isList" [gridData]="deliveries" [extraData]="extraData" (itemBtnClick)="selectAction($event)"></app-data-grid>
  <ion-infinite-scroll *ngIf="showLoadMore" threshold="100px" (ionInfinite)="loadMore($event)">
    <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{ 'BUTTONS.LOAD_MORE' | translate }}"></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
<app-action-button [button]="button"></app-action-button>
<app-footer></app-footer>
