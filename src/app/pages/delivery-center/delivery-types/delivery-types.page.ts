import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Activity } from 'src/app/constants/activity';
import { APIs } from 'src/app/constants/apis';
import { Constants, UserGroup } from 'src/app/constants/constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { ActionButton, DataListExtraData, DeliveryData, DeliveryResponse } from 'src/app/interfaces/common-interface';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { isPresent } from 'src/app/utils/utils';

@Component({
  selector: 'app-delivery-types',
  templateUrl: './delivery-types.page.html',
  styleUrls: ['./delivery-types.page.scss']
})
export class DeliveryTypesPage implements OnInit {
  deliveries: DeliveryData[] = [];
  subHead: string;
  deliveryType: string;
  deliveryStatus: string;
  button: ActionButton = {};
  pageCount = Constants.defaultPageCount;
  limit = Constants.defaultLimit;
  offset = Constants.defaultOffset;
  extraData: DataListExtraData = {};
  isList = true;
  showLoadMore: boolean;
  searchText: string;
  userType = Constants.staffValue;
  constructor(
    private readonly httpService: HttpService,
    private readonly common: CommonService,
    public readonly route: ActivatedRoute,
    private readonly sharedService: SharedService
  ) {
    this.route.paramMap.subscribe((paramMap) => {
      this.deliveryType = paramMap.get('type');
      switch (this.deliveryType) {
        case Constants.deliveryTypes.pending:
          this.subHead = 'LABELS.PENDING_DELIVERY';
          this.deliveryStatus = Constants.InProgress;
          break;
        case Constants.deliveryTypes.completed:
          this.subHead = 'LABELS.COMPLETED_DELIVERY';
          this.deliveryStatus = Constants.completed;
          break;
        default:
          this.common.redirectToPage('not-found');
          break;
      }
    });
    if (Number(this.sharedService.userData?.group) === UserGroup.PATIENT) {
      this.button = {
        iconCustom: false,
        type: 'new-chat',
        buttonType: 'ion-button',
        buttonIcon: 'chatbox-ellipses',
        colorTheme: 'de-york'
      };
      this.userType = Constants.patientValue;
    } else {
      this.userType = Constants.staffValue;
    }
    this.extraData = {
      image: '',
      type: 'delivery',
      trackBy: 'id',
      showNoDataMessage: false,
      search: {}
    };
  }

  ngOnInit() {
    this.getDelivery();
  }

  getDelivery(hardLoad = true, event = undefined): void {
    this.pageCount = hardLoad ? Constants.defaultPageCount : this.pageCount;
    this.showLoadMore = false;
    this.extraData.showNoDataMessage = false;
    const body = {
      offset: this.offset,
      limit: this.limit,
      userId: this.sharedService.userData?.userId,
      tenantId: this.sharedService.userData?.tenantId,
      searchText: this.searchText || '',
      orderData: Constants.courierDateTime,
      orderby: Constants.sortOrderDesc,
      inApp: 0,
      userType: this.userType,
      viewStatus: this.deliveryStatus
    };
    this.extraData.search = { text: this.searchText };
    this.httpService.doGet({ endpoint: APIs.getDeliveryListing, extraParams: body, loader: false }).subscribe(
      (response: DeliveryResponse) => {
        if (isPresent(response?.deliveryData)) {
          this.showLoadMore = response.totalCount >= Constants.defaultLimit;
          if (this.pageCount === 0) {
            this.deliveries = response.deliveryData;
          } else {
            this.deliveries = [...this.deliveries, ...response.deliveryData];
          }
        } else {
          this.resetDeliveries();
        }
        event?.target?.complete();
      },
      () => {
        event?.target?.complete();
        this.resetDeliveries();
      }
    );
  }
  resetDeliveries(): void {
    this.showLoadMore = false;
    this.extraData.showNoDataMessage = this.pageCount === 0;
  }
  pullRefresh(event): void {
    this.getDelivery(true, event);
  }
  selectAction(e): void {
    this.sharedService.trackActivity({
      type: Activity.viewDeliveryDetails,
      name: Activity.deliveryDetailsPageView,
      des: {
        data: {
          ticketNumber: e.item.courierTicketNumber,
          uId: e.item.id
        },
        desConstant: Activity.viewDeliveryDetailsDes
      }
    });
    this.common.redirectToPage(`${PageRoutes.deliveryDetails}/${e.item.courierTicketNumber}/${e.item.id}`);
  }
  searchDelivery(searchData: { text: string }): void {
    this.deliveries = [];
    this.searchText = searchData?.text;
    this.getDelivery();
    this.sharedService.trackActivity({
      type: Activity.delivery,
      name: Activity.searchDelivery,
      des: {
        data: {
          searchText: this.searchText
        },
        desConstant: Activity.searchDeliveryDes
      }
    });
  }
  loadDelivery(): void {
    this.deliveries = [];
    this.searchText = '';
    this.getDelivery();
  }
  loadMore(event) {
    this.pageCount += 1;
    this.getDelivery(false, event);
  }
}
