import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { of, throwError } from 'rxjs';
import { HttpService } from 'src/app/services/http-service/http.service';
import { DeliveryTypesPage } from './delivery-types.page';

describe('DeliveryTypesPage', () => {
  let component: DeliveryTypesPage;
  let fixture: ComponentFixture<DeliveryTypesPage>;
  let httpService: HttpService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DeliveryTypesPage],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        HttpService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    httpService = TestBed.inject(HttpService);
    fixture = TestBed.createComponent(DeliveryTypesPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute loadMore', fakeAsync(() => {
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    component.loadMore({ target: spy });
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
      expect(component.loadMore).toBeTruthy();
    });
  }));
  it('execute loadDelivery', () => {
    component.loadDelivery();
    expect(component.loadDelivery).toBeDefined();
  });
  it('execute searchDelivery', () => {
    component.searchDelivery({ text: 'test' });
    expect(component.searchDelivery).toBeDefined();
  });
  it('execute selectAction', () => {
    const event = { item: { courierTicketNumber: '1', id: '1' } };
    component.selectAction(event);
    expect(component.selectAction).toBeDefined();
  });
  it('execute pullRefresh', () => {
    component.pullRefresh('');
    expect(component.pullRefresh).toBeDefined();
  });
  it('execute getDelivery', fakeAsync(() => {
    const response = { deliveryData: 'test', totalCount: 100 };
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.getDelivery(true, { target: spy });
    expect(component.getDelivery).toBeDefined();
  }));
  it('execute getDelivery : else', fakeAsync(() => {
    const response = { deliveryData: '', totalCount: 100 };
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.getDelivery();
    expect(component.getDelivery).toBeDefined();
  }));
  it('execute getDelivery : throw error', fakeAsync(() => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.getDelivery();
    expect(component.getDelivery).toBeDefined();
  }));
});
