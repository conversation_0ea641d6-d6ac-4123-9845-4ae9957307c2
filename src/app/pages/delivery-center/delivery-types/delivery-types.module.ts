import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { DeliveryTypesPageRoutingModule } from './delivery-types-routing.module';

import { DeliveryTypesPage } from './delivery-types.page';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';
import { DataGridModule } from 'src/app/components/data-grid/data-grid.module';
import { DataListModule } from 'src/app/components/data-list/data-list.module';
import { SearchBarModule } from 'src/app/components/search-bar/search-bar.module';
import { SelectListGridModule } from 'src/app/components/select-list-grid/select-list-grid.module';
import { SharedModule } from 'src/app/shared.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    DeliveryTypesPageRoutingModule,
    SharedModule,
    SearchBarModule,
    DataListModule,
    SelectListGridModule,
    ActionButtonModule,
    DataGridModule
  ],
  declarations: [DeliveryTypesPage]
})
export class DeliveryTypesPageModule {}
