import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { APIs } from 'src/app/constants/apis';
import { Constants } from 'src/app/constants/constants';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { deepCopyJSON, isBlank, isPresent } from 'src/app/utils/utils';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { DomSanitizer } from '@angular/platform-browser';
import { Activity } from 'src/app/constants/activity';
import { DeliveryData, DeliveryResponse } from 'src/app/interfaces/common-interface';

@Component({
  selector: 'app-delivery-details',
  templateUrl: './delivery-details.page.html',
  styleUrls: ['./delivery-details.page.scss']
})
export class DeliveryDetailsPage {
  deliveryData: DeliveryData[] = [];
  hideSigninDeliveryBtn = false;
  constants = Constants;
  ticketNumber: string;
  selectedDeliveryId: string;
  deliveryDetails: DeliveryData;
  constructor(
    private readonly sharedService: SharedService,
    private readonly httpService: HttpService,
    private readonly route: ActivatedRoute,
    private inAppBrowser: InAppBrowser,
    private readonly sanitizer: DomSanitizer
  ) {
    this.route.paramMap.subscribe((paramMap) => {
      this.ticketNumber = paramMap.get('courierTicketNumber');
      this.selectedDeliveryId = paramMap.get('id');
      if (this.ticketNumber && this.selectedDeliveryId) {
        this.getDeliveryDetails();
      }
    });
  }
  getDeliveryDetails(): void {
    const body = {
      offset: Constants.defaultOffset,
      selectedDeliveryId: this.selectedDeliveryId,
      selectedDeliveryTicket: this.ticketNumber,
      limit: Constants.defaultLimit,
      orderData: Constants.courierDateTime,
      orderby: Constants.sortOrderDesc,
      inApp: 0
    };
    this.httpService.doGet({ endpoint: APIs.getDeliveryDetails, extraParams: body }).subscribe((response: DeliveryResponse) => {
      if (!isBlank(response)) {
        this.deliveryData = response?.deliveryData.map((item) => ({
          ...item,
          courierStatus: item.courierStatus.charAt(0).toUpperCase() + item.courierStatus.slice(1).toLowerCase()
        }));
        if (isBlank(this.deliveryData[0]?.signatureURL)) {
          this.hideSigninDeliveryBtn = true;
        }
        if (isPresent(this.deliveryData[0])) {
          this.deliveryDetails = deepCopyJSON(this.deliveryData[0]);
        }
      }
    });
  }

  redirectToURL(signDelivery = false): void {
    if (this.sharedService.platform.is('capacitor')) {
      const formUrl: any = this.sanitizer.bypassSecurityTrustResourceUrl(unescape(this.deliveryDetails.courierTrackingURL));
      this.inAppBrowser.create(formUrl.changingThisBreaksApplicationSecurity, '_system', {
        location: 'no'
      });
    }
    if (signDelivery) {
      this.sharedService.trackActivity({
        type: Activity.signDelivery,
        name: Activity.deliverySign,
        des: {
          data: {
            signatureURL: this.deliveryDetails.signatureURL
          },
          desConstant: Activity.signDeliveryDes
        }
      });
    } else {
      this.sharedService.trackActivity({
        type: Activity.courierUrlForTicket,
        name: Activity.courierTrackingUrl,
        des: {
          data: {
            courierTrackingURL: this.deliveryDetails.courierTrackingURL
          },
          desConstant: Activity.courierTrackingDes
        }
      });
    }
  }
}
