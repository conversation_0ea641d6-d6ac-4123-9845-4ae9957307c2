import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { DeliveryDetailsPage } from 'src/app/pages/delivery-center/delivery-details/delivery-details.page';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { DeliveryData } from 'src/app/interfaces/common-interface';

describe('DeliveryDetailsPage', () => {
  let component: DeliveryDetailsPage;
  let fixture: ComponentFixture<DeliveryDetailsPage>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DeliveryDetailsPage],
      imports: [IonicModule.forRoot(), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        InAppBrowser,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(DeliveryDetailsPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call getDeliveryDetails function', () => {
    component.getDeliveryDetails();
    expect(component.getDeliveryDetails).toBeDefined();
  });

  it('should call redirectTocourierTrackingURL function', () => {
    component.deliveryDetails = <DeliveryData>{ courierTrackingURL: 'test', signatureURL: 'test' };
    component.redirectToURL();
    expect(component.redirectToURL).toBeDefined();
  });

  it('should call redirectTosignatureURL function', () => {
    component.deliveryDetails = <DeliveryData>{ courierTrackingURL: 'test', signatureURL: 'test' };
    component.redirectToURL(true);
    expect(component.redirectToURL).toBeDefined();
  });
});
