<app-header headerTitle="TITLES.DELIVERY_DETAILS"></app-header>
<ion-content class="common-page-layout">
  <ion-grid>
    <ion-row>
      <ion-col>
        <ion-text color="medium">{{ 'LABELS.TICKET_NUMBER' | translate }} :{{ ticketNumber }}</ion-text>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-text color="medium">
          {{ 'LABELS.TRACKING_NUMBER' | translate }} :
          <a class="tracking-number-link" id="tracking-number-link" (click)="redirectToURL()"> {{ deliveryDetails?.courierTrackingNumber }}</a>
        </ion-text>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-text color="medium">{{ 'LABELS.COURIER_STATUS' | translate }} :{{ deliveryDetails?.courierStatus }} </ion-text>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-text color="medium">{{ 'LABELS.MRN' | translate }} :{{ deliveryDetails?.mrn }} </ion-text>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-button *ngIf="!hideSigninDeliveryBtn" id="sign-delivery" class="btn ion-text-capitalize" color="medium" (click)="redirectToURL(true)">
          {{ 'BUTTONS.SIGN_THE_DELIVERY' | translate }}
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
  <ion-grid>
    <ion-row class="row">
      <ion-col class="ion-text-bold"> {{ 'GENERAL.STATUS' | translate }} </ion-col>
      <ion-col class="ion-text-bold"> {{ 'GENERAL.DATE_TIME' | translate }} </ion-col>
      <ion-col class="ion-text-bold"> {{ 'GENERAL.MESSAGES' | translate }} </ion-col>
    </ion-row>
    <ion-row class="row" *ngFor="let data of deliveryData; trackByIdentifier: 'id'; let i = index">
      <ion-col>
        <ion-text color="medium">{{ data.courierStatus }}</ion-text>
      </ion-col>
      <ion-col>
        <ion-text color="medium">{{
          data.courierDateTime ? (data.courierDateTime * 1000 | date: constants?.dateFormat?.mmddhmaComma) : data.courierDateTime
        }}</ion-text>
      </ion-col>
      <ion-col>
        <ion-text color="medium">{{ data.messageText }}</ion-text>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
<app-footer></app-footer>
