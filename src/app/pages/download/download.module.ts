import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { DownloadPageRoutingModule } from './download-routing.module';

import { DownloadPage } from './download.page';
import { SharedModule } from 'src/app/shared.module';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, SharedModule, DownloadPageRoutingModule, HeaderPlainModule],
  declarations: [DownloadPage]
})
export class DownloadPageModule {}
