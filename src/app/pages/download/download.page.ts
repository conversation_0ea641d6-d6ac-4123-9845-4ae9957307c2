import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Activity } from 'src/app/constants/activity';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { deepParseJSON, isPresent } from 'src/app/utils/utils';

@Component({
  selector: 'app-download',
  templateUrl: './download.page.html',
  styleUrls: ['./download.page.scss']
})
export class DownloadPage implements OnInit {
  fileName = '';
  downloadLink: string;
  fetchCompleted = false;
  showButton = false;
  cardTitle = 'MESSAGES.INVALID_LINK';
  cardContent = 'MESSAGES.FILE_DOES_NOT_EXISTS';
  constructor(private httpService: HttpService, private router: Router, private sharedService: SharedService) {}
  ngOnInit(): void {
    const state = this.router.getCurrentNavigation()?.extras?.state;
    if (isPresent(state?.downloadLink)) {
      const downloadLink = state.downloadLink;
      this.fileName = state?.fileName || '';
      const extraParams = state?.extraParams || {};
      const isExternalLink = state?.isExternalLink || false;
      this.fetchDownloadUrl(downloadLink, extraParams, isExternalLink);
    } else {
      this.fetchCompleted = true;
      this.cardTitle = 'MESSAGES.NOTHING_TO_SHOW_HERE';
      this.cardContent = '';
    }
  }
  fetchDownloadUrl(endpoint: string, extraParams: Record<string, string> = {}, isExternalLink = false): void {
    this.sharedService.trackActivity({
      type: Activity.download,
      name: Activity.downloadFetch,
      des: { desConstant: Activity.downloadFetchDes, data: { endpoint, params: JSON.stringify(extraParams) } }
    });
    this.httpService.doFetchFile({ endpoint, extraParams, isExternalLink }).subscribe(
      (response) => {
        const blobData = response.body;
        response.body
          .text()
          .then((blobContent) => {
            const result = deepParseJSON(blobContent);
            if (!result && blobData.type !== 'text/html') {
              this.downloadLink = URL.createObjectURL(blobData);
            } else {
              this.sharedService.trackActivity({
                type: Activity.download,
                name: Activity.downloadFetchFail,
                des: {
                  desConstant: Activity.downloadFetchFailDes,
                  data: { endpoint, params: JSON.stringify(extraParams), error: JSON.stringify(result) }
                }
              });
            }
            this.fetchCompleted = true;
          })
          .catch((error) => {
            this.sharedService.trackActivity({
              type: Activity.download,
              name: Activity.downloadFetchFail,
              des: {
                desConstant: Activity.downloadFetchFailBeforeParseDes,
                data: { endpoint, params: JSON.stringify(extraParams) }
              }
            });
            this.fetchCompleted = true;
          });
      },
      (error) => {
        this.fetchCompleted = true;
      }
    );
  }
}
