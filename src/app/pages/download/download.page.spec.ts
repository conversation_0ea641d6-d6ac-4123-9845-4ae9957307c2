import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { DownloadPage } from './download.page';
import { HttpService } from 'src/app/services/http-service/http.service';
import { HttpClientModule, HttpResponse } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { of, throwError } from 'rxjs';

describe('DownloadPage', () => {
  let component: DownloadPage;
  let fixture: ComponentFixture<DownloadPage>;
  let httpService: HttpService;
  let router: Router;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DownloadPage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      providers: [
        HttpService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    router = TestBed.inject(Router);
    fixture = TestBed.createComponent(DownloadPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  afterEach(() => document.querySelector('ion-content').remove());
  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute ngOnInit', () => {
    spyOn(router, 'getCurrentNavigation').and.returnValue({
      extras: { state: { downloadLink: 'endpoint' } }
    } as any);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute fetchDownloadUrl', () => {
    const response = new HttpResponse<Blob>();
    spyOn(httpService, 'doFetchFile').and.returnValue(of(response));
    component.fetchDownloadUrl('');
    expect(component.fetchDownloadUrl).toBeTruthy();
  });
  it('execute fetchDownloadUrl:error', () => {
    spyOn(httpService, 'doFetchFile').and.returnValue(throwError(''));
    component.fetchDownloadUrl('');
    expect(component.fetchDownloadUrl).toBeTruthy();
  });
});
