<app-header-plain headerTitle="BUTTONS.DOWNLOAD" [showButton]="showButton"></app-header-plain>
<ion-content [fullscreen]="true" class="centered-content">
  <ion-grid class="content-grid">
    <ion-row *ngIf="fetchCompleted" class="content-row">
      <ion-button class="common-button download-button" color="de-york" [download]="fileName" *ngIf="downloadLink" [href]="downloadLink" id="click-to-download">
        <ion-icon name="cloud-download-outline" slot="start" class="download-icon" ></ion-icon>
        <ion-label class="break-spaces" slot="end" translate>MESSAGES.DOWNLOAD_FILE</ion-label>
      </ion-button>
      <ion-card *ngIf="!downloadLink" class="invalid-card">
        <ion-icon name="warning-outline" color="warning" class="warning-icon"></ion-icon>
        <ion-card-header>
          <ion-card-title>
            <ion-label translate>{{cardTitle}}</ion-label>
          </ion-card-title>
        </ion-card-header>
        <ion-card-content>
        </ion-card-content>
        <ion-card-content translate>{{cardContent}}</ion-card-content>
      </ion-card>
    </ion-row>
  </ion-grid>
</ion-content>
<app-footer [showBackButton]="false"></app-footer>