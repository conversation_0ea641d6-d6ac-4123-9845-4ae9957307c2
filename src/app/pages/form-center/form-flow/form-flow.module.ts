import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FormFlowPageRoutingModule } from './form-flow-routing.module';

import { FormFlowPage } from './form-flow.page';
import { SharedModule } from '../../../shared.module';

import { RecipientsComponentModule } from '../recipients/recipients.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        FormFlowPageRoutingModule,
        SharedModule,
        RecipientsComponentModule
    ],
    declarations: [FormFlowPage]
})
export class FormFlowPageModule { }
