<app-header headerTitle="LABELS.CHOOSE_FORM_FLOW"></app-header>
<ion-content class="select-forms-flow">
  <ion-list>
    <ion-item></ion-item>
    <ion-item class="item" (click)="chooseFormFlow('form')" tappable>
      <ion-label translate>LABELS.FORM_DRIVEN</ion-label>
      <ion-checkbox class="common-checkbox" slot="end" mode="ios" id="select-form-driven" [(ngModel)]="formDrivenFlow"> </ion-checkbox>
    </ion-item>
    <ion-item class="item" (click)="chooseFormFlow('patient')" tappable>
      <ion-label translate>LABELS.PATIENT_DRIVEN</ion-label>
      <ion-checkbox class="common-checkbox" slot="end" mode="ios" id="select-patient-driven" [(ngModel)]="patientDrivenFlow"> </ion-checkbox>
    </ion-item>
  </ion-list>
</ion-content>
<app-footer backButtonLink="form-center"></app-footer>
