import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { FormFlowPage } from './form-flow.page';
import { Config } from 'src/app/constants/config';
import { PageRoutes } from 'src/app/constants/page-routes';
import { PermissionGuard } from 'src/app/services/permission-guard/permission.guard';

const routes: Routes = [
  {
    path: '',
    component: FormFlowPage,
    canActivate: [PermissionGuard],
    data: {
      permissions: {
        only: Config.enablePatientDrievenFlowForms,
        redirectTo: PageRoutes.sendForms
      }
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class FormFlowPageRoutingModule {}
