import { Component } from '@angular/core';
import { CommonService } from 'src/app/services/common-service/common.service';
import { RecipientsComponent } from '../recipients/recipients.component';
import { ModalController } from '@ionic/angular';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { PageRoutes } from 'src/app/constants/page-routes';

@Component({
  selector: 'app-form-flow',
  templateUrl: './form-flow.page.html',
  styleUrls: ['./form-flow.page.scss']
})
export class FormFlowPage {
  formType;
  formDrivenFlow = false;
  patientDrivenFlow = false;
  constructor(
    private readonly common: CommonService,
    private readonly modalController: ModalController,
    private readonly router: Router,
    private readonly route: ActivatedRoute
  ) {
    this.route.queryParams.subscribe(() => {
      if (this.router.getCurrentNavigation()?.extras.state) {
        this.formType = this.router.getCurrentNavigation()?.extras.state.formType;
      }
    });
  }

  chooseFormFlow(flow: string): void {
    switch (flow) {
      case 'form':
        this.formDrivenFlow = true;
        this.patientDrivenFlow = false;
        this.formDriven();
        break;
      case 'patient':
        this.patientDrivenFlow = true;
        this.formDrivenFlow = false;
        this.patientDriven();
        break;
      default:
        this.formDriven();
        break;
    }
  }
  formDriven(): void {
    const navigationExtras: NavigationExtras = {
      state: {
        formType: this.formType
      }
    };
    this.router.navigate([PageRoutes.sendForms], navigationExtras);
  }
  async patientDriven(): Promise<void> {
    const modal = await this.modalController.create({
      component: RecipientsComponent,
      componentProps: {
        drivenBy: 'patient',
        associatedPatient: true
      },
      id: 'choose-recipient'
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        const navigationExtras: NavigationExtras = {
          state: {
            patient: data,
            formType: this.formType
          }
        };
        this.router.navigate([PageRoutes.sendForms], navigationExtras);
      }
    });
    this.patientDrivenFlow = false;
    return await modal.present();
  }
  ionViewWillLeave() {
    this.formDrivenFlow = false;
    this.patientDrivenFlow = false;
  }
}
