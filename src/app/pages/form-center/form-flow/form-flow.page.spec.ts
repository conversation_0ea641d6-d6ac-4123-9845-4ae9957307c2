
import { TranslateModule } from '@ngx-translate/core';

import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';

import { FormFlowPage } from './form-flow.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('FormFlowPage', () => {
  let component: FormFlowPage;
  let fixture: ComponentFixture<FormFlowPage>;
  const modalSpy = jasmine.createSpyObj('Modal', ['onDidDismiss', 'present']);
  let modalController: ModalController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [FormFlowPage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), TranslateModule.forRoot()],
      providers: [ModalController],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    fixture = TestBed.createComponent(FormFlowPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should call chooseFormFlow', () => {
    component.chooseFormFlow('');
    expect(component.chooseFormFlow).toBeTruthy();
  });
  it('chooseFormFlow function check patient driven flow', () => {
    component.chooseFormFlow('patient');
    expect(component.chooseFormFlow).toBeTruthy();
  });
  it('chooseFormFlow function check form driven flow', () => {
    component.chooseFormFlow('form');
    expect(component.chooseFormFlow).toBeTruthy();
  });
  it('should call patientDriven', () => {
    component.patientDriven();
    expect(component.patientDriven).toBeDefined();
  });
  it('should call ionViewWillLeave', () => {
    component.ionViewWillLeave();
    expect(component.ionViewWillLeave).toBeDefined();
  });
});
