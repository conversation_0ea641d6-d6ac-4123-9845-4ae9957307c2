import { Component, OnInit } from '@angular/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { APIs } from 'src/app/constants/apis';
import { isBlank, formatDate, isPresent } from 'src/app/utils/utils';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NavParams, ModalController, NavController } from '@ionic/angular';
import { Constants, IntegrationType } from 'src/app/constants/constants';
import { UntypedFormGroup, UntypedFormBuilder, UntypedFormArray, UntypedFormControl } from '@angular/forms';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Config } from 'src/app/constants/config';
import { Activity } from 'src/app/constants/activity';
import { AddVirtualPatientComponent } from 'src/app/pages/user/add-virtual-patient/add-virtual-patient.component';
import { PageRoutes } from 'src/app/constants/page-routes';
import { CompletedDocumentRecipientsComponent } from 'src/app/components/completed-document-recipients/completed-document-recipients.component';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { catchError, forkJoin, of, zip } from 'rxjs';
import { getValueFromLocalStorage } from 'src/app/utils/storage-utils';
import { AdmissionComponent } from 'src/app/components/admission/admission.component';
import { UserService } from 'src/app/services/user-service/user.service';

@Component({
  selector: 'app-recipients',
  templateUrl: './recipients.component.html',
  styleUrls: ['./recipients.component.scss']
})
export class RecipientsComponent implements OnInit {
  userData: any;
  tagDetails: any;
  recipients: any = [];
  formFacing: string;
  formType: string;
  tagId: number;
  headerTitle: string;
  pageCount = Constants.defaultPageCount;
  showLoadMore: boolean;
  selectRecipientForm: UntypedFormGroup;
  form: any = {};
  formId: number;
  formName: string;
  defaultMsgTitle: string;
  mrn: string;
  searchKeyword: string;
  addPatient = false;
  drivenBy: string;
  selectedAssociatedPatient: any;
  isBlank: any;
  defaultMsg: string;
  multipleRecipientMsg: any;
  constants: any;
  tagRecipient: any = [];
  appendTags = true;
  filteredListData: any = [];
  patientDrivenReqBody = {
    siteIds: [0]
  };
  errorMessage = '';
  createPatientLinkMessage = '';
  isLoadMore = false;
  isStaffFilling = false;
  isEnableMultiSite = false;
  isEnableMultiAdmissions = false;
  interactionChannel: string;
  selectedItem: any = {};
  storeSelectedValue = [];
  storeSelectedData: any = [];
  alternateSelectedId: string;
  associatedPatient: boolean;
  recipientsCompletedDocumentIDs = '';
  admissionId;
  siteLabel = '';
  selectedRecipientsForDisplay = [];
  constructor(
    private readonly httpService: HttpService,
    private readonly sharedService: SharedService,
    private readonly navParams: NavParams,
    private readonly modalController: ModalController,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly common: CommonService,
    private readonly persistentService: PersistentService,
    private readonly userService: UserService,
    private navController: NavController
  ) {
    this.userData = this.sharedService.userData;
    this.formFacing = this.navParams.get('formFacing');
    this.formType = this.navParams.get('formType');
    this.tagId = this.navParams.get('tagId');
    this.form = this.navParams.get('form');
    this.drivenBy = this.navParams.get('drivenBy');
    this.selectedAssociatedPatient = this.navParams.get('selectedAssociatedPatient');
    this.formId = this.form ? this.form.id : '';
    this.formName = this.form ? this.form.name : '';
    this.selectRecipientForm = this.formBuilder.group({
      recipientList: new UntypedFormArray([]),
      alternateContactsList: new UntypedFormArray([])
    });
    this.defaultMsgTitle = 'MESSAGES.ARE_YOU_SURE';
    this.defaultMsg = 'MESSAGES.SINGLE_RECIPIENT';
    this.multipleRecipientMsg = 'MESSAGES.MULTIPLE_RECIPIENTS';
    this.mrn = this.common.getTranslateData('GENERAL.MRN');
    this.isBlank = isBlank;
    this.constants = Constants;
    this.isStaffFilling = Boolean(this.form?.staffFill);
    this.isEnableMultiSite = this.sharedService.isEnableConfig(Config.enableMultiSite);
    this.isEnableMultiAdmissions = this.sharedService.isEnableConfig(Config.enableMultiAdmissions);
    this.recipientsCompletedDocumentIDs = this.navParams.get('copyOfRecipient');
  }

  ngOnInit(): void {
    if (this.drivenBy === Constants.patientValue) {
      this.headerTitle = 'TITLES.SELECT_ASSOCIATED_PATIENT';
      this.getAssociatedPatients();
      this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITE');
    } else if (
      this.formFacing === Constants.staffValue ||
      (this.formFacing === Constants.practitioner && isBlank(this.selectedAssociatedPatient) && this.associatedPatient)
    ) {
      this.addPatient = true;
      this.headerTitle = 'TITLES.CHOOSE_ASSOCIATED_PATIENT';
      this.getAssociatedPatients();
      this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITE');
    } else {
      this.headerTitle = 'TITLES.CHOOSE_RECIPIENTS';
      this.errorMessage = this.common.getTranslateData('MESSAGES.SEARCH_FOR_RECIPIENTS');
      if (this.formFacing === Constants.patientValue) {
        this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITE');
      } else this.siteLabel = 'LABELS.SITE';
    }
  }

  addRecipientToDisplay(
    displayName: string,
    id: string,
    recipientType: 'tag' | 'patient' | 'staff' | 'alternate contact',
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    additionalData?: any
  ): void {
    // Check if recipient is already in the display array
    const existingIndex = this.selectedRecipientsForDisplay.findIndex((item) => item.id === id);

    if (existingIndex === -1) {
      // Add new recipient to display array
      const displayItem = {
        displayName,
        id,
        recipientType,
        isUserTag: recipientType === 'tag',
        isAlternateContact: recipientType === 'alternate contact',
        ...additionalData
      };

      this.selectedRecipientsForDisplay.push(displayItem);
    }
  }
  removeRecipientFromSelection(recipient, index: number): void {
    if (index >= 0 && index < this.selectedRecipientsForDisplay.length) {
      this.selectedRecipientsForDisplay.splice(index, 1);
    }
    const recipientId = recipient.id;
    const storeIndex = this.storeSelectedValue.indexOf(recipientId);
    if (storeIndex !== -1) {
      this.storeSelectedValue.splice(storeIndex, 1);
    }

    let mainRecipientIndex = -1;

    if (recipient.recipientType === 'tag') {
      // For tags, find by tag userId (format: tag-{id})
      mainRecipientIndex = this.recipients.findIndex((r) => r.userId === recipientId || r.userid === recipientId);
    } else if (recipient.recipientType !== 'alternate contact') {
      // For regular patients/staff, find by userId, userid, or caregiver_userid
      mainRecipientIndex = this.recipients.findIndex(
        (r) => r.userId === recipientId || r.userid === recipientId || r.caregiver_userid === recipientId
      );
    }
    if (mainRecipientIndex !== -1) {
      this.recipients[mainRecipientIndex].checked = false;
      const recipientListControl = this.selectRecipientForm.get('recipientList') as UntypedFormArray;
      if (recipientListControl && recipientListControl.at(mainRecipientIndex)) {
        recipientListControl.at(mainRecipientIndex).setValue(false);
      }
    }
  }
  private getRecipientsObservable() {
    const selectedSiteIds =
      this.patientDrivenReqBody && this.patientDrivenReqBody.siteIds ? this.patientDrivenReqBody.siteIds.toString() : Constants.defaultSiteId;

    const body = {
      isTenantRoles: this.formFacing === Constants.staffValue ? undefined : true,
      roleId: this.userData?.roleId,
      pageCount: this.pageCount,
      tagsId: this.tagId,
      formRecipients: Constants.formRecipients,
      searchKeyword: this.searchKeyword,
      siteIds: this.associatedPatient && this.formFacing === Constants.practitioner ? this.selectedAssociatedPatient.siteId : selectedSiteIds,
      nursingAgencies: this.userData?.nursing_agencies || '',
      reoleSearchNeeded: false,
      status: Constants.notRejected,
      admissionId: this.sharedService.isMultiAdmissionsEnabled ? this.navParams.get('admissionId') : undefined,
      ...(this.sharedService.isEnableConfig(Config.enableApplessModel) && { needVirtualPatients: true })
    };
    const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    if (body.siteIds === Constants.defaultSiteId && isPresent(localSelectedSites)) {
      body.siteIds = JSON.parse(localSelectedSites).toString();
    } else if (isBlank(body.siteIds)) {
      body.siteIds = Constants.defaultSiteId;
    }
    return this.sharedService.getUsersByTenantRoleWithTagId(body).pipe(
      catchError((error) => {
        this.sharedService.errorHandler(error);
        return of([]);
      })
    );
  }

  private getTagDetailsObservable() {
    return this.httpService
      .doPost({
        endpoint: APIs.getTagDetails,
        payload: {},
        extraParams: {
          tagId: this.tagId,
          searchKeyword: this.searchKeyword || ''
        },
        contentType: 'form',
        loader: true
      })
      .pipe(
        catchError((error) => {
          this.sharedService.errorHandler(error);
          return of([]);
        })
      );
  }
  getUsersByTenantRoleWithTagId(): void {
    this.showLoadMore = false;
    const recipientsObservable = this.getRecipientsObservable();
    const tagDetailsObservable =
      this.tagId && this.formFacing === Constants.patientValue && !this.isEnableMultiAdmissions ? this.getTagDetailsObservable() : of([]);

    forkJoin([recipientsObservable, tagDetailsObservable]).subscribe({
      next: ([recipients, tagDetails]) => {
        if (!isBlank(recipients)) {
          this.errorMessage = '';
          this.showLoadMore = recipients.length >= Constants.defaultLimit;
          const recipientsData = recipients?.map((r) => ({
            ...r,
            sitename: r?.sitename ? r.sitename : r.siteName,
            displayDob: !isBlank(r?.dob) ? formatDate(r?.dob, Constants.dateFormat.mmddyy) : '',
            displayName: `${this.common.getPatientDisplayName(r, Constants.patientValue)}`,
            checked: this.storeSelectedValue?.includes(r?.userId) ?? false,
            isNotContactable: !this.userService.isUserContactable(r),
            isContactNotOptedIn: !this.userService.isContactOptedIn(r),
            alternateContacts: !isBlank(r?.alternateContacts)
              ? r.alternateContacts.map((altUsr) => ({
                  ...altUsr,
                  checked: this.isAlternateContactChecked(altUsr?.userId, r?.userid),
                  isNotContactable: !this.userService.isUserContactable(altUsr),
                  isContactNotOptedIn: !this.userService.isContactOptedIn(altUsr),
                  contactDisplayName: this.common.getAlternateContactDisplayName(altUsr)
                }))
              : []
          }));
          this.recipients = this.recipients.concat(recipientsData);
        }
        this.tagDetails = tagDetails ?? [];
        this.appendUserTags();
      }
    });
  }
  loadRecipientsAndTags(): void {
    this.recipients = [];
    if (
      this.formFacing === Constants.staffValue ||
      this.drivenBy === Constants.patientValue ||
      (this.formFacing === Constants.practitioner && isBlank(this.selectedAssociatedPatient) && this.associatedPatient)
    ) {
      this.getAssociatedPatients();
    } else {
      this.getUsersByTenantRoleWithTagId();
    }
  }
  appendUserTags(): void {
    if (this.appendTags && this.formFacing === Constants.patientValue && !isBlank(this.tagDetails)) {
      const patientTags = this.tagDetails.map((r) => ({
        ...r,
        displayName: !isBlank(r.tag_name) ? `${r.tag_name} [${this.common.getTranslateData('GENERAL.USER_TAG')}]` : '',
        userId: `tag-${r.id}`,
        checked: this.storeSelectedValue?.includes(`tag-${r.id}`) ?? false,
        isUserTag: true
      }));
      const combinedList = patientTags.concat(this.recipients);
      this.recipients = combinedList;
      this.createForm(combinedList);
    } else {
      this.createForm(this.recipients);
    }
    this.errorMessage = isBlank(this.recipients) ? this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND') : '';
  }

  goBack(): void {
    this.modalController.dismiss();
  }

  getAssociatedPatients(): void {
    this.showLoadMore = false;
    const payload = {
      pageCount: this.pageCount,
      tagId: this.tagId,
      searchKeyword: this.searchKeyword,
      siteIds: this.patientDrivenReqBody && this.patientDrivenReqBody.siteIds ? this.patientDrivenReqBody.siteIds.toString() : Constants.defaultSiteId
    };
    const localSelectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    if (payload.siteIds === Constants.defaultSiteId && isPresent(localSelectedSites)) {
      payload.siteIds = JSON.parse(localSelectedSites).toString();
    } else if (isBlank(payload.siteIds)) {
      payload.siteIds = Constants.defaultSiteId;
    }

    this.sharedService.getAssociatedPatientsByTagId(payload).subscribe(
      (recipients) => {
        if (!isBlank(recipients)) {
          if (recipients.status === 401) {
            this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
            return false;
          }
          this.errorMessage = '';
          this.createPatientLinkMessage = '';
          this.showLoadMore = recipients.length >= Constants.defaultLimit;
          const recipientsData = recipients.map((r) => ({
            ...r,
            displayDob: !isBlank(r.dob) ? formatDate(r.dob, Constants.dateFormat.mmddyy) : '',
            displayName: this.common.getPatientDisplayName(r, Constants.patientValue, Constants.displayNamePatientCaregiverFormat)
          }));
          this.recipients = this.recipients.concat(recipientsData);
          this.createForm(this.recipients);
        } else if (isBlank(this.recipients)) {
          this.errorMessage = this.userService.showCommonMessage(this.addPatient, '', false);
          this.createPatientLinkMessage = this.addPatient ? this.common.getTranslateData('MESSAGES.TO_CREATE_NEW_PATIENT') : '';
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
        this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
      }
    );
  }
  loadMore(): void {
    this.appendTags = false;
    this.pageCount++;
    this.formFacing === Constants.staffValue ||
      this.drivenBy === Constants.patientValue ||
      (this.formFacing === Constants.practitioner && isBlank(this.selectedAssociatedPatient) && this.associatedPatient)
      ? this.getAssociatedPatients()
      : this.getUsersByTenantRoleWithTagId();
  }

  createForm(list: []): void {
    const controls = list.map((c: any) => new UntypedFormControl(c.checked || false));
    this.selectRecipientForm = this.formBuilder.group({
      recipientList: new UntypedFormArray(controls),
      alternateContactsList: new UntypedFormArray(controls)
    });
  }
  clearAll(value?: string): void {
    this.selectRecipientForm.reset();
    this.alternateSelectedId = undefined;
    if (this.formType === Constants.formTypes.offline && this.isEnableMultiAdmissions) {
      this.recipients.map(
        (item: { checked: boolean; admissionId: string; admissionName: string; admissionSiteName: string; admissionSiteId: string }) => {
        item.checked = false;
        item.admissionId = '';
        item.admissionName = '';
        item.admissionSiteName = '';
        item.admissionSiteId = '';
        return item;
      });
    } else {
      this.recipients.map((item) => (item.checked = false));
    }
    if (value === 'ButtonClick') {
      this.storeSelectedValue = [];
    }
    this.selectedRecipientsForDisplay = [];
  }
  get isDisabled(): boolean {
    return this.storeSelectedValue.length === 0;
  }
  sendPractitionerForm(formFacing: string): void {
    const selectedRecipient = this.storeSelectedData.filter((user: any) => {
      return user.userid === this.filteredListData[0];
    });
    if (this.form.staffFill === 'true' && this.filteredListData.length <= 1) {
      if (this.formFacing === Constants.practitioner) {
        this.modalController.dismiss({
          form: {
            name: this.formName,
            id: this.formId,
            populatePreviousSubmission: this.form.populatePreviousSubmission,
            confirmActionPrefill: this.form.confirmActionPrefill,
            staffFilling: this.form.staffFill,
            admissionId: this.admissionId
          },
          recipient: selectedRecipient[0],
          associatedPatient: this.selectedAssociatedPatient,
          facing: formFacing,
          interactionChannel: this.interactionChannel,
          copyOfRecipient: this.recipientsCompletedDocumentIDs
        });
      }
    } else {
      const message = this.filteredListData.length <= 1 ? this.defaultMsg : this.multipleRecipientMsg;
      const alertData = {
        message,
        header: this.defaultMsgTitle
      };
      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation) {
          // Send form here.
          this.sendForm();
          const ids = [];
          if (this.filteredListData.length) {
            this.filteredListData.forEach((value) => {
              ids.push(`(${value})`);
            });
          }
          this.sharedService.trackActivity({
            type: Activity.forms,
            name: Activity.sendForm,
            des: {
              data: {
                displayName: this.userData.displayName,
                formName: this.formName,
                formId: this.formId,
                recipientId: ids.join(',')
              },
              desConstant: this.filteredListData.length <= 1 ? Activity.sendFormDes : Activity.sendFormMultipleUsersDes
            }
          });
        }
      });
    }
  }

  downloadFormRecipients(formFacing: string): void {
    let filteredListArray = [];
    let filteredRecipientsWithAdmission = [];
    if (this.storeSelectedValue.length > 0) {
      filteredListArray = [...new Set([...this.filteredList, ...this.storeSelectedValue])];
    } else {
      filteredListArray = this.filteredList;
    }
    if (this.isEnableMultiAdmissions) {
      filteredRecipientsWithAdmission = this.recipients.filter((item: any) => item?.checked);
    }
    if (filteredListArray.length > 0) {
      const alertData = {
        message: filteredListArray.length === 1 ? 'MESSAGES.SINGLE_OFFLINE_DOWNLOAD_RECIPIENT' : 'MESSAGES.MULTIPLE_OFFLINE_DOWNLOAD_RECIPIENTS',
        header: this.defaultMsgTitle
      };

      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation) {
          const forms = [];
          filteredListArray.forEach((recipientId: any, index: number) => {
            const params = {
              form_id: this.formId,
              tenant_id: this.userData.tenantId,
              recipientId,
              admissionId: this.isEnableMultiAdmissions ? filteredRecipientsWithAdmission[index]?.admissionId : undefined
            };
            forms.push(this.httpService.doGet({ endpoint: APIs.getHtmlForm, extraParams: params, loader: true, useBaseAs: 'forms' }));
          });
          this.sharedService.loaderMessage = filteredListArray.length === 1 ? 'LOADER_MESSAGES.FILE_IS_DOWNLOADING' : 'LOADER_MESSAGES.FILES_ARE_DOWNLOADING';
          zip(...forms).subscribe((res) => {
            res.forEach((form, index) => {
              this.common.renderIframe(form);
              const userData = this.recipients.find((recipient) => {
                let userId = '';
                if (recipient.role === Constants.roleName.caregiver) {
                  userId = recipient.caregiver_userid;
                } else if (recipient.userId) {
                  userId = recipient.userId;
                } else {
                  userId = `tag-${recipient.id}`;
                }
                return userId === filteredListArray[index];
              });
              let formData = {
                firstName: '',
                lastName: '',
                site: this.isEnableMultiAdmissions ? userData?.admissionSiteName : userData.sitename,
                dob: '',
                mrn: '',
                id: filteredListArray[index],
                formName: this.formName,
                formContent: form,
                admissionId: this.isEnableMultiAdmissions ? userData?.admissionId : '',
                admissionName: this.isEnableMultiAdmissions ? userData?.admissionName : ''
              };
              if (!filteredListArray[index].startsWith('tag-')) {
                formData = {
                  ...formData,
                  firstName: userData.role === Constants.roleName.caregiver ? userData.c_fname : userData.firstname,
                  lastName: userData.role === Constants.roleName.caregiver ? userData.c_lname : userData.lastname,
                  dob: userData.role === Constants.roleName.caregiver ? userData.caregiver_dob : userData.dob,
                  mrn: userData.role === Constants.roleName.caregiver ? userData.caregiverIdentityValue : userData.IdentityValue
                };
              }
              const siteId = this.isEnableMultiAdmissions ? userData?.admissionSiteId : userData.siteId;
              this.persistentService.addOfflineForm(`${this.formId}`, siteId.toString(), JSON.stringify(formData)).then(() => {
                if (index === res.length - 1) {
                  this.sharedService.loaderMessage = '';
                  this.common.showMessage(this.common.getTranslateData('MESSAGES.OFFLINE_DOWNLOAD_SUCCESSFULLY'));
                  this.goBack();
                  this.navController.navigateForward(PageRoutes.offlineForms);
                }
              });
            });
          });
        }
      });
    }
  }

  sendFormRecipients(formFacing: string): void {
    const appLessMode = Boolean(this.sharedService.isEnableConfig(Config.enableApplessModel));
    let filteredListArray = [];

    if (this.storeSelectedValue.length > 0) {
      filteredListArray = [...new Set([...this.filteredList, ...this.storeSelectedValue])];
    } else {
      filteredListArray = this.filteredList;
    }
    if (filteredListArray.length <= 1 && appLessMode && this.selectedItem.passwordStatus && !this.selectedItem.isContactNotOptedIn) {
      let buttons = [];
      const applessButtonText = [this.common.getTranslateData('BUTTONS.APP_LESS'), this.common.getTranslateData('BUTTONS.MAGIC_LINK')].join(' ');
      if (this.selectedItem && !this.selectedItem.passwordStatus) {
        buttons = [{ text: applessButtonText, confirm: true, id: 'app-less' }];
      } else {
        buttons = [
          { text: applessButtonText, confirm: true, role: 'done', id: 'app-less' },
          { text: this.common.getTranslateData('BUTTONS.IN_APP'), confirm: false, role: 'done', id: 'in-app' }
        ];
        this.common
          .showAlert({
            message: 'MESSAGES.SEND_FORM_VIA',
            buttons,
            cssClass: 'send-form-via-modal',
            backDrop: true
          })
          .then((confirmation) => {
            this.interactionChannel = confirmation ? Constants.appless : Constants.mobileapp;
            this.sendFormToInAppLess(formFacing);
          });
      }
    } else {
      this.sendFormToInAppLess(formFacing);
    }
  }

  sendFormToInAppLess(formFacing: string) {
    const filteredListArray = [...new Set([...this.filteredList, ...this.storeSelectedValue])];
    this.filteredListData = filteredListArray;
    if (formFacing === Constants.practitioner) {
      const ids = [];
      if (this.filteredListData.length) {
        this.filteredListData.forEach((value) => {
          ids.push(`(${value})`);
        });
      }
      const activityData = {
        displayName: this.userData.displayName,
        recipientId: ids.join(','),
        recipientName: '',
        formName: this.formName,
        formId: this.formId
      };
      if (this.associatedPatient) {
        activityData.recipientName = this.selectedAssociatedPatient.displayname;
        this.sharedService.trackActivity({
          type: Activity.forms,
          name: Activity.selectRecipient,
          des: { data: activityData, desConstant: Activity.selectRecipientDes }
        });
      } else if (this.filteredListData.length <= 1 && this.selectedItem) {
        activityData.recipientName = this.selectedItem.displayname;
        this.sharedService.trackActivity({
          type: Activity.forms,
          name: Activity.selectRecipient,
          des: { data: activityData, desConstant: Activity.selectRecipientDes }
        });
      }
      this.sendPractitionerForm(formFacing);
    } else {
      const tagRecipients = [];
      const nonTagRecipients = [];

      this.filteredListData.forEach((value) => {
        if (typeof value === 'string' && value.includes('tag-')) {
          tagRecipients.push(value.replace('tag-', ''));
        } else {
          nonTagRecipients.push(value);
        }
      });

      this.tagRecipient = tagRecipients;
      this.filteredListData = nonTagRecipients;

      if (filteredListArray.length <= 1 && this.tagRecipient.length < 1) {
        // Check integration status if only one recipient selected.
        // TODO : Check activity tracking based on in app and appless.

        this.checkIntegrationStatus();
        this.sharedService.trackActivity({
          type: Activity.forms,
          name: Activity.sendForm,
          des: {
            data: {
              displayName: this.userData.displayName,
              formName: this.formName,
              formId: this.formId,
              recipientId: filteredListArray
            },
            desConstant: Activity.sendFormDes
          }
        });
      } else if (filteredListArray.length > 1 || this.tagRecipient.length >= 1) {
        const alertData = {
          message: this.multipleRecipientMsg,
          header: this.defaultMsgTitle
        };
        this.common.showAlert(alertData).then((confirmation) => {
          if (confirmation) {
            this.sendForm();
            const ids = [];
            filteredListArray.forEach((value) => {
              ids.push(`(${value})`);
            });
            const data = {
              displayName: this.userData.displayName,
              formName: this.formName,
              formId: this.formId,
              recipientId: ids.join(',')
            };
            this.sharedService.trackActivity({
              type: Activity.forms,
              name: Activity.sendApplessForm,
              des: { data, desConstant: Activity.sendApplessFormDes }
            });
          }
        });
      }
    }
  }
  get filteredList(): [] {
    if (!this.selectRecipientForm?.value?.recipientList) {
      return [];
    }
    return this.selectRecipientForm.value.recipientList
      .map((v: any, i: number) =>
        v
          ? isPresent(this.recipients[i]?.caregiver_userid)
            ? this.recipients[i].caregiver_userid
            : this.recipients[i]?.userId
              ? this.recipients[i].userId
              : `tag-${this.recipients[i]?.id}`
          : null
      )
      .filter((v: any) => v !== null);
  }

  // Form Facing = 'patientValue', 'staffFacing'
  checkIntegrationStatus(): void {
    let alterNateContactId;
    if (this.storeSelectedValue.length === 1) {
      if (this.storeSelectedValue[0].includes('--')) {
        const getVal: any = this.storeSelectedValue[0].split('--');
        alterNateContactId = getVal.length ? getVal[1] : '';
      } else {
        alterNateContactId = this.storeSelectedValue[0];
      }
    }
    let patient = [];
    patient = this.filteredList.length ? this.filteredList : alterNateContactId ? [alterNateContactId] : this.filteredList;
    if (!isBlank(patient) && !this.sharedService.isEnableConfig(Config.enableSftp)) {
      if (this.form.staffFill === 'true') {
        this.viewPatientForm(patient);
        return;
      }
      // sftp Integration not enabled check.
      const body = {
        patient_id: patient[0],
        form_id: this.formId,
        admissionId: this.isEnableMultiAdmissions ? this.admissionId : undefined,
        action: IntegrationType.FORM_SUBMIT
      };
      this.sharedService.isLoading = true;
      this.httpService.doPost({ endpoint: APIs.getIntegrationStatus, payload: body, loader: false, skipErrorHandling: true }).subscribe(
        () => {
          this.sharedService.isLoading = false;
          // Send form here.
          this.viewPatientForm(patient);
        },
        (error: any) => {
          this.sharedService.isLoading = false;
          this.common
            .showAlert({
              message: `<center>
              <strong>${error.status.message}</strong> <br/><br/> ${this.common.getTranslateData('MESSAGES.YOU_CAN_CONTINUE_ANYWAY')}.
              </center>`,
              header: '',
              cssClass: 'common-alert visit-alert ',
              buttons: [
                {
                  text: this.common.getTranslateData('BUTTONS.GO_BACK')
                },
                {
                  text: this.common.getTranslateData('BUTTONS.CONTINUE_ANYWAY')
                }
              ]
            })
            .then((confirmation) => {
              if (confirmation) {
                this.viewPatientForm(patient);
              }
          });
        }
      );
    } else {
      const alertData = {
        message: this.defaultMsg,
        header: this.defaultMsgTitle
      };
      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation) {
          // Send form here.
          this.viewPatientForm(patient);
        }
      });
    }
  }
  viewPatientForm(patient: any): void {
    if (this.form.staffFill === 'true') {
      const recipient = this.recipients.filter((user: any) => {
        return user.userid === patient[0];
      });

      if (this.formFacing === Constants.patientValue || this.formFacing === Constants.staffValue) {
        this.openCompletedDocumentRecipientsModal(recipient, (returnCallBackData: any) => {
          if (
            (!returnCallBackData && !this.form.sendCompletedForm) ||
            returnCallBackData === 'SKIP' ||
            (returnCallBackData && this.form.sendCompletedForm)
          ) {
            this.modalController.dismiss({
              form: {
                name: this.formName,
                id: this.formId,
                populatePreviousSubmission: this.form.populatePreviousSubmission,
                confirmActionPrefill: this.form.confirmActionPrefill,
                staffFilling: this.form.staffFill,
                externalFileExchange: this.form.externalFileExchange,
                alternateSelectedId: this.alternateSelectedId,
                admissionId: this.isEnableMultiAdmissions ? this.admissionId : undefined
              },
              recipient: recipient[0],
              facing: this.formFacing,
              interactionChannel: this.interactionChannel,
              copyOfRecipient: returnCallBackData === 'SKIP' ? '' : returnCallBackData || ''
            });
          } else {
            this.recipientsCompletedDocumentIDs = '';
            this.clearAll('ButtonClick');
          }
        });
      }
    } else {
      const message = this.filteredListData.length > 1 || this.tagRecipient.length >= 1 ? this.multipleRecipientMsg : this.defaultMsg;
      const alertData = {
        message,
        header: this.defaultMsgTitle
      };
      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation) {
          this.sendForm();
        }
      });
    }
  }

  openCompletedDocumentRecipientsModal(recipient: any, callBack: any) {
    if (this.form.sendCompletedForm) {
      this.modalController
        .create({
          component: CompletedDocumentRecipientsComponent,
          componentProps: {
            recipient: recipient[0],
            formFacing: this.formFacing,
            pageName: Constants.copyDocumentPageName.formCenter,
            isSkipButtonShow: true,
            admissionId: this.admissionId
          }
        })
        .then((modal: any) => {
          modal.present();
          modal.onDidDismiss().then(({ data }) => {
            callBack(data);
          });
        });
    } else {
      callBack();
    }
  }

  getStatusWarning(statusData: any): any {
    if (
      !isBlank(statusData.data) &&
      statusData.status.integrationStatus === Constants.enabled &&
      statusData.status.statusMessage === Constants.success
    ) {
      if (statusData.data.defaultFromFilingCenterSubmitjson && statusData.data.progressNoteIntegration) {
        return this.sharedService.showWarningMessages(statusData.data.patientIdentity, statusData.data.staffId, Constants.pageType.recipients);
      }
      if (statusData.data.defaultFromFilingCenterSubmit && this.sharedService.isEnableConfig(Config.showFaxQueueWarning)) {
        return this.sharedService.showWarningMessages(statusData.data.patientIdentity, statusData.data.staffId, Constants.pageType.recipients);
      }
    }
    return this.sharedService.setMsgTitle('', Constants.pageType.recipients);
  }
  sendForm(): void {
    let staffFacingValue = 'false';
    if (this.formFacing === Constants.staffValue) {
      staffFacingValue = 'true';
    } else if (this.formFacing === Constants.practitioner) {
      staffFacingValue = 'practitioner';
    }
    const body = {
      form_id: this.formId,
      recipients: this.filteredListData ?? [],
      tagRecipients: this.tagRecipient?.length > 0 ? this.tagRecipient.join(',') : '',
      message: '',
      formSubmissionId: 0,
      siteIds: this.sharedService.userSiteIds.join(','),
      formSendMode: '',
      applessMode: this.sharedService.isEnableConfig(Config.enableApplessModel) ? Constants.applessDevices : '',
      staffFacing: staffFacingValue,
      sendForm: 1,
      savedDraftID: 0,
      bottombtn: 0,
      callbackURL: '',
      formUniqueId: this.sharedService.guidCreate(this.formId),
      formSendingSource:
        this.filteredListData.length > 1 || this.tagRecipient.length > 0 ? 'Desktop-patient-MultipleUser' : 'Desktop-patient-SingleUser',
      formType: this.formFacing,
      useragent: 'mobile',
      practitioner: undefined as number | undefined,
      practitionerAssociatePatient: undefined as string[] | undefined
    };
    if (this.formFacing === Constants.practitioner && this.associatedPatient && this.selectedAssociatedPatient?.userid) {
      body.practitioner = 1;
      body.practitionerAssociatePatient = [this.selectedAssociatedPatient.userid];
    }
    let message = '';
    this.sharedService.sendFormToRecipients(body).subscribe({
      next: (response) => {
        message =
          response?.success === 'true'
            ? this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_HAS_BEEN_SENT_SUCCESSFULLY', { form: this.formName })
            : this.common.getTranslateDataWithParam('ERROR_MESSAGES.FORM_SENDING_FAILED', { form: this.formName });
        if (response?.success === 'true') {
          this.clearAll();
          this.modalController.dismiss();
          this.common.redirectToPage(PageRoutes.pendingForms);
          this.sharedService.formPolling(response);
        }
      },
      error: () => {
        message = this.common.getTranslateDataWithParam('ERROR_MESSAGES.FORM_SENDING_FAILED', { form: this.formName });
      },
      complete: () => {
        this.common.showMessage(message);
      }
    });
  }

  alternateSelect(altIndex: number, index: number): void {
    const recipient = this.recipients[index];
    const selectedItem = recipient.alternateContacts[altIndex];
    if (selectedItem.isNotContactable) {
      this.showUserNotContactableMessage();
      return;
    }
    const id = `${selectedItem.userId}--${recipient.userid}`;
    if (this.storeSelectedValue.includes(id)) {
      this.storeSelectedValue.splice(this.storeSelectedValue.indexOf(id), 1);
      const displayIndex = this.selectedRecipientsForDisplay.findIndex((item) => item.id === id);
      if (displayIndex !== -1) {
        this.selectedRecipientsForDisplay.splice(displayIndex, 1);
      }
    } else {
      this.selectedItem = { ...selectedItem, passwordStatus: selectedItem.password, userid: selectedItem.userId };
      this.alternateSelectedId = id;
      this.storeSelectedValue.push(id);
      const displayName = selectedItem.contactDisplayName;
      this.addRecipientToDisplay(displayName, id, 'alternate contact', {
        parentRecipientId: recipient.userid || recipient.userId,
        alternateContactId: selectedItem.userId,
        alternateContactIndex: altIndex
      });
    }
  }
  alternateChecked(altIndex: number, index: number): boolean {
    const recipient = this.recipients[index];
    const alternateContact = recipient?.alternateContacts[altIndex];
    return this.isAlternateContactChecked(alternateContact?.userId, recipient?.userid);
  }
  getFormattedACId(contactId: number, recipientId: number): string {
    return `${contactId}--${recipientId}`;
  }
  isAlternateContactChecked(contactId: number, recipientId: number): boolean {
    const id = this.getFormattedACId(contactId, recipientId);
    return this.storeSelectedValue.includes(id);
  }
  showUserNotContactableMessage() {
    this.common.showToast({
      message: 'ERROR_MESSAGES.USER_CANT_BE_SELECTED',
      color: 'dark'
    });
  }
  viewForm(facing: string, recipient: any, form: any, index?: number, altIndex?: number) {
    let user = recipient;
    if (!isBlank(altIndex)) {
      user = recipient.alternateContacts[altIndex];
    }
    if (user.isNotContactable) {
      this.showUserNotContactableMessage();
      return;
    }
    this.selectedItem = { ...user, passwordStatus: user.password };
    if (this.recipients[index]) {
      this.recipients[index].checked = !this.recipients[index].checked;
    }
    let updateSelection = false;
    if (!isBlank(altIndex)) {
      this.alternateSelectedId = `${recipient.alternateContacts[altIndex].userId}--${recipient.userid}`;
    }
    const selectedSiteIds = this.patientDrivenReqBody && this.patientDrivenReqBody.siteIds ? this.patientDrivenReqBody.siteIds : [];
    if (this.formType !== Constants.formTypes.offline && facing !== Constants.patientValue) {
      if (isBlank(this.selectedAssociatedPatient) && this.associatedPatient) {
        if (this.isEnableMultiAdmissions) {
          this.admissionId = '';
          this.selectAdmission(recipient.userId, selectedSiteIds, (response: any) => {
            if (!isBlank(response.admissionId)) {
              this.openCompletedDocumentRecipientsModal([recipient], (returnCallBackData: any) => {
                if (
                  (!returnCallBackData && !this.form.sendCompletedForm) ||
                  returnCallBackData === 'SKIP' ||
                  (returnCallBackData && this.form.sendCompletedForm)
                ) {
                  
                  this.modalController.dismiss({
                    form: { ...form, admissionId: this.admissionId, alternateSelectedId: this.alternateSelectedId },
                    recipient,
                    facing,
                    tagId: this.tagId,
                    interactionChannel: this.interactionChannel,
                    copyOfRecipient: returnCallBackData === 'SKIP' ? '' : returnCallBackData || '',
                  });
                } else {
                  this.recipientsCompletedDocumentIDs = '';
                  this.clearAll('ButtonClick');
                }
              });
            } else {
              this.clearAll('ButtonClick');
            }
          });
        } else if (facing === Constants.staffValue || facing === Constants.practitioner) {
          this.openCompletedDocumentRecipientsModal([recipient], (returnCallBackData: any) => {
            if (
              (!returnCallBackData && !this.form.sendCompletedForm) ||
              returnCallBackData === 'SKIP' ||
              (returnCallBackData && this.form.sendCompletedForm)
            ) {
              this.modalController.dismiss({
                form: { ...form, alternateSelectedId: this.alternateSelectedId },
                recipient,
                facing,
                tagId: this.tagId,
                interactionChannel: this.interactionChannel,
                copyOfRecipient: returnCallBackData === 'SKIP' ? '' : returnCallBackData || ''
              });
            } else {
              this.recipientsCompletedDocumentIDs = '';
              this.clearAll('ButtonClick');
            }
          });
        } else if (this.drivenBy === Constants.patientValue) {
          this.modalController.dismiss({
            form: { ...form, alternateSelectedId: this.alternateSelectedId },
            recipient,
            facing,
            tagId: this.tagId,
            interactionChannel: this.interactionChannel
          });
        }
      } else if (facing === Constants.practitioner) {
        updateSelection = true;
      }
    } else {
      updateSelection = true;
      const userID = this.getSelectedUserID(this.recipients[index]);
      // set compained id and user id if role is caregiver
      if (isPresent(this.recipients[index].caregiver_userid)) {
        this.recipients[index].userid = this.recipients[index].caregiver_userid;
        this.recipients[index].compainedId = `${this.recipients[index].userId}--${this.recipients[index].caregiver_userid}`;
      }

      if (
        this.isEnableMultiAdmissions &&
        (this.formType === Constants.formTypes.offline || facing === Constants.patientValue) &&
        this.recipients[index].checked
      ) {
        this.admissionId = '';
        this.selectAdmission(recipient.userId, selectedSiteIds, (admission: any) => {
          if (!isBlank(admission.admissionId)) {
            this.recipients[index].admissionId = admission.admissionId;
            this.recipients[index].admissionName = admission.admissionName;
            if (this.formType === Constants.formTypes.offline) {
              this.recipients[index].admissionSiteId = admission.siteId;
              this.recipients[index].admissionSiteName = admission.siteName;
            }
            if (facing === Constants.patientValue) {
              this.sendFormRecipients(facing);
            }
            if (isBlank(admission.admissionId)) {
              this.selectRecipientForm.value.recipientList[index] = false;
              this.selectRecipientForm.patchValue({
                recipientList: this.selectRecipientForm.value.recipientList
              });
              this.storeSelectedValue.splice(this.storeSelectedValue.indexOf(userID), 1);
              this.recipients[index].checked = false;
            }
          } else {
            this.clearAll('ButtonClick');
          }
        });
      }
    }
    if (updateSelection) {
      const userID = this.getSelectedUserID(this.recipients[index]);
      if (this.recipients[index].checked) {
        if (isBlank(altIndex)) this.alternateSelectedId = undefined;
        this.storeSelectedValue.push(userID);
        if (facing === Constants.practitioner) {
          this.storeSelectedData.push(this.recipients[index]);
        }

        // Add to display array
        const currentRecipient = this.recipients[index];
        let displayName = '';
        let recipientType: 'tag' | 'patient' | 'staff' | 'alternate contact' = 'patient';
        if (currentRecipient.isUserTag) {
          displayName = currentRecipient.displayName;
          recipientType = 'tag';
        } else if (!isBlank(altIndex)) {
          const altContact = currentRecipient.alternateContacts[altIndex];
          displayName = altContact?.contactDisplayName;
          recipientType = 'alternate contact';
        } else {
          displayName = currentRecipient.displayName;
          recipientType = currentRecipient.role === Constants.roleName.caregiver ? 'patient' : 'staff';
        }
        this.addRecipientToDisplay(displayName, userID, recipientType, {
          recipient: currentRecipient,
          alternateContactIndex: altIndex
        });
      } else {
        this.storeSelectedValue.splice(this.storeSelectedValue.indexOf(userID), 1);
        if (facing === Constants.practitioner) {
          this.storeSelectedData = this.storeSelectedData.filter((selectedData) => selectedData.id !== userID);
        }
        if (this.formType === Constants.formTypes.offline && this.isEnableMultiAdmissions) {
          this.recipients[index].admissionId = '';
          this.recipients[index].admissionName = '';
          this.recipients[index].admissionSiteName = '';
          this.recipients[index].admissionSiteId = '';
        }
        const displayIndex = this.selectedRecipientsForDisplay.findIndex((item) => item.id === userID);
        if (displayIndex !== -1) {
          this.selectedRecipientsForDisplay.splice(displayIndex, 1);
        }
      }
    }
  }
  searchOperations(action: any): void {
    if (action.do === 'search' || action.do === 'reset') {
      if (action.do === 'reset' && this.formFacing !== Constants.patientValue) {
        this.storeSelectedValue = []; // TODO: Clear selected recipients on reset only
        this.selectedRecipientsForDisplay = [];
      }
      this.showLoadMore = false;
      this.appendTags = true;
      this.selectRecipientForm.reset();
      this.alternateSelectedId = undefined;
      this.recipients.forEach((item) => {
        // eslint-disable-next-line no-param-reassign
        item.checked = false;
      });
      this.recipients = [];
      this.searchKeyword = action.value;
      this.pageCount = Constants.defaultPageCount;
      if (!isBlank(this.searchKeyword)) {
        this.errorMessage = '';
        this.loadRecipientsAndTags();
      } else {
        this.errorMessage = this.common.getTranslateData('MESSAGES.SEARCH_FOR_RECIPIENTS');
        this.recipients = [];
        this.tagDetails = null;
      }
      if (action.do === 'search') {
        switch (this.formFacing) {
          case 'staff': {
            const data = {
              displayName: this.userData.displayName,
              searchKey: this.searchKeyword,
              formName: this.formName,
              id: this.formId
            };
            this.sharedService.trackActivity({
              type: Activity.forms,
              name: Activity.patientSearch,
              des: { data, desConstant: Activity.patientSearchDes }
            });
            break;
          }
          case 'patient':
            this.sharedService.trackActivity({
              type: Activity.forms,
              name: Activity.searchRecipients,
              des: {
                data: {
                  displayName: this.userData.displayName,
                  keyWord: this.searchKeyword
                },
                desConstant: Activity.searchRecipientsDes
              }
            });
            break;
          default:
            break;
        }
      }
    } else if (action.do === 'add') {
      this.showAddPatient();
    }
  }
  async showAddPatient(): Promise<void> {
    const page = 'formCenter';
    const modal = await this.modalController.create({
      component: AddVirtualPatientComponent,
      componentProps: {
        form: this.form,
        tagId: this.tagId,
        formFacing: this.formFacing,
        page
      },
      id: 'add-patient'
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        this.modalController.dismiss(data, null, 'choose-recipient');
      }
    });
    return await modal.present();
  }

  loadData() {
    //TODO! CHP-3598
    this.loadMore();
  }
  filterSitesData(data: []): void {
    this.pageCount = Constants.defaultPageCount;
    this.recipients = [];
    this.patientDrivenReqBody.siteIds = data;
    this.formFacing === Constants.staffValue ||
      this.drivenBy === Constants.patientValue ||
      (this.formFacing === Constants.practitioner && isBlank(this.selectedAssociatedPatient) && this.associatedPatient)
      ? this.getAssociatedPatients()
      : this.getUsersByTenantRoleWithTagId();
  }

  loadSitesData(data: []): void {
    this.patientDrivenReqBody.siteIds = data;
  }

  selectAdmission(userId: any, selectedSiteIds, callBack) {
    const params = {
      userId,
      siteIds: selectedSiteIds
    };
    this.sharedService.selectAdmission(params, undefined, AdmissionComponent, (admission: any) => {
      let admissionValue = {
        admissionId: '',
        admissionName: '',
        siteId: '',
        siteName: '',
      };
      if (admission) {
        this.admissionId = admission.admissionId;
        admissionValue = JSON.parse(JSON.stringify(admission));
      } else {
        this.admissionId = '';
      }
      callBack(admissionValue);
    });
  }

  getSelectedUserID(item: any) {
    if (item.role === Constants.roleName.caregiver) {
      return item.caregiver_userid;
    }
    return item.userId ? item.userId : item.userid;
  }
}
