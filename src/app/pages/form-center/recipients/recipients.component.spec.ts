import { UntypedForm<PERSON>rray, UntypedFormControl, UntypedFormGroup, UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';
import { RecipientsComponent } from 'src/app/pages/form-center/recipients/recipients.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { TestConstants } from 'src/app/constants/test-constants';
import { OrderByPipe } from 'src/app/pipes/order-by/order-by.pipe';
import { Constants } from 'src/app/constants/constants';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';

import { Config } from 'src/app/constants/config';

describe('RecipientsComponent', () => {
  let component: RecipientsComponent;
  let fixture: ComponentFixture<RecipientsComponent>;
  const mockData: any = [558, 669];
  let navParams: NavParams;
  let sharedService: SharedService;
  let common: CommonService;
  let httpService: HttpService;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  const testData = [
    {
      alternateContacts: [{ userId: '1553167' }],
      role: 'Patient',
      userId: '121371'
    }
  ];
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [RecipientsComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule
      ],
      providers: [
        OrderByPipe,
        ModalController,
        HttpService,
        CommonService,
        NavParams,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        NativeStorage,
        SQLite,
        PersistentService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    navParams = TestBed.inject(NavParams);
    sharedService = TestBed.inject(SharedService);
    sharedService.localConfig = TestConstants.localConfig;
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    spyOn(sharedService, 'trackActivity').and.stub();
    spyOn(navParams, 'get').and.returnValue({ form: { staffFill: true } });
    common = TestBed.inject(CommonService);
    spyOn(common, 'showMessage').and.stub();
    spyOn(common, 'redirectToPage').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    fixture = TestBed.createComponent(RecipientsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('filterSitesData function should be defined', () => {
    component.filterSitesData(mockData);
    expect(component.filterSitesData).toBeTruthy();
  });
  it('execute sendFormRecipients', () => {
    component.userData = { displayName: '' };
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray([{ userId: '1' }].map((c) => new UntypedFormControl(c)))
    });
    component.recipients = [{ userId: '1' }];
    component.selectedItem = { passwordStatus: true };
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendFormRecipients('');
    expect(component.sendFormRecipients).toBeTruthy();
  });
  it('execute sendFormRecipients', () => {
    component.userData = { displayName: '' };
    const data = [{ userId: '1' }, { userId: 'tag-' }];
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray(data.map((c) => new UntypedFormControl(c)))
    });
    component.recipients = data;
    component.selectedItem = { passwordStatus: true };
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendFormRecipients('');
    expect(component.sendFormRecipients).toBeTruthy();
  });
  describe('sendForm', () => {
    it('should send form successfully when formFacing is patient', () => {
      component.formFacing = 'patient';
      spyOn(sharedService, 'sendFormToRecipients').and.returnValue(of({ success: 'true' }));
      component.sendForm();
      expect(component.sendForm).toBeTruthy();
    });

    it('should handle error when sendForm fails for patient', () => {
      component.formFacing = 'patient';
      spyOn(sharedService, 'sendFormToRecipients').and.returnValue(throwError(() => ''));
      component.sendForm();
      expect(component.sendForm).toBeTruthy();
    });

    it('should send form successfully when formFacing is staff with interactionChannel appless', () => {
      component.formFacing = 'staff';
      spyOn(sharedService, 'sendFormToRecipients').and.returnValue(of({ success: 'true' }));
    component.interactionChannel = 'appless';
    component.sendForm();
    expect(component.sendForm).toBeTruthy();
  });

    it('should send form successfully when formFacing is practitioner with associatedPatient', () => {
      component.formFacing = 'practitioner';
      component.associatedPatient = true;
      component.selectedAssociatedPatient = { userid: '1' };
      spyOn(sharedService, 'sendFormToRecipients').and.returnValue(of({ success: 'true' }));
      component.interactionChannel = 'appless';
      component.sendForm();
      expect(component.sendForm).toBeTruthy();
    });
  });

  it('execute viewForm', () => {
    component.selectedAssociatedPatient = [];
    component.viewForm('', '', '');
    expect(component.viewForm).toBeTruthy();
  });
  it('execute searchOperations: staff', () => {
    component.formFacing = 'staff';
    component.searchOperations({ do: 'search' });
    expect(component.searchOperations).toBeTruthy();
  });
  it('execute searchOperations: patient', () => {
    component.formFacing = 'patient';
    component.searchOperations({ do: 'search' });
    expect(component.searchOperations).toBeTruthy();
  });
  it('execute searchOperations: add', () => {
    component.searchOperations({ do: 'add' });
    expect(component.searchOperations).toBeTruthy();
  });
  it('execute searchOperations: reset', () => {
    component.searchOperations({ do: 'reset' });
    expect(component.searchOperations).toBeTruthy();
  });
  it('execute loadData', fakeAsync(() => {
    component.loadData();
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.loadData).toBeTruthy();
  }));
  it('execute showAddPatient', fakeAsync(() => {
    component.showAddPatient();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.showAddPatient).toBeTruthy();
  }));
  it('execute getStatusWarning: submitjson', () => {
    component.getStatusWarning({
      data: { defaultFromFilingCenterSubmitjson: true, progressNoteIntegration: true },
      status: { statusMessage: 'Success', integrationStatus: 'Enabled' }
    });
    expect(component.getStatusWarning).toBeTruthy();
  });
  it('execute getStatusWarning: submit', () => {
    component.getStatusWarning({
      data: { defaultFromFilingCenterSubmit: true, progressNoteIntegration: true },
      status: { statusMessage: 'Success', integrationStatus: 'Enabled' }
    });
    expect(component.getStatusWarning).toBeTruthy();
  });
  it('execute getStatusWarning: default', () => {
    component.getStatusWarning({
      data: {},
      status: {}
    });
    expect(component.getStatusWarning).toBeTruthy();
  });
  it('execute viewPatientForm:  staffFill true', () => {
    component.form = { staffFill: 'true' };
    component.recipients = [{ userId: 11 }];
    component.viewPatientForm([11]);
    expect(component.viewPatientForm).toBeTruthy();
  });
  it('execute viewPatientForm: staffFill false', () => {
    component.form = { staffFill: 'false' };
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.viewPatientForm([11]);
    expect(component.viewPatientForm).toBeTruthy();
  });
  it('execute checkIntegrationStatus: alternate contact', fakeAsync(() => {
    component.storeSelectedValue = ['43--44'];
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  }));

  it('execute checkIntegrationStatus', fakeAsync(() => {
    component.storeSelectedValue = ['43'];
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  }));
  it('execute checkIntegrationStatus: api throw error', async () => {
    component.storeSelectedValue = ['43'];
    spyOn(httpService, 'doPost').and.returnValue(throwError(''));
    spyOn(common, 'showAlert').and.resolveTo(true);
    await component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  });

  it('execute checkIntegrationStatus: not recipient', fakeAsync(() => {
    component.storeSelectedValue = [];
    component.recipients = [];
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  }));
  it('execute checkIntegrationStatus: with patients', () => {
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray([{ userId: 1 }].map((c) => new UntypedFormControl(c)))
    });
    component.recipients = [{ userId: 'tag-abc' }];
    spyOn(common, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    component.checkIntegrationStatus();
    expect(component.checkIntegrationStatus).toBeTruthy();
  });

  it('should handle alternate contact id', () => {
    component.form = { staffFill: 'false' };
    component.storeSelectedValue = ['1--2'];
    sharedService.isEnableConfig = () => false;
    spyOn(httpService, 'doPost').and.returnValue(of({}));
    spyOn(component, 'viewPatientForm');
    component.checkIntegrationStatus();
    expect(httpService.doPost).toHaveBeenCalledWith(jasmine.objectContaining({ payload: jasmine.objectContaining({ patient_id: '2' }) }));
    expect(component.viewPatientForm).toHaveBeenCalled();
  });

  it('execute appendUserTags: patient', () => {
    component.formFacing = 'patient';
    component.appendTags = true;
    component.tagDetails = [{}];
    component.appendUserTags();
    expect(component.appendUserTags).toBeTruthy();
  });
  it('execute appendUserTag: not patient', () => {
    component.appendUserTags();
    expect(component.appendUserTags).toBeTruthy();
  });
  it('execute goBack', () => {
    component.goBack();
    expect(component.goBack).toBeTruthy();
  });
  it('execute sendFormToInAppLess', () => {
    const data = [{ userId: '1' }, { userId: '2' }, { userId: 'tag-12' }];
    component.selectRecipientForm = new UntypedFormGroup({
      recipientList: new UntypedFormArray(data.map((c) => new UntypedFormControl(c)))
    });
    component.recipients = data;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendFormToInAppLess('');
    expect(component.sendFormToInAppLess).toBeTruthy();
  });
  it('execute sendPractitionerForm: practitioner facing', () => {
    component.filteredListData = [3];
    component.storeSelectedData = [{ userid: 3 }];
    component.form = { staffFill: 'true' };
    component.formFacing = Constants.practitioner;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.sendPractitionerForm('');
    expect(component.sendPractitionerForm).toBeTruthy();
  });
  describe('sendPractitionerForm', () => {
    it('should execute sendPractitionerForm with multiple recipients', fakeAsync(() => {
      component.filteredListData = ['3', '4'];
      component.storeSelectedData = [{ userid: '3' }];
      component.form = { staffFill: 'true' };
      component.formFacing = Constants.practitioner;
      component.recipientsCompletedDocumentIDs = '';
      spyOn(common, 'showAlert').and.resolveTo(true);
      component.sendPractitionerForm('');
      expect(component.sendPractitionerForm).toBeTruthy();
    }));

    it('should execute sendPractitionerForm with form data', () => {
      const data = [{ userId: '1' }];
      component.selectRecipientForm = new UntypedFormGroup({
        recipientList: new UntypedFormArray(data.map((c) => new UntypedFormControl(c)))
      });
      component.recipients = data;
      component.form = { staffFill: 'true' };
      component.sendPractitionerForm('');
      expect(component.sendPractitionerForm).toBeTruthy();
    });
  });

  describe('sendFormToInAppLess', () => {
    it('should execute sendFormToInAppLess for practitioner', () => {
      const data = [{ userId: '1' }, { userId: '2' }, { userId: 'tag-12' }];
      component.selectRecipientForm = new UntypedFormGroup({
        recipientList: new UntypedFormArray(data.map((c) => new UntypedFormControl(c)))
      });
      component.recipients = data;
      spyOn(common, 'showAlert').and.resolveTo(true);
      component.sendFormToInAppLess('practitioner');
      expect(component.sendFormToInAppLess).toBeTruthy();
    });
  });
  describe('getUsersByTenantRoleWithTagId', () => {
    let expectedBody;
    beforeEach(() => {
      expectedBody = {
        isTenantRoles: true,
        roleId: component.userData?.roleId,
        pageCount: component.pageCount,
        tagsId: component.tagId,
        formRecipients: Constants.formRecipients,
        searchKeyword: component.searchKeyword,
        siteIds: '0',
        nursingAgencies: '',
        reoleSearchNeeded: false,
        status: Constants.notRejected,
        admissionId: undefined
      };
    });
    it('execute getUsersByTenantRoleWithTagId', fakeAsync(() => {
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([{ id: 1, userId: '1' }]));
      spyOn(common, 'getPatientDisplayName').and.returnValue('Test User');
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(component.getUsersByTenantRoleWithTagId).toBeTruthy();
    }));
    it('execute getUsersByTenantRoleWithTagId: no items', fakeAsync(() => {
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(component.getUsersByTenantRoleWithTagId).toBeTruthy();
    }));
    it('execute getUsersByTenantRoleWithTagId: throw error', fakeAsync(() => {
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(throwError(() => 'error'));
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(component.getUsersByTenantRoleWithTagId).toBeTruthy();
    }));
    it('should include needVirtualPatients when Config.enableApplessModel is enabled', fakeAsync(() => {
      sharedService.userData.config[Config.enableApplessModel] = '1';
      expectedBody.needVirtualPatients = true;
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalledOnceWith(expectedBody);
    }));

    it('should not include needVirtualPatients when Config.enableApplessModel is disabled', fakeAsync(() => {
      sharedService.userData.config[Config.enableApplessModel] = '0';
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalledOnceWith(expectedBody);
    }));

    it('should call getUsersByTenantRoleWithTagId and handle response', fakeAsync(() => {
      component.formFacing = Constants.patientValue;
      sharedService.userData.config[Config.enableApplessModel] = '0';
      const eachObj: any = {
        status: 7,
        userid: '528',
        userId: '528',
        passwordStatus: false,
        countryCode: '+1',
        mobile: '111111111',
        email: '<EMAIL>',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        enable_email_notifications: '0',
        // eslint-disable-next-line @typescript-eslint/naming-convention
        enable_sms_notifications: '0',
        isNotContactable: true,
        alternateContacts: [{}]
      };
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([eachObj]));
      spyOn(common, 'getPatientDisplayName').and.returnValue('Test User');
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalled();
    }));

    it('should include nursingAgencies when userData has nursing_agencies', fakeAsync(() => {
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const nursing_agencies = 12345;
      component.userData.nursing_agencies = nursing_agencies;
      expectedBody.nursingAgencies = nursing_agencies;
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalledOnceWith(expectedBody);
    }));

    it('should include nursingAgencies with empty value when userData does not have nursing_agencies', fakeAsync(() => {
      component.userData.nursing_agencies = '';
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalledOnceWith(expectedBody);
    }));

    it('should not fetch tag details when multi-admission is enabled', fakeAsync(() => {
      component.tagId = 123;
      component.formFacing = Constants.patientValue;
      component.isEnableMultiAdmissions = true;
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      spyOn(httpService, 'doPost').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalled();
      expect(httpService.doPost).not.toHaveBeenCalled();
    }));

    it('should fetch tag details when multi-admission is disabled and conditions are met', fakeAsync(() => {
      component.tagId = 123;
      component.formFacing = Constants.patientValue;
      component.isEnableMultiAdmissions = false;
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      spyOn(httpService, 'doPost').and.returnValue(of([]));
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalled();
      expect(httpService.doPost).toHaveBeenCalled();
    }));

    it('should handle error in getTagDetailsObservable catch block', fakeAsync(() => {
      component.tagId = 123;
      component.formFacing = Constants.patientValue;
      component.isEnableMultiAdmissions = false;
      spyOn(sharedService, 'getUsersByTenantRoleWithTagId').and.returnValue(of([]));
      spyOn(httpService, 'doPost').and.returnValue(throwError(() => 'tag details error'));
      spyOn(sharedService, 'errorHandler');
      component.getUsersByTenantRoleWithTagId();
      tick();
      expect(sharedService.getUsersByTenantRoleWithTagId).toHaveBeenCalled();
      expect(httpService.doPost).toHaveBeenCalled();
      expect(sharedService.errorHandler).toHaveBeenCalledWith('tag details error');
    }));
  });

  describe('getAssociatedPatients', () => {
    it('should execute getAssociatedPatients with data', () => {
      spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of([{ id: 1 }]));
      component.getAssociatedPatients();
      expect(component.getAssociatedPatients).toBeTruthy();
    });

    it('should execute getAssociatedPatients with no items', () => {
      spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of([]));
      component.getAssociatedPatients();
      expect(component.getAssociatedPatients).toBeTruthy();
    });

    it('should handle error in getAssociatedPatients', () => {
      spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(throwError(''));
      component.getAssociatedPatients();
      expect(component.getAssociatedPatients).toBeTruthy();
    });

    it('should handle 401 status in getAssociatedPatients', () => {
      spyOn(sharedService, 'getAssociatedPatientsByTagId').and.returnValue(of({ status: 401 }));
      component.getAssociatedPatients();
      expect(component.getAssociatedPatients).toBeTruthy();
    });
  });
  describe('clearAll', () => {
    it('should reset/append checked boolean to false', () => {
      component.recipients = [{ id: 43 }];
      component.clearAll('');
      expect(component.recipients).toEqual([{ id: 43, checked: false }]);
    });

    it('should reset store selected value', () => {
      component.storeSelectedValue = [{ id: 43 }];
      component.clearAll('ButtonClick');
      expect(component.storeSelectedValue).toEqual([]);
    });

    it('should clear selectedRecipientsForDisplay when called with ButtonClick', () => {
      component.selectedRecipientsForDisplay = [{ id: '1', displayName: 'Test' }];
      component.clearAll('ButtonClick');
      expect(component.selectedRecipientsForDisplay).toEqual([]);
    });
  });

  describe('addRecipientToDisplay', () => {
    it('should add recipient to display array', () => {
      component.addRecipientToDisplay('John Doe', '123', 'patient');
      expect(component.selectedRecipientsForDisplay.length).toBe(1);
      expect(component.selectedRecipientsForDisplay[0].displayName).toBe('John Doe');
    });

    it('should not add duplicate recipients', () => {
      component.addRecipientToDisplay('John Doe', '123', 'patient');
      component.addRecipientToDisplay('John Doe', '123', 'patient');
      expect(component.selectedRecipientsForDisplay.length).toBe(1);
    });
  });

  describe('removeRecipientFromSelection', () => {
    let formBuilder: UntypedFormBuilder;

    beforeEach(() => {
      formBuilder = TestBed.inject(UntypedFormBuilder);
      component.selectedRecipientsForDisplay = [
        { id: '123', displayName: 'Test User', recipientType: 'patient' },
        { id: 'tag-456', displayName: 'Test Tag', recipientType: 'tag' }
      ];
      component.storeSelectedValue = ['123', 'tag-456'];
      component.recipients = [
        { userId: '123', userid: '123', checked: true },
        { userId: 'tag-456', userid: 'tag-456', checked: true }
      ];
      component.selectRecipientForm = formBuilder.group({
        recipientList: formBuilder.array([formBuilder.control(true), formBuilder.control(true)])
      });
    });

    it('should remove patient recipient from selection', () => {
      const mockRecipient = { id: '123', recipientType: 'patient' };
      component.removeRecipientFromSelection(mockRecipient, 0);

      expect(component.selectedRecipientsForDisplay.length).toBe(1);
      expect(component.storeSelectedValue).not.toContain('123');
      expect(component.recipients[0].checked).toBe(false);
    });

    it('should remove tag recipient from selection', () => {
      const mockRecipient = { id: 'tag-456', recipientType: 'tag' };
      component.removeRecipientFromSelection(mockRecipient, 1);

      expect(component.selectedRecipientsForDisplay.length).toBe(1);
      expect(component.storeSelectedValue).not.toContain('tag-456');
      expect(component.recipients[1].checked).toBe(false);
    });

    it('should handle alternate contact removal', () => {
      const mockRecipient = { id: '123--456', recipientType: 'alternate contact' };
      component.removeRecipientFromSelection(mockRecipient, 0);

      expect(component.selectedRecipientsForDisplay.length).toBe(1);
      expect(component.storeSelectedValue).not.toContain('123--456');
    });

    it('should handle invalid index gracefully', () => {
      const mockRecipient = { id: '123', recipientType: 'patient' };
      const initialLength = component.selectedRecipientsForDisplay.length;

      component.removeRecipientFromSelection(mockRecipient, -1);
      expect(component.selectedRecipientsForDisplay.length).toBe(initialLength);

      component.removeRecipientFromSelection(mockRecipient, 999);
      expect(component.selectedRecipientsForDisplay.length).toBe(initialLength);
    });
  });

  describe('loadRecipientsAndTags', () => {
    it('should call getAssociatedPatients for staff facing forms', () => {
      component.formFacing = Constants.staffValue;
      spyOn(component, 'getAssociatedPatients');

      component.loadRecipientsAndTags();

      expect(component.getAssociatedPatients).toHaveBeenCalled();
      expect(component.recipients).toEqual([]);
    });

    it('should call getAssociatedPatients for patient driven forms', () => {
      component.drivenBy = Constants.patientValue;
      spyOn(component, 'getAssociatedPatients');

      component.loadRecipientsAndTags();

      expect(component.getAssociatedPatients).toHaveBeenCalled();
    });

    it('should call getAssociatedPatients for practitioner with no selected patient', () => {
      component.formFacing = Constants.practitioner;
      component.selectedAssociatedPatient = null;
      component.associatedPatient = true;
      spyOn(component, 'getAssociatedPatients');

      component.loadRecipientsAndTags();

      expect(component.getAssociatedPatients).toHaveBeenCalled();
    });

    it('should call getUsersByTenantRoleWithTagId for other cases', () => {
      component.formFacing = Constants.patientValue;
      component.drivenBy = Constants.staffValue;
      spyOn(component, 'getUsersByTenantRoleWithTagId');

      component.loadRecipientsAndTags();

      expect(component.getUsersByTenantRoleWithTagId).toHaveBeenCalled();
    });
  });

  describe('viewForm updateSelection logic', () => {
    beforeEach(() => {
      component.recipients = [{ userId: '123', userid: '123', checked: false, displayName: 'Test User' }];
      component.storeSelectedValue = [];
      component.storeSelectedData = [];
      component.formType = Constants.formTypes.offline;
    });

    it('should update selection for offline forms', () => {
      const mockRecipient = { userId: '123', userid: '123' };
      const mockForm = { id: 'form1' };
      spyOn(component, 'getSelectedUserID').and.returnValue('123');
      spyOn(component, 'addRecipientToDisplay');

      component.viewForm(Constants.patientValue, mockRecipient, mockForm, 0);

      expect(component.recipients[0].checked).toBe(true);
      expect(component.storeSelectedValue).toContain('123');
    });

    it('should update selection for practitioner facing forms', () => {
      component.formType = Constants.formTypes.pending; // Use a valid form type
      const mockRecipient = { userId: '123', userid: '123' };
      const mockForm = { id: 'form1' };
      spyOn(component, 'getSelectedUserID').and.returnValue('123');
      spyOn(component, 'addRecipientToDisplay');

      component.viewForm(Constants.practitioner, mockRecipient, mockForm, 0);

      expect(component.recipients[0].checked).toBe(true);
      expect(component.storeSelectedValue).toContain('123');
      expect(component.storeSelectedData).toContain(component.recipients[0]);
    });

    it('should handle alternate contact selection', () => {
      component.recipients[0].alternateContacts = [{ userId: '456', userid: '456' }];
      const mockRecipient = component.recipients[0];
      const mockForm = { id: 'form1' };
      spyOn(component, 'getSelectedUserID').and.returnValue('456--123');

      component.viewForm(Constants.patientValue, mockRecipient, mockForm, 0, 0);

      expect(component.alternateSelectedId).toBe('456--123');
    });

    it('should return early if user is not contactable', () => {
      const mockRecipient = { userId: '123', userid: '123', isNotContactable: true };
      const mockForm = { id: 'form1' };
      spyOn(component, 'showUserNotContactableMessage');

      component.viewForm(Constants.patientValue, mockRecipient, mockForm, 0);

      expect(component.showUserNotContactableMessage).toHaveBeenCalled();
      expect(component.recipients[0].checked).toBe(false);
    });

    it('should handle user tag selection with correct display name and type', () => {
      component.recipients[0] = {
        userId: 'tag-123',
        userid: 'tag-123',
        checked: false,
        displayName: 'Test Tag',
        isUserTag: true
      };
      const mockRecipient = component.recipients[0];
      const mockForm = { id: 'form1' };
      spyOn(component, 'getSelectedUserID').and.returnValue('tag-123');
      spyOn(component, 'addRecipientToDisplay');

      component.viewForm(Constants.patientValue, mockRecipient, mockForm, 0);

      expect(component.recipients[0].checked).toBe(true);
      expect(component.storeSelectedValue).toContain('tag-123');
      expect(component.addRecipientToDisplay).toHaveBeenCalledWith('Test Tag', 'tag-123', 'tag', {
        recipient: component.recipients[0],
        alternateContactIndex: undefined
      });
    });
  });

  describe('alternateSelect', () => {
    beforeEach(() => {
      component.recipients = [
        {
          userid: '123',
          userId: '123',
          alternateContacts: [
            {
              userId: '456',
              contactDisplayName: 'John Doe (Alternate)',
              isNotContactable: false,
              password: true
            },
            {
              userId: '789',
              contactDisplayName: 'Jane Smith (Alternate)',
              isNotContactable: true,
              password: false
            }
          ]
        }
      ];
      component.storeSelectedValue = [];
      component.selectedRecipientsForDisplay = [];
      spyOn(component, 'addRecipientToDisplay');
    });

    it('should select alternate contact when not already selected', () => {
      component.alternateSelect(0, 0);

      expect(component.storeSelectedValue).toContain('456--123');
      expect(component.alternateSelectedId).toBe('456--123');
      expect(component.addRecipientToDisplay).toHaveBeenCalledWith('John Doe (Alternate)', '456--123', 'alternate contact', {
        parentRecipientId: '123',
        alternateContactId: '456',
        alternateContactIndex: 0
      });
    });

    it('should deselect alternate contact when already selected', () => {
      component.storeSelectedValue = ['456--123'];
      component.selectedRecipientsForDisplay = [{ id: '456--123', displayName: 'John Doe (Alternate)', recipientType: 'alternate contact' }];

      component.alternateSelect(0, 0);

      expect(component.storeSelectedValue).not.toContain('456--123');
      expect(component.selectedRecipientsForDisplay.length).toBe(0);
    });

    it('should show not contactable message and return early for non-contactable users', () => {
      spyOn(component, 'showUserNotContactableMessage');

      component.alternateSelect(1, 0); // Second alternate contact is not contactable

      expect(component.showUserNotContactableMessage).toHaveBeenCalled();
      expect(component.storeSelectedValue.length).toBe(0);
      expect(component.addRecipientToDisplay).not.toHaveBeenCalled();
    });

    it('should set selectedItem with correct properties', () => {
      component.alternateSelect(0, 0);

      expect(component.selectedItem).toEqual({
        userId: '456',
        contactDisplayName: 'John Doe (Alternate)',
        isNotContactable: false,
        password: true,
        passwordStatus: true,
        userid: '456'
      });
    });
  });

  describe('filteredList getter', () => {
    it('should return empty array when selectRecipientForm is null', () => {
      component.selectRecipientForm = null;
      expect(component.filteredList).toEqual([]);
    });

    it('should return empty array when selectRecipientForm value is null', () => {
      const formBuilder = TestBed.inject(UntypedFormBuilder);
      component.selectRecipientForm = formBuilder.group({
        recipientList: null
      });
      expect(component.filteredList).toEqual([]);
    });

    it('should return empty array when recipientList is undefined', () => {
      const formBuilder = TestBed.inject(UntypedFormBuilder);
      component.selectRecipientForm = formBuilder.group({
        otherField: 'value'
      });
      expect(component.filteredList).toEqual([]);
    });
  });

  describe('alternate contact methods', () => {
    it('should format alternate contact ID correctly', () => {
      expect(component.getFormattedACId(123, 456)).toBe('123--456');
    });

    it('should check alternate contact selection status', () => {
      component.storeSelectedValue = ['123--456'];
      expect(component.isAlternateContactChecked(123, 456)).toBe(true);
      expect(component.isAlternateContactChecked(789, 456)).toBe(false);
    });
  });

  it('should clear selectedRecipientsForDisplay on clearAll', () => {
    component.selectedRecipientsForDisplay = [{ id: '1', displayName: 'Test' }];
    component.clearAll('ButtonClick');
    expect(component.selectedRecipientsForDisplay).toEqual([]);
  });

  it('should add recipient to display array', () => {
    component.addRecipientToDisplay('John Doe', '123', 'patient');
    expect(component.selectedRecipientsForDisplay.length).toBe(1);
    expect(component.selectedRecipientsForDisplay[0].displayName).toBe('John Doe');
  });

  it('should format alternate contact ID correctly', () => {
    expect(component.getFormattedACId(123, 456)).toBe('123--456');
  });

  it('should check alternate contact selection status', () => {
    component.storeSelectedValue = ['123--456'];
    expect(component.isAlternateContactChecked(123, 456)).toBe(true);
    expect(component.isAlternateContactChecked(789, 456)).toBe(false);
  });
  it('execute ngOnInit: patient', () => {
    component.drivenBy = 'patient';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ngOnInit: staff', () => {
    component.formFacing = 'staff';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute isDisabled', () => {
    const data = component.isDisabled;
    expect(data).toBeTrue();
  });
  describe('alternateSelect', () => {
    it('should execute alternateSelect', () => {
      component.recipients = testData;
      component.alternateSelect(0, 0);
      expect(component.alternateSelect).toBeTruthy();
    });
  });

  describe('alternateChecked', () => {
    it('should execute alternateChecked', () => {
      component.recipients = testData;
      component.alternateChecked(0, 0);
      expect(component.alternateChecked).toBeTruthy();
    });
  });
  describe('viewForm', () => {
    it('should be defined and check patient facing form and caregiver role', () => {
      const mockForm = {
        confirmActionPrefill: true,
        externalFileExchange: false,
        id: 9380033,
        name: 'deep_form',
        populatePreviousSubmission: true,
        staffFill: 'true'
      };
      component.recipients = [{ role: Constants.roleName.caregiver }];
      component.viewForm('patient', {}, mockForm, 0);
      expect(component.viewForm).toBeTruthy();
    });

    it('should return early if recipient.isNotContactable is true', () => {
      const recipient = { isNotContactable: true };
      const form = {};
      const facing = 'patient';
      const index = 1;
      component.storeSelectedData = [];
      spyOn(component, 'viewForm').and.callThrough();
      spyOn(component, 'showUserNotContactableMessage').and.callThrough();
      component.viewForm(facing, recipient, form, index);
      expect(component.viewForm).toHaveBeenCalledWith(facing, recipient, form, index);
      expect(component.storeSelectedData).toEqual([]);
      expect(component.showUserNotContactableMessage).toHaveBeenCalled();
    });

    it('should return early if recipient.alternateContact.isNotContactable is true when multi admission on', () => {
      const recipient = { isNotContactable: false, alternateContacts: [{ isNotContactable: true }] };
      const form = {};
      const facing = 'patient';
      const index = 1;
      const altIndex = 0;
      component.storeSelectedData = [];
      spyOn(component, 'viewForm').and.callThrough();
      spyOn(component, 'showUserNotContactableMessage').and.callThrough();
      component.viewForm(facing, recipient, form, index, altIndex);
      expect(component.viewForm).toHaveBeenCalledWith(facing, recipient, form, index, altIndex);
      expect(component.storeSelectedData).toEqual([]);
      expect(component.showUserNotContactableMessage).toHaveBeenCalled();
    });
  });

  describe('downloadFormRecipients', () => {
    it('should execute downloadFormRecipients', () => {
      spyOn(common, 'showAlert').and.resolveTo(true);
      component.downloadFormRecipients('');
      expect(component.downloadFormRecipients).toBeTruthy();
    });

    it('should handle downloadFormRecipients with storeSelectedValue', () => {
      component.storeSelectedValue = ['123'];
      component.recipients = [{ userId: '123', checked: true }];
      spyOn(common, 'showAlert').and.resolveTo(false);
      component.downloadFormRecipients('patient');
      expect(common.showAlert).toHaveBeenCalled();
    });

    it('should handle downloadFormRecipients with empty filteredList', () => {
      component.storeSelectedValue = [];
      Object.defineProperty(component, 'filteredList', { value: [], configurable: true });
      component.downloadFormRecipients('patient');
      expect(component.downloadFormRecipients).toBeTruthy();
    });
  });
  it('should return early if recipient.alternateContact.isNotContactable is true when multi admission off', () => {
    component.recipients = [{ isNotContactable: false, alternateContacts: [{ isNotContactable: true }] }];
    const index = 0;
    const altIndex = 0;
    spyOn(component, 'alternateSelect').and.callThrough();
    spyOn(component, 'showUserNotContactableMessage').and.callThrough();
    component.alternateSelect(index, altIndex);
    expect(component.alternateSelect).toHaveBeenCalledWith(index, altIndex);
    expect(component.showUserNotContactableMessage).toHaveBeenCalled();
  });

  describe('getSelectedUserID', () => {
    it('should return caregiver_userid when role is caregiver', () => {
      const item = {
        role: Constants.roleName.caregiver,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        caregiver_userid: 'caregiver123',
        userId: 'user123',
        userid: 'userid123'
      };
      const result = component.getSelectedUserID(item);
      expect(result).toBe('caregiver123');
    });

    it('should return userId when role is not caregiver and userId exists', () => {
      const item = {
        role: 'patient',
        userId: 'user123',
        userid: 'userid123'
      };
      const result = component.getSelectedUserID(item);
      expect(result).toBe('user123');
    });

    it('should return userid when role is not caregiver and userId does not exist', () => {
      const item = {
        role: 'patient',
        userid: 'userid123'
      };
      const result = component.getSelectedUserID(item);
      expect(result).toBe('userid123');
    });

    it('should return userid when role is not caregiver and userId is null', () => {
      const item = {
        role: 'patient',
        userId: null,
        userid: 'userid123'
      };
      const result = component.getSelectedUserID(item);
      expect(result).toBe('userid123');
    });

    it('should return userid when role is not caregiver and userId is undefined', () => {
      const item = {
        role: 'patient',
        userId: undefined,
        userid: 'userid123'
      };
      const result = component.getSelectedUserID(item);
      expect(result).toBe('userid123');
    });
  });

  describe('showUserNotContactableMessage', () => {
    it('should call common.showToast with correct parameters', () => {
      spyOn(common, 'showToast');
      component.showUserNotContactableMessage();
      expect(common.showToast).toHaveBeenCalledWith({
        message: 'ERROR_MESSAGES.USER_CANT_BE_SELECTED',
        color: 'dark'
      });
    });
  });

  describe('viewForm additional coverage', () => {
    beforeEach(() => {
      component.recipients = [
        {
          userId: '123',
          userid: '123',
          checked: false,
          displayName: 'Test User',
          // eslint-disable-next-line @typescript-eslint/naming-convention
          caregiver_userid: 'cg123',
          role: Constants.roleName.caregiver
        }
      ];
      component.formType = Constants.formTypes.offline;
      component.isEnableMultiAdmissions = true;
      component.patientDrivenReqBody = { siteIds: [1] };
    });

    it('should handle viewForm with caregiver role and combined id', () => {
      const recipient = component.recipients[0];
      const form = { id: 'form1' };
      spyOn(component, 'getSelectedUserID').and.returnValue('cg123');

      component.viewForm(Constants.patientValue, recipient, form, 0);

      expect(component.recipients[0].userid).toBe('cg123');
      expect(component.recipients[0].compainedId).toBe('123--cg123');
    });

    it('should handle viewForm with updateSelection true', () => {
      const recipient = component.recipients[0];
      const form = { id: 'form1' };
      component.recipients[0].checked = false;
      spyOn(component, 'getSelectedUserID').and.returnValue('123');
      spyOn(component, 'addRecipientToDisplay');

      component.viewForm(Constants.patientValue, recipient, form, 0);

      expect(component.recipients[0].checked).toBe(true);
      expect(component.storeSelectedValue).toContain('123');
      expect(component.addRecipientToDisplay).toHaveBeenCalled();
    });

    it('should handle viewForm unchecking recipient and clearing admission data', () => {
      component.recipients[0].checked = true;
      component.recipients[0].admissionId = 'adm1';
      component.recipients[0].admissionName = 'Test';
      component.recipients[0].admissionSiteName = 'Site';
      component.recipients[0].admissionSiteId = 'site1';
      component.storeSelectedValue = ['123'];
      component.selectedRecipientsForDisplay = [{ id: '123', displayName: 'Test' }];

      const recipient = component.recipients[0];
      const form = { id: 'form1' };
      spyOn(component, 'getSelectedUserID').and.returnValue('123');

      component.viewForm(Constants.patientValue, recipient, form, 0);

      expect(component.recipients[0].admissionId).toBe('');
      expect(component.recipients[0].admissionName).toBe('');
      expect(component.recipients[0].admissionSiteName).toBe('');
      expect(component.recipients[0].admissionSiteId).toBe('');
      expect(component.selectedRecipientsForDisplay.length).toBe(0);
    });
  });
});
