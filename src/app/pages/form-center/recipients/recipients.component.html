<app-header-plain [headerTitle]="headerTitle" (close)="goBack()"></app-header-plain>
<div *ngIf="!(associatedPatient && formFacing === constants.practitioner && selectedAssociatedPatient)">
  <app-sites (filterSites)="filterSitesData($event)" (onLoadSites)="loadSitesData($event)" [siteLabel]="siteLabel"></app-sites>
</div>
<app-search-bar-recipients
  [addPatient]="addPatient"
  [disableAddPatient]="formId && isEnableMultiAdmissions"
  (searchAction)="searchOperations($event)"
>
</app-search-bar-recipients>
<ion-content>
  <form name="selectRecipientForm" id="select-recipient-form" [formGroup]="selectRecipientForm">
    <div class="common-list">
      <ion-list lines="none" *ngFor="let recipient of recipients; let id = index" class="recipient-list ion-no-padding">
        <ion-item
          class="recipient-parent"
          formArrayName="recipientList"
          [ngClass]="{ 'disabled-user': recipient.isNotContactable }"
          (click)="viewForm(formFacing, recipient, form, id)"
          id="recipient-{{ id }}"
        >
          <ion-label class="recipient-label-wrap">
            {{ recipient?.displayName }}
            <span *ngIf="recipient?.password" translate>({{ 'GENERAL.ENROLLED' | translate }})</span>
            <span *ngIf="!recipient?.password && !recipient.tag_type_id">({{ 'GENERAL.VIRTUAL' | translate }})</span>
            <span class="common-na-space common-capitalize-text" *ngIf="recipient.naTags && recipient.naTags !== ''">
              &nbsp;({{ recipient?.naTagNames }})</span
            >
            <span *ngIf="recipient?.sitename && isEnableMultiSite && associatedPatient"> - {{ recipient?.sitename }}</span>
          </ion-label>
          @if (!recipient.isNotContactable) {
            <ion-checkbox [formControlName]="id" class="common-checkbox" slot="end" mode="ios" id="select-recipient-{{ id }}"> </ion-checkbox>
          }
        </ion-item>
        <ion-item
          *ngFor="let alternate of recipient.alternateContacts; let idAlt = index"
          class="recipient-child"
          [ngClass]="{ 'disabled-user': alternate.isNotContactable }"
          formArrayName="alternateContactsList"
          (click)="isEnableMultiAdmissions ? viewForm(formFacing, recipient, form, id, idAlt) : alternateSelect(idAlt, id)"
          id="recipient-child-{{ idAlt }}"
        >
          <ion-label class="recipient-label-wrap">
            {{ alternate?.contactDisplayName }}
            <span *ngIf="alternate?.password" translate>({{ 'GENERAL.ENROLLED' | translate }})</span>
            <span *ngIf="!alternate?.password && !alternate.tag_type_id">({{ 'GENERAL.VIRTUAL' | translate }})</span>
          </ion-label>
          @if (!alternate.isNotContactable) {
            <ion-checkbox
              class="common-checkbox"
              slot="end"
              mode="ios"
              [checked]="alternateChecked(idAlt, id)"
              id="select-recipient-alternate-{{ idAlt }}"
            ></ion-checkbox>
          }
        </ion-item>
      </ion-list>
    </div>
    <div class="common-no-items">
      {{ errorMessage }}
      <span *ngIf="createPatientLinkMessage !== '' && !isEnableMultiAdmissions" (click)="showAddPatient()">
        <u class="show-cursor-pointer">{{ 'LABELS.CLICK_HERE' | translate }}</u>
        {{ createPatientLinkMessage }}
      </span>
    </div>
  </form>

  <ion-row class="ion-padding-top" *ngIf="showLoadMore">
    <ion-col size="12" class="ion-text-center">
      <ion-badge color="blumine" mode="md" (click)="loadData()">{{ 'BUTTONS.LOAD_MORE' | translate }}</ion-badge>
    </ion-col>
  </ion-row>
</ion-content>
<ion-footer>
  <ion-row
    *ngIf="
      formType === constants.formTypes.offline ||
      formFacing === constants.patientValue ||
      (formFacing === constants.practitioner && !isBlank(selectedAssociatedPatient)) ||
      (formFacing === constants.practitioner && !associatedPatient)
    "
  >
    <ion-col size="6">
      <ion-button class="ion-text-capitalize" (click)="clearAll('ButtonClick')" expand="block" id="clear-all" [disabled]="isDisabled" color="de-york">
        {{ 'BUTTONS.CLEAR_ALL' | translate }}
      </ion-button>
    </ion-col>
    <ion-col size="6">
      <ion-button
        *ngIf="formType !== constants.formTypes.offline; else offlineDownload"
        class="ion-text-capitalize"
        (click)="sendFormRecipients(formFacing)"
        id="next"
        translate
        expand="block"
        [disabled]="isDisabled"
        color="de-york"
      >
        {{ isStaffFilling || (formFacing === constants.practitioner && !associatedPatient) ? 'BUTTONS.NEXT' : 'BUTTONS.SEND' }}
      </ion-button>
      <ng-template #offlineDownload>
        <ion-button
          class="ion-text-capitalize"
          (click)="downloadFormRecipients(formFacing)"
          id="download"
          translate
          expand="block"
          [disabled]="isDisabled"
          color="de-york"
        >
          {{ 'BUTTONS.DOWNLOAD' }}
        </ion-button>
      </ng-template>
    </ion-col>
  </ion-row>
  <app-accordion-chips
    *ngIf="selectedRecipientsForDisplay.length > 0"
    [items]="selectedRecipientsForDisplay"
    [headerLabel]="'LABELS.SELECTED_RECIPIENTS'"
    [showCount]="true"
    (removeItem)="removeRecipientFromSelection($event.item, $event.index)"
  >
  </app-accordion-chips>
</ion-footer>
