import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';
import { TranslateModule } from '@ngx-translate/core';
import { SitesModule } from 'src/app/components/sites/sites.module';
import { RecipientsComponent } from './recipients.component';
import { HeaderPlainModule } from '../../../components/header-plain/header-plain.module';
import { PatientModule } from '../../user/invite/patient/patient.module';
import { AddVirtualPatientComponentModule } from '../../user/add-virtual-patient/add-virtual-patient.module';
import { AccordionChipsComponent } from '../../../components/accordion-chips/accordion-chips.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    HeaderPlainModule,
    SearchBarRecipientsModule,
    TranslateModule,
    PatientModule,
    AddVirtualPatientComponentModule,
    SitesModule,
    AccordionChipsComponent
  ],
  declarations: [RecipientsComponent],
  exports: [RecipientsComponent]
})
export class RecipientsComponentModule {}
