import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FormTypesPageRoutingModule } from './form-types-routing.module';

import { FormTypesPage } from './form-types.page';
import { SharedModule } from 'src/app/shared.module';
import { SearchBarModule } from 'src/app/components/search-bar/search-bar.module';
import { DataListModule } from 'src/app/components/data-list/data-list.module';
import { SelectListGridModule } from 'src/app/components/select-list-grid/select-list-grid.module';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';
import { DataGridModule } from 'src/app/components/data-grid/data-grid.module';
import { SitesModule } from 'src/app/components/sites/sites.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        FormTypesPageRoutingModule,
        SharedModule,
        SearchBarModule,
        DataListModule,
        SelectListGridModule,
        ActionButtonModule,
        DataGridModule,
        SitesModule,
    ],
    declarations: [FormTypesPage]
})
export class FormTypesPageModule { }
