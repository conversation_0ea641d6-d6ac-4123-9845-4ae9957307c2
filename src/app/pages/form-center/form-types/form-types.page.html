<app-header headerTitle="MENU.FORM_CENTER" [subHead]="subHead"></app-header>
<div *ngIf="isEnableMultiSite && sharedService?.userData?.mySites?.length > 1" class="types-page">
  <app-sites (filterSites)="filterSitesData($event)" (onLoadSites)="initialSiteData($event)" [siteLabel]="siteLabel"></app-sites>
</div>
<div class="site-header-padding" *ngIf="!(isEnableMultiSite && sharedService?.userData?.mySites?.length > 1)"></div>
<app-search-bar
  (seachAction)="searchForms($event)"
  (closeSearch)="loadForms()"
  (closeDateFilter)="loadFilterData($event)"
  [selectedDateOptions]="selectedDateOptions"
  [isDateFilter]="true"
  (closeSearch)="closeSearch()"
  [dateRange]="dateRange"
  [searchText]="searchText"
></app-search-bar>
<app-select-list-grid [subHead]="subHead" (viewType)="isList=$event" [showIcons]="forms?.length"></app-select-list-grid>
<ion-content class="form-type">
  <ion-refresher slot="fixed" (ionRefresh)="pullRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <app-data-list *ngIf="isList" [extraData]="extraData" [listData]="forms" (itemBtnClick)="selectAction($event)"></app-data-list>
  <app-data-grid *ngIf="!isList" [gridData]="forms" [extraData]="extraData" (itemBtnClick)="selectAction($event)"></app-data-grid>
  <ion-infinite-scroll *ngIf="showLoadMore && formType !== constants.formTypes.offline" threshold="100px" (ionInfinite)="loadData($event)">
    <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="{{ 'BUTTONS.LOAD_MORE' | translate }}"></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
<!-- TODO! CHP-3597 -->
<app-action-button [button]="button" (buttonClick)="doAction()" *ngIf="networkOnline"></app-action-button>
<app-footer backButtonLink="form-center"></app-footer>
