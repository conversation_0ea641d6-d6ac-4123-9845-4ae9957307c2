import { Component, OnInit } from '@angular/core';
import { APIs } from 'src/app/constants/apis';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Constants } from 'src/app/constants/constants';
import { Activity } from 'src/app/constants/activity';
import { CommonService } from 'src/app/services/common-service/common.service';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { Buttons } from 'src/app/constants/buttons';
import { convertFromTzToTz, isBlank, isPresent, arraysMatch, deepCopyJSON } from 'src/app/utils/utils';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Permissions } from 'src/app/constants/permissions';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Socket } from 'src/app/constants/socket';
import { Config } from 'src/app/constants/config';
import { PageRoutes } from 'src/app/constants/page-routes';
import { map } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import * as moment from 'moment-timezone';
import { FormMessageCountUpdateEvent, FormWorkListPayload } from 'src/app/interfaces/common-interface';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { BulkResponse, CancelFormResponse } from 'src/app/interfaces/forms-interface';
import { FormService } from '../services/form.service';

@Component({
  selector: 'app-form-types',
  templateUrl: './form-types.page.html',
  styleUrls: ['./form-types.page.scss']
})
export class FormTypesPage implements OnInit {
  forms: any = [];
  subHead: string;
  formType: string;
  isPageLoaded = false;
  searchKey: string;
  formStatus: string;
  button: any = {};
  buttonsList: any = {};
  pageCount = Constants.defaultPageCount;
  extraData: any = {};
  isList = true;
  buttons: any = [];
  showLoadMore: boolean = true;
  searchText: string;
  selectedSiteIds: any = [];
  isEnableMultiSite: boolean;
  patientDrivenEnabled: boolean;
  formPollingSubscription: Subscription;
  public networkOnline: boolean = navigator.onLine;

  selectedDateOptions = Constants.filterSelectedOptions.lastMonth;
  dateRange = null;
  isEnableMultiAdmissions = false;
  isApiCalled = true;
  siteLabel = 'LABELS.SITES';
  constants = Constants;
  constructor(
    private readonly httpService: HttpService,
    private readonly common: CommonService,
    public readonly route: ActivatedRoute,
    public readonly sharedService: SharedService,
    private readonly permissionService: PermissionService,
    private readonly router: Router,
    private readonly socketService: SocketService,
    private readonly persistentService: PersistentService,
    private readonly formService: FormService
  ) {
    this.siteLabel = this.sharedService.getSiteLabelBasedOnMultiAdmission('SITES');
    this.isEnableMultiSite = this.sharedService.isEnableConfig(Config.enableMultiSite);
    this.isEnableMultiAdmissions = this.sharedService.isEnableConfig(Config.enableMultiAdmissions);
    this.buttonsList = Buttons;
    this.buttonsList.archive.permission = Permissions.allowArchiveForms;
    this.buttonsList.restore.permission = Permissions.allowArchiveForms;
    this.route.paramMap.subscribe((paramMap) => {
      this.formType = paramMap.get('type');
      this.selectedDateOptions = this.sharedService.selectedDateOptions;
      if (Number(this.sharedService.userData?.group) === Constants.patientGroupId) {
        this.button = {
          iconCustom: false,
          type: 'new-chat',
          buttonType: 'ion-button',
          buttonIcon: 'chatbox-ellipses',
          colorTheme: 'de-york',
          customClass: 'setBottomFabButton'
        };
      } else {
        this.button = {
          iconCustom: false,
          buttonType: 'ion-button',
          buttonIcon: 'send',
          colorTheme: 'de-york',
          customClass: 'setBottomFabButton form-center-btn',
          ...this.common.setActionButton(
            this.formType === Constants.formTypes.offline ? 'BUTTONS.DOWNLOAD_FORMS' : 'BUTTONS.SEND_FORMS',
            'send-forms.png'
          )
        };
      }
    });
    this.extraData = {
      image: 'icon/forms/form-grid.png',
      type: 'forms',
      trackBy: 'id',
      formType: this.formType,
      showNoDataMessage: false,
      search: {}
    };
  }
  initializeFormPage() {
    switch (this.formType) {
      case Constants.formTypes.pending:
        this.subHead = 'LABELS.PENDING_FORMS';
        this.buttons = [this.buttonsList.reminder, this.buttonsList.cancel];
        this.formStatus = Constants.formPendingStatus;
        this.searchKey = Constants.storageKeys.searchMyFormWorkPending;
        break;
      case Constants.formTypes.completed:
        this.subHead = 'LABELS.COMPLETED_FORMS';
        this.buttons = [this.buttonsList.archive, this.buttonsList.cancel];
        this.formStatus = Constants.formCompletedStatus;
        this.searchKey = Constants.storageKeys.searchMyFormWorkCompleted;
        break;
      case Constants.formTypes.archived:
        this.subHead = 'LABELS.ARCHIVED_FORMS';
        this.buttons = [this.buttonsList.restore, this.buttonsList.cancel];
        this.formStatus = Constants.formArchivedStatus;
        this.searchKey = Constants.storageKeys.searchMyFormWorkArchived;
        break;
      case Constants.formTypes.draft:
        this.subHead = 'LABELS.DRAFT_FORMS';
        this.buttons = [this.buttonsList.cancelDraft];
        this.formStatus = Constants.formDraftStatus;
        this.searchKey = Constants.storageKeys.searchMyFormWorkDraft;
        break;
      case Constants.formTypes.offline:
        if (this.sharedService.isOfflineFormsEnabled()) {
          this.subHead = 'LABELS.OFFLINE_FORMS';
          this.buttons = [this.buttonsList.delete];
          this.formStatus = Constants.formOfflineStatus;
        } else {
          this.common.redirectToPage('not-found');
        }
        break;
      default:
        this.common.redirectToPage('not-found');
        break;
    }
    this.getForms(true);
  }
  ngOnInit(): void {
    this.formPollingSubscription = this.sharedService.messageFormCountUpdated.subscribe((pollingData: FormMessageCountUpdateEvent) => {
      if (pollingData.countType === Constants.countTypes.forms && pollingData.isPolling) {
        this.getForms(true);
      }
    });
    this.socketService.subscribeEvent(Socket.formAllowEditPolling).subscribe(() => {
      this.getForms(true);
    });
    this.updateConfigPermissions();
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.updateConfigPermissions();
    });
  }
  getStoredData(): void {
    this.dateRange = this.persistentService.getPersistentData(Constants.storageKeys.dateRangeFilterMyForms) || { from: '', to: '' };
    this.selectedDateOptions = !isBlank(this.persistentService.getPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsMyForms))
      ? this.persistentService.getPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsMyForms)
      : Constants.filterSelectedOptions.lastMonth;
  }
  updateConfigPermissions(): void {
    if (!this.button.type) {
      this.button.permission = this.permissionService.userHasPermission(Permissions.fillStructuredForms);
      this.patientDrivenEnabled = this.sharedService.isEnableConfig(Config.enablePatientDrievenFlowForms);
    }
  }
  ionViewDidLeave(): void {
    this.formPollingSubscription.unsubscribe();
  }
  getForms(hardLoad = false, event?: any): void {
    if (this.formType === Constants.formTypes.offline) {
      this.downloadOfflineForms(event);
    } else {
      this.pageCount = hardLoad ? Constants.defaultPageCount : this.pageCount;
      this.extraData.showNoDataMessage = false;
      this.extraData.formType = this.formType;
      let orderData = Constants.orderData.sentOn;
      if (this.formType === Constants.formTypes.draft) {
        orderData = Constants.orderData.modifiedOn;
      } else if (this.formType === Constants.formTypes.archived) {
        orderData = Constants.orderData.deletedOn;
      }
      let showFilterAppliedMessage = false;
      if (!this.isPageLoaded) {
        this.isPageLoaded = true;
        this.getStoredData();
        const searchStored = this.persistentService.getPersistentData(this.searchKey);
        const storedSearchText = searchStored && searchStored.searchText ? searchStored.searchText.trim() : '';
        this.searchText = storedSearchText;
        if (storedSearchText) {
          showFilterAppliedMessage = true;
        }
        if (
          !isBlank(this.dateRange) &&
          ((!isBlank(this.dateRange.from) && !isBlank(this.dateRange.to)) || this.selectedDateOptions !== 1) &&
          this.dateRange.type !== Constants.filterSelectedOptions.lastMonth
        ) {
          this.common.notifySearchFilterApplied(true);
        }
      }
      this.common.notifySearchFilterApplied(showFilterAppliedMessage);
      this.persistentService.setPersistentData(this.searchKey, { searchText: this.searchText });
      this.persistentService.setPersistentData(Constants.storageKeys.dateRangeFilterMyForms, this.dateRange);
      this.persistentService.setPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsMyForms, this.selectedDateOptions);
      const body: FormWorkListPayload = {
        roleid: this.sharedService.userData.roleId,
        zone: moment.tz.guess(),
        isForms: true,
        isPrivilege:
          this.permissionService.userHasPermission(Permissions.viewFormEntries) ||
          this.permissionService.userHasPermission(Permissions.manageTenants),
        limit: Constants.offset,
        offset: Constants.offset * this.pageCount,
        searchText: this.searchText || '',
        orderData,
        orderby: Constants.sortOrderDesc,
        isScheduled: Constants.noValue,
        archived: this.formType === Constants.formTypes.archived,
        pending: this.formType === Constants.formTypes.pending,
        completed: this.formType === Constants.formTypes.completed,
        draft: this.formType === Constants.formTypes.draft,
        accessSecurityEnabled: this.sharedService.userData.accessSecurityEnabled,
        startDate: '',
        endDate: '',
        ...this.sharedService.getFilterDateRange(this.selectedDateOptions, this.dateRange),
        citusRoleId: this.sharedService.userData.group,
        enableIntegrationStatus: false,
        enableSftpIntegration: false
      };
      if (this.sharedService.isEnableConfig(Config.enableNursingAgencies)) {
        body.nursingAgencies = this.sharedService.userData.nursing_agencies;
      }
      if (this.sharedService.userData.group !== Constants.patientGroupId.toString()) {
        if (isPresent(this.selectedSiteIds)) {
          body.siteIds = this.selectedSiteIds.toString();
        } else {
          body.siteIds = this.sharedService.userData.mySites.map((site) => site.id).join(',');
        }
      }
      let endpoint = APIs.getMyFormWorklist;
      if (this.sharedService.isEnableConfig(Config.enableCollaborateEdit) && body.draft) {
        endpoint = APIs.getAllTaggedForms;
        body.enableIntegrationStatus = this.sharedService.isEnableConfig(Config.enableIntegrationStatusWorklist);
        body.enableSftpIntegration = this.sharedService.isEnableConfig(Config.enableSftp);
        body.isScheduled = Constants.statusAll.toLowerCase();
        body.orderData = Constants.orderData.sentOn;
      } else {
        body.filterType = Constants.filterTypeMyWorkListDefault;
      }
      this.httpService
        .doGet({ endpoint, extraParams: body, loader: false })
        .pipe(
          map((x) => this.formService.generalizeResponse(x, this.formType, this.formStatus)),
          map((items) => items.map(item => ({
            ...item,
            swipeButtons: this.showFormSwipeButton(item)
          })))
        )
        .subscribe(
          (response) => {
            if (!isBlank(response)) {
              this.showLoadMore = response.length >= Constants.offset;
              if (hardLoad) {
                this.forms = [];
              }
              if (this.pageCount === 0) {
                this.forms = [];
              }
              this.forms = this.forms.concat(response);
              if (event) {
                event.target.complete();
              }
            } else if (this.pageCount === 0) {
              this.forms = [];
              this.extraData.showNoDataMessage = true;
            }
          },
          () => {
            if (this.forms.length === 0) {
              this.extraData.showNoDataMessage = true;
            }
          }
        );
    }
  }
  closeSearch() {
    this.persistentService.removePersistentData(this.searchKey);
  }

  pullRefresh(event: any): void {
    this.getForms(true, event);
  }

  downloadOfflineForms(event?) {
    this.persistentService.dbState().subscribe((isDbReady) => {
      if (isDbReady) {
        if(!event){
          this.sharedService.isLoading = true;
        }
        this.persistentService.getOfflineForms(this.selectedSiteIds).then((items: any) => {
          if (event) {
            event.target.complete();
          } else {
            this.sharedService.isLoading = false;
          }
          this.forms = items.map((item) => {
            const formData = JSON.parse(item.data);
            const splitDate = item.modifiedOn.split(' ');
            const data = {
              id: item.id,
              formId: item.formId,
              tenantId: item.tenantId,
              formname: formData.formName,
              patientDOB: formData.dob,
              siteName: formData.site,
              recipient_id: formData.id,
              patientName: `${formData.firstName || ''} ${formData.lastName || ''}`.trim(),
              IdentityValue: formData.mrn,
              patientAssociation: !isBlank(formData.id),
              offlineFormId: item.id,
              isFormDataExist: !isBlank(item.formData),
              status: item.status,
              displayLabel: formData.formName,
              time: new Date(convertFromTzToTz(splitDate[0], splitDate[1], Constants.timezoneDefault, moment.tz.guess())).getTime() / 1000,
              sync: item.sync,
              formContent: formData.formContent,
              draftId: item.draftId,
              draftuniqueID: item.draftuniqueID,
              swipeButtons: this.showFormSwipeButton(item),
              admissionId: formData?.admissionId || '',
              admissionName: formData?.admissionName || ''
            };
            return data;
          });
          this.extraData.showNoDataMessage = this.forms.length === 0;
          this.extraData.formType = this.formType;
        });
      }
    });
  }

  
  doAction(): void {
    const navigationExtras: NavigationExtras = {
      state: {
        formType: this.formType,
        forceRefresh: true
      }
    };
    this.router.navigate(
      [!this.patientDrivenEnabled || this.isEnableMultiAdmissions ? PageRoutes.sendForms : PageRoutes.formFlow], navigationExtras);
  }
  selectAction(e: any): void {
    switch (e.action) {
      case 'click':
        this.viewForm(e.item);
        break;
      case 'reminder':
        this.doResend(e.item);
        break;
      case 'cancel':
        this.doCancel(e.item);
        break;
      case 'archive':
        this.doArchive(e.item, e.index);
        break;
      case 'restore':
        this.doRestore(e.item, e.index);
        break;
      case 'cancelDraft':
        this.doDeleteDraft(e.item);
        break;
      case 'sync':
        this.doSyncOfflineForm(e.item);
        break;
      case 'delete':
        this.doDeleteOfflineForm(e.item);
        break;
      default:
        break;
    }
  }
  searchForms(searchData: any): void {
    this.forms = [];
    this.searchText = searchData.text;
    this.pageCount = Constants.defaultPageCount;
    this.extraData.search.text = this.searchText;
    this.getForms();
    // TODO: verify below activity present for completed and archived forms.
    const data = {
      displayName: this.sharedService.userData.displayName,
      formType: this.formStatus,
      keyWord: this.searchText
    };
    this.sharedService.trackActivity({
      type: Activity.forms,
      name: Activity.searchForms,
      des: { data, desConstant: Activity.searchFormDes }
    });
  }

  loadForms(): void {
    this.forms = [];
    this.searchText = '';
    this.pageCount = Constants.defaultPageCount;
    this.getForms();
  }

  doResend(item: any): void {
    const formSendMode = this.sharedService.isEnableConfig(Config.enableApplessModel) ? Constants.appless : Constants.mobileapp;
    const alertData = {
      message: 'MESSAGES.GOING_TO_SEND_REMINDER',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    const deepLinking = {
      pushType: '',
      state: Constants.deepLinkingStates.formsCenter,
      stateParams: {},
      tenantId: this.sharedService.userData.tenantId,
      tenantName: this.sharedService.userData.tenantName,
      formSendMode,
      applessMode: formSendMode === Constants.appless ? 'both' : '',
      sentId: ''
    };
    const pushMessage = this.common.getTranslateData('MESSAGES.REMINDER_MESSAGE');
    const selectedRecipientsPolling = [];
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        const body = {
          form_id: item.formId,
          recipients: [item.recipient_id],
          previousSendId: item.sentId,
          formSendMode,
          resend: true
        };
        this.sharedService.sendFormToRecipients(body).subscribe((response) => {
          deepLinking.sentId = response.sentId;
          selectedRecipientsPolling.push({
            userid: item.recipient_id,
            senderId: this.sharedService.userData.userId,
            reminderforForm: 1,
            organizationMasterId: this.sharedService.userData.organizationMasterId,
            formSendMode,
            applessMode: formSendMode === Constants.appless ? Constants.applessDevices : '',
            sentId: response.sentId
          });
          const notificationData = {
            sourceId: Constants.sourceId.form.toString(),
            sourceCategoryId: Constants.sourceCategoryId.formReSendNotification
          };
          this.sharedService.sentPushNotification(selectedRecipientsPolling, '0', pushMessage, '', deepLinking, '', notificationData);
          this.getForms(true);
          let message = this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.REMINDER_SENT', {
            form: item.formName
          });
          const notifyItems = [];
          if (formSendMode === Constants.appless) {
            if (item.patienUsername) {
              notifyItems.push(
                this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.REMINDER_MESSAGE_EMAIL', {
                  userName: item.patienUsername
                })
              );
            }

            if (item.mobile && item.countryCode) {
              notifyItems.push(
                this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.REMINDER_MESSAGE_MOBILE', {
                  countryCode: item.countryCode,
                  mobile: item.mobile
                })
              );
            }
            if (notifyItems.length > 0) {
              message += this.common.getTranslateData('SUCCESS_MESSAGES.APPLESS_MAGICLINK_SENDTO');
              if (notifyItems.length === 2) {
                message += notifyItems.join(' and ');
              } else {
                message += notifyItems[0];
              }
            }
          }
          const successMessage = this.common.getTranslateData(message);
          this.common.showMessage(successMessage);
        });
      }
    });
  }
  doCancel(item): void {
    // Show alert for cancellation reason input
    const tab = this.formType.split('-')[0].toUpperCase();
    this.sharedService.cancelDocumentOrForm('FORMS', item.form_name, tab).then((cancelReason) => {
      if (cancelReason !== false) {
        this.processCancellation(item, cancelReason);
      }
    });
  }
  processCancellation(item, cancelReason: string | null) {
    const bodyItem: any = {
      form_id: item?.formId,
      form_name: item?.form_name,
      sent_id: item?.sentId,
      recipient_id: item?.recipient_id,
      id: item?.id,
      form_submission_id: item?.form_submission_id
    };

    if (cancelReason) {
      bodyItem.cancel_reason = cancelReason;
    }

    const body = [bodyItem];
    this.httpService
      .doPut({
        endpoint: APIs.cancelForm,
        payload: body
      })
      .subscribe(({ response }: BulkResponse<CancelFormResponse>) => {
        const activityData = {
          type: Activity.structuredForms,
          name: Activity.cancelPendingForm,
          des: {
            data: {
              displayName: this.sharedService.userData.displayName,
              formName: response[0]?.data?.form_name,
              formId: response[0]?.data?.form_id,
              sentId: response[0]?.data?.sent_id,
              recipientId: response[0]?.data?.recipient_id
            },
            desConstant: Activity.cancelPendingFormFailDes
          }
        };
        if (response[0]?.success) {
          activityData.des.desConstant = Activity.cancelPendingFormDes;
          this.getForms(true);
          const successMessage = this.common.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_HAS_BEEN_CANCELLED_SUCCESSFULLY', {
            form: response[0]?.data?.form_name
          });
          this.common.showMessage(successMessage);
        }
        this.sharedService.trackActivity(activityData);
      });
  }
  doArchive(item: any, selectedIndex: number): void {
    const alertData = {
      message: 'MESSAGES.GOING_TO_ARCHIVE_THIS_FORM',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        // TODO: Remove notifyUsersPolling function to call multiple API
        const body = {
          ...item
        };
        const successMessage = this.common.getTranslateData('SUCCESS_MESSAGES.SUCCESSFULLY_ARCHIVED_ITEM');
        this.httpService
          .doPost({
            endpoint: APIs.deleteSentForm,
            payload: body,
            contentType: 'form'
          })
          .subscribe((response) => {
            // TODO: Check status and show error message if not archive
            if (isPresent(response.status) && response.status === Constants.successStatus) {
              this.common.showMessage(`${successMessage} - ${body.formName}`);
              this.forms.splice(selectedIndex, 1);
              this.getForms(true);
            } else {
              this.common.showToast({
                message: response?.error || this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
                color: 'danger'
              });
            }
          });
      }
    });
  }

  doRestore(item: any, selectedIndex: number): void {
    const alertData = {
      message: 'MESSAGES.GOING_TO_RESTORE_THIS_FORM',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        // TODO: Remove notifyUsersPolling function to call multiple API
        const body = {
          ...item,
          archiveing: 1
        };

        this.httpService
          .doPost({
            endpoint: APIs.restoreSentForm,
            payload: body,
            contentType: 'form'
          })
          .subscribe((response) => {
            if (isPresent(response.status) && response.status === Constants.successStatus) {
              this.forms.splice(selectedIndex, 1);
              this.getForms(true);
            } else {
              this.common.showToast({
                message: response?.error || this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
                color: 'danger'
              });
            }
          });
      }
    });
  }

  doDeleteDraft(item: any): void {
    const alertData = {
      message: 'MESSAGES.GOING_TO_CANCEL_THIS_DRAFT',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        const body = {
          ...item,
          createdUserName: item.fromUsername,
          caregiver_userid: null,
          archiveing: 1
        };
        this.httpService
          .doPost({
            endpoint: APIs.deleteDraftForm,
            payload: body,
            contentType: 'form'
          })
          .subscribe((response) => {
            if (response) {
              this.getForms(true);
            }
          });
      }
    });
  }
  notifyUsersPolling(item: any): void {
    let notifyUsers = item.notify_users;
    notifyUsers = notifyUsers.split(',');
    const notifyUsersPolling = [];
    notifyUsers.forEach((value) => {
      notifyUsersPolling.push({
        userid: value,
        senderId: this.sharedService.userData.userId
      });
    });
    if (item.fromId !== Number(this.sharedService.userData.userId)) {
      notifyUsersPolling.push({
        userid: item.fromId,
        senderId: this.sharedService.userData.userId
      });
    }
    if (Number(this.sharedService.userData.group) !== Constants.patientGroupId && notifyUsersPolling.length) {
      this.socketService.emitEvent(Socket.sendFormsToRecipients, notifyUsersPolling);
    }
  }

  showFormSwipeButton(item: any): any {
    const newButtons = JSON.parse(JSON.stringify(this.buttons));
    this.buttons.forEach((button: any, index: number) => {
      switch (button.action) {
        case 'restore':
          newButtons[index].show =
            Number(this.sharedService.userData.group) !== Constants.patientGroupId ||
            (String(item?.admin_archived) === Constants.configFalse && Number(this.sharedService.userData.group) === Constants.patientGroupId);
          break;
        case 'reminder':
          newButtons[index].show =
            Number(this.sharedService.userData.group) !== Constants.patientGroupId &&
            String(item.fromId) === this.sharedService.userData.userId &&
            item.allow_edit !== Constants.allowEditValue;
          break;
        case 'cancel':
          if (['completed', 'archived'].includes(item.formStatus)) {
            newButtons[index].show = this.permissionService.userHasPermission(Permissions.cancelCompletedArchivedForms);
          } else {
            newButtons[index].show =
              !this.sharedService.loggedUserIsPatient() &&
              Number(item.allow_edit) !== Constants.allowEditValue &&
              this.permissionService.userHasPermission(Permissions.cancelForms);
          }
          break;
        case 'archive':
          newButtons[index].show = !(
            String(item.recipient_id) === this.sharedService.userData.userId && Number(item.facing_new) === Constants.practitionerFacingValue
          );
          break;
        default:
          newButtons[index].show = true;
          break;
      }
    });
    return newButtons;
  }
  viewForm(form: any): void {
    const navigationExtras: NavigationExtras = {
      state: {
        viewData: { form },
        formStatus: this.formStatus
      }
    };
    if (this.formType === Constants.formTypes.offline) {
      this.router.navigate([PageRoutes.viewOfflineForm], navigationExtras);
    } else {
      this.router.navigate([PageRoutes.viewForms], navigationExtras);
    }
  }

  doSyncOfflineForm(form: any): void {
    this.persistentService.getOfflineFormData(form.offlineFormId)?.then((res) => {
      const payload = new FormData();
      payload.append('formId', form.formId);
      payload.append('formData', res.formData);
      payload.append('recipientId', form.recipient_id);
      payload.append('draftId', res.draftId);
      payload.append('eSignatureLogs', res.eSignatureLogs);
      if (isPresent(form.admissionId) && this.isEnableMultiAdmissions) {
        payload.append('admissionId', form.admissionId);
      }
      this.httpService.doPost({ endpoint: APIs.syncOfflineForm, payload, contentType: 'form', parseToString: true }).subscribe((response) => {
        if (response.success) {
          const buttons = [
            { text: this.common.getTranslateData('BUTTONS.CLOSE'), confirm: false },
            { text: this.common.getTranslateData('BUTTONS.GOT_TO_DRAFTS'), confirm: true }
          ];
          this.common
            .showAlert({
              message: 'MESSAGES.SYNC_FORM_SUCCESSFULLY',
              header: 'MESSAGES.SYNC_FORM_SUCCESSFULLY_HEADER',
              buttons
            })
            .then((confirmation) => {
              if (confirmation) {
                this.router.navigate([PageRoutes.draftForms]);
              }
            });
          this.persistentService.updateOfflineFormDataSync(form.offlineFormId, 1, response.data.draftID, response.data.draftuniqueID);
          this.downloadOfflineForms();
        } else {
          this.common.showMessage(response?.data?.message || this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'))
        }
      });
    });
  }

  doDeleteOfflineForm(form: any): void {
    const buttons = [
      { text: this.common.getTranslateData('BUTTONS.NO'), confirm: false },
      { text: this.common.getTranslateData('BUTTONS.YES'), confirm: true }
    ];
    const message = this.common.getTranslateData('MESSAGES.DO_YOU_WANT_TOD_DELETE_THIS_FORM');
    this.common.showAlert({ message, buttons }).then((confirmation) => {
      if (confirmation) {
        this.persistentService.deleteOfflineForm(form.offlineFormId).then(() => {
          this.downloadOfflineForms();
        });
      }
    });
  }

  loadData(event: any) {
    this.pageCount += 1;
    this.getForms(false, event);
  }

  filterSitesData(data: []): void {
    if (!arraysMatch(this.selectedSiteIds, data)) {
      this.selectedSiteIds = deepCopyJSON(data);
      this.pageCount = Constants.defaultPageCount;
      this.forms = [];
      this.getForms(true);
    }
  }

  loadFilterData(value) {
    this.forms = [];
    this.selectedDateOptions = value.text !== '' ? value.text : 0;
    this.persistentService.setPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsMyForms, this.selectedDateOptions.toString());
    if (value.text === Constants.filterSelectedOptions.custom) {
      this.dateRange.from = value.dates.from;
      this.dateRange.to = value.dates.to;
      this.persistentService.setPersistentData(Constants.storageKeys.dateRangeFilterMyForms, value.dates);
    }
    this.getForms(true);
  }
  ionViewWillEnter() {
    this.initializeFormPage();
  }
  initialSiteData(data: any): void {
    this.selectedSiteIds = data;
  }
}