import { RouterTestingModule } from '@angular/router/testing';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { FormTypesPage } from 'src/app/pages/form-center/form-types/form-types.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Keepalive } from '@ng-idle/keepalive';
import { CommonService } from 'src/app/services/common-service/common.service';
import { of, throwError } from 'rxjs';
import { HttpService } from 'src/app/services/http-service/http.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { Constants } from 'src/app/constants/constants';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { APIs } from 'src/app/constants/apis';

describe('FormTypesPage', () => {
  let component: FormTypesPage;
  let fixture: ComponentFixture<FormTypesPage>;
  let sharedService: SharedService;
  let common: CommonService;
  let httpService: HttpService;
  let persistentService: PersistentService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [FormTypesPage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        RouterTestingModule.withRoutes([{ path: 'form-center/draft-forms', component: FormTypesPage }])
      ],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        CommonService,
        HttpService,
        NativeStorage,
        SQLite,
        PersistentService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              get: () => 'pending-forms'
            })
          }
        }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    common = TestBed.inject(CommonService);
    spyOn(common, 'showMessage').and.stub();
    spyOn(common, 'showToast').and.stub();
    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    httpService = TestBed.inject(HttpService);
    persistentService = TestBed.inject(PersistentService);
    spyOn(common, 'showAlert').and.resolveTo(true);
    fixture = TestBed.createComponent(FormTypesPage);
    component = fixture.componentInstance;
    const val = {
      paramMap: of({
        get: () => {
          return 'pending-forms';
        }
      })
    };
    Object.defineProperty(component, 'route', { value: val });
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('Check restore button action', () => {
    component.selectAction({ action: 'restore', item: '' });
    expect(component.selectAction).toBeTruthy();
  });
  it('Check reminder button action', () => {
    component.selectAction({ action: 'reminder', item: '' });
    expect(component.selectAction).toBeDefined();
  });
  it('Check cancel button action', () => {
    component.selectAction({ action: 'cancel', item: '' });
    expect(component.selectAction).toBeDefined();
  });

  it('Execute doResend()', () => {
    const item = { patienUsername: '<EMAIL>', mobile: '**********', countryCode: '+1' };
    const response = {
      status: 1,
      sentId: 820134,
      send_to: 1032667,
      message: '',
      progressNoteCreated: true,
      missingvalues: '',
      identity_value_Patient: 'PPT01',
      CPRUSERNO: '123w',
      CPRUSERNAME: 'Demo, Staff',
      firstnamestaff: 'Demo',
      lastnamestaff: 'Staff',
      firstnamepatient: 'Performance Test',
      lastnamepatient: 'Patient',
      progressNoteIntegrationSend: true,
      form_name: 'Alb patient facing',
      form_id: '8603437',
      defaultFromFilingCenterSubmitjson: 'Atlanta Send to CPR+',
      patientAssociation: false,
      progressNoteIntegration: true,
      staffFacing: null
    };
    spyOn(sharedService, 'sendFormToRecipients').and.returnValue(of(response));
    spyOn(sharedService, 'sentPushNotification').and.returnValue(of(1));
    component.doResend(item);
    expect(component.doResend).toBeDefined();
  });
  it('Check notifyItems else condition', fakeAsync(() => {
    const item = { mobile: '32948732897', countryCode: '+1', patienUsername: 'abcd' };
    sharedService.userData.config.enable_appless_model = '1';
    const response = {
      status: 1,
      sentId: 820134,
      send_to: 1032667,
      message: '',
      progressNoteCreated: true,
      missingvalues: '',
      identity_value_Patient: 'PPT01',
      CPRUSERNO: '123w',
      CPRUSERNAME: 'Demo, Staff',
      firstnamestaff: 'Demo',
      lastnamestaff: 'Staff',
      firstnamepatient: 'Performance Test',
      lastnamepatient: 'Patient',
      progressNoteIntegrationSend: true,
      form_name: 'Alb patient facing',
      form_id: '3242',
      defaultFromFilingCenterSubmitjson: 'Atlanta Send to CPR+',
      patientAssociation: false,
      progressNoteIntegration: true,
      staffFacing: null
    };
    spyOn(sharedService, 'sendFormToRecipients').and.returnValue(of(response));
    spyOn(sharedService, 'sentPushNotification').and.returnValue(of(1));
    component.doResend(item);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.doResend).toBeDefined();
  }));
  it('Execute doCancel()', fakeAsync(() => {
    const notifyUsers = [{ displayname: 'Demo Staff', userid: 674837, username: '<EMAIL>' }];
    const response = {
      form_id: 9555984,
      form_name: 'PatientFacing',
      notifyUsers,
      id: '001134418',
      recipient_id: 1472155,
      sent_id: 1134418,
      status: '1'
    };
    spyOn(sharedService, 'cancelDocumentOrForm').and.resolveTo('Test');
    spyOn(httpService, 'doPut').and.returnValue(of({ response: [{ success: true, data: response }] }));
    component.doCancel({});
    tick(2000);
    expect(common.showMessage).toHaveBeenCalledWith('SUCCESS_MESSAGES.FORM_HAS_BEEN_CANCELLED_SUCCESSFULLY');
    expect(component.doCancel).toBeTruthy();
  }));
  it('Execute getForms', () => {
    const response = [
      {
        form_id: 9555984,
        form_name: 'PatientFacing',
        id: '001134418',
        recipient_id: 1472155,
        sent_id: 1134418,
        status: '1'
      }
    ];
    spyOn(httpService, 'doGet').and.returnValue(of({ response }));
    component.getForms(true, {
      target: {
        complete: () => {
          // void
        }
      }
    });
    expect(component.getForms).toBeDefined();
  });
  it('Execute getForms : response = []', () => {
    component.pageCount = 0;
    spyOn(httpService, 'doGet').and.returnValue(of(''));
    component.getForms(false, '');
    expect(component.getForms).toBeDefined();
  });
  it('Execute getForms : throw error', () => {
    component.pageCount = 0;
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.getForms(false, '');
    expect(component.getForms).toBeDefined();
  });

  it('execute doArchive', fakeAsync(() => {
    const response = { status: '1' };
    const item = { formName: '1' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doArchive(item, 0);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.doArchive).toBeTruthy();
  }));

  it('execute doArchive: error', fakeAsync(() => {
    const response = { error: 'Error' };
    const item = { formName: '1' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doArchive(item, 0);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.doArchive).toBeTruthy();
  }));

  it('execute doRestore', fakeAsync(() => {
    const response = { status: '1' };
    const item = { formName: '1' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doRestore(item, 0);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.doRestore).toBeTruthy();
  }));

  it('execute doRestore: error', fakeAsync(() => {
    const response = { error: 'Error' };
    const item = { formName: '1' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doRestore(item, 0);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.doRestore).toBeTruthy();
  }));

  it('execute doDeleteDraft', fakeAsync(() => {
    const response = { status: '1' };
    const item = { formName: '1' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doDeleteDraft(item);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.doDeleteDraft).toBeTruthy();
  }));

  it('execute notifyUsersPolling', () => {
    const item = { notify_users: '1' };
    component.notifyUsersPolling(item);
    expect(component.notifyUsersPolling).toBeTruthy();
  });

  it('execute filterSitesData', () => {
    const item: any = [];
    component.filterSitesData(item);
    expect(component.filterSitesData).toBeTruthy();
  });

  it('execute loadData', fakeAsync(() => {
    const spy = jasmine.createSpyObj('IonInfiniteScroll', ['complete']);
    component.loadData({ target: spy });
    tick(2500);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.loadData).toBeTruthy();
  }));

  it('execute viewForm', () => {
    const item: any = [];
    component.viewForm(item);
    expect(component.viewForm).toBeTruthy();
  });

  it('execute loadForms', () => {
    component.loadForms();
    expect(component.loadForms).toBeTruthy();
  });

  it('execute searchForms', () => {
    component.searchForms({ text: 'abc' });
    expect(component.searchForms).toBeTruthy();
  });

  it('execute selectAction: click', () => {
    component.selectAction({ action: 'click', item: '' });
    expect(component.selectAction).toBeTruthy();
  });

  it('execute selectAction: archive', () => {
    component.selectAction({ action: 'archive', item: '' });
    expect(component.selectAction).toBeTruthy();
  });

  it('execute selectAction: cancelDraft', () => {
    component.selectAction({ action: 'cancelDraft', item: '' });
    expect(component.selectAction).toBeTruthy();
  });

  it('execute selectAction: default', () => {
    component.selectAction({ action: '', item: '' });
    expect(component.selectAction).toBeTruthy();
  });

  it('execute doAction', () => {
    component.doAction();
    expect(component.doAction).toBeTruthy();
  });

  it('execute showFormSwipeButton: restore', () => {
    component.buttons = [{ action: 'restore' }];
    const item = { admin_archived: '0' };
    component.showFormSwipeButton(item);
    expect(component.showFormSwipeButton).toBeTruthy();
  });

  it('execute showFormSwipeButton: archive', () => {
    component.buttons = [{ action: 'archive' }];
    const item = { admin_archived: '0' };
    component.showFormSwipeButton(item);
    expect(component.showFormSwipeButton).toBeTruthy();
  });

  it('execute showFormSwipeButton: default', () => {
    component.buttons = [{ action: '' }];
    const item = { admin_archived: '0' };
    component.showFormSwipeButton(item);
    expect(component.showFormSwipeButton).toBeTruthy();
  });

  it('execute doCancel', fakeAsync(() => {
    const response = { status: '1' };
    const item = { formName: '1' };
    spyOn(httpService, 'doPost').and.returnValue(of(response));
    component.doCancel(item);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.doCancel).toBeTruthy();
  }));

  it('execute pullRefresh', () => {
    component.pullRefresh({});
    expect(component.pullRefresh).toBeTruthy();
  });

  it('execute patientName : doAtion', () => {
    component.patientDrivenEnabled = true;
    component.doAction();
    expect(component.doAction).toBeTruthy();
  });

  it('Execute getForms : Draft', () => {
    const response = [];
    component.formType = Constants.formTypes.draft;
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.getForms(true, '');
    expect(component.getForms).toBeDefined();
  });

  it('Execute getForms : archived', () => {
    const response = [{}];
    component.formType = Constants.formTypes.archived;
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.getForms(true, '');
    expect(component.getForms).toBeDefined();
  });

  it('Execute ionViewDidLeave', () => {
    component.ionViewDidLeave();
    expect(component.ionViewDidLeave).toBeTruthy();
  });

  it('Execute initializeFormPage: completed', () => {
    component.formType = Constants.formTypes.completed;
    component.initializeFormPage();
    expect(component.initializeFormPage).toBeTruthy();
  });

  it('Execute initializeFormPage: draft', () => {
    component.formType = Constants.formTypes.draft;
    component.initializeFormPage();
    expect(component.initializeFormPage).toBeTruthy();
  });

  it('Execute initializeFormPage: archived', () => {
    component.formType = Constants.formTypes.archived;
    component.initializeFormPage();
    expect(component.initializeFormPage).toBeTruthy();
  });

  it('Execute initializeFormPage: Page not found', () => {
    component.formType = '';
    component.initializeFormPage();
    expect(component.initializeFormPage).toBeTruthy();
  });

  it('Execute ngOnInit: messageFormCountUpdated', () => {
    sharedService.messageFormCountUpdated.next({ countType: Constants.countTypes.forms });
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('Execute ngOnInit: configValuesUpdated', () => {
    component.button = { type: '' };
    sharedService.configValuesUpdated.next(null);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  it('Execute getForms : no data', () => {
    const response = [];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.getForms(true, '');
    expect(component.getForms).toBeDefined();
  });

  it('Execute getForms with getPersistentData ', () => {
    const response = [];
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    spyOn(persistentService, 'getPersistentData').and.returnValue({ searchText: 'test' });
    component.getForms(true, '');
    expect(component.getForms).toBeDefined();
  });
  it('Execute closeSearch', () => {
    component.closeSearch();
    expect(component.closeSearch).toBeTruthy();
  });

  it('Execute initializeFormPage: pending', () => {
    component.formType = Constants.formTypes.pending;
    component.initializeFormPage();
    expect(component.initializeFormPage).toBeTruthy();
  });
  it('Execute initializeFormPage: offline', () => {
    spyOn(sharedService, 'isOfflineFormsEnabled').and.returnValue(true);
    component.formType = Constants.formTypes.offline;
    component.initializeFormPage();
    expect(component.initializeFormPage).toBeTruthy();
  });
  it('Execute initializeFormPage: offline', () => {
    spyOn(sharedService, 'isOfflineFormsEnabled').and.returnValue(false);
    component.formType = Constants.formTypes.offline;
    component.initializeFormPage();
    expect(component.initializeFormPage).toBeTruthy();
  });
  it('Check sync button action', () => {
    component.doSyncOfflineForm({
      action: 'sync',
      offlineFormId: 1234,
      formId: '123',
      formData: 'Test',
      recipient_id: '123',
      draftId: '123456',
      eSignatureLogs: 'test'
    });
    expect(component.doSyncOfflineForm).toBeDefined();
  });
  it('Check sync button action', () => {
    spyOn(component, 'doSyncOfflineForm').and.stub();
    component.selectAction({ action: 'sync', item: '' });
    expect(component.doSyncOfflineForm).toHaveBeenCalled();
  });
  it('Check delete button action', () => {
    spyOn(component, 'doDeleteOfflineForm').and.stub();
    component.selectAction({ action: 'delete', item: '' });
    expect(component.doDeleteOfflineForm).toHaveBeenCalled();
  });
  it('should call ionViewWillEnter', () => {
    spyOn(component, 'initializeFormPage').and.stub();
    component.ionViewWillEnter();
    expect(component.initializeFormPage).toHaveBeenCalled();
  });
  it('should call initialSiteData', () => {
    const siteIds = [12, 124];
    component.initialSiteData(siteIds);
    expect(component.selectedSiteIds).toBe(siteIds);
  });
  it('should call doDeleteOfflineForm', () => {
    const form = { offlineFormId: 123 };
    component.doDeleteOfflineForm(form);
    expect(component.doDeleteOfflineForm).toBeDefined();
  });
  describe('loadFilterData', () => {
    it('should load filter data with empty text', () => {
      const value = { text: '', dates: { from: '2022-01-01', to: '2022-01-31' } };
      spyOn(localStorage, 'setItem');
      spyOn(component, 'getForms');
      spyOn(persistentService, 'setPersistentData');
      component.loadFilterData(value);
      expect(component.getForms).toHaveBeenCalledWith(true);
    });

    it('should call getForms with true', () => {
      const value = { text: Constants.filterSelectedOptions.lastMonth, dates: { from: '', to: '' } };
      spyOn(component, 'getForms');
      spyOn(persistentService, 'setPersistentData');
      component.loadFilterData(value);
      expect(component.getForms).toHaveBeenCalledWith(true);
    });
    it('should load filter data for custom dates', () => {
      component.dateRange = { from: '2022-01-01', to: '2022-01-31' };
      const value = { text: Constants.filterSelectedOptions.custom, dates: { from: '2022-01-01', to: '2022-01-31' } };
      const expectedDateRange = value.dates;
      spyOn(component, 'getForms');
      spyOn(persistentService, 'setPersistentData');
      component.loadFilterData(value);
      expect(component.dateRange).toEqual(expectedDateRange);
      expect(component.getForms).toHaveBeenCalledWith(true);
    });
  });

  it('should set selectedDateOptions and call setPersistentData with text value', () => {
    const value = { text: '2024-10-01' };
    spyOn(persistentService, 'setPersistentData').and.returnValue();
    component.loadFilterData(value);
    expect(persistentService.setPersistentData).toHaveBeenCalledWith(Constants.storageKeys.dateRangeSelectedDateOptionsMyForms, value.text);
  });

  it('execute showFormSwipeButton: reminder', () => {
    component.buttons = [{ action: 'reminder' }];
    const item = { admin_archived: '0' };
    component.showFormSwipeButton(item);
    expect(component.showFormSwipeButton).toBeTruthy();
  });

  it('execute showFormSwipeButton: cancel', () => {
    component.buttons = [{ action: 'cancel' }];
    const item = { admin_archived: '0' };
    component.showFormSwipeButton(item);
    expect(component.showFormSwipeButton).toBeTruthy();
  });

  it('should call syncOfflineForm method with correct parameters', fakeAsync(() => {
    const form = {
      offlineFormId: 1,
      formId: 2,
      recipient_id: 3
    };
    const res = {
      formData: 'mockFormData',
      eSignatureLogs: 'mockLogs',
      id: 1234455,
      tenantId: '8',
      formId: '12',
      draftId: '1234',
      draftuniqueID: '12345',
      data: '',
      sync: true,
      modifiedOn: '04/19/2024',
      status: 'ok'
    };
    spyOn(persistentService, 'getOfflineFormData').and.returnValue(Promise.resolve(res));
    spyOn(httpService, 'doPost').and.returnValue(of({ success: true, data: { draftID: 5, draftuniqueID: 6 } }));
    spyOn(persistentService, 'updateOfflineFormDataSync');
    spyOn(component, 'downloadOfflineForms');

    component.doSyncOfflineForm(form);
    tick();

    expect(persistentService.getOfflineFormData).toHaveBeenCalledWith(form.offlineFormId);
    expect(httpService.doPost).toHaveBeenCalledWith({
      endpoint: APIs.syncOfflineForm,
      payload: jasmine.any(FormData),
      contentType: 'form',
      parseToString: true
    });
    expect(component.downloadOfflineForms).toBeDefined();
  }));

  it('should call syncOfflineForm method with in correct parameters', fakeAsync(() => {
    const form = {
      offlineFormId: 0,
      formId: 2,
      recipient_id: 3
    };
    const res = {
      formData: 'mockFormData',
      eSignatureLogs: 'mockLogs',
      id: 1234455,
      tenantId: '8',
      formId: null,
      draftId: '1234',
      draftuniqueID: '12345',
      data: '',
      sync: false,
      modifiedOn: '04/19/2024',
      status: 'ok'
    };
    spyOn(persistentService, 'getOfflineFormData').and.returnValue(Promise.resolve(res));
    spyOn(httpService, 'doPost').and.returnValue(of({ success: false, data: { draftID: 5, draftuniqueID: 6 } }));
    spyOn(persistentService, 'updateOfflineFormDataSync');
    spyOn(component, 'downloadOfflineForms');

    component.doSyncOfflineForm(form);
    tick();

    expect(persistentService.getOfflineFormData).toHaveBeenCalledWith(form.offlineFormId);
    expect(httpService.doPost).toHaveBeenCalledWith({
      endpoint: APIs.syncOfflineForm,
      payload: jasmine.any(FormData),
      contentType: 'form',
      parseToString: true
    });
    expect(component.downloadOfflineForms).toBeDefined();
  }));

  it('should call OfflineForms method with in correct parameters', fakeAsync(() => {
    const res = [
      {
        id: 1,
        tenantId: 'string',
        formId: 'string',
        draftId: 'string',
        draftuniqueID: 'string',
        data: '{}',
        formData: '',
        sync: false,
        modifiedOn: 'string',
        eSignatureLogs: 'string',
        status: ''
      }
    ];

    spyOn(persistentService, 'dbState').and.returnValue(of(true));
    spyOn(persistentService, 'getOfflineForms').and.returnValue(Promise.resolve(res));
    component.downloadOfflineForms();
    tick();
    expect(component.downloadOfflineForms).toBeDefined();
  }));
});
