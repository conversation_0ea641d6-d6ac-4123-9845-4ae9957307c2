import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { UserGroup } from 'src/app/constants/constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { PermissionGuard } from 'src/app/services/permission-guard/permission.guard';



const routes: Routes = [
    {
        path: '',
        loadChildren: () => import('./forms/forms.module').then(m => m.FormsPageModule)
    },
    {
        path: 'send-forms',
        loadChildren: () => import('./send-forms/send-forms.module').then(m => m.SendFormsPageModule),
        canActivate: [PermissionGuard],
        data: {
            permissions: {
                restrictedTo: {
                    userTypes: [UserGroup.PATIENT]
                },
                redirectTo: PageRoutes.accessDenied
            }
        }
    },
    {
        path: 'view-forms',
        loadChildren: () => import('./view-forms/view-forms.module').then(m => m.ViewFormsPageModule)
    },
    {
        path: 'view-offline-form',
        loadChildren: () => import('./view-offline-form/view-offline-form.module').then(m => m.ViewOfflineFormModule)
    },
    {
        path: 'form-flow',
        loadChildren: () => import('./form-flow/form-flow.module').then(m => m.FormFlowPageModule),
        canActivate: [PermissionGuard],
        data: {
            permissions: {
                restrictedTo: {
                    userTypes: [UserGroup.PATIENT]
                },
                redirectTo: PageRoutes.accessDenied
            }
        }
    },
    {
        path: ':type',
        loadChildren: () => import('./form-types/form-types.module').then(m => m.FormTypesPageModule),
        canActivate: [PermissionGuard],
        data: {
            permissions: {
                restrictedTo: {
                    userTypes: [UserGroup.PATIENT],
                    restrictedType: 'draft-forms' 
                },
                redirectTo: PageRoutes.accessDenied
            }
        }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class FormCenterRoutingModule { }
