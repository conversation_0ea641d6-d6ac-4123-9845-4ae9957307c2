/* eslint-disable @typescript-eslint/no-explicit-any */
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { ViewFormsPage } from 'src/app/pages/form-center/view-forms/view-forms.page';
import { Router, RouterModule } from '@angular/router';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { of, throwError } from 'rxjs';
import { HttpService } from 'src/app/services/http-service/http.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Constants } from 'src/app/constants/constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { ApplessResponse } from 'src/app/interfaces/login';

describe('ViewFormsPage', () => {
  let component: ViewFormsPage;
  let fixture: ComponentFixture<ViewFormsPage>;
  let sharedService: SharedService;
  let httpService: HttpService;
  let common: CommonService;
  let socketService: SocketService;
  let router: Router;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ViewFormsPage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        RouterTestingModule
      ],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        CommonService,
        HttpService,
        SocketService,
        Idle,
        IdleExpiry,
        Keepalive,
        InAppBrowser,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    socketService = TestBed.inject(SocketService);
    common = TestBed.inject(CommonService);
    spyOn(common, 'showMessage').and.stub();
    spyOn(common, 'redirectToPage').and.stub();
    router = TestBed.inject(Router);
    spyOn(router, 'getCurrentNavigation').and.returnValue({
      extras: { state: { viewData: { formStatus: true, form: { id: 1, name: '', formStatus: 'draft', isCampaignForm: true } } } }
    } as any);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);

    httpService = TestBed.inject(HttpService);
    fixture = TestBed.createComponent(ViewFormsPage);
    component = fixture.componentInstance;
    component.structuredFormData = [];
    component.formReq = { clearForm: 1 };
    component.form = {
      staff_facing: '',
      recipient_id: '1',
      allowEdit: '',
      tag_meta: JSON.stringify({ stafffill: '' })
    };
    component.viewData = { form: component.form, recipient: { patientId: 1 } };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute checkAllowEditForm', () => {
    spyOn(sharedService, 'checkAllowEditForm').and.resolveTo({ status: true });
    component.checkAllowEditForm();
    expect(component.checkAllowEditForm).toBeTruthy();
  });
  it('execute checkFormSubmitted: pending or draft', () => {
    spyOn(httpService, 'doPost').and.returnValue(of({}));
    component.checkFormSubmitted();
    expect(component.checkFormSubmitted).toBeTruthy();
  });
  it('execute checkFormSubmitted: completed or archived', () => {
    spyOn(httpService, 'doPost').and.returnValue(of({ created_on: '34763287949', structuredFormResult: [1, 2] }));

    component.checkFormSubmitted();
    expect(component.checkFormSubmitted).toBeTruthy();
  });
  it('execute clearFormData', () => {
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.clearFormData();
    expect(component.clearFormData).toBeTruthy();
  });
  it('execute viewFormByUrl', () => {
    component.form.allowEdit = 'true';
    component.viewFormByUrl();
    expect(component.viewFormByUrl).toBeTruthy();
  });
  it('execute viewFormByUrl: for draft form, guid should be reused', () => {
    component.viewData = {
      form: { form_guid: 'test-guid-2321' }
    };
    component.formStatus = Constants.formDraftStatus;
    component.viewFormByUrl();
    expect(component.viewFormByUrl).toBeTruthy();
    expect(component.formReq.uniqueFormIdentity).toEqual('test-guid-2321');
  });
  it('execute viewFormByUrl: for non-draft form, a new guid should be generated', () => {
    component.viewData = {
      form: { form_guid: 'test-guid-2321' }
    };
    component.formStatus = Constants.formPendingStatus;
    component.viewFormByUrl();
    expect(component.viewFormByUrl).toBeTruthy();
    expect(component.formReq.uniqueFormIdentity).not.toEqual('test-guid-2321');
  });
  it('execute viewFormByUrl: append Submission source', () => {
    component.appLessResponse = { isReminder: 1 } as ApplessResponse;
    component.viewFormByUrl();
    expect(component.viewFormByUrl).toBeTruthy();
    expect(component.formReq.submissionSource).toEqual(component.appLessResponse.isReminder);
  });

  it('execute viewFormByUrl: for staff facing form', () => {
    component.viewData = {
      facing: 'staff-facing',
      form: { recipient_id: '', tag_meta: JSON.stringify({ stafffill: '' }) },
      recipient: {}
    };
    component.viewFormByUrl();
    expect(component.viewFormByUrl).toBeTruthy();
  });
  it('execute viewFormByUrl: pending and does not allow edit', () => {
    component.formStatus = 'pending';
    component.viewData = {
      facing: 'patient',
      form: { recipient_id: '', tag_meta: JSON.stringify({ stafffill: '' }) },
      recipient: { role: Constants.roleName.caregiver }
    };
    component.viewFormByUrl();
    expect(component.viewFormByUrl).toBeTruthy();
  });
  it('execute viewFormByUrl: patient', () => {
    component.form = { allowEdit: 'true' };
    component.viewData = {
      facing: 'patient',
      form: { recipient_id: '', tag_meta: JSON.stringify({ stafffill: '' }) },
      recipient: { role: Constants.roleName.caregiver }
    };
    component.viewFormByUrl();
    expect(component.viewFormByUrl).toBeTruthy();
  });
  it('execute ngOnInit: practitioner', () => {
    component.viewData = {
      facing: 'practitioner',
      associatedPatient: { userid: '' },
      form: {
        staff_facing: '',
        tag_meta: JSON.stringify({ stafffill: '' }),
        practitioner_id: 2,
        form_submission_id: 3
      },
      recipient: { patientId: 1, userid: 3 }
    };
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ data: '' }]));
    spyOn(sharedService, 'sendFormToRecipients').and.returnValue(of({ send_to: [1], sentId: 3, status: 1 }));
    component.formStatus = 'draft';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ngOnInit: not practitioner, status as Draft', () => {
    component.viewData = {
      facing: 'staff',
      recipient: { displayname: '' },
      form: { patientName: '' }
    };
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ formData: { submissionId: 1, form_Success_message: 'Draft' } }]));
    spyOn(sharedService, 'trackActivity').and.stub();
    component.form = { name: '', formStatus: 'Draft' };
    component.formStatus = 'Draft';
    component.userData = { displayName: '' };
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ngOnInit: not practitioner, status as pending', () => {
    component.viewData = {
      facing: 'staff',
      recipient: { displayname: '' },
      form: { patientName: '' }
    };
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ formData: { submissionId: 1, form_Success_message: 'pending' } }]));
    spyOn(sharedService, 'trackActivity').and.stub();
    component.form = { name: '', formStatus: 'pending' };
    component.formStatus = 'pending';
    component.userData = { displayName: '' };
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ngOnInit: not practitioner, status not as  Draft/pending', () => {
    component.viewData = {
      facing: 'staff',
      recipient: { displayname: '' },
      form: { patientName: '' }
    };
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ formData: { submissionId: 1, form_Success_message: 'completed' } }]));
    spyOn(sharedService, 'trackActivity').and.stub();
    component.form = { name: '', formStatus: 'pending' };
    component.formStatus = 'completed';
    component.userData = { displayName: '' };
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ionViewDidLeave', () => {
    component.ionViewDidLeave();
    expect(component.ionViewDidLeave).toBeTruthy();
  });
  it('execute formRedirect : if', () => {
    component.form.formStatus = Constants.formStatusDraft;
    component.formRedirect();
    expect(component.formRedirect).toBeTruthy();
  });
  it('execute formRedirect : else', () => {
    component.formRedirect();
    expect(component.formRedirect).toBeTruthy();
  });
  it('execute formRedirect : else if', () => {
    component.formStatus = Constants.formPendingStatus;
    component.form.staff_facing = 3;
    component.formRedirect();
    expect(component.formRedirect).toBeTruthy();
  });
  it('execute checkDraftForm', () => {
    spyOn(sharedService, 'checkDraftForm').and.resolveTo({ status: true });
    component.checkDraftForm();
    expect(component.checkDraftForm).toBeTruthy();
  });
  it('execute pageLoad', () => {
    component.pageLoad();
    expect(component.pageLoad).toBeTruthy();
  });
  it('execute hideLoader', fakeAsync(() => {
    component.hideLoader();
    tick(3000);
    expect(component.hideLoader).toBeTruthy();
  }));
  it('execute viewFormDetails', () => {
    spyOn(httpService, 'doPost').and.returnValue(throwError({}));
    component.viewFormDetails();
    expect(component.viewFormDetails).toBeTruthy();
  });

  it('execute viewFormDetails', () => {
    spyOn(httpService, 'doPost').and.returnValue(of({}));
    component.viewFormDetails();
    expect(component.viewFormDetails).toBeTruthy();
  });

  it('execute postMessageCallback: AllowEditFormSubmitted', () => {
    component.postMessageCallback({ data: { AllowEditFormSubmitted: true } });
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute postMessageCallback: formIframeHeight', () => {
    component.postMessageCallback({ data: { formIframeHeight: 100 } });
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute postMessageCallback: mf_iframe_height', () => {
    component.postMessageCallback({ data: 'mf_iframe_height=100' });
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute postMessageCallback: scrollTopformsubmit', () => {
    component.postMessageCallback({ data: 'scrollTopformsubmit=true' });
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute postMessageCallback: loaderOnSubmitForm', () => {
    component.postMessageCallback({ data: 'loaderOnSubmitForm=true' });
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute postMessageCallback: hideFormSubmitLoader', () => {
    component.postMessageCallback({ data: 'hideFormSubmitLoader=true' });
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute postMessageCallback: iframeLanguage', () => {
    component.postMessageCallback({ data: 'mf_iframe_language_code=true' });
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute postMessageCallback: iframePageTitle', () => {
    component.postMessageCallback({ data: 'mf_iframe_page_title=true' });
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute formRedirect', () => {
    component.formSendMode = 'appless';
    sharedService.userData.appLessSession = true;
    component.formRedirect();
    expect(component.postMessageCallback).toBeTruthy();
  });
  it('execute ngOnInit: no view data', () => {
    component.viewData = undefined;
    sharedService.brandConfig = {
      serverUrlCustom: 'test',
      appName: 'a',
      tenantId: 434,
      title: '324',
      alias: 'f',
      machFormUrl: 'fds',
      theme: ''
    };
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ngOnInit: practitioner alternate', () => {
    component.viewData = {
      facing: 'practitioner',
      associatedPatient: { userid: '' },
      form: {
        staff_facing: '',
        tag_meta: JSON.stringify({ stafffill: '' }),
        practitioner_id: 2,
        form_submission_id: 3,
        alternateSelectedId: '4332--453'
      },
      recipient: { patientId: 1, userid: 3 }
    };
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ data: '' }]));
    spyOn(sharedService, 'sendFormToRecipients').and.returnValue(of({ send_to: [1], sentId: 3, status: 1 }));
    component.formStatus = 'draft';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });

  describe('clearFormInBrowser', () => {
    it('should not execute script when not in mobile view', () => {
      // Arrange
      component.mobileView = false;
      component.formEmbedUrl = 'http://test.com/';
      component.formReq = { id: 1, name: 'test' };

      // Act
      component.clearFormInBrowser();

      // Assert
      expect(component.clearFormInBrowser).toBeTruthy();
    });
    it('should execute script when not in mobile view', () => {
      // Arrange
      component.mobileView = true;
      component.formEmbedUrl = 'http://test.com/';
      component.formReq = { id: 1, name: 'test' };
      sharedService.browser = {
        executeScript: jasmine.createSpy('executeScript').and.returnValue(Promise.resolve(['']))
      } as unknown as InAppBrowserObject;
      // Act
      component.clearFormInBrowser();

      // Assert
      expect(component.clearFormInBrowser).toBeTruthy();
    });

    it('should not execute script when browser is not defined', () => {
      // Arrange
      component.mobileView = true;
      sharedService.browser = undefined;
      component.formEmbedUrl = 'http://test.com/';
      component.formReq = { id: 1, name: 'test' };

      // Act
      component.clearFormInBrowser();

      // Assert - since browser is undefined, we can't directly test the executeScript
      // but we can verify no error was thrown
      expect(true).toBeTruthy();
    });

    it('should execute script with correct URL when in mobile view and browser exists', () => {
      // Arrange
      component.mobileView = true;
      component.formEmbedUrl = 'http://test.com';
      component.formReq = {
        id: 1,
        name: 'test',
        toId: '123',
        patientId: '456'
      };

      // Restore the browser object with spy
      sharedService.browser = {
        executeScript: jasmine.createSpy('executeScript').and.returnValue(Promise.resolve([''])),
        close: jasmine.createSpy('close'),
        on: jasmine.createSpy('on').and.returnValue(of({})),
        _objectInstance: {},
        _loadAfterBeforeload: jasmine.createSpy('_loadAfterBeforeload'),
        show: jasmine.createSpy('show'),
        hide: jasmine.createSpy('hide'),
        insertCSS: jasmine.createSpy('insertCSS').and.returnValue(Promise.resolve())
      } as unknown as InAppBrowserObject;

      // Act
      component.clearFormInBrowser();

      // Assert
      expect(sharedService.browser.executeScript).toHaveBeenCalled();

      // Check that the executeScript was called with a code parameter that includes:
      // 1. window.location.href assignment
      // 2. The formEmbedUrl
      // 3. The clearForm parameter set to 1
      // 4. The mf_page parameter set to 1
      const executeScriptArg = (sharedService.browser.executeScript as jasmine.Spy).calls.mostRecent().args[0];
      expect(executeScriptArg.code).toContain('window.location.href');
      expect(executeScriptArg.code).toContain('http://test.com');
      expect(executeScriptArg.code).toContain('clearForm=1');
      expect(executeScriptArg.code).toContain('mf_page=1');
    });
  });

  describe('formRedirect', () => {
    beforeEach(() => {
      // Reset dismissConfirmationPopup before each test
      component.dismissConfirmationPopup = false;
      component.form = { id: 1, formStatus: 'pending' };
      component.formStatus = 'pending';
      component.formSendMode = undefined;
      component.consoloFlag = false;
      spyOn(component, 'resetPage').and.stub();
      spyOn(component, 'redirectToAppLessHome').and.stub();
    });

    it('should return early when dismissConfirmationPopup is true', () => {
      // Arrange
      component.dismissConfirmationPopup = true;
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(false);

      // Act
      component.formRedirect();

      // Assert
      expect(sharedService.isAppLessHomeLoggedIn).not.toHaveBeenCalled();
      expect(component.redirectToAppLessHome).not.toHaveBeenCalled();
      expect(component.resetPage).not.toHaveBeenCalled();
      expect(common.redirectToPage).not.toHaveBeenCalled();
    });

    it('should set dismissConfirmationPopup to true and redirect to appless home when logged in', () => {
      // Arrange
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(true);
      const message = 'Test message';

      // Act
      component.formRedirect(message);

      // Assert
      expect(component.dismissConfirmationPopup).toBe(true);
      expect(sharedService.isAppLessHomeLoggedIn).toHaveBeenCalled();
      expect(component.redirectToAppLessHome).toHaveBeenCalledWith(component.form.id, message);
    });

    it('should set dismissConfirmationPopup to true and reset page for appless session', () => {
      // Arrange
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(false);
      component.formSendMode = Constants.appless;
      sharedService.userData = { appLessSession: true } as any;

      // Act
      component.formRedirect();

      // Assert
      expect(component.dismissConfirmationPopup).toBe(true);
      expect(component.resetPage).toHaveBeenCalled();
    });

    it('should set dismissConfirmationPopup to true and reset page for consolo flag', () => {
      // Arrange
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(false);
      component.consoloFlag = true;

      // Act
      component.formRedirect();

      // Assert
      expect(component.dismissConfirmationPopup).toBe(true);
      expect(component.resetPage).toHaveBeenCalled();
    });

    it('should redirect to draft forms when form status is draft', () => {
      // Arrange
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(false);
      component.form.formStatus = Constants.formStatusDraft;

      // Act
      component.formRedirect();

      // Assert
      expect(component.dismissConfirmationPopup).toBe(true);
      expect(common.redirectToPage).toHaveBeenCalledWith(PageRoutes.draftForms);
      expect(component.resetPage).toHaveBeenCalled();
    });

    it('should redirect to pending forms when form status is pending and not practitioner facing', () => {
      // Arrange
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(false);
      component.formStatus = Constants.formPendingStatus;
      component.form.staff_facing = 'not-practitioner';

      // Act
      component.formRedirect();

      // Assert
      expect(component.dismissConfirmationPopup).toBe(true);
      expect(common.redirectToPage).toHaveBeenCalledWith(PageRoutes.pendingForms);
      expect(component.resetPage).toHaveBeenCalled();
    });

    it('should redirect to completed forms for other statuses', () => {
      // Arrange
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(false);
      component.formStatus = 'completed';

      // Act
      component.formRedirect();

      // Assert
      expect(component.dismissConfirmationPopup).toBe(true);
      expect(common.redirectToPage).toHaveBeenCalledWith(PageRoutes.completedForms);
      expect(component.resetPage).toHaveBeenCalled();
    });

    it('should not call redirectToPage when form is blank', () => {
      // Arrange
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(false);
      component.form = null;

      // Act
      component.formRedirect();

      // Assert
      expect(component.dismissConfirmationPopup).toBe(true);
      expect(common.redirectToPage).not.toHaveBeenCalled();
      expect(component.resetPage).not.toHaveBeenCalled();
    });

    it('should prevent multiple calls when dismissConfirmationPopup is already set', () => {
      // Arrange
      spyOn(sharedService, 'isAppLessHomeLoggedIn').and.returnValue(false);

      // First call
      component.formRedirect();
      expect(component.dismissConfirmationPopup).toBe(true);

      // Reset spies to track second call
      (common.redirectToPage as jasmine.Spy).calls.reset();
      (component.resetPage as jasmine.Spy).calls.reset();

      // Act - Second call
      component.formRedirect();

      // Assert - Second call should be ignored
      expect(common.redirectToPage).not.toHaveBeenCalled();
      expect(component.resetPage).not.toHaveBeenCalled();
    });
  });

  describe('isUserTagSelected', () => {
    it('should return true when recipient userid starts with tag-', () => {
      component.viewData = {
        recipient: { userid: 'tag-123' }
      };
      expect(component.isUserTagSelected()).toBe(true);
    });

    it('should return true when recipient userId starts with tag-', () => {
      component.viewData = {
        recipient: { userId: 'tag-456' }
      };
      expect(component.isUserTagSelected()).toBe(true);
    });

    it('should return false when recipient userid does not start with tag-', () => {
      component.viewData = {
        recipient: { userid: '123' }
      };
      expect(component.isUserTagSelected()).toBe(false);
    });

    it('should return false when no recipient data exists', () => {
      component.viewData = {};
      expect(component.isUserTagSelected()).toBe(false);
    });
  });
});
