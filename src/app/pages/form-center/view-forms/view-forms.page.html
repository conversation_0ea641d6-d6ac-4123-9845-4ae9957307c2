<app-header *ngIf="showHeader" [headerTitle]="formName"></app-header>
<ion-content class="view-forms-page ion-padding">
  <div *ngIf="formUrl">
    <div id="iframe-div-top"></div>
    <div class="common-document-head-title"
      *ngIf="viewData.recipient && viewData.facing !== constants.patientValue && viewData.recipient.patientAssociation">
      {{ 'GENERAL.ASSOCIATED_PATIENT' | translate }}:{{
      viewData?.facing === constants.practitioner ? viewData.associatedPatient.displayname :
      viewData?.recipient?.displayname || ''
      }}
    </div>
    <div class="common-document-head-title" *ngIf="form.formStatus === constants.formStatusDraft">
      {{ 'LABELS.PATIENT_NAME' | translate }}: {{ form?.patientName || '' }}
    </div>
    <div class="form-edit-message" *ngIf="form?.completedFormRecipients">
      <span>{{ 'LABELS.COMPLETED_FORMS_RECIPIENTS' | translate }}:&nbsp;</span>{{ form?.completedFormRecipients }}
    </div>
    <div class="form-edit-message" *ngIf="form?.message">
      <span>{{ 'LABELS.MESSAGE' | translate }}:&nbsp;</span>{{ form?.message }}
    </div>
    <a (click)="clearFormData()" class="clear-button" *ngIf="viewData.facing" id="clear-form-data">{{
      'BUTTONS.CLEAR_FORM' | translate }}</a>
    <div id="iframe-overlay" class="hide">
      <div class="loader-container">
        <div id="loader-img">
          <img src="assets/images/loaderspin.gif" alt="gif-image" />
        </div>
        <div [hidden]="form?.isCampaignForm" id="loader-message"></div>
      </div>
    </div>
    <iframe allowTransparency="true" frameborder="0" scrolling="no" style="display: none" [src]="authUrl"
      id="auth-form-view-frame" *ngIf="authUrl" (load)="hideLoader()"></iframe>
    <iframe [title]="iframeTitle"  (load)="pageLoad()" allowTransparency="true" id="form-view-frame" frameborder="0" scrolling="no"
      style="width: 100%; overflow-y: scroll; min-height: 600px; border: none" [src]="formUrl"
      *ngIf="formUrl && !mobileView"></iframe>
  </div>
  <div *ngIf="formDetails && !applessworkFlowComplete">
    <div class="subheader ion-padding-start">
      <div *ngIf="
          form?.allowEdit?.toString() === '1' &&
          ((form.recipient_id.toString() === userData.userId.toString() && form.staff_facing !== constants?.checkWorkFlow?.staffFacing) ||
            (form.staff_facing === constants?.checkWorkFlow?.staffFacing && userData.userId.toString() === form.fromId.toString()))
        " class="icon-class" id="view-form-by-url" (click)="viewFormByUrl()">
        <ion-icon src="assets/icon/material-svg/edit.svg" slot="start"></ion-icon>
      </div>
    </div>
    <div class="form-position">
      <div class="form-info">
        <span class="ion-text-capitalize">{{ 'LABELS.SENT_BY' | translate }}:</span> {{ formInfo?.createdUser
        }}<br /><br />
        <div *ngIf="
            (formInfo?.facing_new !== constants.practitionerFacingValue &&
              !isBlank(formInfo?.patientName) &&
              !(formInfo?.facing_new === constants.staffFacingValue && formInfo?.assosiatePatient == formInfo?.createdUser)) ||
            (formInfo?.facing_new === constants.practitionerFacingValue &&
              formInfo?.patientAssociation === constants.trueAsString &&
              formInfo?.caregiver_displayname !== formInfo?.createdUser)
          ">
          <span>{{ 'LABELS.PATIENT_NAME' | translate }}:</span>
          {{
          formInfo?.facing_new === constants.practitionerFacingValue &&
          formInfo?.patientAssociation === constants.trueAsString &&
          formInfo?.caregiver_displayname !== formInfo.createdUser
          ? formInfo?.caregiver_displayname
          : formInfo?.patientName
          }}
          <br /><br />
          <div *ngIf="
              formInfo?.facing_new !== constants.practitionerFacingValue &&
              isBlank(formInfo?.patientName) &&
              !(formInfo?.facing_new === constants.staffFacingValue && formInfo?.assosiatePatient == formInfo?.createdUser) &&
              formInfo?.caregiver_displayname
            ">
            <span>{{ 'LABELS.CAREGIVER' | translate }}:</span> {{ formInfo?.caregiver_displayname }} <br /><br />
          </div>
          <div *ngIf="sharedService.isMultiAdmissionsEnabled && !isBlank(formInfo?.admissionName)"><span>{{ 'LABELS.ADMISSION' | translate }}:</span> {{ formInfo?.admissionName }}<br /><br /></div>
          <span>{{ 'LABELS.DOB' | translate }}:</span>
          {{ formInfo?.pat_dob | date: constants.dateFormat.mmddyyyy }}
          <br /><br />
          <span>{{ 'LABELS.MRN' | translate }}:</span> {{ formInfo?.mrn || '-' }}<br /><br />
        </div>
        <div
          *ngIf="formInfo?.facing_new === constants.practitionerFacingValue && viewData.form.staff_facing == constants.practitionerFacingValue">
          <span>{{ 'LABELS.FILLED_BY' | translate }}:</span>
          {{ viewData?.form?.patientFirstname }} {{ viewData?.form?.patientLastname }}<br /><br />
        </div>
        <span>{{ 'LABELS.FORM_NAME' | translate }}:</span> <span [innerHTML]="form.formName"></span> <br /><br />
        <span>{{ 'LABELS.SUBMITTED_ON' | translate }}:</span>
        {{ submittedOn * 1000 | date: constants.dateFormat.mmmmdy }}
      </div>
      <br />

      <ion-grid *ngIf="structuredFormData">
        <ion-row class="row-width">
          <ion-col size="6">
            <b>{{ 'LABELS.LABEL' | translate }}</b>
          </ion-col>
          <ion-col size="6">
            <b>{{ 'LABELS.VALUE' | translate }}</b>
          </ion-col>
        </ion-row>
        <ng-container *ngFor="let data of structuredFormData; let id = index">
          <ion-row *ngIf="data.value">
            <ion-col
              size="6"
              *ngIf="data.element_type !== constants.formElementTypes.section && data.element_type !== constants.formElementTypes.media"
            >
              <span [innerHTML]="data.label | safe: 'html'"></span>
            </ion-col>
            <span>
              <ion-col size="6" *ngIf="data?.element_type === constants.formElementTypes.media">
                <span [innerHTML]="data.label | safe: 'html'"></span>
              </ion-col>
              <div *ngIf="data?.element_type === constants.formElementTypes.media">
                <span [innerHTML]="data.value | safe: 'html'"></span>
              </div>
            </span>
            <ion-col size="6" *ngIf="data?.element_type === constants.formElementTypes.section">
              <b class="section-class" [innerHTML]="data.label"></b>
            </ion-col>
            <ion-col
              size="6"
              [innerText]="data?.value"
              *ngIf="
                data?.element_type !== constants.formElementTypes.signature &&
                data?.element_type !== constants.formElementTypes.media &&
                data?.element_type !== constants.formElementTypes.file &&
                data?.element_type !== constants.formElementTypes.section
              "
            >
            </ion-col>
            <ion-col
              size="6"
              *ngIf="data?.element_type === constants.formElementTypes.file || data?.element_type === constants.formElementTypes.section"
            >
              <span [innerHTML]="data?.value | safe: 'html'"></span>
            </ion-col>
            <div *ngIf="data?.element_type === constants.formElementTypes.signature">
              <img src="{{ data?.singpad_img_src }}" alt="signature" />
            </div>
          </ion-row>
        </ng-container>
        <hr />
      </ion-grid>
      <div class="basic-info ion-padding-start">
        <span>{{ 'LABELS.SENT_BY_CLINICIAN' | translate }}:</span> {{ formInfo?.createdUserLastname }}
      </div>
    </div>
  </div>
  <ion-row *ngIf="applessworkFlowComplete" class="ion-align-items-center">
    <ion-col size="12" class="text-center">
      <ion-label class="ion-text-wrap" [innerHTML]="applessFormWorkFlowCompletedMessage | safe: 'html'"></ion-label>
    </ion-col>
  </ion-row>
</ion-content>
<app-footer [showBackButton]="!consoloFlag" *ngIf="showFooter"></app-footer>
