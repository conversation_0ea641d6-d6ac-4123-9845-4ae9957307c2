import { <PERSON><PERSON><PERSON>ontroller } from '@ionic/angular';
import { Constants } from 'src/app/constants/constants';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { IonicModule, NavController } from '@ionic/angular';
import { SendFormsPage } from './send-forms.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedModule } from 'src/app/shared.module';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';
import { TestConstants } from 'src/app/constants/test-constants';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';

describe('SendFormsPage', () => {
  let component: SendFormsPage;
  let fixture: ComponentFixture<SendFormsPage>;
  const modalSpy = TestConstants.modalSpy;
  let modalController: ModalController;
  const selectedFormItem = {
    tagId: '27585',
    id: 8082258,
    name: 'Test Form',
    tenant_id: 558,
    formName: 'Test Form',
    signaturePadPresent: false,
    recipientRoles: [7587],
    stafFacing: 'false',
    staffFill: 'true',
    patientAssociation: 'false',
    tag_name: 'aaa patient facing',
    externalFileExchange: false,
    enableSaveDraftStaff: 1,
    enableSaveDraftPatient: 1,
    externalFileDisclosePHI: false,
    populatePreviousSubmission: true,
    confirmActionPrefill: true,
    enableMendatoryFieldChecking: true,
    triggerOnPHI: null,
    enableAppless: false,
    applessDevices: ''
  };
  const form = {
    id: selectedFormItem.id,
    name: selectedFormItem.name,
    populatePreviousSubmission: selectedFormItem.populatePreviousSubmission,
    confirmActionPrefill: selectedFormItem.confirmActionPrefill,
    staffFill: selectedFormItem.staffFill,
    externalFileExchange: selectedFormItem.externalFileExchange
  };
  let navCtrl: NavController;
  const isStaff = selectedFormItem.stafFacing;
  let sharedService: SharedService;
  let httpService: HttpService;
  let router: Router;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SendFormsPage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        SharedModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      providers: [
        SharedService,
        ModalController,
        HttpService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        NativeStorage,
        SQLite,
        PersistentService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    httpService = TestBed.inject(HttpService);
    router = TestBed.inject(Router);
    spyOn(router, 'navigate').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    Object.defineProperty(sharedService, 'userData', { value: TestConstants.userData });
    spyOn(sharedService, 'trackActivity').and.stub();
    fixture = TestBed.createComponent(SendFormsPage);
    navCtrl = TestBed.inject(NavController);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call listForms', () => {
    spyOn(httpService, 'doPost').and.returnValue(of([1]));
    component.listForms();
    expect(component).toBeTruthy();
  });
  it('should call listForms: no items', () => {
    spyOn(httpService, 'doPost').and.returnValue(of([]));
    component.listForms();
    expect(component).toBeTruthy();
  });

  it('listForms throw error', () => {
    spyOn(httpService, 'doPost').and.returnValue(throwError(''));
    component.listForms();
    expect(component).toBeTruthy();
  });

  it('should call filterList', () => {
    const element = {
      target: {
        value: 'te'
      }
    } as any;
    component.loadedForms = [{ name: 'test' }];
    component.filterList(element);
    expect(component.filterList).toBeDefined();
  });
  it('should call filterList: search not found', () => {
    const element = {
      target: {
        value: 'ti'
      }
    } as any;
    component.loadedForms = [{ name: 'test' }];
    component.filterList(element);
    expect(component.filterList).toBeDefined();
  });
  it('should call filterList: no search term', () => {
    const element = {
      target: {
        value: ''
      }
    } as any;
    component.loadedForms = [{ name: 'test' }];
    component.filterList(element);
    expect(component.filterList).toBeDefined();
  });

  it('should call selectForm', () => {
    modalSpy.onDidDismiss.and.resolveTo({ data: null });
    component.selectForm(selectedFormItem);
    expect(component.selectForm).toBeDefined();
  });
  it('should call selectForm: patientDrivenData', () => {
    component.patientDrivenData = { recipient: '' };
    component.selectForm(selectedFormItem);
    expect(component.selectForm).toBeDefined();
  });

  it('should call viewForms', () => {
    const formFacing = isStaff === 'true' ? Constants.staffValue : Constants.patientValue;
    const data = {
      form,
      recipient: component.patientDrivenData?.recipient,
      facing: formFacing
    };
    spyOn(navCtrl, 'navigateForward').and.stub();
    component.viewForms(data);
    expect(component.viewForms).toBeDefined();
  });

  it('should call chooseFromModal: practitioner', fakeAsync(() => {
    const data = {
      data: {
        facing: 'practitioner',
        recipient: { displayname: '', userid: '', patientId: '' },
        form: { name: '', id: '' }
      }
    };
    modalSpy.onDidDismiss.and.resolveTo(data);
    spyOn(sharedService, 'checkAllowEditForm').and.resolveTo({ status: 1 });
    component.chooseFromModal(selectedFormItem.staffFill, parseInt(selectedFormItem.tagId), form, true);
    expect(component.chooseFromModal).toBeTruthy();
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
  }));

  it('should call chooseFromModal', fakeAsync(() => {
    const data = { data: {} };
    modalSpy.onDidDismiss.and.resolveTo(data);
    component.chooseFromModal(selectedFormItem.staffFill, parseInt(selectedFormItem.tagId), form, true);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.chooseFromModal).toBeTruthy();
  }));

  it('should call chooseRecipientsForPractitioner', fakeAsync(() => {
    const formFacing = isStaff === 'true' ? Constants.staffValue : Constants.patientValue;
    const componentProps = {
      tagId: selectedFormItem.tagId,
      form: { ...form, admissionId: '2342-23423' },
      selectedAssociatedPatient: 'demo',
      formFacing
    };
    modalSpy.onDidDismiss.and.resolveTo({ data: { form: {} } });
    component.chooseRecipientsForPractitioner(componentProps, true);
    fixture.whenStable().then(() => {
      fixture.detectChanges();
    });
    expect(component.chooseRecipientsForPractitioner).toBeTruthy();
  }));

  it('should call patientForms', () => {
    spyOn(httpService, 'doPost').and.returnValue(of({ patientForms: [1] }));
    component.patientForms();
    expect(component.patientForms).toBeDefined();
  });
  it('should call patientForms: no items', () => {
    spyOn(httpService, 'doPost').and.returnValue(of({ patientForms: [] }));
    component.patientForms();
    expect(component.patientForms).toBeDefined();
  });
  it('should call patientForms: throw error', () => {
    spyOn(httpService, 'doPost').and.returnValue(throwError(''));
    component.patientForms();
    expect(component.patientForms).toBeDefined();
  });
});
