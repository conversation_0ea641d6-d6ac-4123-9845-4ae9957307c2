import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SendFormsPageRoutingModule } from 'src/app/pages/form-center/send-forms/send-forms-routing.module';
import { SendFormsPage } from 'src/app/pages/form-center/send-forms/send-forms.page';
import { SharedModule } from 'src/app/shared.module';
import { RecipientsComponentModule } from 'src/app/pages/form-center/recipients/recipients.module';
import { ScrollingModule } from '@angular/cdk/scrolling';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, SendFormsPageRoutingModule, SharedModule, ScrollingModule, RecipientsComponentModule],
  declarations: [SendFormsPage]
})
export class SendFormsPageModule {}
