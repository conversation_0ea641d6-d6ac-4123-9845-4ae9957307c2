<app-header headerTitle="LABELS.CHOOSE_FORM"></app-header>
<ion-searchbar [disabled]="!loadedForms" (ionInput)="filterList($event)" id="search-forms" [(ngModel)]="searchText"></ion-searchbar>
<ion-content class="send-forms-page">
  <ion-list>
    @for (item of forms | orderBy: 'asc' : 'name'; track item.id; let id = $index; let l = $last) {
      <ion-item [lines]="l === true ? 'none' : ''" class="item" (click)="selectForm(item)" id="form-{{ id }}" tappable>
        <ion-label>{{ item.name }}</ion-label>
        <ion-badge [ngClass]="item.stafFacing === 'true' ? 'badge-staff' : 'badge-patient'" translate mode="ios">
          {{ item.stafFacing === 'true' ? 'GENERAL.STAFF' : item.stafFacing === constants.practitioner ? 'GENERAL.PRACTITIONER' : 'GENERAL.PATIENT' }}
        </ion-badge>
        <ion-icon name="chevron-forward" color="de-york"></ion-icon>
      </ion-item>
    }
  </ion-list>
  @if (errorMessage) {
    <div class="common-no-items" [ngClass]="{ 'no-forms': loadedForms?.length === 0 }">{{ errorMessage }}</div>
  } @else if (!loadedForms) {
    <app-skeleton-loader [skeletonWidths]="[100]" [count]="10"></app-skeleton-loader>
  }
</ion-content>
<app-footer></app-footer>
