import { Component, OnInit } from '@angular/core';
import { APIs } from 'src/app/constants/apis';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { RecipientsComponent } from 'src/app/pages/form-center/recipients/recipients.component';
import { InputCustomEvent, ModalController } from '@ionic/angular';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Router, NavigationExtras } from '@angular/router';
import { isBlank } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';
import { Activity } from 'src/app/constants/activity';
import { PageRoutes } from 'src/app/constants/page-routes';
import { Config } from 'src/app/constants/config';
import { LoginResponse } from 'src/app/interfaces/login';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { SurveyNamesResponse } from 'src/app/interfaces/common-interface';

@Component({
  selector: 'app-send-forms',
  templateUrl: './send-forms.page.html',
  styleUrls: ['./send-forms.page.scss']
})
export class SendFormsPage implements OnInit {
  forms: SurveyNamesResponse = [];
  loadedForms: SurveyNamesResponse;
  userData: LoginResponse;
  patientDrivenData;
  errorMessage = '';
  noFormMessage = '';
  constants = Constants;
  searchText: string;
  noMatchesMessage = '';
  selectedForm;
  formType;
  constructor(
    private readonly httpService: HttpService,
    readonly sharedService: SharedService,
    private readonly modalController: ModalController,
    private readonly common: CommonService,
    private readonly persistentService: PersistentService,
    private readonly router: Router
  ) {
    this.userData = this.sharedService.userData;
    if (!isBlank(this.router.getCurrentNavigation()?.extras.state)) {
      this.patientDrivenData = this.router.getCurrentNavigation().extras.state.patient;
      this.formType = this.router.getCurrentNavigation().extras.state.formType;
    } else {
      this.patientDrivenData = undefined;
      this.formType = undefined;
    }
    this.noFormMessage = this.common.getTranslateData('MESSAGES.NO_FORMS_AVAILABLE');
    this.noMatchesMessage = this.common.getTranslateData('MESSAGES.NO_MATCHES_FOUND');
  }

  ngOnInit(): void {
    if (this.patientDrivenData) {
      this.patientForms();
    } else {
      this.listForms();
    }
  }
  listForms(): void {
    let payload: {
      roleId: string;
      enable_practitioner_flow: boolean;
      nursingAgencies?: string;
    } = {
      roleId: this.userData.roleId,
      enable_practitioner_flow: true
    };
    if (this.sharedService.isEnableConfig(Config.enableNursingAgencies)) {
      payload = { ...payload, nursingAgencies: this.userData.nursing_agencies };
    }
    this.httpService.doPost({ endpoint: APIs.getForms, payload, loader: false }).subscribe(
      (response: SurveyNamesResponse) => {
        if (!isBlank(response)) {
          if (this.formType === Constants.formTypes.offline) {
            const res = response.filter((form) => form.stafFacing === Constants.trueAsString);
            this.forms = res;
            this.loadedForms = res;
          } else {
            this.forms = response;
            this.loadedForms = response;
          }
        }
        this.fetchErrorMessage();
      },
      () => {
        this.loadedForms = [];
      }
    );
  }

  filterList(event: InputCustomEvent): void {
    this.errorMessage = '';
    this.forms = this.loadedForms;
    this.searchText = event?.target?.value.toString();
    if (!this.searchText) {
      return;
    }
    this.forms = this.forms?.filter((form) => {
      return form?.name && this.searchText && form.name.toLowerCase().indexOf(this.searchText.toLowerCase()) > -1;
    });
    this.fetchErrorMessage();
  }
  fetchErrorMessage(): void {
    const searchNoMatchesFoundMessage = isBlank(this.forms) ? this.noMatchesMessage : '';
    this.errorMessage = isBlank(this.loadedForms) ? this.noFormMessage : searchNoMatchesFoundMessage;
  }

  selectForm(item): void {
    this.selectedForm = item;
    const isStaff = item.stafFacing;
    const tag = item.tagId;
    const form = {
      id: item.id,
      name: item.name,
      populatePreviousSubmission: item.populatePreviousSubmission,
      confirmActionPrefill: item.confirmActionPrefill,
      staffFill: item.staffFill,
      externalFileExchange: item.externalFileExchange,
      sendCompletedForm: item.sendCompletedForm
    };
    const formFacing = this.getFacing(isStaff);
    if (!isBlank(this.patientDrivenData)) {
      if (this.formType === Constants.formTypes.offline) {
        this.addFormToOffline(form, this.patientDrivenData.recipient);
      } else {
        const data = {
          form,
          recipient: this.patientDrivenData.recipient,
          facing: formFacing
        };
        this.viewForms(data);
      }
    } else {
      const data = {
        displayName: this.userData?.displayName,
        formName: form.name,
        formId: form.id
      };
      this.sharedService.trackActivity({
        type: Activity.forms,
        name: Activity.selectForm,
        des: { data, desConstant: Activity.selectFormDes }
      });

      // associated patient off work flow
      if (item.patientAssociation === 'false' && (formFacing === Constants.practitioner || formFacing === Constants.staffValue)) {
        if (formFacing === Constants.staffValue) {
          if (this.formType === Constants.formTypes.offline) {
            this.addFormToOffline(form);
          } else {
            const navParams = {
              facing: formFacing,
              form,
              recipient: {
                tenantid: this.userData.tenantId,
                userid: this.userData.userId,
                patientAssociation: false
              }
            };
            this.viewForms(navParams);
          }
        } else {
          this.chooseFromModal(isStaff, tag, form, false);
        }
      } else {
        this.chooseFromModal(isStaff, tag, form, true);
      }
    }
  }
  addFormToOffline(form: any, recipient?: any) {
    let recipientId = '';
    let siteId = '';
    if (recipient) {
      recipientId = recipient.role === Constants.roleName.caregiver ? recipient.caregiver_userid : recipient.userId;
      siteId = recipient.siteId.toString();
    }
    this.sharedService.loaderMessage = 'LOADER_MESSAGES.FILE_IS_DOWNLOADING';
    const params = {
      form_id: form.id,
      tenant_id: this.userData.tenantId,
      recipientId
    };
    this.httpService.doGet({ endpoint: APIs.getHtmlForm, extraParams: params, loader: true, useBaseAs: 'forms' }).subscribe((res: any) => {
      this.common.renderIframe(res);
      let formData = {
        firstName: '',
        lastName: '',
        site: '',
        dob: '',
        mrn: '',
        id: '',
        formName: form.name,
        formContent: res
      };
      if (recipientId) {
        formData = {
          ...formData,
          site: recipient.sitename,
          id: recipientId,
          firstName: recipient.role === Constants.roleName.caregiver ? recipient.c_fname : recipient.firstname,
          lastName: recipient.role === Constants.roleName.caregiver ? recipient.c_lname : recipient.lastname,
          dob: recipient.role === Constants.roleName.caregiver ? recipient.caregiver_dob : recipient.dob,
          mrn: recipient.role === Constants.roleName.caregiver ? recipient.caregiverIdentityValue : recipient.IdentityValue
        };
      }
      this.persistentService.addOfflineForm(`${form.id}`, siteId, JSON.stringify(formData)).then(() => {
        this.common.showMessage(this.common.getTranslateData('MESSAGES.OFFLINE_DOWNLOAD_SUCCESSFULLY'));
        this.router.navigate([PageRoutes.offlineForms]);
        this.sharedService.loaderMessage = '';
      });
    });
  }
  viewForms(data): void {
    const navigationExtras: NavigationExtras = {
      state: {
        viewData: data,
        selectedForm: this.selectedForm
      }
    };
    this.searchText = '';
    this.forms = this.loadedForms;
    this.router.navigate([`.${PageRoutes.viewForms}`], navigationExtras);
  }
  getFacing(isStaff: string): string {
    if (isStaff === Constants.trueAsString) {
      return Constants.staffValue;
    }
    if (isStaff === Constants.practitioner) {
      return Constants.practitioner;
    }
    return Constants.patientValue;
  }
  async chooseFromModal(isStaff: string, tag: number, form, associatedPatient: boolean): Promise<void> {
    const formFacing = this.getFacing(isStaff);
    const modal = await this.modalController.create({
      component: RecipientsComponent,
      componentProps: {
        formFacing,
        tagId: tag,
        form,
        associatedPatient,
        formType: this.formType,
        admissionId: ''
      },
      id: 'choose-recipient'
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        let formData = data;
        if (data.facing !== Constants.practitioner || (data.facing === Constants.practitioner && !associatedPatient)) {
          if (data.facing === Constants.practitioner && !associatedPatient) {
            formData = {
              form: data.form,
              practitionerId: data.recipient.userid,
              recipient: {
                tenantid: this.userData.tenantId,
                userid: this.userData.userId,
                patientAssociation: false
              },
              facing: data.facing,
              interactionChannel: data.interactionChannel
            };
          }
          this.viewForms(formData);
        } else {
          const activityData = {
            displayName: this.userData.displayName,
            recipientName: data.recipient.displayname,
            recipientId: data.recipient.userid,
            formName: data.form.name,
            formId: data.form.id
          };
          this.sharedService.trackActivity({
            type: Activity.forms,
            name: Activity.selectAssociatedPatient,
            des: {
              data: activityData,
              desConstant: Activity.selectAssociatedPatientDes
            }
          });
          const payload = {
            formId: data.form.id,
            patientId: data.recipient.patientId ? data.recipient.patientId : data.recipient.userid,
            formName: data.form.name,
            patientName: data.recipient.displayname,
            admissionId: data.form.admissionId
          };

          this.sharedService.checkAllowEditForm(payload, activityData).then((response) => {
            if (response.status) {
              this.chooseRecipientsForPractitioner(data, associatedPatient);
            }
          });
        }
      }
    });
    return modal.present();
  }
  async chooseRecipientsForPractitioner(formData, associatedPatient): Promise<void> {
    const modal = await this.modalController.create({
      component: RecipientsComponent,
      componentProps: {
        tagId: formData.tagId,
        form: formData.form,
        selectedAssociatedPatient: formData.recipient,
        formFacing: formData.facing,
        associatedPatient,
        copyOfRecipient: formData.copyOfRecipient,
        formType: this.formType,
        admissionId: formData.form.admissionId
      },
      id: 'choose-recipient'
    });
    modal.onDidDismiss().then(({ data }) => {
      if (data) {
        const recipientData = data;
        recipientData.form.admissionId = data.form.admissionId || formData.form.admissionId;
        if (data.facing === Constants.practitioner && associatedPatient) {
          recipientData.recipient.patientAssociation = true;
        }
        this.viewForms(recipientData);
      }
    });
    return modal.present();
  }
  patientForms(): void {
    const payload = {
      patientId: this.patientDrivenData?.recipient?.userId,
      type: Constants.get,
      dataType: Constants.patientForms
    };
    this.httpService
      .doPost({
        endpoint: APIs.getPatientAssocForms,
        payload,
        contentType: 'form',
        loader: true
      })
      .subscribe(
        (forms) => {
          if (!isBlank(forms.patientForms)) {
            if (this.formType === Constants.formTypes.offline) {
              const res = forms.patientForms.filter((form) => form.stafFacing === Constants.trueAsString);
              this.forms = res;
              this.loadedForms = res;
            } else {
              this.forms = forms.patientForms;
              this.loadedForms = forms.patientForms;
            }
          }
          this.fetchErrorMessage();
        },
        (error) => {
          this.sharedService.errorHandler(error);
          this.fetchErrorMessage();
        }
      );
  }
}
