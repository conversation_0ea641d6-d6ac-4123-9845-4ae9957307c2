import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { SendFormsPage } from './send-forms.page';

const routes: Routes = [
    {
        path: '',
        component: SendFormsPage,
        data: {
            title: 'TITLES.SEND_FORMS'
        }
    },
    {
        path: 'search-patients',
        loadChildren: () => import('../../../pages/user/search-patients/search-patients.module').then(m => m.SearchPatientsPageModule)
    },

];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class SendFormsPageRoutingModule { }
