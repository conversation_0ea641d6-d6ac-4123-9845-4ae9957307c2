import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of } from 'rxjs';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { OfflineForm } from 'src/app/services/persistent-service/offline-form.model';
import { ViewOfflineFormPage } from './view-offline-form.page';

describe('ViewOfflineFormPage', () => {
  let component: ViewOfflineFormPage;
  let fixture: ComponentFixture<ViewOfflineFormPage>;
  let persistentService: jasmine.SpyObj<PersistentService>;
  let commonService: jasmine.SpyObj<CommonService>;
  let router: jasmine.SpyObj<Router>;
  let iframe;
  let docFrame;

  beforeEach(async () => {
    const persistentServiceSpy = jasmine.createSpyObj('PersistentService', ['updateOfflineFormData', 'getOfflineFormData']);
    const commonServiceSpy = jasmine.createSpyObj('CommonService', ['showMessage', 'getTranslateData']);
    const routerSpy = jasmine.createSpyObj('Router', ['getCurrentNavigation']);

    await TestBed.configureTestingModule({
      declarations: [ViewOfflineFormPage],
      providers: [
        { provide: PersistentService, useValue: persistentServiceSpy },
        { provide: CommonService, useValue: commonServiceSpy },
        { provide: Router, useValue: routerSpy },
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({})
          }
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ViewOfflineFormPage);
    component = fixture.componentInstance;
    persistentService = TestBed.inject(PersistentService) as jasmine.SpyObj<PersistentService>;
    commonService = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Create and append a fake iframe
    iframe = document.createElement('iframe');
    iframe.id = 'view-form';
    document.body.appendChild(iframe);

    docFrame = iframe.contentDocument || iframe.contentWindow.document;

    // Spy on DOM methods
    spyOn(docFrame, 'getElementById').and.callFake((id) => {
      return {
        value: '',
        dispatchEvent: jasmine.createSpy('dispatchEvent')
      };
    });

    spyOn(docFrame, 'getElementsByName').and.callFake((name) => {
      return [{ value: 'value1', click: jasmine.createSpy('click') }];
    });

    Object.defineProperty(<any>iframe.contentWindow, 'getFormData', {
      value: jasmine.createSpy('getFormData'),
      writable: true,
      configurable: true
    });
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call goBackToForms if manualSave is true', async () => {
    spyOn(component, 'goBackToForms');

    const event = {
      data: 'formData%5B0%5D%5Bid%5D=element_6&formData%5B0%5D%5Bvalue%5D=&formData%5B0%5D%5Btype%5D=select&formData%5B1%5D%5Bid%5D=element_3&formData%5B1%5D%5Bvalue%5D=&formData%5B1%5D%5Btype%5D=radio&formData%5B2%5D%5Bid%5D=element_5_1&formData%5B2%5D%5Bvalue%5D=0&formData%5B2%5D%5Btype%5D=checkbox&formData%5B3%5D%5Bid%5D=element_5_2&formData%5B3%5D%5Bvalue%5D=0&formData%5B3%5D%5Btype%5D=checkbox&formData%5B4%5D%5Bid%5D=element_5_3&formData%5B4%5D%5Bvalue%5D=0&formData%5B4%5D%5Btype%5D=checkbox&formData%5B5%5D%5Bid%5D=element_2&formData%5B5%5D%5Bvalue%5D=&formData%5B5%5D%5Btype%5D=textarea&formData%5B6%5D%5Bid%5D=element_1&formData%5B6%5D%5Bvalue%5D=&formData%5B6%5D%5Btype%5D=text&formData%5B7%5D%5Bid%5D=element_4&formData%5B7%5D%5Bvalue%5D=&formData%5B7%5D%5Btype%5D=text&formData%5B8%5D%5Bid%5D=element_7_1&formData%5B8%5D%5Bvalue%5D=&formData%5B8%5D%5Btype%5D=text&formData%5B9%5D%5Bid%5D=element_7_2&formData%5B9%5D%5Bvalue%5D=&formData%5B9%5D%5Btype%5D=text&formData%5B10%5D%5Bid%5D=element_8&formData%5B10%5D%5Bvalue%5D=&formData%5B10%5D%5Btype%5D=date&manualSave=true&eSignatureLog'
    };
    component.formData = { offlineFormId: 456 };
    persistentService.updateOfflineFormData.and.returnValue(Promise.resolve({}));

    await component.onIncomingMessage(event);

    expect(component.goBackToForms).toHaveBeenCalled();
  });

  it('should call getOfflineFormData if formLoaded is true', async () => {
    const event = {
      data: 'formLoaded=true'
    };
    const mockForm: OfflineForm = {
      id: 5,
      tenantId: '558',
      formId: '11103020',
      draftId: null,
      draftuniqueID: null,
      data: '',
      formData:
        '[{"id":"element_6","value":"1","type":"select"},{"id":"element_3","value":"","type":"radio"},{"id":"element_5_1","value":"0","type":"checkbox"},{"id":"element_5_2","value":"0","type":"checkbox"},{"id":"element_5_3","value":"0","type":"checkbox"},{"id":"element_2","value":"","type":"textarea"},{"id":"element_1","value":"","type":"text"},{"id":"element_4","value":"","type":"text"},{"id":"element_7_1","value":"","type":"text"},{"id":"element_7_2","value":"","type":"text"},{"id":"element_8","value":"","type":"date"}]',
      sync: false,
      modifiedOn: '2024-09-04 17:48:46',
      eSignatureLogs: '',
      status: null
    };
    component.formData = { offlineFormId: 456 };
    persistentService.getOfflineFormData.and.returnValue(Promise.resolve(mockForm));

    await component.onIncomingMessage(event);

    expect(persistentService.getOfflineFormData).toHaveBeenCalledWith(456);
    expect(component.formDataExists).toBeTrue();
  });

  it('should show message on goBackToForms', () => {
    commonService.getTranslateData.and.returnValue('Save as draft message');

    component.goBackToForms();

    expect(commonService.showMessage).toHaveBeenCalledWith('Save as draft message');
  });

  it('should set formData on ngOnInit', () => {
    const mockNavigation = {
      extras: {
        state: {
          viewData: {
            form: { formName: 'Test Form', id: '123' }
          }
        }
      }
    } as any;
    router.getCurrentNavigation.and.returnValue(mockNavigation);

    component.ngOnInit();

    expect(component.formData).toEqual({ formName: 'Test Form', id: '123' });
  });
});
