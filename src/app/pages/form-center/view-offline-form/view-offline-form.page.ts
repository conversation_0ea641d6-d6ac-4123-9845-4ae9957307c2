import { ChangeDetectionStrategy, Component, HostListener, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OfflineForm } from 'src/app/services/persistent-service/offline-form.model';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { isBlank, isTrue } from 'src/app/utils/utils';
import { CommonService } from 'src/app/services/common-service/common.service';

@Component({
  selector: 'app-view-offline-form',
  template: `<app-header [headerTitle]="formData.formName"></app-header>
    <div style="overflow-y: hidden;height: 80%;">
      <iframe id="view-form" style="width: 100%; overflow-y: scroll; height: 100%; border: none"></iframe>
    </div>
    <app-footer style="position:fixed;bottom:0;width:100%"></app-footer>`,
  styleUrls: ['./view-offline-form.page.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ViewOfflineFormPage implements OnInit {
  formData = undefined as any;
  formDataExists = false;
  iframedoc;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private persistentService: PersistentService,
    private readonly common: CommonService
  ) {}

  @HostListener('window:message', ['$event'])
  onIncomingMessage($event: any) {
    if (!isBlank($event.data)) {
      const data = typeof $event.data === 'object' ? $event.data : this.urlEncodedStringToJson($event.data);
      if (!isBlank(data.formData)) {
        data.formData = Object.values(data.formData);
        this.persistentService
          .updateOfflineFormData(this.formData.offlineFormId, JSON.stringify(data.formData), data.eSignatureLogs || '')
          .then(() => {
            if (isTrue(data.manualSave)) {
              this.goBackToForms();
            }
          });
      }
      if (isTrue(data.formLoaded)) {
        this.persistentService.getOfflineFormData(this.formData.offlineFormId).then((res: OfflineForm) => {
          if (res) {
            this.formDataExists = true;
            this.uploadData(res.formData);
          }
        });
      }
    }
  }

  goBackToForms() {
    this.common.showMessage(this.common.getTranslateData('MESSAGES.SAVE_AS_DRAFT_MESSAGE'));
  }

  ngOnInit() {
    this.route.queryParams.subscribe(() => {
      if (this.router.getCurrentNavigation()?.extras.state) {
        this.formData = this.router.getCurrentNavigation().extras.state.viewData.form;
        this.renderIframe();
      }
    });
  }

  renderIframe() {
    setTimeout(() => {
      const iframe = document.querySelector<HTMLIFrameElement>('#view-form');
      this.iframedoc = iframe.contentDocument || iframe.contentWindow.document;
      this.iframedoc.open();
      this.iframedoc.write(`${this.formData.formContent}`);
      this.iframedoc.close();
    }, 100);
  }

  uploadData(data: any) {
    const iframe = document.querySelector<HTMLIFrameElement>('#view-form');
    const docFrame = iframe.contentDocument || iframe.contentWindow.document;

    const formData = JSON.parse(data);
    if (!isBlank(formData)) {
      formData.forEach((element) => {
        const { id, value, type } = element;
        let targetElement = <any>docFrame.getElementById(element.id);
        if (type === 'radio') {
          targetElement = <any>docFrame.getElementsByName(element.id);
        } else if (type === 'date') {
          const [date0, date1, date2] = value.split('-');
          if (date0 && date1 && date2) {
            targetElement = <any>docFrame.getElementById(`${element.id}_1`);
            targetElement.value = date1;
            targetElement = <any>docFrame.getElementById(`${element.id}_2`);
            targetElement.value = date2;
            targetElement = <any>docFrame.getElementById(`${element.id}_3`);
            targetElement.value = date0;
          }
          return;
        } else if (type === 'time') {
          const [time0, time1] = value.split(':');
          if (time0 && time1) {
            targetElement = <any>docFrame.getElementById(`${element.id}_1`);
            targetElement.value = time0;
            targetElement = <any>docFrame.getElementById(`${element.id}_2`);
            targetElement.value = time1;
            targetElement = <any>docFrame.getElementById(`${element.id}_4`);
            targetElement.value = Number(time0) > 12 ? 'PM' : 'AM';
          }
          return;
        }
        if (targetElement) {
          switch (type) {
            case 'select':
            case 'textarea':
            case 'text':
            case 'price':
              // For input elements, set the value property
              targetElement.value = value;
              targetElement.dispatchEvent(new Event('change'));
              break;
            case 'radio':
              targetElement.forEach((radio) => {
                if (radio.value === value) {
                  radio.click();
                }
              });
              break;
            case 'checkbox':
              if (value && String(value) === '1') {
                targetElement.click();
              }
              break;
            case 'signature':
              if (!isBlank(value)) {
                targetElement.value = value;
                setTimeout(() => {
                  const iframeSign = document.querySelector<HTMLIFrameElement>('#view-form');
                  (<any>iframeSign.contentWindow).drawSignatures();
                }, 500);
              }
              break;
            default:
              console.error(`Unsupported element type: ${type}`);
          }
        } else {
          console.error(`Element with id ${id} not found`);
        }
      });
      (<any>iframe.contentWindow).getFormData(false);
    }
  }

  urlEncodedStringToJson(urlEncodedString: string): any {
    const pairs = urlEncodedString.split('&');
    const result = {};

    pairs.forEach((pair: string) => {
      const [key, value] = pair.split('=');
      const decodedKey = decodeURIComponent(key);
      const decodedValue = decodeURIComponent(value && value.replace(/\+/g, ' ')); // handle '+' as space

      if (decodedKey.includes('[')) {
        const keys = decodedKey.split('[').map((item) => item.replace(']', ''));
        let currentObj = result;

        keys.forEach((item, index) => {
          if (index === keys.length - 1) {
            // last key
            if (!currentObj[item]) {
              currentObj[item] = decodedValue;
            } else {
              if (!Array.isArray(currentObj[item])) {
                currentObj[item] = [currentObj[item]];
              }
              currentObj[item].push(decodedValue);
            }
          } else {
            // intermediate key
            if (!currentObj[item]) {
              currentObj[item] = Array.isArray(currentObj[item]) ? currentObj[item] : {};
            }
            currentObj = currentObj[item];
          }
        });
      } else if (!result[decodedKey]) {
        result[decodedKey] = decodedValue;
      } else {
        if (!Array.isArray(result[decodedKey])) {
          result[decodedKey] = [result[decodedKey]];
        }
        result[decodedKey].push(decodedValue);
      }
    });

    return result;
  }
}
