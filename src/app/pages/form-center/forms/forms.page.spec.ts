import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { FormsPage } from './forms.page';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, throwError } from 'rxjs';
import { HttpService } from 'src/app/services/http-service/http.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { PageRoutes } from 'src/app/constants/page-routes';
import { Config } from 'src/app/constants/config';

describe('FormsPage', () => {
  let component: FormsPage;
  let fixture: ComponentFixture<FormsPage>;
  let sharedService: SharedService;
  let httpService: HttpService;
  let commonService: CommonService;
  let router: Router;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [FormsPage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    Object.defineProperty(sharedService, 'configValuesUpdated', { value: of('') });
    fixture = TestBed.createComponent(FormsPage);
    component = fixture.componentInstance;
    httpService = TestBed.inject(HttpService);
    commonService = TestBed.inject(CommonService);
    router = TestBed.inject(Router);
    component.fullscreen = false;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should check other patinet group', () => {
    sharedService.userData = {
      ...sharedService.userData,
      group: '2'
    };
    fixture = TestBed.createComponent(FormsPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
    expect(sharedService.userData.group).not.toBe('3');
  });

  it('execute updateConfigPermissions', () => {
    component.updateConfigPermissions();
    expect(component.updateConfigPermissions).toBeTruthy();
  });
  it('execute getFormCounts', () => {
    let response = { forms: { pending: 1, archived: 2, completed: 3, draft: 4 } };
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.getFormCounts();
    expect(component.getFormCounts).toBeTruthy();
    expect(component.pendingForms).toEqual(1);
    expect(component.archivedForms).toEqual(2);
    expect(component.completedForms).toEqual(3);
    expect(component.draftForms).toEqual(4);
  });
  it('execute getFormCounts : API throws error', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    spyOn(sharedService, 'errorHandler').and.stub();
    component.getFormCounts();
    expect(component.getFormCounts).toBeTruthy();
  });
  it('execute doAction : API throws error', () => {
    spyOn(commonService, 'redirectToPage').and.stub();
    component.doAction();
    expect(component.doAction).toBeTruthy();
  });

  it('execute ionViewDidEnter', () => {
    component.ionViewDidEnter();
    expect(component.ionViewDidEnter).toBeTruthy();
  });

  it('execute getFormCounts', () => {
    sharedService.formIndividualCounts = {
      pending: 1,
      draft: 1,
      completed: 1,
      archived: 1
    };
    component.getFormCounts();
    expect(component.pendingForms).toBe(1);
    expect(component.draftForms).toBe(1);
    expect(component.completedForms).toBe(1);
    expect(component.archivedForms).toBe(1);
  });
  it('execute doAction with patientDrivenEnabled=true', () => {
    component.patientDrivenEnabled = true;
    sharedService.userData.config[Config.enableMultiAdmissions] = '0';
    spyOn(router, 'navigate');
    spyOn(commonService, 'redirectToPage');
    component.doAction();
    expect(router.navigate).toHaveBeenCalledWith([PageRoutes.formFlow], {
      state: {
        forceRefresh: true
      }
    });
  });
});
