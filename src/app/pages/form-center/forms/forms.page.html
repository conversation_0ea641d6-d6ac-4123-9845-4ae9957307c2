<app-header headerTitle="MENU.FORM_CENTER"></app-header>

<ion-content [fullscreen]="fullscreen" class="form-page common-page-layout">

    <div class="common-page-sections">
        <div class="common-page-section row" tappable [routerLink]="['pending-forms']" id="pending-forms">
            <div class="common-page-btns common-btn1 col common-full-width" id="pending">
                <span>{{ 'LABELS.PENDING_FORMS' | translate }}</span>
                <i><img src="../../assets/icon/forms/pending.png">
                    <div *ngIf="pendingForms>0" class="common-count" alt="pending">{{ pendingForms }}</div>
                </i>
            </div>
        </div>
        <div class="common-page-section row" tappable [routerLink]="['completed-forms']" id="completed-forms">
            <div class="common-page-btns common-btn2 col common-full-width" id="completed">
                <span>{{ 'LABELS.COMPLETED_FORMS' | translate }}</span>
                <i><img src="../../assets/icon/forms/completed.png">
                    <div *ngIf="completedForms>0" class="common-count" alt="completed">{{ completedForms }}</div>
                </i>
            </div>
        </div>
        <div class="common-page-section row" tappable [routerLink]="['archived-forms']" id="archived-forms">
            <div class="common-page-btns common-btn3 col common-full-width" id="archived">
                <span>{{ 'LABELS.ARCHIVED_FORMS' | translate }}</span>
                <i><img src="../../assets/icon/forms/archived.png" alt="archived">
                    <div *ngIf="archivedForms>0" class="common-count">{{ archivedForms }}</div>
                </i>
            </div>
        </div>
        <div class="common-page-section row" tappable [routerLink]="['draft-forms']" *ngIf="!draftInVisible"
            id="draft-forms">
            <div class="common-page-btns common-btn4 col common-full-width" id="draft">
                <span>{{ 'LABELS.DRAFT_FORMS' | translate }}</span>
                <i><img src="../../assets/icon/forms/draft.png" alt="draft">
                    <div *ngIf="draftForms>0" class="common-count">{{ draftForms }}</div>
                </i>
            </div>
        </div>
        <div class="common-page-section row" tappable [routerLink]="['offline-forms']" *ngIf="!draftInVisible && isOfflineFormEnabled" id="offline-forms" >
            <div class="common-page-btns common-btn5 col common-full-width" id="offline">
                <span>{{ 'LABELS.OFFLINE_FORMS' | translate }}</span>
                <i><img src="../../assets/icon/forms/draft.png" alt="draft"></i>
            </div>
        </div>
    </div>
</ion-content>
<!-- TODO! CHP-3597 -->
<app-action-button [button]="button" (buttonClick)="doAction()"></app-action-button>
<app-footer backButtonLink="home"></app-footer>