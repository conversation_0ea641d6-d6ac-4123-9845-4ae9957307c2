import { Component, OnInit } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants } from 'src/app/constants/constants';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Permissions } from 'src/app/constants/permissions';
import { PageRoutes } from 'src/app/constants/page-routes';
import { Config } from 'src/app/constants/config';
import { Platform } from '@ionic/angular';
import { FormMessageCountUpdateEvent } from 'src/app/interfaces/common-interface';
import { NavigationExtras, Router } from '@angular/router';

@Component({
  selector: 'app-forms',
  templateUrl: './forms.page.html',
  styleUrls: ['./forms.page.scss']
})
export class FormsPage implements OnInit {
  pendingForms = 0;
  completedForms = 0;
  archivedForms = 0;
  draftForms = 0;
  offlineForms = 0;
  fullscreen = true;
  button: {
    iconCustom?: boolean;
    type?: string;
    buttonType?: string;
    buttonIcon?: string;
    colorTheme?: string;
    permission?: boolean;
  } = {};
  draftInVisible: boolean;
  patientDrivenEnabled: boolean;
  isOfflineFormEnabled: boolean;
  constructor(
    public readonly platform: Platform,
    public readonly sharedService: SharedService,
    private readonly common: CommonService,
    private readonly permissionService: PermissionService,
    private readonly router: Router
  ) {
    if (Number(this.sharedService.userData?.group) === Constants.patientGroupId) {
      // TODO! CHP-3597
      this.button = {
        iconCustom: false,
        type: 'new-chat',
        buttonType: 'ion-button',
        buttonIcon: 'chatbox-ellipses',
        colorTheme: 'de-york'
      };

      this.draftInVisible = true;
    } else {
      // TODO! CHP-3597
      this.button = {
        iconCustom: false,
        customClass: 'form-center-btn',
        buttonType: 'ion-button',
        buttonIcon: 'send',
        colorTheme: 'de-york',
        ...this.common.setActionButton('BUTTONS.SEND_FORMS', 'send-forms.png')
      };
    }
  }

  ngOnInit(): void {
    this.updateConfigPermissions();
    this.isOfflineFormEnabled = this.sharedService.isOfflineFormsEnabled();
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.updateConfigPermissions();
    });
    this.sharedService.messageFormCountUpdated.subscribe((pollingData: FormMessageCountUpdateEvent) => {
      if (pollingData.countType === Constants.countTypes.forms) {
        this.pendingForms = this.sharedService.formIndividualCounts.pending;
        this.draftForms = this.sharedService.formIndividualCounts.draft;
        this.completedForms = this.sharedService.formIndividualCounts.completed;
        this.archivedForms = this.sharedService.formIndividualCounts.archived;
      }
    });
  }
  ionViewDidEnter() {
    this.getFormCounts();
    if (this.sharedService.branchSwitched) {
      this.sharedService.resetSelectedDateFilterData();
      this.sharedService.branchSwitched = false;
    }
  }

  updateConfigPermissions(): void {
    if (!this.button.type) {
      this.button.permission = this.permissionService.userHasPermission(Permissions.fillStructuredForms);
      this.patientDrivenEnabled = this.sharedService.isEnableConfig(Config.enablePatientDrievenFlowForms);
    }
  }

  getFormCounts(): any {
    if (this.sharedService.formIndividualCounts) {
      this.pendingForms = this.sharedService.formIndividualCounts.pending;
      this.draftForms = this.sharedService.formIndividualCounts.draft;
      this.completedForms = this.sharedService.formIndividualCounts.completed;
      this.archivedForms = this.sharedService.formIndividualCounts.archived;
    }
    this.sharedService.getMessageFormCounts(Constants.countTypes.forms);
  }

  doAction(): void {
    const navigationExtras: NavigationExtras = {
      state: {
        forceRefresh: true
      }
    };
    if (!this.patientDrivenEnabled || this.sharedService.isEnableConfig(Config.enableMultiAdmissions)) {
      this.router.navigate([PageRoutes.sendForms], navigationExtras);
    } else {
      this.router.navigate([PageRoutes.formFlow], navigationExtras);
    }
  }
}
