import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FormsPageRoutingModule } from './forms-routing.module';

import { FormsPage } from './forms.page';
import { SharedModule } from 'src/app/shared.module';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        FormsPageRoutingModule,
        SharedModule,
        ActionButtonModule
    ],
    declarations: [FormsPage]
})
export class FormsPageModule { }
