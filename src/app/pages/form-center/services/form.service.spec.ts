import { TestBed } from '@angular/core/testing';
import { FormService } from './form.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of } from 'rxjs';
import { Constants } from 'src/app/constants/constants';

describe('FormService', () => {
  let service: FormService;
  let httpServiceSpy: jasmine.SpyObj<HttpService>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('HttpService', ['doGet']);
    TestBed.configureTestingModule({
      providers: [
        FormService,
        { provide: HttpService, useValue: spy }
      ]
    });
    service = TestBed.inject(FormService);
    httpServiceSpy = TestBed.inject(HttpService) as jasmine.SpyObj<HttpService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('generalizeResponse', () => {
    it('should map response correctly', () => {
      const mockResponse = {
        response: [
          {
            allow_edit: true,
            caregiver_userid: 123,
            caregiver_displayname: '<PERSON>',
            cgiverIdentityValue: 'ID123',
            form_id: 1,
            tenant_id: 2,
            form_name: 'Test Form',
            createdUser: 'Admin',
            createdUserEmail: '<EMAIL>',
            patientIdentityValue: 'PID123',
            patientDob: '2000-01-01',
            fname: 'Jane',
            lname: 'Doe',
            username: 'jane.doe',
            practitionerid: 456,
            applessMode: false,
            fgrp: 'Group1',
            senton: '2023-01-01',
            status: 'Sent',
            sent_id: 789,
            siteId: '10',
            staffFacing: true,
            updatedtimestamp: '2023-01-02',
            unreadCount: 0
          }
        ]
      };
      const formType = Constants.formTypes.draft;
      const formStatus = Constants.formDraftStatus;

      const result = service.generalizeResponse(mockResponse, formType, formStatus);

      expect(result.length).toBe(1);
      expect(result[0]).toEqual(
        jasmine.objectContaining({
          allowEdit: true,
          associated_user_id: 123,
          caregiver_displayname: 'John Doe',
          caregiverIdentityValue: 'ID123',
          formId: 1,
          tenantId: 2,
          formName: 'Test Form',
          fromName: 'Admin',
          fromUsername: '<EMAIL>',
          IdentityValue: '',
          patientDisplayName: jasmine.any(String),
          patientDOB: '2000-01-01',
          patientFirstname: 'Jane',
          patientLastname: 'Doe',
          patienUsername: 'jane.doe',
          practitioner_id: 456,
          appless_mode: false,
          recipient_grp_id: 'Group1',
          sent: '2023-01-01',
          sent_status: 'Sent',
          sentId: 789,
          siteId: 10,
          staff_facing: true,
          displayLabel: 'Test Form',
          fgrp: 'Group1',
          time: '2023-01-02',
          isRead: true,
          swipeButtons: []
        })
      );
    });

    it('should handle empty response', () => {
      const mockResponse = { response: [] };
      const formType = Constants.formTypes.draft;
      const formStatus = Constants.formDraftStatus;

      const result = service.generalizeResponse(mockResponse, formType, formStatus);

      expect(result.length).toBe(0);
    });

    it('should handle missing fields in response', () => {
      const mockResponse = {
        response: [
          {
            form_id: 1,
            form_name: 'Test Form',
            senton: '2023-01-01',
            status: 'Sent'
          }
        ]
      };
      const formType = Constants.formTypes.draft;
      const formStatus = Constants.formDraftStatus;

      const result = service.generalizeResponse(mockResponse, formType, formStatus);

      expect(result.length).toBe(1);
      expect(result[0]).toEqual(
        jasmine.objectContaining({
          formId: 1,
          formName: 'Test Form',
          sent: '2023-01-01',
          sent_status: 'Sent',
          displayLabel: 'Test Form',
          isRead: true
        })
      );
    });

    it('should handle null response', () => {
      const result = service.generalizeResponse(null, Constants.formTypes.draft, Constants.formDraftStatus);
      expect(result).toEqual([]);
    });

    it('should handle undefined response', () => {
      const result = service.generalizeResponse(undefined, Constants.formTypes.draft, Constants.formDraftStatus);
      expect(result).toEqual([]);
    });

    it('should handle response with invalid form type', () => {
      const mockResponse = {
        response: [
          {
            form_id: 1,
            form_name: 'Invalid Form',
            senton: '2023-01-01',
            status: 'Invalid'
          }
        ]
      };
      const result = service.generalizeResponse(mockResponse, 'invalidType', 'invalidStatus');
      expect(result.length).toBe(1);
      expect(result[0]).toEqual(
        jasmine.objectContaining({
          formId: 1,
          formName: 'Invalid Form',
          sent: '2023-01-01',
          sent_status: 'Invalid',
          displayLabel: 'Invalid Form',
          isRead: true
        })
      );
    });
  });

  describe('patientName', () => {
    it('should return empty string for blank row', () => {
      const result = service.patientName(null, Constants.formDraftStatus);
      expect(result).toBe('');
    });

    it('should return formatted patient name for draft status with caregiver', () => {
      const row = {
        patientName: 'Jane Doe',
        original_patient_displayname: 'Original Name',
        caregiver: true
      };
      const result = service.patientName(row, Constants.formDraftStatus);
      expect(result).toBe('Jane Doe (Original Name)');
    });

    it('should return caregiver display name for practitioner flow', () => {
      const row = {
        facing_new: Constants.practitionerFacingValue,
        caregiver_displayname: 'John Doe',
        createdUser: 'Admin'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should return patient name if no caregiver user ID', () => {
      const row = {
        patientName: 'Jane Doe',
        caregiver_userid: null
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('Jane Doe');
    });

    it('should return empty string for null row', () => {
      const result = service.patientName(null, 'Sent');
      expect(result).toBe('');
    });

    it('should return patient name for draft status without caregiver', () => {
      const row = {
        patientName: 'Jane Doe',
        caregiver: false
      };
      const result = service.patientName(row, Constants.formDraftStatus);
      expect(result).toBe('Jane Doe');
    });

    it('should return empty string if patientName is missing', () => {
      const row = {
        caregiver: true
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should return caregiver display name for caregiver flow', () => {
      const row = {
        facing_new: Constants.formTypes.pending,
        caregiver_displayname: 'John Doe',
        createdUser: 'Admin'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should return empty string if caregiver display name and patient name are missing', () => {
      const row = {
        facing_new: Constants.practitionerFacingValue,
        createdUser: 'Admin'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should return empty string for undefined row', () => {
      const result = service.patientName(undefined, Constants.formDraftStatus);
      expect(result).toBe('');
    });

    it('should return empty string for undefined patientName', () => {
      const row = {
        caregiver: true,
        original_patient_displayname: 'Original Name'
      };
      const result = service.patientName(row, Constants.formDraftStatus);
      expect(result).toBe('');
    });

    it('should return formatted patient name for caregiver with missing original display name', () => {
      const row = {
        patientName: 'Jane Doe',
        caregiver: true
      };
      const result = service.patientName(row, Constants.formDraftStatus);
      expect(result).toBe('Jane Doe (undefined)');
    });

    it('should return empty string for invalid facing_new value', () => {
      const row = {
        facing_new: 'invalidValue',
        caregiver_displayname: 'John Doe',
        createdUser: 'Admin'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should construct patient name from patientFirstname and patientLastname when patientName is missing', () => {
      const row = {
        patientFirstname: 'Jane',
        patientLastname: 'Doe'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should construct patient name from fname and lname when patientName and patientFirstname/patientLastname are missing', () => {
      const row = {
        fname: 'Jane',
        lname: 'Doe'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should return empty string when all name properties are empty', () => {
      const row = {
        patientName: '',
        patientFirstname: '',
        patientLastname: '',
        fname: '',
        lname: ''
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should include middle name when available in patientName format', () => {
      const row = {
        patientFirstname: 'Jane',
        patientLastname: 'Doe',
        middle_name: 'Marie'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('');
    });

    it('should handle patient facing forms correctly', () => {
      const row = {
        patientName: 'Jane Doe',
        createdUser: 'Admin'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('Jane Doe');
    });

    it('should handle staff facing forms with caregiver_userid', () => {
      const row = {
        facing_new: Constants.staffFacingValue,
        caregiver_userid: 123,
        caregiver_displayname: 'John Caregiver',
        patientName: 'Jane Doe'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('John Caregiver (Jane Doe)');
    });

    it('should prioritize patientName over constructed name', () => {
      const row = {
        patientName: 'Jane Doe-Smith',
        patientFirstname: 'Jane',
        patientLastname: 'Doe',
        fname: 'J',
        lname: 'D'
      };
      const result = service.patientName(row, 'Sent');
      expect(result).toBe('Jane Doe-Smith');
    });
  });
});
