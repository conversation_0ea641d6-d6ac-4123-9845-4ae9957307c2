import { Injectable } from '@angular/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Constants } from 'src/app/constants/constants';
import { isBlank } from 'src/app/utils/utils';

@Injectable({
  providedIn: 'root'
})
export class FormService {
  constructor(private readonly httpService: HttpService) {}

  generalizeResponse(response, formType, formStatus): any {
    const mappedResponse =
      response?.response?.map((r) => ({
        ...r,
        allowEdit: r.allow_edit,
        associated_user_id: r.caregiver_userid || 0,
        caregiver_displayname: r.caregiver_displayname || ' ',
        caregiverIdentityValue: r.cgiverIdentityValue,
        formId: r.form_id,
        tenantId: r.tenant_id,
        formName: r.form_name,
        fromName: r.createdUser,
        fromUsername: r.createdUserEmail,
        IdentityValue: !r.cgiverIdentityValue ? r.patientIdentityValue : '',
        patientDisplayName: this.patientName(r, formStatus),
        patientDOB: r.patientDob,
        patientFirstname: r.fname,
        patientLastname: r.lname,
        patienUsername: r.username,
        practitioner_id: r.practitionerid,
        appless_mode: r?.applessMode,
        recipient_grp_id: r.fgrp,
        sent: r.senton,
        sent_status: r.status,
        sentId: r.sent_id,
        siteId: Number(r.siteId),
        staff_facing: r.staffFacing,
        displayLabel: r.form_name,
        fgrp: r.fgrp,
        time: formType === Constants.formTypes.draft ? r.updatedtimestamp : r.senton,
        isRead: Number(r.unreadCount) !== 1,
        swipeButtons: []
      })) || [];
    return mappedResponse;
  }
  
  patientName(row, formStatus) {
    if (isBlank(row) || isBlank(row?.patientName) || row?.patientName === Constants.nullValue) {
      return '';
    }
    if (formStatus === Constants.formDraftStatus) {
      if (row?.caregiver) {
        return `${row.patientName} (${row.original_patient_displayname})`;
      }
      if (row?.facing_new === Constants.practitionerFacingValue && row?.createdUser === row?.patientName) {
        return ' ';
      }
      return row.patientName;
    }
    if (row.facing_new === Constants.practitionerFacingValue) {
      // Practitioner flow
      if ((row && isBlank(row?.caregiver_displayname)) || row?.createdUser === row?.caregiver_displayname) {
        return '';
      }
      return row?.caregiver_displayname;
    }
    if (row?.caregiver_userid) {
      return `${row?.caregiver_displayname} (${row?.patientName})`;
    }
    return row?.patientName;
  }
}
