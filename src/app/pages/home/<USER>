.home-page {
    background: #fafafa;
    --padding-top: 20px;
    .banner-message {
        text-align: center;
        margin: 10px 10px 0px 10px;

        .alert-main {
            color: #fff;
        }

        .alert-icon-img {
            width: 10%;
            float: left;
            margin-right: 10px;
            margin-top: 20px;
            text-align: center;
        }

        .alert-icon-img img {
            margin: auto;
        }

        .alert-text-right {
            width: 85%;
            float: left;
            text-align: left;

            h2 {
                font-weight: normal;
                font-size: 15px;
                margin-bottom: 0px;
                height: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            p {
                font-size: 11px;
                line-height: 13px;
                margin-top: 0px;
                height: 25px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .show-more-link {
                margin-bottom: 0px;
                height: 15px;
            }
        }

        .alert-close .close {
            background: none;
            border: none;
            position: absolute;
            top: 0;
            right: 2px;
            width: 20px;
            height: 30px;
            z-index: 99;
            color: #fff;
        }

        .alert-section-default {
            padding: 0px 35px;
            background: red;
            position: relative;
            height: 100px;
        }

        .alert-section-warning {
            padding: 0px 35px;
            background: #f49b3b;
            position: relative;
            height: 100px;
        }

        .alert-section-information {
            padding: 0px 35px;
            background: #37bf8d;
            position: relative;
            height: 100px;
        }

        .alert-section-error {
            padding: 0px 35px;
            background: #fe4748;
            position: relative;
            height: 100px;
        }

        .show-more {
            color: white;
            background: transparent;
            background-color: transparent;
            font-weight: bold;
            text-decoration: underline;
        }

        ion-icon.previous-button,
        ion-icon.next-button {
            background: none;
            color: #fff;
            border-radius: 3px;
            cursor: pointer;
            border: 0;
            padding: 0;
        }

        ion-icon.previous-button {
            position: absolute;
            left: 10px;
            height: 25px;
            top: 40px;
            width: 25px;
        }

        ion-icon.next-button {
            position: absolute;
            right: 10px;
            height: 25px;
            top: 40px;
            width: 25px;
        }
    }

    .user-msg-welcome {
        color: #7f7f7f;
        font-size: 25px;
        text-align: center;
        margin: 25px 0 20px 0;
        width: 100%;
        float: left;

        span {
            color: #31859c;
            font-size: 25px;
            padding-left: 0;
        }
    }

    .home-page-sections {
        padding: 11px;
        padding-bottom: 15px;
        display: table;
        width: 100%;

        .row {
            display: flex;

            .home-page-btns {
                text-align: center;
                float: left;
                min-height: 140px;
                margin-bottom: 10px;
                width: 50%;

                span {
                    color: #fff;
                    padding: 15px 0;
                    display: block;
                    font-size: 17px;
                }

                img {
                    width: 65px;
                }

                i {
                    position: relative;
                }
                i .tile-ion-icon {
                    font-size: 65px;
                }
            }

            .hp-btn1 {
                background: var(--ion-color-hpbtn1);
                margin-right: 11px;
                flex-grow: 1;
            }

            .hp-btn2 {
                background: var(--ion-color-hpbtn2);
                flex-grow: 2;
            }

            .hp-btn3 {
                background: var(--ion-color-hpbtn3);
                margin-right: 11px;
                flex-grow: 1;
            }

            .hp-btn4 {
                background: var(--ion-color-hpbtn4);
                flex-grow: 2;
            }

            .hp-btn5 {
                background: var(--ion-color-hpbtn3);
                flex-grow: 1;
            }

            .hp-count {
                background: var(--ion-color-count-bright-bg);
                min-width: 35px;
                height: 35px;
                border-radius: 50%;
                color: var(--ion-color-count-bright-color);
                position: absolute;
                right: 0;
                top: 0;
                padding-top: 10px;
                padding-right: 2px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
                z-index: 9999;
            }
        }
    }
}