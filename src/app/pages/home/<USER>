import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule } from '@angular/forms';

import { HomePage } from './home.page';
import { HomePageRoutingModule } from './home-routing.module';
import { SharedModule } from '../../shared.module';
import { ActionButtonModule } from 'src/app/components/action-button/action-button.module';
import { MessageModalComponentModule } from './message-modal/message-modal.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        HomePageRoutingModule,
        SharedModule,
        ActionButtonModule,
        MessageModalComponentModule
    ],
    declarations: [HomePage]
})
export class HomePageModule { }
