import { Router } from '@angular/router';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { isBlank, generateSort } from 'src/app/utils/utils';
import { PageRoutes } from 'src/app/constants/page-routes';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { DomSanitizer } from '@angular/platform-browser';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Activity } from 'src/app/constants/activity';
import { theme } from 'src/theme/theme';
import { Subscription } from 'rxjs';
import { DocumentDetailedCount } from 'src/app/interfaces/document-request-signature';
import { Banner, BannerPollingData } from 'src/app/interfaces/login';
import { Config } from '../../constants/config';
import { Socket } from '../../constants/socket';
import { Constants } from '../../constants/constants';
import { MessageModalComponent } from './message-modal/message-modal.component';
import { GraphqlService } from '../../services/graphql-service/graphql.service';
import { SharedService } from '../../services/shared-service/shared.service';
import { APIs } from '../../constants/apis';
import { HttpService } from '../../services/http-service/http.service';


@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss']
})
export class HomePage implements OnInit {
  messageCount: number;
  documentCount: number;
  button: any = {};
  alertBanner: Banner[] = [];
  showBanner: string;
  count: number;
  callBack: any;
  enableMsgCenter: boolean;
  enableUserCenter: boolean;
  enableMaskedMsg: boolean;
  enableDocCenter: boolean;
  enableFormCenter: boolean;
  enableEduCenter: boolean;
  enableScheduleCenter: boolean;
  enableOfflineForms: boolean;
  activeMessagesRoute = PageRoutes.activeMessages;
  scheduleCenterVisitsRoute = PageRoutes.scheduleCenterVisits;
  platformValue: string;
  showUpdateConfirmation = true;
  headerTitle = theme.headerTitle.value;
  headerImage = theme.headerTitle.type === 'image';
  alertTypes = Constants.alertTypes;
  socketStatusUpdatedSubscription: Subscription;
  navigator = navigator;
  socketStatus: boolean;
  mySiteIds: number[] = [];
  profileUserRoute = PageRoutes.profileUser;
  constructor(
    private httpService: HttpService,
    public readonly sharedService: SharedService,
    private readonly graphqlService: GraphqlService,
    private readonly modalController: ModalController,
    private readonly socketService: SocketService,
    public router: Router,
    private inAppBrowser: InAppBrowser,
    private readonly sanitizer: DomSanitizer,
    private readonly common: CommonService,
    private readonly cd: ChangeDetectorRef,
    public platform: Platform
  ) {
    this.showBanner = localStorage.getItem(Constants.storageKeys.showBanner);
    this.checkConfigValues();
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.checkConfigValues();
    });
    // TODO! CHP-3597
    this.button = {
      type: 'new-chat',
      buttonType: 'ion-button',
      buttonIcon: 'chatbox-ellipses',
      colorTheme: 'de-york',
      iconCustom: false
    };
    if (this.sharedService.messageCount === undefined || !this.sharedService.formCount) {
      this.sharedService.getMessageFormCounts();
    }
    this.platformValue = sharedService.platformValue;
  }

  checkConfigValues(): void {
    this.enableMsgCenter = this.sharedService.isEnableConfig(Config.enableMsgCenter);
    this.enableUserCenter = this.sharedService.isEnableConfig(Config.enableUserCenter);
    this.enableDocCenter = this.sharedService.isEnableConfig(Config.enableDocCenter);
    this.enableFormCenter = this.sharedService.isEnableConfig(Config.enableFormCenter);
    this.enableEduCenter = this.sharedService.isEnableConfig(Config.enableEduCenter);
    this.enableScheduleCenter = this.sharedService.isEnableConfig(Config.enableScheduleCenter);
    this.enableOfflineForms = this.sharedService.isOfflineFormsEnabled();
  }
  ionViewDidEnter(): void {
    this.sharedService.isSwipeGestureEnabled = false;
    this.sharedService.resetSelectedDateFilterData();
    /* TODO: Call count Api only when count is not set and when socket disconnected and connected again */
    if (this.sharedService.isEnableConfig(Config.enableDocCenter)) {
      this.getDocumentsDetailedCounts();
    }
    this.socketStatusUpdatedSubscription = this.socketService.socketStatusUpdated.subscribe(() => {
      if (this.socketService.status) {
        if (this.sharedService.isEnableConfig(Config.enableDocCenter)) {
          this.getDocumentsDetailedCounts();
        }
        this.sharedService.getMessageFormCounts();
      }
    });
  }
  ionViewDidLeave(): void {
    this.socketStatusUpdatedSubscription.unsubscribe();
    this.sharedService.isSwipeGestureEnabled = true;
  }
  ngOnInit(): void {
    if (isBlank(this.sharedService?.userData) || (!navigator.onLine && this.sharedService.isOfflineFormsEnabled())) {
      const activityData = {
        name: Activity.pageNavigation,
        des: { desConstant: Activity.redirectToLoginNoSessionOrOfflineDes }
      };
      this.sharedService.trackActivity(activityData);
      this.router.navigate([PageRoutes.login]);
    }
    if (this.sharedService.brandConfig) {
      this.headerTitle = this.sharedService.brandConfig.appName;
      this.headerImage = false;
    }
    if (this.platformValue !== Constants?.platform.web && this.showUpdateConfirmation && this.sharedService.userData?.updateAvailable) {
      this.appConfirmationModal();
    }
    if (this.showBanner === Constants.trueAsString && this.sharedService?.userData?.mySites) {
      this.mySiteIds = this.sharedService.userData?.mySites?.map((x) => x.id);
      this.getAlertBannerMessage();
      this.bannerAlertWithPolling();
    }
    this.enablePolling();
  }
  /**
   * enablePolling subscribe to listen to polling updates
   */
  enablePolling(): void {
    this.sharedService.documentCountUpdated.subscribe(() => {
      this.getDocumentsDetailedCounts();
    });
    this.sharedService.messageFormCountUpdated.subscribe(() => {
      this.cd.detectChanges(); // sharedService.messageCount is not reflecting in html after phone became idle and reconnect
    });
  }
  /**
   * getDocumentsDetailedCounts calls mySignatureRequestCount graphQL API and set count
   */
  getDocumentsDetailedCounts(): void {
    this.graphqlService.getDocumentsDetailedCounts()?.valueChanges.subscribe(
      ({ data }: DocumentDetailedCount) => {
        this.sharedService.isLoading = false;
        this.sharedService.updateDocumentDetailedCount(data);
        if (this.sharedService.documentCount) {
          this.documentCount =
            this.sharedService.documentCount.totalPendingCount +
            this.sharedService.documentCount.totalSignedCount +
            this.sharedService.documentCount.totalArchiveCount;
        }
      },
      (error) => {
        this.sharedService.errorHandler(error);
      }
    );
  }

  getAlertBannerMessage(): void {
    this.httpService
      .doGet({
        endpoint: APIs.getAlertMessage
      })
      .subscribe(
        (messages: Banner[]) => {
          if (messages.length) {
            this.count = 0;
            this.alertBanner = this.formatBannerData(messages);
          }
        },
        (error) => {
          this.sharedService.errorHandler(error);
        }
      );
  }
  async showMore(): Promise<void> {
    const modal = await this.modalController.create({
      component: MessageModalComponent,
      componentProps: {
        message: this.alertBanner[this.count]
      }
    });
    return modal.present();
  }
  closeBanner(): void {
    this.showBanner = 'false';
    localStorage.setItem(Constants.storageKeys.showBanner, this.showBanner);
    this.alertBanner = [];
  }
  next(): void {
    this.count++;
  }
  previous(): void {
    this.count--;
  }
  formatBannerData(messages: Banner[]): Banner[] {
    const orderedMessages = messages.sort(
      generateSort([
        { name: 'start_date_formated', reverse: true },
        { name: 'alert_type', reverse: true }
      ])
    );
    return orderedMessages.map((r) => {
      const banner = {
        ...r,
        message: unescape(r.description),
        showMore: r.description.length > 50 || r.subject.length > 30
      };
      if (
        banner?.branchesToShow.includes(this.sharedService.userData.tenantId) &&
        banner?.assignedSites.split(',').some((assignedSite) => this.mySiteIds.includes(Number(assignedSite)))
      ) {
        return banner;
      }
    });
  }
  bannerAlertWithPolling(): void {
    this.socketService.subscribeEvent(Socket.bannerAlertWithPolling).subscribe((banners: BannerPollingData[]) => {
      if (banners.length > 0 && !isBlank(banners[0]?.bannerId)) {
        const banner = banners[0];
        this.count = 0;
        this.alertBanner = this.removeBannerMessage(banner);
        if (banner.status !== Constants.removeStatus) {
          // Add or Update banner
          if (!isBlank(banner.data)) {
            this.alertBanner.push(banner.data[0]);
          }
        }
        this.alertBanner = this.formatBannerData(this.alertBanner);
      }
    });
  }
  removeBannerMessage(banner: BannerPollingData): Banner[] {
    return this.alertBanner.filter((bannerData) => {
      return Number(bannerData.bannerid) !== Number(banner.bannerId);
    });
  }
  /**
   *
   * @param routerName Page URL name
   * @returns
   */
  navigatePage(routerName: string) {
    this.router.navigateByUrl(routerName);
  }

  appConfirmationModal(): void {
    const appUpgradeMessages = this.sharedService.appConfig?.appUpgradeMessages;
    const buttons = [
      { text: appUpgradeMessages.cancelText, confirm: false, class: 'warn-btn alert-cancel' },
      { text: appUpgradeMessages.confirmText, confirm: true, class: 'warn-btn alert-update' }
    ];
    this.common
      .showAlert({
        message: this.common.getTranslateData(appUpgradeMessages?.contentMessage),
        buttons,
        backDrop: false
      })
      .then((confirmation) => {
        if (confirmation) {
          this.upgradeApp(appUpgradeMessages);
        } else {
          this.showUpdateConfirmation = false;
        }
      });
  }

  upgradeApp(appUpgradeMessages): void {
    const url = this.platform.is('ipad') ? appUpgradeMessages?.upgradeUrlForIpad : appUpgradeMessages?.upgradeUrl;
    const formUrl: any = this.sanitizer.bypassSecurityTrustResourceUrl(unescape(url));
    this.inAppBrowser.create(formUrl.changingThisBreaksApplicationSecurity, '_system', {
      location: 'no'
    });

    const activityData = {
      type: Activity.appUpdate,
      name: Activity.updateApp,
      des: {
        data: {
          displayName: this.sharedService.userData?.displayName,
          url: appUpgradeMessages?.upgradeUrl
        },
        desConstant: Activity.representiveConfirm
      }
    };
    this.sharedService.trackActivity(activityData);
  }
}
