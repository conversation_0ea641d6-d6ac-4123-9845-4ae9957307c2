import { RouterTestingModule } from '@angular/router/testing';
import { Apollo } from 'apollo-angular';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { of, throwError } from 'rxjs';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { HomePage } from './home.page';
import { MessagesPage } from '../message-center/messages/messages.page';

describe('HomePage', () => {
  let component: HomePage;
  let sharedService: SharedService;
  let socketService: SocketService;
  let commonService: CommonService;
  let graphqlService: GraphqlService;
  let httpService: HttpService;
  let fixture: ComponentFixture<HomePage>;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  const sampleBanner = {
    bannerid: 61,
    id: 'new banner from superadmin',
    subject: 'new banner from superadmin',
    description: 'test banner 1',
    alert_type: 2,
    start_date: '1690741800',
    start_date_formated: 1690675200,
    end_date: '**********',
    created_branch: 0,
    is_published: 1,
    branchesToShow: '1,44',
    assignedSites: '1,44'
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [HomePage],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        RouterTestingModule.withRoutes([{ path: 'message-center/messages/active', component: MessagesPage }])
      ],
      providers: [
        Apollo,
        NgxPermissionsService,
        NgxPermissionsStore,
        SharedService,
        SocketService,
        CommonService,
        HttpService,
        GraphqlService,
        Idle,
        IdleExpiry,
        Keepalive,
        InAppBrowser,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    sharedService = TestBed.inject(SharedService);
    socketService = TestBed.inject(SocketService);
    commonService = TestBed.inject(CommonService);
    graphqlService = TestBed.inject(GraphqlService);
    httpService = TestBed.inject(HttpService);
    spyOn(commonService, 'getTranslateData').and.returnValue(of(''));
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    fixture = TestBed.createComponent(HomePage);
    sharedService.configValuesUpdated.next(null);
    component = fixture.componentInstance;
    Object.defineProperty(component, 'inAppBrowser', { value: { create: () => undefined } });
    fixture.detectChanges();
  });

  // used to hide dom element from screen
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should define navigate page', () => {
    component.navigatePage(component.activeMessagesRoute);
    expect(component.navigatePage).toBeDefined();
  });

  it('execute upgradeApp', () => {
    const data = {
      cancelText: 'Cancel',
      confirmText: 'Update',
      contentMessage: '',
      upgradeUrl: '',
      upgradeUrlForIpad: ''
    };
    component.upgradeApp(data);
    expect(component.upgradeApp).toBeTruthy();
  });
  it('execute ionViewDidEnter', () => {
    sharedService.userData.config.show_document_tagging = '1';
    socketService.status = true;
    component.ionViewDidEnter();
    socketService.socketStatusUpdated.next(null);
    expect(component.ionViewDidEnter).toBeTruthy();
  });
  it('execute ionViewDidLeave', () => {
    component.ionViewDidEnter();
    component.ionViewDidLeave();
    expect(component.ionViewDidLeave).toBeTruthy();
  });
  it('execute bannerAlertWithPolling', () => {
    component.alertBanner = [sampleBanner];
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ data: [sampleBanner], bannerId: '111', status: 'add' }]));
    component.bannerAlertWithPolling();
    expect(component.bannerAlertWithPolling).toBeTruthy();
  });
  it('execute bannerAlertWithPolling:remove', () => {
    spyOn(socketService, 'subscribeEvent').and.returnValue(of([{ data: [], bannerId: '111', status: 'remove' }]));
    component.bannerAlertWithPolling();
    expect(component.bannerAlertWithPolling).toBeTruthy();
  });
  it('execute appConfirmationModal', () => {
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    component.appConfirmationModal();
    expect(component.appConfirmationModal).toBeTruthy();
  });
  it('execute appConfirmationModal: cancel click', () => {
    spyOn(commonService, 'showAlert').and.resolveTo(false);
    component.appConfirmationModal();
    expect(component.appConfirmationModal).toBeTruthy();
  });
  it('execute getAlertBannerMessage', () => {
    spyOn(httpService, 'doGet').and.returnValue(of([{ start_date_formated: '', alert_type: '' }]));
    component.getAlertBannerMessage();
    expect(component.getAlertBannerMessage).toBeTruthy();
  });
  it('execute getAlertBannerMessage:Error', () => {
    spyOn(httpService, 'doGet').and.returnValue(throwError(''));
    component.getAlertBannerMessage();
    expect(component.getAlertBannerMessage).toBeTruthy();
  });
  it('execute closeBanner', () => {
    component.closeBanner();
    expect(component.closeBanner).toBeTruthy();
  });
  it('execute next', () => {
    component.count = 1;
    component.next();
    expect(component.count).toEqual(2);
  });
  it('execute previous', () => {
    component.count = 2;
    component.previous();
    expect(component.count).toEqual(1);
  });
  it('execute showMore', () => {
    component.showMore();
    expect(component.showMore).toBeTruthy();
  });
  it('execute ngOnInit', () => {
    component.platformValue = 'ios';
    component.showUpdateConfirmation = true;
    sharedService.userData.updateAvailable = true;
    spyOn(commonService, 'showAlert').and.resolveTo(false);
    component.showBanner = 'true';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute enablePolling', () => {
    component.enablePolling();
    sharedService.messageFormCountUpdated.next(null);
    sharedService.documentCountUpdated.next(null);
    expect(component.enablePolling).toBeTruthy();
  });
  it('execute getDocumentsDetailedCounts', () => {
    sharedService.documentCount = {
      totalPendingCount: 1,
      totalSignedCount: 1,
      totalArchiveCount: 1
    };
    spyOn(graphqlService, 'getDocumentsDetailedCounts').and.returnValue({ valueChanges: of({ data: {} }) });
    component.getDocumentsDetailedCounts();
    expect(component.documentCount).toEqual(3);
  });
  it('execute getDocumentsDetailedCounts', () => {
    spyOn(graphqlService, 'getDocumentsDetailedCounts').and.returnValue({ valueChanges: throwError({}) });
    component.getDocumentsDetailedCounts();
    expect(component.getDocumentsDetailedCounts).toBeTruthy();
  });
});
