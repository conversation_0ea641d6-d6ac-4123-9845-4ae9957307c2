<app-header [headerTitle]="headerTitle" [headerImage]="headerImage"
  ngClass="theme-{{ sharedService.appTheme }} small-dev-title"> </app-header>
<ion-content [fullscreen]="true" class="home-page" ngClass="theme-{{ sharedService.appTheme }}">
  <ion-row [hidden]="sharedService?.userData || navigator.onLine || enableOfflineForms">
    <ion-col size="12" class="ion-text-center">
      <img src="assets/images/no-internet.gif" />
    </ion-col>
    <ion-col size="12" class="ion-text-center">
      {{ 'LABELS.NO_INTERNET_CONNECTION' | translate }}
    </ion-col>
  </ion-row>
  <div *ngIf="alertBanner && showBanner === 'true'">
    <div class="banner-message" *ngIf="alertBanner[count]">
      <div class="alert-main" [ngClass]="{
          'alert alert-warning alert-section-warning alert-warning-dismissible': alertBanner[count].alert_type === alertTypes.warning,
          'alert alert-danger alert-section-error alert-danger-dismissible': alertBanner[count].alert_type === alertTypes.error,
          'alert alert-info alert-section-information alert-info-dismissible': alertBanner[count].alert_type === alertTypes.info
        }">
        <div class="row">
          <div class="alert-icon-img" *ngIf="alertBanner[count].alert_type === alertTypes.warning">
            <img src="../../assets/icon/home/<USER>" alt="warning" />
          </div>
          <div class="alert-icon-img" *ngIf="alertBanner[count].alert_type === alertTypes.error">
            <img src="../../assets/icon/home/<USER>" alt="error" />
          </div>
          <div class="alert-icon-img" *ngIf="alertBanner[count].alert_type === alertTypes.info">
            <img src="../../assets/icon/home/<USER>" alt="information" />
          </div>
          <div class="alert-text-right">
            <h2>
              <strong>{{ alertBanner[count].subject }}</strong>
            </h2>
            <span class="alert-close">
              <ion-icon id="close-alert" class="close" tappable (click)="closeBanner()"
                src="../../assets/icon/material-svg/close.svg"> </ion-icon>
            </span>
            <p [innerHTML]="alertBanner[count].message | safe: 'html'"></p>
            <p class="show-more-link">
              <a *ngIf="alertBanner[count].showMore" tappable class="show-more" (click)="showMore()">{{
                'BUTTONS.SHOW_MORE' | translate }}</a>
            </p>
          </div>
          <div class="next-prev-section">
            <ion-icon *ngIf="count !== 0" id="previous-alert" class="previous-button" tappable (click)="previous()"
              src="../../assets/icon/material-svg/chevron-left.svg">
            </ion-icon>
            <ion-icon *ngIf="alertBanner.length - 1 !== count" id="next-alert" class="next-button" tappable
              (click)="next()" src="../../assets/icon/material-svg/chevron-right.svg">
            </ion-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="user-msg-welcome" *ngIf="sharedService.userData">
    {{ 'LABELS.WELCOME' | translate }} <span> {{ sharedService.userData?.firstName || '' }} </span>
  </div>
  <div class="home-page-sections">
    <div class="home-page-section row">
      <div *ngIf="enableMsgCenter" class="home-page-btns hp-btn1 col" id="message-center" tappable
        (click)="navigatePage(activeMessagesRoute)">
        <span>{{ 'MENU.MESSAGE_CENTER' | translate }}</span>
        <i>
          <img src="../../assets/icon/home/<USER>" alt="message-center" />
          <div *ngIf="sharedService.messageCount" class="hp-count">{{
            sharedService.messageCount }}</div>
        </i>
      </div>
      <div *ngIf="enableFormCenter" class="home-page-btns hp-btn2 col" id="form-center" tappable
        (click)="navigatePage('/form-center')">
        <span>{{ 'MENU.FORM_CENTER' | translate }}</span>
        <i>
          <img src="../../assets/icon/home/<USER>" alt="form-center" />
          <div *ngIf="sharedService.formCount" class="hp-count">{{ sharedService.formCount }}</div>
        </i>
      </div>
    </div>
    <div class="home-page-section row">
      <div *ngIf="enableEduCenter" class="home-page-btns hp-btn3 col" id="edu-center" tappable
        (click)="navigatePage('/education-center/materials')">
        <span>{{ 'MENU.EDUCATION_CENTER' | translate }}</span>
        <i><img src="../../assets/icon/home/<USER>" alt="book" /></i>
      </div>

      <div class="home-page-btns hp-btn3 col" id="profile-center" tappable *ngIf="!enableEduCenter"
        (click)="navigatePage(profileUserRoute)">
        <span>{{ 'LABELS.MY_PROFILE' | translate }}</span>
        <i><img src="../../assets/icon/home/<USER>" alt="myprofile" /></i>
      </div>

      <div *ngIf="enableDocCenter" class="home-page-btns hp-btn4 col" id="doc-center" tappable
        (click)="navigatePage('/document-center')">
        <span>{{ 'MENU.DOCUMENT_CENTER' | translate }}</span>
        <i>
          <img src="../../assets/icon/home/<USER>" alt="document-center" />
          <div *ngIf="documentCount > 0" class="hp-count">{{ documentCount }}</div>
        </i>
      </div>
    </div>
    <div class="home-page-section row">
      <div *ngIf="enableScheduleCenter" class="home-page-btns hp-btn5 col" id="schedule-center" tappable
        (click)="navigatePage(scheduleCenterVisitsRoute)">
        <span>{{ 'MENU.SCHEDULE_CENTER' | translate }}</span>
        <i><img src="../../assets/icon/home/<USER>" alt="schedule-center" /></i>
      </div>
    </div>
  </div>
</ion-content>
<!-- TODO! CHP-3597 -->
<app-action-button [button]="button"></app-action-button>
<ion-footer class="common-footer">
  <ion-toolbar></ion-toolbar>
</ion-footer>