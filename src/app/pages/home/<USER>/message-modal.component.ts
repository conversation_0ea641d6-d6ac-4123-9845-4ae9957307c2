import { Component } from '@angular/core';
import { ModalController, NavParams } from '@ionic/angular';

@Component({
  selector: 'app-message-modal',
  templateUrl: './message-modal.component.html',
  styleUrls: ['./message-modal.component.scss']
})
export class MessageModalComponent {
  message: any;
  constructor(
    private modalController: ModalController,
    private navParams: NavParams
  ) {
    this.message = this.navParams.get('message');
  }

  cancel(): void {
    this.modalController.dismiss();
  }
}
