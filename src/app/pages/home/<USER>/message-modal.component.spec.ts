import { SafePipe } from './../../../pipes/safe.pipe';
import { ModalController } from '@ionic/angular';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavParams } from '@ionic/angular';
import { MessageModalComponent } from './message-modal.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('MessageModalComponent', () => {
  let component: MessageModalComponent;
  let fixture: ComponentFixture<MessageModalComponent>;
  const modalSpy = jasmine.createSpyObj('Modal', ['present', 'onDidDismiss', 'dismiss']);
  let modalController: ModalController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [MessageModalComponent, SafePipe],
      imports: [IonicModule.forRoot()],
      providers: [ModalController, NavParams],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    fixture = TestBed.createComponent(MessageModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
