import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { HeaderPlainModule } from '../../../components/header-plain/header-plain.module';
import { MessageModalComponent } from './message-modal.component';
import { SharedModule } from 'src/app/shared.module';
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    HeaderPlainModule,
    SharedModule
  ],
  declarations: [MessageModalComponent],
  exports: [MessageModalComponent]
})
export class MessageModalComponentModule {}
