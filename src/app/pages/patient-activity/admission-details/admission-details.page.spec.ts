import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, RouterModule } from '@angular/router';
import { HttpService } from 'src/app/services/http-service/http.service';
import { IonicModule } from '@ionic/angular';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { AdmissionDetailsPage } from './admission-details.page';

describe('AdmissionDetailsPage', () => {
  let component: AdmissionDetailsPage;
  let fixture: ComponentFixture<AdmissionDetailsPage>;
  let router: Router;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [AdmissionDetailsPage],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        HttpService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(AdmissionDetailsPage);
    const extras = { state: { admission: { admissionId: '123' } } };
    router = TestBed.inject(Router);
    spyOn(router, 'getCurrentNavigation').and.returnValue({ extras } as any);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('changeSegment', () => {
    it('should set selectedIndex and headerValue to correct values for "admission-details"', () => {
      component.changeSegment('admission-details');
      expect(component.selectedIndex).toBe(1);
      expect(component.headerValue).toBe('ADMISSION.LABELS.ADMISSION_DETAILS');
    });
    it('should set selectedIndex and headerValue to correct values for "address-details"', () => {
      component.changeSegment('address-details');
      expect(component.selectedIndex).toBe(2);
      expect(component.headerValue).toBe('TITLES.ADDRESS_DETAILS');
    });
    it('should set selectedIndex and headerValue to default values for unknown segment', () => {
      component.changeSegment('unknown-segment');
      expect(component.selectedIndex).toBe(2);
      expect(component.headerValue).toBe('TITLES.ADDRESS_DETAILS');
    });
  });
});
