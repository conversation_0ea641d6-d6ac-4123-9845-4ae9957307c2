import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { APIs } from 'src/app/constants/apis';
import { AdmissionDetails } from 'src/app/interfaces/common-interface';
import { Constants } from 'src/app/constants/constants';
@Component({
  selector: 'app-admission-details',
  templateUrl: './admission-details.page.html',
  styleUrls: ['./admission-details.page.scss']
})
export class AdmissionDetailsPage implements OnInit {
  headerValue = 'ADMISSION.LABELS.ADMISSION_DETAILS';
  selectedValue = 'admission-details';
  selectedIndex = 1;
  admissionDetails: AdmissionDetails;
  routerData;
  constants = Constants;
  constructor(
    private readonly router: Router,
    public sharedService: SharedService,
    private httpService: HttpService
  ) {
    this.routerData = this.router.getCurrentNavigation()?.extras.state;
  }

  ngOnInit() {
    this.getAdmissionDetails();
  }

  changeSegment(value: string): void {
    this.selectedValue = value;
    if (this.selectedValue === 'admission-details') {
      this.selectedIndex = 1;
      this.headerValue = 'ADMISSION.LABELS.ADMISSION_DETAILS';
    } else {
      this.selectedIndex = 2;
      this.headerValue = 'TITLES.ADDRESS_DETAILS';
    }
  }

  getAdmissionDetails(): void {
    const url = `${APIs.getAdmissionDetails}${this.routerData?.admission?.admissionId}`;
    this.httpService
      .doGet({
        endpoint: url,
        loader: true
      })
      .subscribe({
        next: (response: AdmissionDetails) => {
          this.admissionDetails = response;
        }
      });
  }
}
