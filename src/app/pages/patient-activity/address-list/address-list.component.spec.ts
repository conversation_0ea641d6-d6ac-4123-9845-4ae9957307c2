import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavParams } from '@ionic/angular';
import { HttpService } from 'src/app/services/http-service/http.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { Apollo } from 'apollo-angular';
import { AddressListComponent } from './address-list.component';

describe('AddressListComponent', () => {
  let component: AddressListComponent;
  let fixture: ComponentFixture<AddressListComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [IonicModule.forRoot(), AddressListComponent, HttpClientTestingModule, RouterModule.forRoot([]), TranslateModule.forRoot()],
      providers: [
        HttpService,
        NavParams,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        CommonService,
        SharedService,
        Apollo,
        GraphqlService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AddressListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('pAddress', () => {
    it('should return line1 if it exists', () => {
      const item = { line1: '123 Main St' };
      expect(component.pAddress(item)).toBe('123 Main St');
    });

    it('should return line2 if line1 does not exist and line2 exists', () => {
      const item = { line2: 'Apt 4B' };
      expect(component.pAddress(item)).toBe('Apt 4B');
    });

    it('should return city if neither line1 nor line2 exist and city exists', () => {
      const item = { city: 'New York' };
      expect(component.pAddress(item)).toBe('New York');
    });

    it('should return district if line1, line2, and city do not exist and district exists', () => {
      const item = { district: 'Manhattan' };
      expect(component.pAddress(item)).toBe('Manhattan');
    });

    it('should return state if line1, line2, city, and district do not exist and state exists', () => {
      const item = { state: 'NY' };
      expect(component.pAddress(item)).toBe('NY');
    });

    it('should return country if line1, line2, city, district, and state do not exist and country exists', () => {
      const item = { country: 'USA' };
      expect(component.pAddress(item)).toBe('USA');
    });

    it('should return zipCode if none of the other fields exist', () => {
      const item = { zipCode: '10001' };
      expect(component.pAddress(item)).toBe('10001');
    });

    it('should return an empty string if no fields exist', () => {
      const item = {};
      expect(component.pAddress(item)).toBe('');
    });

    it('should handle null or undefined input gracefully', () => {
      expect(component.pAddress(null)).toBe('');
      expect(component.pAddress(undefined)).toBe('');
    });
  });

  describe('clearData', () => {
    it('should set isSearch to false while click on close icon', () => {
      component.clearData();
      expect(component.isSearch).toBe(false);
    });

    it('should clear searchText if it has length greater than 0', () => {
      component.queryParams.searchText = 'test';
      component.clearData();
      expect(component.queryParams.searchText).toBe('');
    });

    it('should call searchAddressData if search with text', () => {
      component.queryParams.searchText = 'test';
      spyOn(component, 'searchAddressData').and.stub();
      component.clearData();
      expect(component.searchAddressData).toHaveBeenCalled();
    });
  });

  it('should call getCategory', () => {
    component.getCategory(1);
    expect(component.getCategory).toBeDefined();
  });

  it('should call updateValues', () => {
    expect(component.updateValues).toBeTruthy();
  });

  it('should call changeData', () => {
    component.changeData({ event: { detail: { value: '12' } } }, 'id');
    expect(component.changeData).toBeTruthy();
  });

  it('should call setAddressData', () => {
    const data = [
      {
        id: 1,
        patientId: 1,
        category: 1
      }
    ];
    component.setAddressData(data);
    expect(component.setAddressData).toBeTruthy();
  });

  it('should update address', () => {
    component.updateAddress(1);
    expect(component.updateAddress).toBeDefined();
  });
});
