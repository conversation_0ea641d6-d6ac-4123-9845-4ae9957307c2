@if (patientAddress.length || (queryParams.searchText.length > 0 || isSearch)) {
  @if (!isSearch) {
    <ion-item lines="none" class="setLeftPadding">
      <ion-label color="secondary">{{ 'TITLES.SELECT_ADDRESS_VIEW_DETAILS' | translate }}</ion-label>
      <ion-icon slot="end" name="search" color="de-york" (click)="isSearch = !isSearch" id="search-icon"> </ion-icon>
    </ion-item>
  } @else {
    <ion-item lines="none" class="setLeftPadding">
      <ion-searchbar
        [placeholder]="'PLACEHOLDERS.SEARCH' | translate"
        [debounce]="250"
        mode="ios"
        name="search"
        [(ngModel)]="queryParams.searchText"
        id="search-area"
      ></ion-searchbar>
      <ion-icon slot="end" name="search" (click)="searchAddressData()" id="search-data" color="de-york"> </ion-icon>
      <ion-icon slot="end" name="refresh" (click)="clearData()" id="clear-data" color="de-york" class="removeSpace refresh-icon"> </ion-icon>
    </ion-item>
  }
}
  <ion-accordion-group *ngFor="let addressItems of patientAddress;let i = index; let l = last">
    <ion-accordion [value]="addressItems?.items?.Id">
      <ion-item slot="header" id="item-{{ i }}">
        <ion-avatar slot="start">
          <img src="assets/icon/patient-activity/{{ addressItems?.items?.category | translate }}.png" alt="address" />
        </ion-avatar>
        <ion-label>{{ addressItems?.patientAddress }}</ion-label>
      </ion-item>
      
        <ion-list slot="content">
          <ion-row>
              <ion-col class="ion-align-self-center" size="12">
                <ion-select [class]="!addressItems?.isUpdate ? 'readonly-input' : ''" mode="md" [value]="addressItems?.items?.category | translate" label="{{ 'LABELS.ADDRESS_TYPE' | translate }}" label-placement="floating" fill="outline" id="category-{{i}}" name="category" [disabled]="!addressItems?.isUpdate" (ionChange)="changeData($event,'category')">
                  <ion-select-option [value]="addressType?.home" id="home-{{i}}">{{'MENU.HOME' |
                    translate}}
                    </ion-select-option>
                    <ion-select-option [value]="addressType?.main" id="main-{{i}}">{{'LABELS.MAIN'|
                        translate}}
                    </ion-select-option>
                    <ion-select-option [value]="addressType?.others" id="others-{{i}}">
                        {{'LABELS.OTHERS' | translate}}
                    </ion-select-option>
                    <ion-select-option [value]="addressType?.shipping" id="shipping-{{i}}">
                        {{'LABELS.SHIPPING' |
                        translate}}
                    </ion-select-option>
                </ion-select>
              </ion-col>
          </ion-row>
          <ion-row>
              <ion-col class="ion-align-self-center" size="12">
                <ion-input
                  [class]="!addressItems?.isUpdate ? 'readonly-input' : ''"
                  type="text"
                  id="address1-{{ i }}"
                  label="{{ 'LABELS.ADDRESS' | translate }}"
                  label-placement="floating"
                  fill="outline"
                  name="address1"
                  autocapitalize="on"
                  mode="md"
                  [value]="addressItems?.items?.address1"
                  [disabled]="!addressItems?.isUpdate"
                  (ionChange)="changeData($event, 'line1')"
                ></ion-input>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col class="ion-align-self-center" size="12">
                <ion-input
                  [class]="!addressItems?.isUpdate ? 'readonly-input' : ''"
                  type="text"
                  id="address2-{{ i }}"
                  label="{{ 'LABELS.ADDRESS_2' | translate }}"
                  label-placement="floating"
                  fill="outline"
                  name="address2"
                  autocapitalize="on"
                  mode="md"
                  [value]="addressItems?.items?.address2"
                  [disabled]="!addressItems?.isUpdate"
                  (ionChange)="changeData($event, 'line2')"
                ></ion-input>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col class="ion-align-self-center" size="12">
                <ion-input
                  [class]="!addressItems?.isUpdate ? 'readonly-input' : ''"
                  type="text"
                  id="city-{{ i }}"
                  label="{{ 'LABELS.CITY' | translate }}"
                  label-placement="floating"
                  fill="outline"
                  name="address2"
                  autocapitalize="on"
                  mode="md"
                  [value]="addressItems?.items?.city"
                  [disabled]="!addressItems?.isUpdate"
                  (ionChange)="changeData($event, 'city')"
                ></ion-input>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col class="ion-align-self-center" size="12">
                <ion-input
                  [class]="!addressItems?.isUpdate ? 'readonly-input' : ''"
                  type="text"
                  id="district-{{ i }}"
                  label="{{ 'LABELS.DISTRICT' | translate }}"
                  label-placement="floating"
                  fill="outline"
                  name="address2"
                  autocapitalize="on"
                  mode="md"
                  [value]="addressItems?.items?.district"
                  [disabled]="!addressItems?.isUpdate"
                  (ionChange)="changeData($event, 'district')"
                ></ion-input>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col class="ion-align-self-center" size="12">
                <ion-input
                  [class]="!addressItems?.isUpdate ? 'readonly-input' : ''"
                  type="text"
                  id="state-{{ i }}"
                  label="{{ 'LABELS.STATE' | translate }}"
                  label-placement="floating"
                  fill="outline"
                  name="address2"
                  autocapitalize="on"
                  mode="md"
                  [value]="addressItems?.items?.state"
                  [disabled]="!addressItems?.isUpdate"
                  (ionChange)="changeData($event, 'state')"
                ></ion-input>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col class="ion-align-self-center" size="12">
                <ion-input
                  [class]="!addressItems?.isUpdate ? 'readonly-input' : ''"
                  type="text"
                  id="country-{{ i }}"
                  label="{{ 'LABELS.COUNTRY' | translate }}"
                  label-placement="floating"
                  fill="outline"
                  name="address2"
                  autocapitalize="on"
                  mode="md"
                  [value]="addressItems?.items?.country"
                  [disabled]="!addressItems?.isUpdate"
                  (ionChange)="changeData($event, 'country')"
                ></ion-input>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col class="ion-align-self-center" size="12">
                <ion-input
                  [class]="!addressItems?.isUpdate ? 'readonly-input' : ''"
                  type="text"
                  id="zip-{{ i }}"
                  label="{{ 'LABELS.ZIP' | translate }}"
                  label-placement="floating"
                  fill="outline"
                  name="address2"
                  autocapitalize="on"
                  mode="md"
                  [value]="addressItems?.items?.zip"
                  [disabled]="!addressItems?.isUpdate"
                  (ionChange)="changeData($event, 'zipCode')"
                ></ion-input>
              </ion-col>
          </ion-row>
          <ion-row *ngIf="isEditable">
            <ion-col size="4" offset="2">
              <ion-button (click)="updateValues(i)" mode="ios" expand="full" class="ion-text-capitalize secondary-color" id="update-{{ i }}">
                {{ addressItems?.isUpdate ? ('BUTTONS.CANCEL' | translate) : ('BUTTONS.EDIT' | translate) }}
              </ion-button>
            </ion-col>
            <ion-col size="4">
              @if (!addressItems?.isUpdate) {
                <ion-button
                  color="de-york"
                  mode="ios"
                  expand="full"
                  (click)="confirmRemoveAddress(i)"
                  class="ion-text-capitalize"
                  id="remove-{{ i }}"
                >
                  {{ 'BUTTONS.REMOVE' | translate }}
                </ion-button>
              }@else {
                <ion-button
                  mode="ios"
                  (click)="updateAddress(i)"
                  expand="full"
                  color="de-york"
                  class="ion-text-capitalize"
                  [disabled]="!updateParams?.line1"
                  id="update-{{ i }}"
                >
                  {{ 'BUTTONS.UPDATE' | translate }}
                </ion-button>
              }
            </ion-col>
          </ion-row>
        </ion-list>
    </ion-accordion>
  </ion-accordion-group>
  @if(!patientAddress.length){
    <ion-row>
      <ion-col size="12" class="ion-text-center">
        <ion-text color="secondary">
        @if (queryParams?.searchText?.length) {
          {{ 'LABELS.SORRY_COULDNT_FIND_RESULT' | translate: { searchString: queryParams.searchText } }}
        } @else {
          {{ 'MESSAGES.NO_DATA_AVAILABLE' | translate }}
        }
        </ion-text>
      </ion-col>
    </ion-row>
  }
