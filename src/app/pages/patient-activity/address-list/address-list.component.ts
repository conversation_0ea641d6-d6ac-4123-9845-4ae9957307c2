import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';
import { Activity } from 'src/app/constants/activity';
import { APIs } from 'src/app/constants/apis';
import { Constants, UserGroup } from 'src/app/constants/constants';
import { PatientAddressSearch, UpdatePatientAddress } from 'src/app/interfaces/patient-activity';
import { CommonService } from 'src/app/services/common-service/common.service';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { deepCopyJSON } from 'src/app/utils/utils';

@Component({
  selector: 'app-address-list',
  templateUrl: './address-list.component.html',
  styleUrls: ['./address-list.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, TranslateModule, SearchBarRecipientsModule]
})
export class AddressListComponent implements OnInit {
  patientAddress = [];
  @Input() patientID;
  @Input() admissionID?;
  @Input() patientActivityDetails;
  updateParams = {
    id: '',
    category: '',
    type: '',
    line1: '',
    line2: '',
    city: '',
    state: '',
    country: '',
    district: '',
    zipCode: '',
    userId: '',
    tenantId: '',
    action: 'update'
  };
  isSearch = false;
  public queryParams = {
    patientId: 0,
    tenantId: <any>'',
    startRow: 0,
    endRow: 10,
    searchText: '',
    sessionId: '',
    admissionId: ''
  };
  addressType= Constants.addressType;
  tempAddress
  constructor(
    private readonly sharedService: SharedService,
    private readonly commonService: CommonService,
    private readonly httpService: HttpService,
    private graphqlService: GraphqlService
  ) { }

  ngOnInit() {
    this.queryParams.sessionId = this.sharedService?.userData?.authenticationToken;
    this.queryParams.patientId = this.patientID;
    this.queryParams.tenantId = +this.sharedService.userData?.tenantId;
    this.searchAddressData();
  }
  get isEditable(): boolean {
    return +this.sharedService.userData.group !== UserGroup.PATIENT;
  }
  changeData(event: any, key: string): void {
    this.updateParams[key] = event?.detail?.value;
  }

  updateValues(index: number): void {
    this.patientAddress[index].isUpdate = !this.patientAddress[index].isUpdate;
    if (this.patientAddress[index].isUpdate) {
      this.updateParams.id = this.patientAddress[index].items.Id;
      this.updateParams.category = this.commonService.getTranslateData(this.patientAddress[index].items.category);
      this.updateParams.type = this.patientAddress[index].items.address_type;
      this.updateParams.line1 = this.patientAddress[index].items.address1;
      this.updateParams.line2 = this.patientAddress[index].items.address2;
      this.updateParams.city = this.patientAddress[index].items.city;
      this.updateParams.state = this.patientAddress[index].items.state;
      this.updateParams.country = this.patientAddress[index].items.country;
      this.updateParams.district = this.patientAddress[index].items.district;
      this.updateParams.zipCode = this.patientAddress[index].items.zip;
      this.updateParams.userId = this.patientID;
      this.updateParams.tenantId = this.sharedService.userData.tenantId;
    } else {
      this.patientAddress[index] = deepCopyJSON(this.patientAddress[index]);
    }
  }

  updateAddress(index: number): void {
    this.updateParams.category = this.updateParams.category.toLowerCase();
    this.httpService
      .doPost({
        endpoint: APIs.deleteUpdatePatientAddress,
        payload: this.updateParams,
        loader: true
      })
      .subscribe({
        next: (response: UpdatePatientAddress) => {
            this.patientAddress[index].isUpdate = false;
          if (response.status === Constants.apiStatusCode) {
            this.commonService.showMessage(response.statusMessage);
            this.sharedService.trackActivity({
              type: `${Activity.patientAddress} ${this.updateParams.action}`,
              name: `${Activity.patientAddress} ${this.updateParams.action}`,
              des: {
                data: {
                  userName: this.sharedService.userData.displayName,
                  action: this.updateParams.action,
                  patientName: this.patientActivityDetails?.displayName
                },
                desConstant:  Activity.patientAddressUpdateDesc
              }
            });
            this.searchAddressData();
          }
        },
        error: () => {
          this.patientAddress[index].isUpdate = false;
        }
      });
  }

  confirmRemoveAddress(index: number): void {
    const alertData = {
      message: 'MESSAGES.DELETE_PATIENT_ADDRESS_DETAILS',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.commonService.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        this.deleteAddress(index);
      }
    });
  }

  deleteAddress(index: number): void {
    const deleteData = {
      id: this.patientAddress[index]?.items?.Id,
      action: 'delete'
    };
    this.httpService
      .doPost({
        endpoint: APIs.deleteUpdatePatientAddress,
        payload: deleteData,
        loader: true
      })
      .subscribe({
        next: (response: UpdatePatientAddress) => {
          this.sharedService.isLoading = false;
          if (response.status === Constants.apiStatusCode) {
            this.commonService.showMessage(response.statusMessage);
            this.sharedService.trackActivity({
              type: `${Activity.patientAddress} deleted`,
              name: `${Activity.patientAddress} deleted`,
              des: {
                data: {
                  userName: this.sharedService.userData.displayName,
                  action: 'deleted',
                  patientName: this.patientActivityDetails?.displayName,
                  category: this.commonService.getTranslateData(this.patientAddress[index].items.category)
                },
                desConstant:  Activity.patientAddressDeleteDesc
              }
            });
            this.patientAddress.splice(index,1);
          }
        }
      });
  }

  searchAddressData(): void {
    this.queryParams.admissionId = this.admissionID || '';
    this.queryParams.patientId = +this.patientID;
    this.sharedService.isLoading = true;
    this.graphqlService.addressSearch(this.queryParams)?.subscribe(
      (response: PatientAddressSearch) => {
        this.sharedService.isLoading = false;
        this.patientAddress = [];
        if (response.data.getSessionTenant.patientExternalAddress.data.length) {
          this.setAddressData(response.data.getSessionTenant.patientExternalAddress.data);
        }
      },
      () => {
        this.sharedService.isLoading = false;
      }
    );
  }

  setAddressData(arrayData: any): void {
    for (const item of arrayData) {
      const tempData = {
        patientAddress: this.pAddress(item),
        items: {
          Id: item?.id,
          address1: item?.line1,
          address2: item?.line2,
          address_type: item?.type || '',
          category: this.getCategory(item?.category),
          city: item?.city,
          country: item?.country,
          district: item?.district,
          state: item?.state,
          zip: item?.zipCode
        },
        show: false,
        isUpdate: false
      };
      this.patientAddress.push(tempData);
    }
  }

  getCategory(categoryVal: number) {
    const catData = {
      1: 'LABELS.SHIPPING',
      2: 'MENU.HOME',
      3: 'LABELS.MAIN',
      4: 'LABELS.OTHERS'
    };
    return catData[categoryVal] || 'LABELS.OTHERS';
  }

  clearData(): void {
    this.isSearch = false;
      this.queryParams.searchText = '';
      this.searchAddressData();
  }

  pAddress(item: any): string {
    let pAddress = '';
    if (item?.line1) {
      pAddress = item?.line1;
    } else if (item?.line2) {
      pAddress = item?.line2;
    } else if (item?.city) {
      pAddress = item?.city;
    } else if (item?.district) {
      pAddress = item?.district;
    } else if (item?.state) {
      pAddress = item?.state;
    } else if (item?.country) {
      pAddress = item?.country;
    } else if (item?.zipCode) {
      pAddress = item?.zipCode;
    }
    return pAddress;
  }
}
