.appless-text-align-center {
  text-align: center;
  padding-top: 100px;
}

.appless-page {
  text-align: center;
}

.appless-input-row {
  border: 1px solid #345d70;
  background-color: #fafafa;
  margin-bottom: 1rem;
  padding: 0.2rem;
  border-radius: 5px;
  .item-native {
    background-color: #fafafa;
  }
}

.ion-input-style {
  min-height: 48px;
  top: -3px;
  color: #493c3c;
}


.ion-input-style input {
  font-size: 1rem !important;
}

::ng-deep .native-input.sc-ion-input-md {
  font-size: 1rem !important;
}

@media only screen and (min-width: 500px) {
  .appless-row {
    max-width: 500px;
    margin: 0 auto;
  }
}

.appless-row {
  margin-bottom: .8rem;
  font-size: 0.9rem;
}

.otp-input-box {
  padding: 4px;
  border: 1px solid #345d70;
  width: 40px;
  border-radius: 5px;
  margin: 0.8rem;
}

.otp-input-box:first-child {
  margin-left: 0;
}

.otp-input-box:last-child {
  margin-right: 0;
}

.otp-input-disabled {
  opacity: 0.3;
}

.otp-row {
  justify-content: space-between;
}

.resend-link {
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
}

.resend-link:hover {
  color: #0056b3;
}

.otp-timer {
  text-align: center;
  font-size: 15px;
}

/* Chrome, Safari, Edge, Opera */
::ng-deep input::-webkit-outer-spin-button,
::ng-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
::ng-deep input[type=number] {
  -moz-appearance: textfield;
}

.px-0 {
  padding-right: 0px;
  padding-left: 0px;
}

.mt-8 {
  margin-top: 2rem;
}

::ng-deep {
  .otp-input-container {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    > div {
      padding: 4px !important;
      border: 1px solid #345d70 !important;
      border-radius: 5px !important;
      margin: 0px !important;
      > ion-col {
        padding: 0px !important;
      }
    }
  }

  .otp-input {
    display: inline-block !important;
    height: 44px !important;
    --background: #e1e1e182 !important;
    --padding-start: 0px !important;
    text-align: center !important;
    margin: 0px !important;
    padding: 0px !important;
    width: 44px !important;
  }
}

// Date input specific styles
ion-input[formcontrolname="DOB"] {
  --padding-start: 8px;
  
  &::placeholder {
    color: #999;
  }

  input {
    letter-spacing: 0.5px;
  }
}

.custom-popover {
  --width: 300px !important;
}