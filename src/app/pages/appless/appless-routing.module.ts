import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { ApplessPage } from './appless.page';
import { ApplessHomeComponent } from './appless-home/appless-home.component';

const routes: Routes = [
    {
        path: 'home',
        component: ApplessHomeComponent,
        data: {
            title: 'TITLES.APPLESS_HOME'
        }
    },
    {
        path: '',
        component: ApplessPage,
        data: {
            title: 'TITLES.VERIFY_APPLESS'
        }
    },
    
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class ApplessPageRoutingModule { }
