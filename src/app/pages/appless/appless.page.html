<app-header [hideSocketButton]="true" [hideHelpButton]="true" *ngIf="!isCampaignForm"></app-header>
<ion-content [fullscreen]="true">
  <ion-header collapse="condense" mode="ios">
    <ion-toolbar mode="ios"></ion-toolbar>
  </ion-header>
  <ng-container>
    <div *ngIf="errorInfoMessage" class="appless-text-align-center" role="alert">{{ 'MESSAGES.APPLESS_PAGE_INFO' | translate }}</div>
    <div *ngIf="!verificationMobilePopupShow && !errorInfoMessage" class="appless-text-align-center">{{ verificationMessage }}</div>
    <ion-content class="appless-page" [hidden]="!verificationMobilePopupShow" size="8" *ngIf="!errorInfoMessage">
      <form name="appless-code-form" id="create-availability-form" [formGroup]="appLessForm" class="common-start-padding">
        <ion-grid>
          <h3 class="mt-8">{{ 'TITLES.APPLESS_TITLE' | translate }}</h3>
          <p *ngIf="subTitle" class="appless-row mt-8">{{ subTitle | translate }}</p>
          <p *ngIf="verificationCodeSendMessage" class="appless-row">{{ verificationCodeSendMessage }}</p>
          <ng-container>
            <ion-row class="ion-justify-content-around appless-row" *ngFor="let field of additionalFields">
              <ion-col size="12" size-md="12" class="common-input-row appless-input-row">
                <ion-item>
                  <ion-input
                    *ngIf="field === 'DOB'"
                    label="{{'LABELS.PATIENT' | translate }} {{'LABELS.DATE_OF_BIRTH' | translate }}"
                    labelPlacement="floating"
                    [formControlName]="field"
                    class="ion-input-style"
                    maxlength="10"
                    placeholder="MM/DD/YYYY"
                    (ionInput)="formatDateInput($event, field)"
                    attr.aria-label="{{'LABELS.DATE_OF_BIRTH' | translate }}"
                    inputmode="numeric"
                  ></ion-input>
                  <ion-icon *ngIf="field === 'DOB'" name="calendar-outline" slot="end" id="DOB"></ion-icon>
                  <ion-input
                    *ngIf="field !== 'DOB'"
                    label="{{'LABELS.PATIENT' | translate }} {{'LABELS.'+field | translate }}"
                    labelPlacement="floating"
                    [formControlName]="field"
                    class="ion-input-style"
                    attr.aria-label="{{'LABELS.'+field | translate }}"
                  ></ion-input>
                </ion-item>
              </ion-col>
              <ion-popover trigger="DOB" id="custom-popover" mode="ios" class="custom-popover" *ngIf="field === 'DOB'">
                <ng-template>
                  <ion-datetime
                    #dateOfBirth
                    display-format="MM/DD/YYYY"
                    show-default-buttons="true"
                    presentation="date"
                    (ionChange)="onDateChange($event)"
                    cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                    doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                  ></ion-datetime>
                </ng-template>
              </ion-popover>
            </ion-row>
          </ng-container>
          <ion-row class="appless-row otp-row" *ngIf="isOtpSent" size="10" size-md="10">
            <ion-col class="px-0" *ngIf="!otpSingleFieldEntry">
              <app-otp-input #otpInput [config]="{ otpLength: otpSize, autofocus: true, autoblur: false }" (otpChange)="handleOtpChange($event)" (fill)="handleOtpChange($event)"></app-otp-input>
            </ion-col>
            <ion-col size="12" size-md="12" class="common-input-row appless-input-row" *ngIf="otpSingleFieldEntry">
              <ion-item>
                <ion-input
                  label="{{'LABELS.OTP_WTH_DIGITS' | translate: { digits: otpSize} }}"
                  labelPlacement="floating" 
                  type="text"
                  formControlName="otp"
                  class="ion-input-style"
                  [maxlength]="otpSize"
                  attr.aria-label="{{'LABELS.OTP' | translate }}"
                ></ion-input>
              </ion-item>
            </ion-col>
          </ion-row>
          <ion-row *ngIf="isOtpSent && mobileVerificationTimerText !== ''" size="6" class="ion-align-items-center">
            <ion-col size="12" class="otp-timer">
              <a *ngIf="resendOtp; else displayExpiry;" href="javascript: void(0);" (click)="resendVerification()" class="resend-link">{{ 'LABELS.RESEND_NEW_CODE' | translate }}</a>
              <ng-template #displayExpiry><span *ngIf="mobileVerificationTimerText !== ''">{{ 'MESSAGES.OTP_EXPIRES_IN_SECONDS' | translate:{'expiry': mobileVerificationTimerText} }}</span></ng-template>
            </ion-col>
          </ion-row>
          <ion-row size="6" class="ion-align-items-center appless-row">
            <ion-col size="12">
              <div class="buttons">
                <ion-button id="submit" expand="full" color="success" [disabled]="!appLessForm.valid" (click)="verifyMobileOrEmail()" [attr.aria-label]="'BUTTONS.VERIFY' | translate" (keydown)="handleKeyDownSubmit($event)">
                  {{ 'BUTTONS.VERIFY' | translate }}
                </ion-button>
              </div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </form>
    </ion-content>
  </ng-container>
</ion-content>
