import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavController } from '@ionic/angular';

import { ApplessPage } from 'src/app/pages/appless/appless.page';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { CommonService } from 'src/app/services/common-service/common.service';
import { RouterModule, UrlSerializer, Router } from '@angular/router';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { SessionService } from 'src/app/services/session-service/session.service';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { Apollo } from 'apollo-angular';
import { TranslateModule } from '@ngx-translate/core';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { Constants } from 'src/app/constants/constants';
import { of, throwError } from 'rxjs';
import { ConfigValues } from 'src/assets/config/config';
import { OtpInputComponent } from 'src/app/components/otp-input/otp-input.component';
import { APIs } from 'src/app/constants/apis';
import { PageRoutes } from 'src/app/constants/page-routes';

describe('ApplessPage', () => {
  let component: ApplessPage;
  let fixture: ComponentFixture<ApplessPage>;
  let formBuilder: UntypedFormBuilder = new UntypedFormBuilder();
  let sharedService: SharedService;
  let sessionService: SessionService;
  let common: CommonService;
  let httpService: HttpService;
  let socketService: SocketService;
  let applessForm: UntypedFormGroup;
  let router: Router;
  beforeEach(() => {
    localStorage.clear(); // Clear localStorage before each test
    formBuilder = new UntypedFormBuilder();
    applessForm = formBuilder.group({
      otp: new UntypedFormControl(''),
    });
    TestBed.configureTestingModule({
      declarations: [ApplessPage, OtpInputComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        TranslateTestingModule,
        ReactiveFormsModule
      ],
      providers: [
        CommonService,
        NavController,
        UrlSerializer,
        NgxPermissionsService,
        SharedService,
        SessionService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        Apollo,
        HttpService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        { provide: UntypedFormBuilder, useValue: formBuilder }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    spyOn(sessionStorage, 'setItem');
    sessionService = TestBed.inject(SessionService);
    socketService = TestBed.inject(SocketService);
    httpService = TestBed.inject(HttpService);
    sharedService = TestBed.inject(SharedService);
    common = TestBed.inject(CommonService);
    router = TestBed.inject(Router);
    fixture = TestBed.createComponent(ApplessPage);
    component = fixture.componentInstance;
    component.tokenData = 'someToken';
    component.sharedService.userData = { countryCode: '1', mobile: '1234567890', userName: '<EMAIL>' } as any;
    fixture.detectChanges();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
  it('execute getSubTitle :document', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.document;
    const subtitle = component.getSubTitle();
    expect(subtitle).toEqual('TITLES.APPLESS_DOCUMENT_SUB_TITLE');
  });
  it('execute getSubTitle :form', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.form;
    const subtitle = component.getSubTitle();
    expect(subtitle).toEqual('TITLES.APPLESS_FORM_SUB_TITLE');
  });
  it('execute getSubTitle :download', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.download;
    const subtitle = component.getSubTitle();
    expect(subtitle).toEqual('TITLES.APPLESS_DOWNLOAD_SUB_TITLE');
  });
  it('execute getSubTitle :message', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.message;
    const subtitle = component.getSubTitle();
    expect(subtitle).toEqual('TITLES.APPLESS_MESSAGE_SUB_TITLE');
  });
  it('execute getSubTitle :visit', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.visitView;
    const subtitle = component.getSubTitle();
    expect(subtitle).toEqual('TITLES.APPLESS_VISIT_SUB_TITLE');
  });

  it('execute verifyMobileOrEmail: document', () => {
    component.accessGranted = true;
    component.appLessWorkFlow = Constants.applessFlowMode.document;
    component.verifyMobileOrEmail();
    expect(component.verifyMobileOrEmail).toBeTruthy();
  });
  it('execute verifyMobileOrEmail: download form', () => {
    component.accessGranted = true;
    component.appLessWorkFlow = Constants.applessFlowMode.download;
    component.verifyTokenResponse = { sourceId: Constants.sourceId.document.toString() } as any;
    component.verifyMobileOrEmail();
    expect(component.verifyMobileOrEmail).toBeTruthy();
  });
  it('execute verifyMobileOrEmail: download document', () => {
    component.accessGranted = true;
    component.appLessWorkFlow = Constants.applessFlowMode.download;
    component.verifyTokenResponse = { sourceId: Constants.sourceId.form.toString() } as any;
    component.verifyMobileOrEmail();
    expect(component.verifyMobileOrEmail).toBeTruthy();
  });
  it('execute verifyMobileOrEmail: download invalid source id', () => {
    component.accessGranted = true;
    component.appLessWorkFlow = Constants.applessFlowMode.download;
    component.verifyTokenResponse = { sourceId: '1004' } as any;
    component.verifyMobileOrEmail();
    expect(component.verifyMobileOrEmail).toBeTruthy();
  });
  it('execute verifyMobileOrEmail: visit', () => {
    component.accessGranted = true;
    component.appLessWorkFlow = Constants.applessFlowMode.visitView;
    component.verifyMobileOrEmail();
    expect(component.verifyMobileOrEmail).toBeTruthy();
  });
  it('execute verifyMobileOrEmail: message', () => {
    component.accessGranted = true;
    component.appLessWorkFlow = Constants.applessFlowMode.message;
    sessionService.applessMessagingFlow = true;
    component.verifyMobileOrEmail();
    expect(component.verifyMobileOrEmail).toBeTruthy();
  });

  it('should handle successful OTP verification', () => {
    const additionalData = { otp: '123456', sessionId: 'testSession' };
    spyOn(component, 'navigateToNextPage');
    spyOn(localStorage, 'setItem');
    spyOn(component.sharedService, 'trackActivity');

    component.handleSuccessfulVerification(additionalData);

    expect(component.navigateToNextPage).toHaveBeenCalled();
  });

  // Update campaign form related tests
  it('should decode campaign token correctly', () => {
    const mockToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc0NhbXBhaWduIjoiMSJ9.signature';
    component.tokenData = mockToken;
    component.decodeToken();
    expect(component.isCampaignForm).toBe(true);
  });

  it('should handle error differently for campaign forms', () => {
    component.isCampaignForm = true;
    const response = {
      status: Constants.loginResponseStatus.invalid,
      tokenExpire: false,
      message: 'Custom campaign message'
    };
    
    // Fix: Properly spy on showMessage method
    spyOn(component.commonService, 'showMessage');
    component.handleError(response);
    
    expect(component.verificationMessage).toBe('Custom campaign message');
    expect(component.commonService.showMessage).not.toHaveBeenCalled();
  });

  describe('handleError', () => {
    it('execute handleError', () => {
      component.handleError({});
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response invalid, tokenExpire = true, and applessMessagingFlow= true', () => {
      sessionService.applessMessagingFlow = true;
      const response = {
        status: 0,
        tokenExpire: true
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response invalid, tokenExpire = true, and applessMessagingFlow= false', () => {
      sessionService.applessMessagingFlow = false;
      const response = {
        status: 0,
        tokenExpire: true
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response invalid, tokenExpire = false', () => {
      sessionService.applessMessagingFlow = true;
      const response = {
        status: 0,
        tokenExpire: false
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response invalid, tokenExpire = false and appLessWorkFlow=forms', () => {
      component.appLessWorkFlow = 'forms';
      const response = {
        status: 0,
        tokenExpire: false
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response invalid, tokenExpire = false and appLessWorkFlow=document', () => {
      component.appLessWorkFlow = 'document';
      const response = {
        status: 0,
        tokenExpire: false
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response invalid, tokenExpire = false and appLessWorkFlow=download', () => {
      component.appLessWorkFlow = 'download';
      const response = {
        status: 0,
        tokenExpire: false
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response status=pendingActivation', () => {
      const response = {
        status: 2,
        userData: {
          message: 'Pending Activation'
        }
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response status=disable', () => {
      const response = {
        status: 1,
        userData: {
          message: 'disable'
        }
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response status=dischargeUser', () => {
      const response = {
        status: 5,
        userData: {
          message: 'dischargeUser'
        }
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response status=Unauthorized', () => {
      const response = {
        status: {
          code: 401
        },
        error: {
          message: 'Unauthorized'
        }
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response show message from API user not exists', () => {
      const response = {
        status: 400,
        userId: undefined,
        message: 'Error',
        userData: {
          message: 'Error'
        }
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with appLessWorkFlow=download and responseStatus=invalidCode', () => {
      component.appLessWorkFlow = 'download';
      const response = {
        responseStatus: {
          code: 400
        }
      };
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
    it('execute handleError with response string', () => {
      const response = 'Error';
      component.handleError(response);
      expect(component.handleError).toBeTruthy();
    });
  });
  it('execute resendVerification', () => {
    component.resendVerification();
    expect(component.resendVerification).toBeTruthy();
  });
  it('execute ngOnInit appLessWorkFlow = forms', () => {
    component.appLessWorkFlow = 'forms';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ngOnInit appLessWorkFlow = download', () => {
    component.appLessWorkFlow = 'download';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('execute ngOnInit appLessWorkFlow = visit-view', () => {
    component.appLessWorkFlow = 'visit-view';
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
  it('should handle successful verification', () => {
    spyOn(component, 'handleSuccessfulVerification').and.callThrough();
    component.handleSuccessfulVerification({});
    expect(component.handleSuccessfulVerification).toHaveBeenCalled();
  });

  it('should handle verification failure', () => {
    spyOn(component, 'handleVerificationFailed').and.callThrough();
    component.handleVerificationFailed({}, { message: "Failed" });
    expect(component.handleVerificationFailed).toHaveBeenCalled();
  });

  it('should navigate to the next page', () => {
    spyOn(component, 'navigateToNextPage').and.callThrough();
    component.navigateToNextPage({});
    expect(component.navigateToNextPage).toHaveBeenCalled();
  });

  it('should format string correctly', () => {
    const formattedString = component.formatString('TEST_STRING');
    expect(formattedString).toEqual('teststring');
  });

  it('should show expiry timer', () => {
    spyOn(component, 'showExpiryTimer').and.callThrough();
    component.showExpiryTimer({ otpExpiryTime: 60 });
    expect(component.showExpiryTimer).toHaveBeenCalled();
  });

  it('should add zero for mm:ss format', () => {
    const formattedValue = component.addZeroForMmSs(5);
    expect(formattedValue).toEqual('05');
  });

  it('should handle resend verification', () => {
    spyOn(component, 'resendVerification').and.callThrough();
    component.resendVerification();
    expect(component.resendVerification).toHaveBeenCalled();
  });

  it('should handle save appless document history', () => {
    spyOn(component, 'saveApplessDocumentHistory').and.callThrough();
    component.saveApplessDocumentHistory({});
    expect(component.saveApplessDocumentHistory).toHaveBeenCalled();
  });

  it('should call sendVerification with resend = true', async () => {
    spyOn(component, 'sendVerification').and.returnValue(Promise.resolve({ status: true, message: 'Success' }));
    await component.resendVerification();
    expect(component.sendVerification).toHaveBeenCalledWith(true);
  });

  it('should call sendVerification with resend = false', async () => {
    spyOn(component, 'sendVerification').and.returnValue(Promise.resolve({ status: true, message: 'Success' }));
    const result = await component.sendVerification(false);
    expect(component.sendVerification).toHaveBeenCalledWith(false);
    expect(result).toEqual({ status: true, message: 'Success' });
  });

  it('should call sendEmailOrMobileVerification with resend = true', async () => {
    spyOn(component, 'sendEmailOrMobileVerification').and.returnValue(Promise.resolve({ status: true, message: 'Success' }));
    const result = await component.sendEmailOrMobileVerification(true);
    expect(component.sendEmailOrMobileVerification).toHaveBeenCalledWith(true);
    expect(result).toEqual({ status: true, message: 'Success' });
  });

  it('should call sendEmailOrMobileVerification with resend = false', async () => {
    spyOn(component, 'sendEmailOrMobileVerification').and.returnValue(Promise.resolve({ status: true, message: 'Success' }));
    const result = await component.sendEmailOrMobileVerification(false);
    expect(component.sendEmailOrMobileVerification).toHaveBeenCalledWith(false);
    expect(result).toEqual({ status: true, message: 'Success' });
  });

  it('should call saveApplessDocumentHistory with params', () => {
    spyOn(component, 'saveApplessDocumentHistory').and.callThrough();
    const params = { accessCodeSentMedium: 'mobile', activityName: 'activity', sender: 'sender' };
    component.saveApplessDocumentHistory(params);
    expect(component.saveApplessDocumentHistory).toHaveBeenCalledWith(params);
  });

  it('should call verifyMobileOrEmail and handle invalid form', () => {
    component.appLessForm = new UntypedFormGroup({});
    spyOn(component, 'verifyMobileOrEmail').and.callThrough();
    component.verifyMobileOrEmail();
    expect(component.verifyMobileOrEmail).toHaveBeenCalled();
  });

  it('should call verifyMobileOrEmail and handle valid form', () => {
    component.appLessForm = new UntypedFormGroup({});
    component.accessGranted = true;
    spyOn(component, 'verifyMobileOrEmail').and.callThrough();
    component.verifyMobileOrEmail();
    expect(component.verifyMobileOrEmail).toHaveBeenCalled();
  });

  it('should call handleSuccessfulVerification with additionalData', () => {
    const additionalData = { otp: '123456', sessionId: 'sessionId' };
    spyOn(component, 'handleSuccessfulVerification').and.callThrough();
    component.handleSuccessfulVerification(additionalData);
    expect(component.handleSuccessfulVerification).toHaveBeenCalledWith(additionalData);
  });

  it('should call handleVerificationFailed with request', () => {
    const request = { otp: '123456', sessionId: 'sessionId' };
    spyOn(component.sharedService, 'trackActivity');
    component.handleVerificationFailed(request, { message: "Failed" });
    expect(component.sharedService.trackActivity).toHaveBeenCalled();
  });

  it('should call navigateToNextPage with additionalData', () => {
    const additionalData = { otp: '123456', sessionId: 'sessionId' };
    spyOn(component, 'navigateToNextPage').and.callThrough();
    component.navigateToNextPage(additionalData);
    expect(component.navigateToNextPage).toHaveBeenCalledWith(additionalData);
  });

  it('should call formatString with input', () => {
    const input = 'TEST_STRING';
    spyOn(component, 'formatString').and.callThrough();
    const result = component.formatString(input);
    expect(component.formatString).toHaveBeenCalledWith(input);
    expect(result).toEqual('teststring');
  });

  it('should call showExpiryTimer with response', () => {
    const response = { otpExpiryTime: 60 };
    spyOn(component, 'showExpiryTimer').and.callThrough();
    component.showExpiryTimer(response);
    expect(component.showExpiryTimer).toHaveBeenCalledWith(response);
  });

  it('should call addZeroForMmSs with value', () => {
    const value = 5;
    spyOn(component, 'addZeroForMmSs').and.callThrough();
    const result = component.addZeroForMmSs(value);
    expect(component.addZeroForMmSs).toHaveBeenCalledWith(value);
    expect(result).toEqual('05');
  });

  it('should call ngOnInit and handle tokenData is blank', () => {
    spyOn(component, 'getSubTitle').and.returnValue('SubTitle');
    component.tokenData = '';
    component.ngOnInit();
    expect(component.getSubTitle).not.toHaveBeenCalled();
  });

  it('should call ngOnInit and handle tokenData is present', () => {
    spyOn(component, 'getSubTitle').and.returnValue('SubTitle');
    component.tokenData = 'someToken';
    component.ngOnInit();
    expect(component.getSubTitle).toHaveBeenCalled();
  });

  it('should call sendVerification and handle otpThroughMobile is true', async () => {
    component.otpThroughMobile = true;
    spyOn(component, 'sendEmailOrMobileVerification').and.returnValue(Promise.resolve({ status: true, message: 'Success' }));
    const result = await component.sendVerification();
    expect(result).toEqual({ status: true, message: 'Success' });
  });

  it('should call sendVerification and handle otpThroughMobile is false', async () => {
    component.otpThroughMobile = false;
    spyOn(component, 'sendEmailOrMobileVerification').and.returnValue(Promise.resolve({ status: true, message: 'Success' }));
    const result = await component.sendVerification();
    expect(result).toEqual({ status: true, message: 'Success' });
  });

  it('should call sendEmailOrMobileVerification and handle resend is true', async () => {
    spyOn(httpService, 'doPost').and.returnValue(of({ status: true, message: 'Success', sessionId: 'sessionId' }));
    const result = await component.sendEmailOrMobileVerification(true);
    expect(result).toEqual({ status: true, message: 'Success', sessionId: 'sessionId' });
  });

  it('should call sendEmailOrMobileVerification and handle resend is false', async () => {
    spyOn(httpService, 'doPost').and.returnValue(of({ status: true, message: 'Success', sessionId: 'sessionId' }));
    const result = await component.sendEmailOrMobileVerification(false);
    expect(result).toEqual({ status: true, message: 'Success', sessionId: 'sessionId' });
  });

  it('should call verifyMobileOrEmail and handle additionalFields length > 0', () => {
    component.additionalFields = ['field1', 'field2'];
    component.isOtpSent = true;
    component.appLessForm = new UntypedFormGroup({
      otpGroup: new UntypedFormGroup({
        otp1: new UntypedFormControl('1'),
        otp2: new UntypedFormControl('2'),
        otp3: new UntypedFormControl('3'),
        otp4: new UntypedFormControl('4'),
        otp5: new UntypedFormControl('5'),
        otp6: new UntypedFormControl('6')
      }),
      field1: new UntypedFormControl('value1'),
      field2: new UntypedFormControl('value2')
    });
    spyOn(httpService, 'doPost').and.returnValue(of({ status: true }));
    component.verifyMobileOrEmail();
    expect(httpService.doPost).toHaveBeenCalled();
  });

  it('should call verifyMobileOrEmail and handle additionalFields length = 0', () => {
    component.additionalFields = [];
    component.isOtpSent = false;
    component.appLessForm = new UntypedFormGroup({});
    component.accessGranted = true;
    spyOn(component, 'handleSuccessfulVerification');
    component.verifyMobileOrEmail();
    expect(component.handleSuccessfulVerification).toHaveBeenCalled();
  });

  it('should call handleSuccessfulVerification and handle otpThroughMobile is true', () => {
    component.otpThroughMobile = true;
    spyOn(component, 'saveApplessDocumentHistory');
    component.handleSuccessfulVerification({});
    expect(component.saveApplessDocumentHistory).toHaveBeenCalled();
  });

  it('should call handleSuccessfulVerification and handle otpThroughMobile is false', () => {
    component.otpThroughMobile = false;
    spyOn(component, 'saveApplessDocumentHistory');
    component.handleSuccessfulVerification({});
    expect(component.saveApplessDocumentHistory).toHaveBeenCalled();
  });

  it('should call handleVerificationFailed and handle request', () => {
    const request = { otp: '123456', sessionId: 'sessionId' };
    spyOn(sharedService, 'trackActivity');
    component.handleVerificationFailed(request, { message: "Failed" });
    expect(sharedService.trackActivity).toHaveBeenCalled();
  });

  it('should call navigateToNextPage and handle appLessWorkFlow is form', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.form;
    component.formDetails = {};
    spyOn(router, 'navigate');
    component.navigateToNextPage({});
    expect(router.navigate).toHaveBeenCalled();
  });

  it('should call navigateToNextPage and handle appLessWorkFlow is document', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.document;
    spyOn(router, 'navigate');
    component.navigateToNextPage({});
    expect(router.navigate).toHaveBeenCalled();
  });

  it('should call navigateToNextPage and handle appLessWorkFlow is download', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.download;
    component.verifyTokenResponse = { sourceId: Constants.sourceId.document.toString() } as any;
    spyOn(router, 'navigate');
    component.navigateToNextPage({});
    expect(router.navigate).toHaveBeenCalled();
  });

  it('should call navigateToNextPage and handle appLessWorkFlow is visitView', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.visitView;
    spyOn(router, 'navigate');
    component.navigateToNextPage({});
    expect(router.navigate).toHaveBeenCalled();
  });

  it('should call navigateToNextPage and handle appLessWorkFlow is userConsent', () => {
    component.appLessWorkFlow = Constants.applessFlowMode.userConsent;
    spyOn(router, 'navigate');
    component.navigateToNextPage({});
    expect(router.navigate).toHaveBeenCalled();
  });

  it('should call navigateToNextPage and handle sessionService.applessMessagingFlow is true', () => {
    sessionService.applessMessagingFlow = true;
    spyOn(router, 'navigate');
    component.navigateToNextPage({});
    expect(router.navigate).toHaveBeenCalled();
  });

  it('should call addZeroForMmSs and handle value < 10', () => {
    const result = component.addZeroForMmSs(5);
    expect(result).toEqual('05');
  });

  it('should call handleError with response status=invalid and tokenExpire=true', () => {
    const response = { status: Constants.loginResponseStatus.invalid, tokenExpire: true };
    spyOn(component.commonService, 'showMessage');
    spyOn(component, 'trackErrorLogin');
    component.handleError(response);
    expect(component.commonService.showMessage).toHaveBeenCalledWith(component.commonService.getTranslateData('ERROR_MESSAGES.APPLESS_LINK_EXPIRED'));
    expect(component.trackErrorLogin).toHaveBeenCalledWith(component.commonService.getTranslateData('ERROR_MESSAGES.APPLESS_LINK_EXPIRED'));
  });

  it('should call handleError with response status=invalid and tokenExpire=false', () => {
    const response = { status: Constants.loginResponseStatus.invalid, tokenExpire: false };
    spyOn(component.commonService, 'showMessage');
    spyOn(component, 'trackErrorLogin');
    component.handleError(response);
    expect(component.commonService.showMessage).toHaveBeenCalledWith(component.commonService.getTranslateData('ERROR_MESSAGES.INVALID_APPLESS_TOKEN'));
    expect(component.trackErrorLogin).toHaveBeenCalledWith(component.commonService.getTranslateData('ERROR_MESSAGES.INVALID_APPLESS_TOKEN'));
  });

  it('should call handleError with response status=pendingActivation', () => {
    const response = { status: Constants.loginResponseStatus.pendingActivation, userData: { message: 'Pending Activation' } };
    spyOn(component.commonService, 'showMessage');
    spyOn(component, 'trackErrorLogin');
    component.handleError(response);
    expect(component.commonService.showMessage).toHaveBeenCalledWith(ConfigValues.messages.pendingAccountActivation);
    expect(component.trackErrorLogin).toHaveBeenCalledWith('Pending Activation');
  });

  it('should call handleError with response status=disable', () => {
    const response = { status: Constants.loginResponseStatus.disable, userData: { message: 'disable' } };
    spyOn(component.commonService, 'showMessage');
    spyOn(component, 'trackErrorLogin');
    component.handleError(response);
    expect(component.commonService.showMessage).toHaveBeenCalledWith(component.commonService.getTranslateData('VALIDATION_MESSAGES.ACCOUNT_DISABLED_CONTACT'));
    expect(component.trackErrorLogin).toHaveBeenCalledWith('disable');
  });

  it('should call handleError with response status=dischargeUser', () => {
    const response = { status: Constants.loginResponseStatus.dischargeUser, userData: { message: 'dischargeUser' } };
    spyOn(component.commonService, 'showMessage');
    spyOn(component, 'trackErrorLogin');
    component.handleError(response);
    expect(component.commonService.showMessage).toHaveBeenCalledWith(ConfigValues.messages.dischargedUser);
    expect(component.trackErrorLogin).toHaveBeenCalledWith('dischargeUser');
  });

  it('should call handleError with response status=Unauthorized', () => {
    const response = { status: { code: 401 }, error: { message: 'Unauthorized' } };
    spyOn(component.commonService, 'showMessage');
    spyOn(component, 'trackErrorLogin');
    component.handleError(response);
    expect(component.commonService.showMessage).toHaveBeenCalledWith('Unauthorized');
    expect(component.trackErrorLogin).toHaveBeenCalledWith('Unauthorized');
  });

  it('should call handleError with response string', () => {
    const response = 'Error';
    spyOn(component.commonService, 'showMessage');
    component.handleError(response);
    expect(component.commonService.showMessage).toHaveBeenCalledWith(component.commonService.getTranslateData('ERROR_MESSAGES.TECHNICAL_DIFFICULTY_TRY_AGAIN'));
  });

  it('should call trackErrorLogin', () => {
    spyOn(component.sharedService, 'trackActivity');
    component.trackErrorLogin('Error');
    expect(component.sharedService.trackActivity).toHaveBeenCalled();
  });

  it('should call formatString and handle non-string input', () => {
    const result = component.formatString(123 as any);
    expect(result).toEqual('123');
  });

  it('should call formatString and handle string input', () => {
    const result = component.formatString('TEST_STRING');
    expect(result).toEqual('teststring');
  });

  it('should call addZeroForMmSs and handle value < 10', () => {
    const result = component.addZeroForMmSs(5);
    expect(result).toEqual('05');
  });
  
  it('should call showExpiryTimer and handle successful response', async () => {
    const response = { otpExpiryTime: 60 };
    const spy = spyOn(component.ngZone, 'runOutsideAngular');
    
    component.showExpiryTimer(response);
    
    expect(spy).toHaveBeenCalled();
    expect(component.mobileVerificationTimerText).toBeDefined();
  });
  
  it('should call sendVerification and handle successful response', async () => {
    // Set up required data
    component.sharedService.userData = {
      countryCode: '1',
      mobile: '1234567890',
      userName: '<EMAIL>'
    } as any;
    
    const successResponse = { status: true, message: 'Success', otpExpiryTime: 60 };
    
    // Mock the required methods
    spyOn(component.sharedService, 'trackActivity');
    spyOn(component, 'sendEmailOrMobileVerification').and.returnValue(Promise.resolve(successResponse));
    spyOn(component, 'saveApplessDocumentHistory');
    spyOn(component.commonService, 'showMessage');
  
    await component.sendVerification();
  
    expect(component.sendEmailOrMobileVerification).toHaveBeenCalled();
    expect(component.saveApplessDocumentHistory).toHaveBeenCalled();
    expect(component.sharedService.trackActivity).toHaveBeenCalled();
  });
  it('should set isCampaignForm to true when isCampaign is 1', () => {
    component.tokenData = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc0NhbXBhaWduIjoiMSJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
    component.decodeToken();
    expect(component.isCampaignForm).toBe(true);
  });

  it('should set isCampaignForm to false when isCampaign is 0', () => {
    component.tokenData = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc0NhbXBhaWduIjoiMCJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
    component.decodeToken();
    expect(component.isCampaignForm).toBe(false);
  });

  it('should set verification message based on response message and isCampaignForm', () => {
    const response = {
      status: 0,
      tokenExpire: false,
      message: 'Custom campaign message'
    };
    component.isCampaignForm = true;
    spyOn(common, 'showMessage');
    component.handleError(response);
    expect(component.verificationMessage).toBe('Custom campaign message');
    expect(common.showMessage).not.toHaveBeenCalled();

    component.isCampaignForm = false;
    component.handleError(response);
    expect(component.verificationMessage).toBe('ERROR_MESSAGES.INVALID_APPLESS_TOKEN');
    expect(common.showMessage).toHaveBeenCalledWith('ERROR_MESSAGES.INVALID_APPLESS_TOKEN');
  });

  describe('Campaign Form Tests', () => {
    it('should decode campaign token correctly', () => {
      const mockToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc0NhbXBhaWduIjoiMSJ9.signature';
      component.tokenData = mockToken;
      component.decodeToken();
      expect(component.isCampaignForm).toBe(true);
    });

    it('should handle error message differently for campaign forms', () => {
      component.isCampaignForm = true;
      const response = {
        status: Constants.loginResponseStatus.invalid,
        tokenExpire: false,
        message: 'Custom campaign message'
      };
      
      spyOn(common, 'showMessage');
      component.handleError(response);
      
      expect(component.verificationMessage).toBe('Custom campaign message');
      expect(common.showMessage).not.toHaveBeenCalled();
    });
  });

  describe('Date Handling Tests', () => {
    it('should handle date change event', () => {
      const dateEvent = {
        detail: {
          value: '2023-01-01'
        }
      };
      component.appLessForm.addControl('DOB', new UntypedFormControl(''));
      component.onDateChange(dateEvent);
      expect(component.appLessForm.get('DOB').value).toBe('01/01/2023');
    });
  });

  describe('OTP Input Tests', () => {
    it('should handle OTP change', () => {
      component.appLessForm.addControl('otp', new UntypedFormControl(''));
      const otpValue = '123456';
      component.handleOtpChange(otpValue);
      expect(component.appLessForm.get('otp').value).toBe(otpValue);
    });
  });

  describe('Additional Validation Fields Tests', () => {
    it('should add additional validation fields to form', () => {
      const fields = ['firstName', 'lastName', 'DOB'];
      component.additionalFields = fields;
      
      fields.forEach(field => {
        component.appLessForm.addControl(field, new UntypedFormControl(''));
      });

      fields.forEach(field => {
        expect(component.appLessForm.contains(field)).toBe(true);
      });
    });

    it('should format field names correctly', () => {
      expect(component.formatString('FIRST_NAME')).toBe('firstname');
      expect(component.formatString('DOB')).toBe('dob');
      expect(component.formatString('lastName')).toBe('lastname');
    });
  });

  describe('Timer Tests', () => {
    it('should initialize timer correctly', () => {
      const response = { otpExpiryTime: 60 };
      component.showExpiryTimer(response);
      expect(component.mobileVerificationTimerText).toBeDefined();
      expect(component.resendOtp).toBe(false);
    });

    it('should handle resend OTP', () => {
      spyOn(component, 'sendVerification').and.returnValue(Promise.resolve({ status: true }));
      component.resendVerification();
      expect(component.sendVerification).toHaveBeenCalledWith(true);
    });
  });

  describe('Verification Process Tests', () => {
    it('should handle successful verification with additional fields', () => {
      const additionalData = {
        firstName: 'John',
        DOB: '1990-01-01',
        otp: '123456'
      };
      spyOn(component, 'handleSuccessfulVerification');
      component.verifyMobileOrEmail();
      expect(component.handleSuccessfulVerification).not.toHaveBeenCalled(); // Form is invalid
    });

    it('should navigate correctly after successful verification', () => {
      const navigationData = {};
      // Fix: Set up necessary data for navigation
      component.appLessWorkFlow = Constants.applessFlowMode.form;
      component.formDetails = { someData: 'test' };
      spyOn(router, 'navigate').and.returnValue(Promise.resolve(true));
      
      component.navigateToNextPage(navigationData);
      
      expect(router.navigate).toHaveBeenCalledWith(
        jasmine.any(Array),
        jasmine.objectContaining({
          skipLocationChange: true,
          state: jasmine.any(Object)
        })
      );
    });
  });

  describe('Date Formatting Tests', () => {
    beforeEach(() => {
      component.appLessForm.addControl('DOB', new UntypedFormControl(''));
    });

    it('should handle backspace in date input', (done) => {
      // Setup initial value
      component.appLessForm.get('DOB').setValue('12/34');
      
      const event = {
        target: {
          value: '12/34',
          selectionStart: 4,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'deleteContentBackward'
      };
      
      component.formatDateInput(event, 'DOB');
      
      // Use setTimeout to wait for the async setValue operation to complete
      setTimeout(() => {
        expect(component.appLessForm.get('DOB').value).toBe('12/4');
        done();
      });
    });

    it('should auto-insert slashes in date input', () => {
      const event = {
        target: {
          value: '12345',
          selectionStart: 5,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'insertText'
      };
      component.formatDateInput(event, 'DOB');
      expect(component.appLessForm.get('DOB').value).toBe('12/34/5');
    });

    it('should validate complete date', () => {
      const event = {
        target: {
          value: '12/34/2023',
          selectionStart: 10,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'insertText'
      };
      component.formatDateInput(event, 'DOB');
      expect(component.appLessForm.get('DOB').errors).toBeTruthy();
    });
  });

  describe('OTP Verification Tests', () => {
    it('should handle OTP session management', () => {
      const mockResponse = { sessionId: 'test-session-id', status: true };
      spyOn(httpService, 'doPost').and.returnValue(of(mockResponse));
      
      component.sendEmailOrMobileVerification(false).then(response => {
        expect(component.otpSessionId).toBe('test-session-id');
      });
    });

    it('should clear timer on resend verification', () => {
      spyOn(window, 'clearInterval');
      component.resendVerification();
      expect(window.clearInterval).toHaveBeenCalled();
    });
  });

  describe('Form Validation Tests', () => {
    it('should validate required additional fields', () => {
      component.additionalFields = ['firstName', 'lastName'];
      component.additionalFields.forEach(field => {
        component.appLessForm.addControl(field, new UntypedFormControl('', Validators.required));
      });
      // Now the form should be invalid since required fields are empty
      expect(component.appLessForm.valid).toBeFalsy();
    });

    it('should handle submit button state', () => {
      component.appLessForm.addControl('otp', new UntypedFormControl('123456'));
      expect(component.appLessForm.valid).toBeTruthy();
    });
  });

  describe('Navigation Tests', () => {
    it('should handle navigation with different appLessWorkFlow values', () => {
      const navigateSpy = spyOn(router, 'navigate').and.returnValue(Promise.resolve(true));

      // Test document workflow
      component.appLessWorkFlow = Constants.applessFlowMode.document;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalled();
      
      // Clear previous calls
      navigateSpy.calls.reset();
      
      // Test form workflow
      component.appLessWorkFlow = Constants.applessFlowMode.form;
      component.formDetails = { someData: 'test' };
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalled();
      
      // Clear previous calls
      navigateSpy.calls.reset();
      
      // Test download workflow
      component.appLessWorkFlow = Constants.applessFlowMode.download;
      component.verifyTokenResponse = { sourceId: Constants.sourceId.document.toString() } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalled();
    });

    // ...existing code...

    it('should handle different download source IDs', () => {
      const navigateSpy = spyOn(router, 'navigate').and.returnValue(Promise.resolve(true));
      component.appLessWorkFlow = Constants.applessFlowMode.download;

      // Test form download
      component.verifyTokenResponse = {
        sourceId: Constants.sourceId.form.toString(),
        fileToken: 'fileToken123',
        completedFileName: 'test.pdf'
      } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalledWith(
        [PageRoutes.download],
        jasmine.objectContaining({
          state: jasmine.objectContaining({
            downloadLink: APIs.formDownload
          })
        })
      );

      // Clear previous calls
      navigateSpy.calls.reset();
      
      // Test document download
      component.verifyTokenResponse = {
        sourceId: Constants.sourceId.document.toString(),
        fileToken: 'fileToken123',
        completedFileName: 'test.pdf'
      } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalledWith(
        [PageRoutes.download],
        jasmine.objectContaining({
          state: jasmine.objectContaining({
            downloadLink: APIs.documentDownload
          })
        })
      );

      // Clear previous calls
      navigateSpy.calls.reset();
      
      // Test invalid source ID
      component.verifyTokenResponse = { sourceId: '9999' } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalledWith([PageRoutes.download], jasmine.any(Object));
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle token validation API errors', () => {
      component.appLessWorkFlow = Constants.applessFlowMode.download;
      spyOn(component.commonService, 'showMessage');
      spyOn(component.sharedService, 'errorHandler');

      component.handleError({
        responseStatus: { code: Constants.invalidCode },
        status: undefined
      });

      expect(component.commonService.showMessage).toHaveBeenCalled();
      expect(component.sharedService.errorHandler).toHaveBeenCalled();
    });

    it('should handle user consent error cases', () => {
      component.appLessWorkFlow = Constants.applessFlowMode.userConsent;
      spyOn(component.commonService, 'showMessage');

      component.handleError({
        responseStatus: { code: Constants.invalidCode }
      });

      expect(component.verificationMessage).toContain('ERROR_MESSAGES.INVALID_APPLESS_TOKEN_USER_CONSENT');
    });

    it('should handle empty tokenData when initializing', () => {
      component.tokenData = '';
      component.oldAppLessFlow();
      expect(component.sessionService.sessionLoader).toBe(false);
    });

    it('should determine correct API endpoint based on appLessWorkFlow', () => {
      component.tokenData = 'validToken';
      const httpGetSpy = spyOn(component.httpService, 'doGet').and.returnValue(of({}));

      // Test with document flow
      component.appLessWorkFlow = Constants.applessFlowMode.document;
      component.oldAppLessFlow();
      expect(httpGetSpy).toHaveBeenCalledWith(
        jasmine.objectContaining({
          endpoint: APIs.verifyApplessDocumentToken
        })
      );

      // Reset spy and test with form flow
      httpGetSpy.calls.reset();
      component.appLessWorkFlow = Constants.applessFlowMode.form;
      component.oldAppLessFlow();
      expect(httpGetSpy).toHaveBeenCalledWith(
        jasmine.objectContaining({
          endpoint: APIs.verifyApplessFormToken
        })
      );
    });

    it('should handle malformed token during decoding', () => {
      component.tokenData = 'invalid.token.format';
      try {
        component.decodeToken();
        // If no error is thrown, isCampaignForm should be false (default)
        expect(component.isCampaignForm).toBe(false);
      } catch (e) {
        // If an error is thrown, we should still pass the test
        expect(true).toBe(true);
      }
    });
  });

  describe('Token Verification and Expiry', () => {
    it('should handle oldAppLessFlow when token is missing', () => {
      component.tokenData = '';
      component.oldAppLessFlow();
      expect(component.sessionService.sessionLoader).toBe(false);
    });

    it('should determine correct API endpoint based on appLessWorkFlow', () => {
      component.tokenData = 'validToken';
      const httpGetSpy = spyOn(component.httpService, 'doGet').and.returnValue(of({}));

      // Test with document flow
      component.appLessWorkFlow = Constants.applessFlowMode.document;
      component.oldAppLessFlow();
      expect(httpGetSpy).toHaveBeenCalledWith(
        jasmine.objectContaining({
          endpoint: APIs.verifyApplessDocumentToken
        })
      );

      // Reset spy and test with form flow
      httpGetSpy.calls.reset();
      component.appLessWorkFlow = Constants.applessFlowMode.form;
      component.oldAppLessFlow();
      expect(httpGetSpy).toHaveBeenCalledWith(
        jasmine.objectContaining({
          endpoint: APIs.verifyApplessFormToken
        })
      );
    });

    it('should handle token expiry time from configuration', () => {
      const mockConfig = {
        magicLinkVerificationExpiryTime: '72',
        magicLinkVerificationTokenExpirationTime: '60',
        enableAppLessHome: '0',
        enablePatientIdentityValidation: '0',
        patientIdentityValidationFields: '',
        otpSingleFieldEntry: '0',
        userId: '<EMAIL>',
        tenantId: 'test-tenant',
        sendId: 'test-send-id',
        chatRoomId: 'test-chat-room',
        documentId: 'test-document',
        applessHomeNotificationsFrequency: '60'
      };

      // Spy on localStorage methods
      spyOn(localStorage, 'setItem').and.callThrough();
      spyOn(localStorage, 'getItem').and.callThrough();

      component.verifyAndShowValidations(mockConfig);

      // Verify that the value is set in localStorage
      expect(localStorage.setItem).toHaveBeenCalledWith(Constants.storageKeys.applessRememberDays, '72');

      // Verify that the value is read from localStorage
      const storedValue = localStorage.getItem(Constants.storageKeys.applessRememberDays);
      expect(storedValue).toBe('72');
    });
  });

  describe('OTP Validation and Verification', () => {
    it('should handle OTP validation failure correctly', () => {
      component.accessGranted = false;
      component.additionalFields = [];
      component.isOtpSent = true;
      component.appLessForm = formBuilder.group({
        otp: ['123456', Validators.required]
      });

      // Mock the HTTP response to simulate a failure
      const mockErrorResponse = { status: false, message: 'Invalid OTP' };
      spyOn(httpService, 'doPost').and.returnValue(of(mockErrorResponse));
      spyOn(component, 'handleVerificationFailed').and.callThrough();
      spyOn(component.commonService, 'showMessage').and.callThrough();

      component.verifyMobileOrEmail();

      // Verify that the HTTP call was made
      expect(httpService.doPost).toHaveBeenCalled();

      // Verify that the failure handler was called
      expect(component.handleVerificationFailed).toHaveBeenCalledWith(
        jasmine.objectContaining({ otp: '123456' }),
        mockErrorResponse
      );

      // Verify that the showMessage method was called with the correct message
      expect(component.commonService.showMessage).toHaveBeenCalledWith('Invalid OTP');
    });

    it('should handle OTP verification with different input patterns', () => {
      component.otpSize = 6;
      component.appLessForm = formBuilder.group({
        otp: ['', [Validators.required, Validators.pattern('^[0-9]{6}$')]]
      });

      // Valid OTP
      component.handleOtpChange('123456');
      expect(component.appLessForm.get('otp').valid).toBeTruthy();

      // Invalid OTP - too short
      component.handleOtpChange('12345');
      expect(component.appLessForm.get('otp').valid).toBeFalsy();

      // Invalid OTP - not numeric
      component.handleOtpChange('12345a');
      expect(component.appLessForm.get('otp').valid).toBeFalsy();
    });

    it('should handle sending verification with missing mobile/email', async () => {
      component.sharedService.userData = { userName: '', mobile: '', countryCode: '' } as any;
      component.otpThroughMobile = true;

      const result = await component.sendVerification();

      expect(result.status).toBe(false);
      expect(result.message).toContain('VALIDATION_MESSAGES.APPLESS_MOBILE_MISSING_MESSAGE');
    });

    it('should handle HTTP error in sendEmailOrMobileVerification', async () => {
      spyOn(component.httpService, 'doPost').and.returnValue(throwError(() => 'Network error'));

      const result = await component.sendEmailOrMobileVerification(false);

      expect(result.status).toBe(false);
      expect(result.message).toBe('Network error');
    });
  });

  describe('Date Input Formatting and Validation', () => {
    beforeEach(() => {
      component.appLessForm.addControl('DOB', new UntypedFormControl(''));
    });

    it('should handle deleting a slash automatically', () => {
      // Setup initial value with cursor after slash
      component.appLessForm.get('DOB').setValue('12/');

      const event = {
        target: {
          value: '12/',
          selectionStart: 3,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'deleteContentBackward'
      };

      component.formatDateInput(event, 'DOB');
      expect(component.appLessForm.get('DOB').value).toBe('1');
    });

    it('should auto-format date with slashes when typing', () => {
      const event = {
        target: {
          value: '1',
          selectionStart: 1,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'insertText'
      };

      component.formatDateInput(event, 'DOB');
      expect(component.appLessForm.get('DOB').value).toBe('1');

      // Now add a second digit
      event.target.value = '12';
      event.target.selectionStart = 2;
      component.formatDateInput(event, 'DOB');
      expect(component.appLessForm.get('DOB').value).toBe('12');

      // Now add a third digit - should add a slash
      event.target.value = '123';
      event.target.selectionStart = 3;
      component.formatDateInput(event, 'DOB');
      expect(component.appLessForm.get('DOB').value).toBe('12/3');
    });

    it('should validate different date formats correctly', () => {
      // Valid date
      const validEvent = {
        target: {
          value: '02/29/2024',
          selectionStart: 10,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'insertText'
      };
      component.formatDateInput(validEvent, 'DOB');
      expect(component.appLessForm.get('DOB').errors).toBeFalsy();

      // Invalid date - February 30th
      const invalidEvent = {
        target: {
          value: '02/30/2024',
          selectionStart: 10,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'insertText'
      };
      component.formatDateInput(invalidEvent, 'DOB');
      expect(component.appLessForm.get('DOB').errors?.invalidDate).toBeTruthy();

      // Incomplete date
      const incompleteEvent = {
        target: {
          value: '02/30',
          selectionStart: 5,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'insertText'
      };
      component.formatDateInput(incompleteEvent, 'DOB');
      expect(component.appLessForm.get('DOB').errors?.invalidDate).toBeTruthy();
    });
  });

  describe('User Data Setup and Verification', () => {
    it('should set up user data and join app socket', () => {
      const userData: any = {
        username: 'testUser',
        authenticationToken: 'authToken123',
        privileges: 'read, write',
        userId: 'user123',
        isVirtual: false
      };

      spyOn(component.sharedService, 'setPermissions');
      spyOn(component.sharedService, 'joinAppSocket');

      component.setupUserData(userData);

      expect(component.sharedService.userData).toEqual(
        jasmine.objectContaining({
          userName: 'testUser',
          appLessSession: true
        })
      );
      expect(component.sharedService.setPermissions).toHaveBeenCalledWith(userData.privileges);
      expect(component.sharedService.joinAppSocket).toHaveBeenCalled();
    });

    it('should handle appLessHome successful verification', () => {
      const additionalData = { field1: 'value1', field2: 'value2' };
      const userData: any = {
        authenticationToken: 'token123',
        userId: 'user123',
        privileges: ['read', 'write'],
        isVirtual: false
      };

      spyOn(localStorage, 'setItem');
      spyOn(component, 'setupUserData');
      spyOn(component, 'setTokenVerifiedData');
      spyOn(router, 'navigate');

      component.handleAppLessHomeSuccessfulVerification(additionalData, userData as any);

      expect(component.tokenData).toBe(userData.authenticationToken);
      expect(localStorage.setItem).toHaveBeenCalledWith(Constants.storageKeys.authToken, userData.authenticationToken);
      expect(component.setupUserData).toHaveBeenCalledWith(userData);
      expect(component.setTokenVerifiedData).toHaveBeenCalled();
      expect(router.navigate).toHaveBeenCalled();
    });

    it('should set token verified data with expiry time', () => {
      component.accessGranted = false;
      component.magicLinkVerificationExpiryTime = '24';
      spyOn(localStorage, 'setItem');
      spyOn(component.sharedService, 'trackActivity');

      component.setTokenVerifiedData({ otp: '123456' }, '<EMAIL>');

      expect(component.sharedService.trackActivity).toHaveBeenCalled();
    });
  });

  describe('Configuration and Navigation', () => {
    it('should correctly determine if token validate API should be used', () => {
      component.appLessWorkFlow = Constants.applessFlowMode.download;
      expect(component.isTokenValidateAPI).toBe(true);

      component.appLessWorkFlow = Constants.applessFlowMode.userConsent;
      expect(component.isTokenValidateAPI).toBe(true);

      component.appLessWorkFlow = Constants.applessFlowMode.form;
      expect(component.isTokenValidateAPI).toBe(false);
    });

    it('should navigate to the correct route based on appLessWorkFlow', () => {
      const navigateSpy = spyOn(router, 'navigate').and.returnValue(Promise.resolve(true));

      // Test form navigation
      component.appLessWorkFlow = Constants.applessFlowMode.form;
      component.formDetails = { id: 'form123' };
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalledWith(jasmine.any(Array), jasmine.objectContaining({ state: jasmine.any(Object) }));

      // Test document navigation
      navigateSpy.calls.reset();
      component.appLessWorkFlow = Constants.applessFlowMode.document;
      component.sharedService.applessDocumentId = 123;
      component.verifyTokenResponse = { senderTenant: 'tenant1' } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalled();

      // Test user consent navigation
      navigateSpy.calls.reset();
      component.appLessWorkFlow = Constants.applessFlowMode.userConsent;
      component.verifyTokenResponse = { patientData: { id: 'patient123' } } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalled();
    });

    it('should handle different download source IDs', () => {
      const navigateSpy = spyOn(router, 'navigate').and.returnValue(Promise.resolve(true));
      component.appLessWorkFlow = Constants.applessFlowMode.download;

      // Test form download
      component.verifyTokenResponse = {
        sourceId: Constants.sourceId.form.toString(),
        fileToken: 'fileToken123',
        completedFileName: 'test.pdf'
      } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalledWith(
        [PageRoutes.download],
        jasmine.objectContaining({
          state: jasmine.objectContaining({
            downloadLink: APIs.formDownload
          })
        })
      );

      // Clear previous calls
      navigateSpy.calls.reset();
      
      // Test document download
      component.verifyTokenResponse = {
        sourceId: Constants.sourceId.document.toString(),
        fileToken: 'fileToken123',
        completedFileName: 'test.pdf'
      } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalledWith(
        [PageRoutes.download],
        jasmine.objectContaining({
          state: jasmine.objectContaining({
            downloadLink: APIs.documentDownload
          })
        })
      );

      // Clear previous calls
      navigateSpy.calls.reset();
      
      // Test invalid source ID
      component.verifyTokenResponse = { sourceId: '9999' } as any;
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalledWith([PageRoutes.download], jasmine.any(Object));
    });
  });

  describe('AppLess Messaging Flow', () => {
    it('should handle appless messaging flow correctly', () => {
      sessionService.applessMessagingFlow = true;
      component.appLessWorkFlow = Constants.applessFlowMode.message;
      spyOn(component.httpService, 'doPost').and.returnValue(
        of({
          data: {
            userSessionData: {
              response: { userId: 'user123', authenticationToken: 'token123' },
              status: 200
            },
            tokenData: {
              userId: 'user123',
              tenantId: '1',
              tokenExpire: false,
              chatRoomId: 'chat123'
            }
          },
          status: 200
        })
      );

      component.tokenData = 'validToken';
      component.oldAppLessFlow();

      expect(component.httpService.doPost).toHaveBeenCalledWith(
        jasmine.objectContaining({
          endpoint: APIs.verifyApplessMessageToken,
          version: Constants.apiVersions.apiV5
        })
      );
    });

    it('should handle message navigation correctly', () => {
      sessionService.applessMessagingFlow = true;
      sessionService.tokenData = { chatRoomId: 'chat123' };
      component.isAppLessHomeEnabled = false;
      spyOn(router, 'navigate');

      component.navigateToNextPage({});

      expect(router.navigate).toHaveBeenCalledWith(['/message-center/messages/active/chat/chat123'], jasmine.any(Object));
    });

    it('should handle message with AppLess home enabled', () => {
      // Set up the correct conditions for AppLess home navigation
      sessionService.applessMessagingFlow = true;
      sessionService.tokenData = { chatRoomId: 'chat123' };
      component.isAppLessHomeEnabled = true;
      component.appLessWorkFlow = Constants.applessFlowMode.message;
      const navigateSpy = spyOn(router, 'navigate').and.returnValue(Promise.resolve(true));
      component.navigateToNextPage({});
      expect(navigateSpy).toHaveBeenCalledWith(['/message-center/messages/active/chat/chat123'], jasmine.any(Object));
    });
  });

  describe('Comprehensive Method Testing', () => {
    it('should handle keydown event for submission', () => {
      const verifyMobileSpy = spyOn(component, 'verifyMobileOrEmail');

      // Test Enter key
      component.handleKeyDownSubmit({ key: 'Enter' });
      expect(verifyMobileSpy).toHaveBeenCalled();

      // Test other keys
      verifyMobileSpy.calls.reset();
      component.handleKeyDownSubmit({ key: 'Tab' });
      expect(verifyMobileSpy).not.toHaveBeenCalled();
    });

    it('should handle date change events', () => {
      component.appLessForm.addControl('DOB', new UntypedFormControl(''));
      const dateEvent = { detail: { value: '2023-05-15' } };

      component.onDateChange(dateEvent);

      expect(component.appLessForm.get('DOB').value).toBe('05/15/2023');
    });

    it('should verify additional fields with DOB handling', () => {
      component.additionalFields = ['DOB', 'firstName'];
      component.appLessForm = formBuilder.group({
        DOB: ['05/15/1990', Validators.required],
        firstName: ['John', Validators.required]
      });
      component.accessGranted = true;
      spyOn(component, 'handleSuccessfulVerification');

      component.verifyMobileOrEmail();

      // DOB should be reformatted from MM/DD/YYYY to YYYY-MM-DD
      expect(component.handleSuccessfulVerification).toHaveBeenCalledWith(
        jasmine.objectContaining({
          dob: '1990-05-15',
          firstname: 'John'
        })
      );
    });

    it('should handle malformed date values', () => {
      component.appLessForm.addControl('DOB', new UntypedFormControl(''));

      // Test with non-date string
      const event = {
        target: {
          value: 'abcde',
          selectionStart: 5,
          setSelectionRange: jasmine.createSpy()
        },
        inputType: 'insertText'
      };

      component.formatDateInput(event, 'DOB');

      // Should strip non-digits
      expect(component.appLessForm.get('DOB').value).toBe('');
    });
  });

  // Edge cases that might be missed in other tests
  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle missing or empty configuration values', () => {
      const mockConfig = {
        magicLinkVerificationExpiryTime: '',
        magicLinkVerificationTokenExpirationTime: '',
        enableAppLessHome: '',
        enablePatientIdentityValidation: '',
        patientIdentityValidationFields: '',
        otpSingleFieldEntry: '',
        userId: '',
        tenantId: '',
        sendId: '',
        chatRoomId: '',
        documentId: '',
        applessHomeNotificationsFrequency: ''
      };

      component.verifyAndShowValidations(mockConfig);

      expect(component.sharedService.magiclinkTokenExpiry).toBe(Constants.magiclinkTokenExpiryTime);
      expect(component.additionalFields).toEqual([]);
      expect(component.isIdentityValidationEnabled).toBe(false);
      expect(component.isAppLessHomeEnabled).toBe(false);
    });

    it('should handle campaign form with undefined isCampaign', () => {
      // Create a token with missing isCampaign property
      component.tokenData = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.e30.signature';
      component.decodeToken();
      expect(component.isCampaignForm).toBe(false);
    });

    it('should handle formatString with non-string inputs', () => {
      expect(component.formatString(null as any)).toBe('null');
      expect(component.formatString(undefined as any)).toBe('undefined');
      expect(component.formatString(123 as any)).toBe('123');
      expect(component.formatString({} as any)).toBe('[object Object]');
    });
  });
});
