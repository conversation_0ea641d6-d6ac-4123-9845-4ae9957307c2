/* eslint-disable dot-notation */
import { Component, On<PERSON>ni<PERSON>, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute, NavigationExtras } from '@angular/router';
import { forkJoin, Observable, Subscription, of } from 'rxjs';
import { map, finalize, catchError, timeout } from 'rxjs/operators';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { PageRoutes } from 'src/app/constants/page-routes';
import { SessionService } from 'src/app/services/session-service/session.service';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { APIs } from 'src/app/constants/apis';
import { FormWorkListPayload } from 'src/app/interfaces/common-interface';
import * as moment from 'moment';
import { Config } from 'src/app/constants/config';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Permissions } from 'src/app/constants/permissions';
import { HttpService } from 'src/app/services/http-service/http.service';
import { Location } from '@angular/common';
import { MessageInboxPayload } from 'src/app/interfaces/messages';
import { Signature } from 'src/app/constants/signature';
import { GraphqlService } from 'src/app/services/graphql-service/graphql.service';
import { formatDate, isPresent } from 'src/app/utils/utils';
import { FormService } from '../../form-center/services/form.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { UnicodeConvertPipe } from 'src/app/pipes/unicodeConvert/unicode-convert.pipe';
import { environment } from 'src/environments/environment';

interface ListItem {
  id: number;
  name?: string;
  content?: string;
  sentDate: Date;
  form?: any;
  chatroomId?: any;
  itemType?: string;
}
interface CardConfig {
  type: string;
  title: string;
  icon: string;
  btnClass: string;
  data: ListItem[];
  expanded: boolean;
  loading: boolean;
  hidden?: boolean;
}

// First, update the interface in SharedService or create a local interface extension
interface AppLessHomeData {
  message: ListItem[];
  downloadItems: ListItem[];
  pendingItems: ListItem[];
}

@Component({
  selector: 'app-appless-home',
  templateUrl: './appless-home.component.html',
  styleUrls: ['./appless-home.component.scss'],
  providers: [UnicodeConvertPipe]
})
export class ApplessHomeComponent implements OnInit, OnDestroy {
  pendingForms: any[] = [];
  pendingDocuments: any[] = [];
  messages: any[] = [];
  dateRange = {
    from: '',
    to: ''
  };
  selectedDateOptions = Constants.filterSelectedOptions.lastMonth;
  messageTypes = Constants.messageListTypes;
  private readonly subscriptions = new Subscription();

  showPendingForms = false;
  showDownloadForms = false;

  cards: CardConfig[] = this.initializeCards();

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    public sharedService: SharedService,
    public sessionService: SessionService,
    public readonly persistentService: PersistentService,
    private readonly permissionService: PermissionService,
    private readonly httpService: HttpService,
    private readonly location: Location,
    private readonly graphqlService: GraphqlService,
    private readonly formService: FormService,
    public readonly common: CommonService
  ) {
    this.sessionService.sessionLoader = false;
  }

  private initializeCards(): CardConfig[] {
    const cardConfigs = [
      { type: 'pendingItems', title: 'LABELS.PENDING_ITEMS', icon: 'form-center.png', btnClass: 'hp-btn2' },
      { type: 'downloadItems', title: 'LABELS.DOWNLOAD_ITEMS', icon: 'document-center.png', btnClass: 'hp-btn4' },
      { type: 'message', title: 'TITLES.MESSAGES', icon: 'message-center.png', btnClass: 'hp-btn4' }
    ];

    return cardConfigs.map((config) => ({
      type: config.type,
      title: config.title,
      icon: `../../assets/icon/home/<USER>
      btnClass: config.btnClass,
      data: [],
      expanded: true,
      loading: true
    }));
  }

  ngOnInit() {
    if (!this.sharedService.appLessHomeData) {
      this.sharedService.appLessHomeData = {
        pendingItems: [],
        downloadItems: [],
        message: []
      };

      (this.sharedService.appLessHomeData as AppLessHomeData).downloadItems = [];
      (this.sharedService.appLessHomeData as AppLessHomeData).pendingItems = [];
    }

    this.checkForApplessSubmissionSuccess();

    this.checkForDirectNavigation();

    this.initializeData();

    setTimeout(() => {
      this.initializeDownloadItems();
    }, 2000);
  }

  initializeData() {
    if (!this.sharedService.userData) {
      sessionStorage.removeItem(Constants.storageKeys.appLessHomeLoggedIn);
      this.location.back();
      return;
    }
    this.cards.forEach((card) => this.loadData(card));
    this.setupPollingSubscriptions();
    this.subscriptions.add(
      this.sharedService.appLessHomeNext.subscribe((data: { id: number; type: string; message: string }) => {
        if (!data.id) return;
        this.sharedService.appLessHomeNext.next({ id: 0, type: '', message: '' });
        const card = this.cards.find((c) => c.type === data.type);
        if (card) {
          card.loading = true;
          this.loadData(card);
        }

        if (data.type === 'form' || data.type === 'document') {
          const pendingItemsCard = this.cards.find((c) => c.type === 'pendingItems');
          if (pendingItemsCard) {
            pendingItemsCard.loading = true;
            this.loadData(pendingItemsCard);
          }
        }

        setTimeout(() => {
          let isAlertShown = false;
          let totalPendingItems = 0;

          let pendingForms = 0;
          let pendingDocuments = 0;

          const pendingItemsData = this.sharedService.appLessHomeData?.['pendingItems'];
          if (pendingItemsData) {
            const pendingItems = pendingItemsData.filter((item: ListItem) => parseInt(item.id.toString(), 10) !== data.id);

            const formItems = pendingItems.filter((item: ListItem) => item.itemType === 'form');
            const documentItems = pendingItems.filter((item: ListItem) => item.itemType === 'document');

            pendingForms = formItems.length;
            pendingDocuments = documentItems.length;
            totalPendingItems = pendingItems.length;
          }

          if (totalPendingItems > 0) {
            const firstPendingType = 'pendingItems';
            let firstPendingItem = null;

            if (pendingItemsData) {
              const pendingItems = pendingItemsData.filter((item: ListItem) => parseInt(item.id.toString(), 10) !== data.id);
              if (pendingItems.length > 0) {
                [firstPendingItem] = pendingItems;
              }
            }

            const title = this.common.getTranslateData('MESSAGES.SUBMISSION_IS_SAVED');
            let itemsText = '';

            if (pendingForms > 0 && pendingDocuments > 0) {
              itemsText = `${pendingForms} more form(s) and ${pendingDocuments} more document(s)`;
            } else if (pendingForms > 0) {
              itemsText = `${pendingForms} more form(s)`;
            } else if (pendingDocuments > 0) {
              itemsText = `${pendingDocuments} more document(s)`;
            } else {
              itemsText = `${totalPendingItems} ${this.common.getTranslateData('MESSAGES.MORE')}`;
            }

            const message = this.common.getTranslateDataWithParam('MESSAGES.NEXT_FORM_OR_HOME', {
              more: itemsText
            });

            const buttons = [
              {
                text: this.common.getTranslateData('BUTTONS.CANCEL'),
                class: 'warn-btn alert-cancel'
              },
              {
                text: this.common.getTranslateData('BUTTONS.NEXT'),
                class: 'warn-btn alert-ok'
              }
            ];
            isAlertShown = true;
            this.common
              .showAlert({
                message,
                header: title,
                buttons,
                cssClass: 'show-button-center',
                mode: 'ios'
              })
              .then((confirmation) => {
                if (confirmation && firstPendingItem) {
                  this.navigateToDetail(firstPendingType, firstPendingItem);
                }
              });
          }

          this.showFeedbackMessage(isAlertShown, data.message);
        }, 1000);
      })
    );

    const downloadItemCard = this.cards.find((card) => card.type === 'downloadItems');
    if (downloadItemCard) {
      this.loadData(downloadItemCard);
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  get visibleCards(): CardConfig[] {
    return this.cards;
  }

  shouldShowItemIcon(cardType: string): boolean {
    return cardType === 'downloadItems' || cardType === 'pendingItems';
  }

  getItemIconClass(itemType: string): string {
    return itemType === 'downloadForm' || itemType === 'form' ? 'item-type-icon form-icon-container' : 'item-type-icon document-icon-container';
  }

  getItemIconSrc(itemType: string): string {
    return itemType === 'downloadForm' || itemType === 'form' ? 'assets/icon/home/<USER>' : 'assets/icon/home/<USER>';
  }

  getItemIconAlt(itemType: string): string {
    return itemType === 'downloadForm' || itemType === 'form' ? 'Form' : 'Document';
  }

  private checkForApplessSubmissionSuccess(): void {
    const navigation = this.router.getCurrentNavigation();
    const applessSubmissionSuccess = navigation?.extras?.state?.applessSubmissionSuccess;

    if (applessSubmissionSuccess && applessSubmissionSuccess.id) {
      setTimeout(() => {
        this.handleApplessSubmissionSuccess(applessSubmissionSuccess);
      }, 1000);
    }
  }

  private checkForDirectNavigation(): void {
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state;

    if (state?.directFormNavigation) {
      setTimeout(() => {
        this.handleDirectFormNavigation(state.directFormNavigation);
      }, 1000);
    } else if (state?.directDocumentNavigation) {
      setTimeout(() => {
        this.handleDirectDocumentNavigation(state.directDocumentNavigation);
      }, 1000);
    }
  }

  private handleDirectFormNavigation(formNavData: { sendId: string; formRequestPayload: FormWorkListPayload }): void {
    this.httpService
      .doGet({
        endpoint: APIs.getMyFormWorklist,
        extraParams: formNavData.formRequestPayload,
        loader: true
      })
      .pipe(
        map((response) => {
          let responseData = response;
          if (response && response.response) {
            responseData = response.response;
          }
          if (!Array.isArray(responseData)) {
            responseData = responseData?.data || responseData?.content || responseData?.forms || [];
          }
          const filteredForm = Array.isArray(responseData) ? responseData.find((item) => String(item.sent_id) === String(formNavData.sendId)) : null;
          if (filteredForm) {
            const formResponse = {
              response: [filteredForm]
            };
            const form = this.formService.generalizeResponse(formResponse, Constants.formTypes.pending, Constants.formPendingStatus)[0];
            form.interactionChannel = Constants.appless;
            const navigationExtras: NavigationExtras = {
              state: {
                viewData: { form },
                formStatus: Constants.formPendingStatus
              },
              queryParams: { id: form.id }
            };
            this.router.navigate([`.${PageRoutes.viewForms}`], navigationExtras);
          } else {
            this.common.showMessage(this.common.getTranslateData('MESSAGES.FORM_NOT_FOUND'));
          }
        }),
        catchError(() => {
          this.common.showMessage(this.common.getTranslateData('MESSAGES.DOWNLOAD_ERROR'));
          return of(null);
        })
      )
      .subscribe();
  }

  private handleDirectDocumentNavigation(docNavData: { documentId: number; tenantId: number }): void {
    this.router.navigate([`document-center/view-document/${docNavData.documentId}/${docNavData.tenantId}`], {
      skipLocationChange: true
    });
  }

  private handleApplessSubmissionSuccess(data: { id: number; type: string; message: string }): void {
    const card = this.cards.find((c) => c.type === data.type);
    if (card) {
      card.loading = true;
      this.loadData(card);
    }

    if (data.type === 'form' || data.type === 'document') {
      const pendingItemsCard = this.cards.find((c) => c.type === 'pendingItems');
      if (pendingItemsCard) {
        pendingItemsCard.loading = true;
        this.loadData(pendingItemsCard);
      }
    }

    setTimeout(() => {
      this.showFeedbackMessage(false, data.message);
    }, 1000);
  }

  getSenderName(cardType: string, item: any): string {
    switch (cardType) {
      case 'downloadItems':
        return item.itemType === 'downloadForm' || item.itemType === 'downloadDocument' ? item.initiatorName : '';
      case 'pendingItems':
        return item.itemType === 'form' || item.itemType === 'document'
          ? item.form?.createdUser || item.initiatorName || ''
          : item.initiatorName || '';
      default:
        return '';
    }
  }

  isCombinedCard(cardType: string): boolean {
    return ['downloadItems', 'pendingItems'].includes(cardType);
  }

  shouldShowDownloadButton(cardType: string, itemType?: string): boolean {
    return (
      cardType === 'downloadForm' ||
      cardType === 'downloadDocument' ||
      (cardType === 'downloadItems' && ['downloadForm', 'downloadDocument'].includes(itemType))
    );
  }

  handleDownload(event: Event, cardType: string, item: any): void {
    const isFormDownload = cardType === 'downloadForm' || (cardType === 'downloadItems' && item.itemType === 'downloadForm');

    if (isFormDownload) {
      this.downloadForm(event, item);
    } else {
      this.downloadDocument(event, item);
    }
  }

  getDownloadAriaLabel(cardType: string, itemType?: string): string {
    const isFormDownload = cardType === 'downloadForm' || (cardType === 'downloadItems' && itemType === 'downloadForm');

    return isFormDownload ? 'BUTTONS.DOWNLOAD_FORMS' : 'BUTTONS.DOWNLOAD_DOCUMENT';
  }

  getFormattedDate(cardType: string, item: any): number {
    if (cardType === 'message') {
      return +item.messageStatus === 1 ? item.messageOrder * 1000 : item.messageDeletedTime;
    }
    return item.sentDate ? item.sentDate * 1000 : item.sentDate;
  }

  private getCommonApiParams() {
    return {
      startDate: '',
      endDate: '',
      ...this.sharedService.getFilterDateRange(this.selectedDateOptions, this.dateRange)
    };
  }

  private mapMessageResponse(response: any): any[] {
    return response.message.map((message) => ({
      ...message,
      chatroomId: message.chatroomId
    }));
  }

  private setupPollingSubscriptions() {
    this.sharedService.enableFormPolling();
    this.sharedService.enableDocumentPolling();

    this.subscriptions.add(
      this.sharedService.messageFormCountUpdated.subscribe((pollingData) => {
        if (pollingData.countType === Constants.countTypes.forms && pollingData.isPolling) {
          this.refreshCardsByType(['form', 'downloadForm', 'pendingItems']);

          setTimeout(() => {
            const downloadItemCard = this.cards.find((c) => c.type === 'downloadItems');
            if (downloadItemCard) {
              this.refreshDownloadItemsFromExistingData();
            }
          }, 200);
        }
      })
    );

    this.subscriptions.add(
      this.sharedService.documentCountUpdated.subscribe(() => {
        this.refreshDocumentCards();
      })
    );

    this.subscriptions.add(
      this.sharedService.documentPollingEvent.subscribe((data: any) => {
        if (data?.[0]?.notifyOnSubmit) {
          this.refreshDocumentCards();
        }
      })
    );
  }

  private refreshCardsByType(types: string[]): void {
    types.forEach((type) => {
      const cardToRefresh = this.cards.find((c) => c.type === type);
      if (cardToRefresh) {
        cardToRefresh.loading = true;
        this.loadData(cardToRefresh);
      }
    });

    if (types.includes('pendingItems')) {
      const pendingItemsCard = this.cards.find((card) => card.type === 'pendingItems');
      if (pendingItemsCard) {
        pendingItemsCard.loading = true;
        this.getPendingItemsData(pendingItemsCard);
      }
    }
  }

  private refreshDocumentCards(): void {
    this.refreshCardsByType(['document', 'completedDocument', 'pendingItems']);
  }

  private handleApiCall(apiCall: Observable<any>, card: CardConfig) {
    apiCall
      .pipe(
        finalize(() => {
          card.loading = false;
        })
      )
      .subscribe({
        next: (data) => {
          card.data = data;
          this.sharedService.appLessHomeData[card.type] = data;
          if (card.type === 'message') {
            this.sharedService.messageList = data;
            this.sortMessages();
          }
        },
        error: () => {
          card.data = [];
        }
      });
  }

  private getMessageData(card: CardConfig) {
    const messageReqBody: MessageInboxPayload = {
      archived: false,
      viewInventory: true,
      showChatHistory: true,
      pageCount: Constants.defaultPageCount,
      searchKeyword: '',
      flagValue: 0,
      priorityValue: 0,
      siteIds: [],
      isShowLoader: true,
      unread: false,
      filterTags: [],
      mentionUsers: false,
      selectedDateOptions: this.selectedDateOptions,
      dateRange: this.dateRange,
      chatThreadTypes: []
    };

    const apiCall = this.sharedService.fetchAllMessages(messageReqBody).pipe(map((response) => this.mapMessageResponse(response)));

    this.handleApiCall(apiCall, card);
    this.sharedService.enableMessageSocket();
    if (!this.sharedService.messageListUpdated.observed) {
      this.subscriptions.add(
        this.sharedService.messageListUpdated.subscribe((value: any) => {
          this.updateMessagesOnEvents(value);
        })
      );
    }
  }

  loadData(card: CardConfig): void {
    if (!card) return;

    switch (card.type) {
      case 'message':
        this.getMessageData(card);
        break;
      case 'pendingItems':
        this.getPendingItemsData(card);
        break;
      case 'downloadItems':
        this.getDownloadItemData(card);
        break;
      default:
        break;
    }
  }

  toggleSection(card: any): void {
    if (!card) {
      return;
    }
    card.expanded = !card.expanded;
  }

  navigateToDetail(type: string, item: any): void {
    this.sharedService.userData.appLessSession = true;
    const navigationExtras: NavigationExtras = {
      skipLocationChange: true
    };

    if (
      type === 'form' ||
      type === 'downloadForm' ||
      ((type === 'pendingItems' || type === 'downloadItems') && (item.itemType === 'form' || item.itemType === 'downloadForm'))
    ) {
      const { form } = item;
      form.form_submission_id = form.formSubmissionId;
      form.interactionChannel = Constants.appless; // Set to appless for proper flow
      navigationExtras.state = {
        viewData: { form },
        formStatus: Constants.formPendingStatus,
        isEditable: type === 'downloadForm' || (type === 'downloadItems' && item.itemType === 'downloadForm')
      };
      const formId = form.id || form.formId || form.form_id;
      if (formId) {
        navigationExtras.queryParams = { id: formId };
      }
      this.router.navigate([`.${PageRoutes.viewForms}`], navigationExtras);
    } else if (
      type === 'document' ||
      type === 'completedDocument' ||
      type === 'downloadDocument' ||
      ((type === 'pendingItems' || type === 'downloadItems') && (item.itemType === 'document' || item.itemType === 'downloadDocument'))
    ) {
      const documentInfo = {
        ...item,
        id: item.itemType === 'document' ? item.id : item.documentId,
        downloadUrl: item.downloadUrl,
        senderTenant: item.senderTenant ?? item.tenantDetails?.senderTenant ?? this.sharedService.userData?.tenantId,
        displayLabel: item.name ?? item.displayLabel ?? item.displayText?.text,
        signatureStatus:
          type === 'downloadDocument' ? Signature.signatureStatus.signatureSignedStatus : Signature.signatureStatus.signaturePendingStatus,
        enableApplessWorkflow: '0',
        integrationStatus: null,
        isEditable: type === 'downloadDocument' || (type === 'downloadItems' && item.itemType === 'downloadDocument')
      };

      const navigationExtras: NavigationExtras = {
        skipLocationChange: true,
        state: { documentInfo }
      };
      this.router.navigate([`/document-center/view-document/${documentInfo.id}/${documentInfo.senderTenant}`], navigationExtras);
    } else if (type === 'message') {
      this.sessionService.applessMessagingFlow = true;
      this.router.navigate([`/message-center/messages/active/chat/${item.chatroomId}`], {
        skipLocationChange: false
      });
    }
  }

  loadFilterData(value: { text: number; dates?: { from: string; to: string } }): void {
    if (value.text === Constants.filterSelectedOptions.custom && isPresent(value?.dates?.from) && isPresent(value?.dates?.to)) {
      this.dateRange = value.dates;
    }
    this.selectedDateOptions = value.text;
    this.persistentService.setPersistentData(Constants.storageKeys.dateRangeFilterArchivedMessages, value.dates);
    this.persistentService.setPersistentData(Constants.storageKeys.dateRangeSelectedDateOptionsArchivedMessages, value.text);
    this.cards.forEach((card) => this.loadData(card));
  }

  refreshCard(event: Event, card: CardConfig) {
    if (!card) {
      return;
    }
    event.stopPropagation();

    card.loading = true;

    if (card.type === 'downloadItems') {
      this.getDownloadItemData(card);
    } else {
      this.loadData(card);
    }
  }

  get messageList() {
    return this.cards.find((card) => card.type === 'message')?.data || [];
  }

  set messageList(value: any) {
    const card = this.cards.find((card) => card.type === 'message');
    if (card) {
      card.data = value;
    }
  }

  updateMessagesOnEvents(value: any): void {
    if (value?.chatRoomId) {
      this.updateLoadingStatus(value.chatRoomId);
    } else if (value.message ?? value.maskedParent) {
      this.handleMessageThread(value);
    }
    this.sharedService.appLessHomeData.message = this.messageList;
  }

  private updateLoadingStatus(chatRoomId: number): void {
    const index = this.messageList.findIndex((x) => +x.chatroomId === +chatRoomId);
    if (index !== -1) {
      this.messageList[index].loading = true;
    }
  }

  private handleMessageThread(value: any): void {
    const messageThread = value?.maskedParent || value?.message;
    const index = this.messageList.findIndex((x) => +x.chatroomId === +messageThread.chatroomId);
    let { incrementCount } = value;

    if (index === -1 && !value?.removeThread) {
      this.addNewMessageThread(messageThread);
      incrementCount = messageThread?.maskedSubCount > 0 ? messageThread?.maskedUnreadCount : messageThread.unreadCount;
    } else if (value?.removeThread) {
      this.removeMessageThread(index);
    } else {
      this.updateExistingMessageThread(index, messageThread);
    }

    if (isPresent(incrementCount)) {
      this.updateUnreadCount(incrementCount);
    }

    this.sortMessages();
  }

  private addNewMessageThread(messageThread: any): void {
    this.messageList ??= [];
    this.messageList.unshift(messageThread);
  }

  private removeMessageThread(index: number): void {
    this.messageList.splice(index, 1);
  }

  private updateExistingMessageThread(index: number, messageThread: any): void {
    Object.assign(this.messageList[index], messageThread);
  }

  private updateUnreadCount(incrementCount: number): void {
    this.sharedService.messageUnreadCount += incrementCount;
    if (this.sharedService.messageUnreadCount < 0) {
      this.sharedService.messageUnreadCount = 0;
    }
  }

  sortMessages() {
    this.messageList.sort((a, b) => {
      const sentComparison = Number(b.messageOrder) - Number(a.messageOrder);
      if (a.pinnedStatus && !b.pinnedStatus) {
        return -1;
      }
      if (!a.pinnedStatus && b.pinnedStatus) {
        return 1;
      }
      return sentComparison;
    });
  }

  getCardItems(type: string): any[] {
    if (!this.sharedService?.appLessHomeData) {
      return [];
    }
    return this.sharedService.appLessHomeData[type] ?? [];
  }

  private showFeedbackMessage(isAlertShown: boolean, message: string): void {
    if (!isAlertShown && message) {
      setTimeout(() => {
        this.common.showMessage(message);
      }, 0);
    }
  }

  downloadDocument(event: Event, item: any): void {
    event.stopPropagation();
    event.preventDefault();

    const downloadUrl = item.downloadUrl || (item.document && item.document.downloadUrl);
    if (!downloadUrl) {
      this.common.showMessage(this.common.getTranslateData('MESSAGES.NO_DOWNLOAD_URL'));
      return;
    }

    try {
      let fullDownloadUrl = downloadUrl;

      if (!downloadUrl.includes('.json')) {
        fullDownloadUrl = `${environment.DOCUMENT_DOWNLOAD_URL}/api/cmis_wrapper_service/content/public/file/download/${downloadUrl}.json?type=pdf`;
      } else if (!downloadUrl.includes('?type=pdf')) {
        fullDownloadUrl = `${downloadUrl}?type=pdf`;
      }

      window.open(fullDownloadUrl, '_blank');
    } catch (error) {
      this.common.showMessage(this.common.getTranslateData('MESSAGES.DOWNLOAD_ERROR'));
    }
  }

  downloadForm(event: Event, item: any): void {
    event.stopPropagation();
    event.preventDefault();
    const formId = item.formId || (item.form && item.form.formId);
    const formSubmissionId = item.formSubmissionId || (item.form && item.form.formSubmissionId);

    if (!formId || !formSubmissionId) {
      this.common.showMessage(this.common.getTranslateData('MESSAGES.NO_DOWNLOAD_URL'));
      return;
    }

    const tokenEndpoint = 'generate-structured-form-data-pdf.php';
    const payload = {
      formId,
      submissionId: formSubmissionId
    };

    const params = {
      type: 2,
      zone: moment.tz.guess()
    };

    this.httpService
      .doPost({
        endpoint: tokenEndpoint,
        payload,
        extraParams: params,
        loader: true,
        version: Constants.apiVersions.apiV4,
        responseType: 'text'
      })
      .subscribe({
        next: (token: string) => {
          if (token) {
            const downloadUrl = `${environment.apiBasePath}/${Constants.apiVersions.apiV4}/form-download.php?filetoken=${token}`;

            try {
              window.open(downloadUrl, '_blank');
            } catch {
              this.common.showMessage(this.common.getTranslateData('MESSAGES.DOWNLOAD_ERROR'));
            }
          } else {
            this.common.showMessage(this.common.getTranslateData('MESSAGES.NO_DOWNLOAD_URL'));
          }
        },
        error: () => {
          this.common.showMessage(this.common.getTranslateData('MESSAGES.DOWNLOAD_ERROR'));
        }
      });
  }

  private getPendingItemsData(card: CardConfig): void {
    card.loading = true;
    const formObservable = this.httpService
      .doGet({
        endpoint: APIs.getMyFormWorklist,
        extraParams: this.getFormRequestPayload(),
        loader: false
      })
      .pipe(
        map((response) => {
          const formattedResponse = this.formService.generalizeResponse(response, Constants.formTypes.pending, Constants.formPendingStatus);
          return formattedResponse.map((form) => ({
            id: form.id,
            name: form.form_name,
            initiatorName: form.sender || 'Unknown Sender',
            patientName: form.patientName || form.patientDisplayName,
            tenantName: form.tenantName,
            patientDob: form.patientDob,
            sentDate: form.sentDate,
            form,
            itemType: 'form'
          }));
        })
      );
    const params = {
      siteId: '0',
      tenantId: this.sharedService.userData?.tenantId,
      crossTenantId: this.sharedService.userData?.crossTenantId,
      signatureRequestFilterInput: {
        signatureStatus: Signature.signatureStatus.signaturePendingStatus,
        searchText: ''
      },
      ...this.sharedService.getFilterDateRange(this.selectedDateOptions, this.dateRange),
      paginationInput: {
        fetchCount: true,
        orderData: '',
        orderby: Constants.sortOrderAsc
      }
    };

    const documentObservable = this.graphqlService.getDocuments(params).pipe(
      map(({ data }) => {
        return data?.mySignatureRequest?.signatureRequest
          ?.filter((doc) => doc.signatureStatus === Signature.signatureStatus.signaturePendingStatus)
          .map((doc) => ({
            id: doc.id,
            name: doc.displayText.text,
            sentDate: doc.createdOn,
            initiatorName: doc.owner,
            downloadUrl: doc.downloadUrl,
            senderTenant: doc.senderTenant ?? doc.tenantDetails?.senderTenant,
            displayLabel: doc.displayText.text,
            itemType: 'document',
            ...doc
          }));
      })
    );
    forkJoin({
      forms: formObservable.pipe(timeout(30000)),
      documents: documentObservable.pipe(timeout(30000))
    })
      .pipe(
        finalize(() => {
          card.loading = false;
        })
      )
      .subscribe({
        next: (result) => {
          const formData = result.forms || [];
          const documentData = result.documents || [];

          const combinedData = [...formData, ...documentData].sort((a, b) => {
            const dateA = a.sentDate ? a.sentDate * 1000 : a.sentDate;
            const dateB = b.sentDate ? b.sentDate * 1000 : b.sentDate;
            return dateB - dateA;
          });

          card.data = combinedData;
          this.sharedService.appLessHomeData[card.type] = combinedData;
        },
        error: () => {
          card.data = [];
          this.sharedService.appLessHomeData[card.type] = [];
        }
      });
  }

  private getDownloadItemData(card: CardConfig): void {
    card.loading = true;
    const formCard = this.cards.find((c) => c.type === 'downloadForm');
    const documentCard = this.cards.find((c) => c.type === 'downloadDocument');

    const dateFilterParams =
      this.selectedDateOptions === 2
        ? {
            startDate: formatDate(this.dateRange.from, Constants.dateFormat.ymd), // Format as YYYY-MM-DD
            endDate: formatDate(this.dateRange.to, Constants.dateFormat.ymd) // Format as YYYY-MM-DD
          }
        : {};

    // Check if date range exceeds or is outside last 30 days
    if (
      this.selectedDateOptions === Constants.filterSelectedOptions.custom &&
      (card.type === 'downloadForm' || card.type === 'downloadDocument' || card.type === 'downloadItems')
    ) {
      const startDate = moment(this.dateRange.from, Constants.dateFormat.mdy);
      const thirtyDaysAgo = moment().subtract(30, 'days');

      if (startDate.isBefore(thirtyDaysAgo) || moment().diff(startDate, 'days') > 30) {
        this.common.showToast({
          message: this.common.getTranslateData('MESSAGES.DATE_RANGE_EXCEEDS_30_DAYS'),
          duration: 4000,
          color: 'warning',
          cssClass: 'barlow-regular'
        });
      }
    }

    const formsObservable = this.httpService
      .doPost({
        endpoint: APIs.getDownloadForms,
        payload: {
          rowsPerPage: 100,
          currentPage: 0,
          sortDirection: 'ASC',
          sortBy: 'submitted_date',
          ...dateFilterParams
        },
        loader: false,
        version: Constants.apiVersions.apiV5
      })
      .pipe(
        map((response: any) => {
          if (!response || !response.content) {
            return [];
          }

          if (formCard) {
            const formattedData = response.content.map((form: any, index: number) => ({
              id: index,
              formId: form.formId,
              formSubmissionId: form.formSubmissionId,
              name: form.formName,
              initiatorName: form.sender,
              sentId: form.sentId,
              sentDate: form.submittedDate,
              patientName: form.patientName,
              patientId: form.patientId,
              form: {
                formId: form.formId,
                formSubmissionId: form.formSubmissionId,
                initiatorName: form.sender,
                sentId: form.sentId,
                formName: form.formName,
                patientName: form.patientName,
                submittedDate: form.submittedDate
              }
            }));

            formCard.data = formattedData;
            formCard.loading = false;
          }

          return response.content.map((form: any, index: number) => ({
            id: index,
            formId: form.formId,
            formSubmissionId: form.formSubmissionId,
            name: form.formName,
            initiatorName: form.sender,
            sentId: form.sentId,
            sentDate: form.submittedDate,
            patientName: form.patientName,
            patientId: form.patientId,
            itemType: 'downloadForm',
            form: {
              formId: form.formId,
              formSubmissionId: form.formSubmissionId,
              initiatorName: form.sender,
              sentId: form.sentId,
              formName: form.formName,
              patientName: form.patientName,
              submittedDate: form.submittedDate
            }
          }));
        }),
        catchError((error) => {
          return of([]);
        })
      );

    const documentsObservable = this.httpService
      .doPost({
        endpoint: APIs.getDownloadDocuments,
        payload: {
          rowsPerPage: 300,
          currentPage: 0,
          sortDirection: 'DESC',
          sortBy: 'send_date',
          ...dateFilterParams
        },
        loader: false,
        version: Constants.apiVersions.apiV5
      })
      .pipe(
        map((response: any) => {
          if (!response || !response.content) {
            return [];
          }
          if (documentCard) {
            const formattedData = response.content.map((doc: any, index: number) => ({
              id: index,
              documentId: doc.documentId,
              senderTenant: doc.senderTenant,
              initiatorName: doc.sender,
              name: doc.documentName,
              sentDate: doc.sentDate,
              downloadUrl: doc.downloadUrl,
              document: {
                id: index,
                documentId: doc.documentId,
                initiatorName: doc.sender,
                senderTenant: doc.senderTenant,
                documentName: doc.documentName,
                sentDate: doc.sentDate,
                downloadUrl: doc.downloadUrl
              }
            }));

            documentCard.data = formattedData;
            documentCard.loading = false;
          }

          return response.content.map((doc: any, index: number) => ({
            id: index,
            documentId: doc.documentId,
            name: doc.documentName,
            sentDate: doc.sentDate,
            senderTenant: doc.senderTenant,
            initiatorName: doc.sender,
            downloadUrl: doc.downloadUrl,
            itemType: 'downloadDocument',
            document: {
              id: index,
              documentId: doc.documentId,
              senderTenant: doc.senderTenant,
              documentName: doc.documentName,
              initiatorName: doc.sender,
              sentDate: doc.sentDate,
              downloadUrl: doc.downloadUrl
            }
          }));
        }),
        catchError((error) => {
          return of([]);
        })
      );

    forkJoin({
      forms: formsObservable,
      documents: documentsObservable
    })
      .pipe(
        finalize(() => {
          card.loading = false;
        })
      )
      .subscribe({
        next: (result) => {
          const formData = result.forms || [];
          const documentData = result.documents || [];

          // Combine and sort by date DESC
          const combinedData = [...formData, ...documentData].sort((a, b) => {
            // Convert timestamps to milliseconds if needed
            const dateA = a.sentDate ? a.sentDate * 1000 : a.sentDate;
            const dateB = b.sentDate ? b.sentDate * 1000 : b.sentDate;
            return dateB - dateA;
          });

          card.data = combinedData;
          (this.sharedService.appLessHomeData as AppLessHomeData).downloadItems = combinedData;
        },
        error: (err) => {
          card.data = [];
          (this.sharedService.appLessHomeData as AppLessHomeData).downloadItems = [];
        }
      });
  }
  private combineExistingCardData(combinedCard: CardConfig, formCard?: CardConfig, documentCard?: CardConfig): void {
    const formData = formCard?.data || [];
    const documentData = documentCard?.data || [];

    const transformedFormData = formData.map((item) => ({
      ...item,
      itemType: formCard?.type === 'form' ? 'form' : 'downloadForm'
    }));

    // Transform document data to include itemType
    const transformedDocumentData = documentData.map((item) => ({
      ...item,
      itemType: 'downloadDocument'
    }));

    // Combine and sort by date DESC
    const combinedData = [...transformedFormData, ...transformedDocumentData];

    // Sort by date DESC - ensure consistent sorting across both data types
    combinedData.sort((a, b) => {
      const dateA = a.sentDate instanceof Date ? a.sentDate : new Date(a.sentDate);
      const dateB = b.sentDate instanceof Date ? b.sentDate : new Date(b.sentDate);
      return dateB.getTime() - dateA.getTime();
    });
    // Assign the data to the card
    combinedCard.data = combinedData;
    combinedCard.loading = false;
    (this.sharedService.appLessHomeData as AppLessHomeData).downloadItems = combinedData;
  }

  // Method to refresh download items using existing card data
  private refreshDownloadItemsFromExistingData(): void {
    const downloadItemCard = this.cards.find((c) => c.type === 'downloadItems');
    const formCard = this.cards.find((c) => c.type === 'downloadForm');
    const documentCard = this.cards.find((c) => c.type === 'downloadDocument');

    if (downloadItemCard) {
      this.combineExistingCardData(downloadItemCard, formCard, documentCard);
    }
  }

  // Add a method to initialize the download items card
  initializeDownloadItems(): void {
    const downloadItemCard = this.cards.find((card) => card.type === 'downloadItems');
    if (!downloadItemCard) {
      return;
    }

    // Load the data
    this.getDownloadItemData(downloadItemCard);
  }

  // Helper method to get form request payload
  private getFormRequestPayload(): FormWorkListPayload {
    const payload: FormWorkListPayload = {
      roleid: this.sharedService.userData.roleId,
      zone: moment.tz.guess(),
      isForms: true,
      isPrivilege:
        this.permissionService.userHasPermission(Permissions.viewFormEntries) || this.permissionService.userHasPermission(Permissions.manageTenants),
      limit: Constants.offset,
      offset: Constants.offset * Constants.defaultPageCount,
      searchText: '',
      orderData: Constants.orderData.sentOn,
      orderby: Constants.sortOrderDesc,
      isScheduled: Constants.noValue,
      archived: false,
      pending: true,
      completed: false,
      draft: false,
      accessSecurityEnabled: this.sharedService.userData.accessSecurityEnabled,
      ...this.getCommonApiParams(),
      citusRoleId: this.sharedService.userData.group || '',
      enableIntegrationStatus: false,
      enableSftpIntegration: false
    };

    if (this.sharedService.isEnableConfig(Config.enableNursingAgencies)) {
      payload.nursingAgencies = this.sharedService.userData.nursing_agencies;
    }

    return payload;
  }

  refreshAllCards(): void {
    this.refreshCardsByType(['message', 'pendingItems']);
    setTimeout(() => {
      this.refreshDownloadItemsFromExistingData();
    }, 300);
  }
}
