<app-header headerTitle="TITLES.APPLESS_HOME" [hideSocketButton]="true" [hideHelpButton]="true"></app-header>
<ion-content [fullscreen]="true" class="background-white">
  <ion-header collapse="condense" mode="ios">
    <ion-toolbar mode="ios"></ion-toolbar>
  </ion-header>

  <div class="appless-row">
    <div class="page-content">
      <app-search-bar
        (closeDateFilter)="loadFilterData($event)"
        [selectedDateOptions]="selectedDateOptions"
        [isDateFilter]="true"
        [showSearch]="false"
        [searchText]=""
        [dateRange]="dateRange"
      >
      </app-search-bar>
      <ion-card *ngFor="let card of visibleCards" mode="ios" [ngClass]="{ 'download-card': card.type === 'downloadItems' }">
        <ion-card-header [class]="card.btnClass" (click)="toggleSection(card)" (keydown)="toggleSection(card)" tabindex="0">
          <ion-card-title>
            <h2>{{ card.title | translate }}</h2>
            <ion-icon
              name="refresh-outline"
              (click)="refreshCard($event, card)"
              (keypress)="refreshCard($event, card)"
              aria-label="Refresh"
              class="ml-auto"
            ></ion-icon>
            <ion-icon [name]="card.expanded ? 'chevron-up-outline' : 'chevron-down-outline'"></ion-icon>
          </ion-card-title>
        </ion-card-header>
        <ion-card-content *ngIf="card.expanded" class="card-content">
          <ion-spinner *ngIf="card.loading" [attr.aria-label]="card.title | translate"></ion-spinner>

          <!-- Unified display for all card types -->
          <div *ngIf="!card.loading" class="scrollable">
            <ion-list>
              <ion-item
                *ngFor="let item of card.data; let i = index"
                class="message-display item-container hover-effect"
                [ngClass]="{ unread: card.type === 'message' && item.hasUnreadMessages && (item.maskedUnreadCount > 0 || item.unreadCount > 0) }"
                (click)="navigateToDetail(card.type, item)"
                (keydown)="navigateToDetail(card.type, item)"
                tabindex="0"
              >
                <div class="row inbox-message-details">
                  <div class="avatar" *ngIf="card.type === 'message'">
                    <div class="avatar-container">
                      <img
                        draggable="false"
                        appAvatar
                        #avatarDirective="avatarDirective"
                        [src]="avatarDirective.avatarSrc"
                        (error)="avatarDirective.onImageError($event)"
                        [avatar]="item.chatAvatar"
                        [messageCategory]="item.messageCategory"
                        alt=""
                        outOfOfficeStatus
                        [oooInfo]="item.oooInfo"
                        [customClass]="'chat-list-red-badge'"
                      />
                      <div class="unread-count" *ngIf="item.hasUnreadMessages && (item.maskedUnreadCount > 0 || item.unreadCount > 0)">
                        <span>{{ item.maskedSubCount > 0 ? item.maskedUnreadCount : item.unreadCount }}</span>
                      </div>
                      <ion-icon
                        id="expand-button"
                        *ngIf="item.maskedSubCount > 0"
                        class="masked-caret"
                        [name]="item.childExpanded ? 'chevron-up-outline' : 'chevron-down-outline'"
                      ></ion-icon>
                    </div>
                  </div>
                  <!-- Item type icon for form/document items - positioned like avatar -->
                  <div class="item-icon-avatar" *ngIf="card.type === 'pendingItems' || card.type === 'downloadItems'">
                    <div class="item-icon-container">
                      <div *ngIf="shouldShowItemIcon(card.type)" [ngClass]="getItemIconClass(item.itemType)">
                        <img [src]="getItemIconSrc(item.itemType)" [alt]="getItemIconAlt(item.itemType)" />
                      </div>
                    </div>
                  </div>
                  <div class="chat-details-middle wrap-ellipsis">
                    <!-- Handle form and document items -->
                    <div *ngIf="card.type === 'pendingItems' || card.type === 'downloadItems'" class="inbox-message-from wrap-ellipsis">
                      <div class="from-name wrap-ellipsis">
                        <div class="inbox-per-name wrap-ellipsis item-name-container">
                          <!-- Icon moved to avatar position, only text here now -->
                          <span class="item-name-text">{{ item.name }}</span>
                        </div>
                      </div>
                    </div>
                    <!-- Handle message items -->
                    <div *ngIf="card.type === 'message'" class="inbox-message-from wrap-ellipsis">
                      <div class="from-name wrap-ellipsis">
                        <div class="inbox-per-name wrap-ellipsis" *ngIf="item.chatHeading">{{ item.chatHeading }}</div>
                      </div>
                    </div>
                    <!-- Subheading: patient name for forms, chat subheading for messages -->
                    <!-- Message content for unread messages -->
                    <div *ngIf="card.type === 'message'; else elseBlock" class="message-preview">
                      <div class="message-preview-content">
                        <div
                          class="inbox-mes-name wrap-ellipsis"
                          *ngIf="item.chatSubject && (+item.messageType === +messageTypes.messageGroup || +item.messageType === +messageTypes.pdg)"
                        >
                          {{ 'LABELS.SUBJECT' | translate }}: {{ item.chatSubject }}
                        </div>
                        <!-- Self message indicator with proper spacing -->

                        <span *ngIf="+item.messageType !== +messageTypes.pdg" class="message-body compact">
                          <ion-icon name="chatbubble-outline" class="message-icon" size="small" aria-hidden="true"></ion-icon>
                          <span
                            [dirParseMessageToHtml]="item.message | unicodeConvert"
                            class="preview-text truncate-message"
                            [ngClass]="{ 'pending-status': +item.messageStatus === 0 }"
                          >
                          </span>
                        </span>
                      </div>
                    </div>
                    <ng-template #elseBlock>
                      <!-- Unified sender name display -->
                      <div *ngIf="getSenderName(card.type, item)" class="inbox-mes-name wrap-ellipsis">
                        <div *ngIf="isCombinedCard(card.type)" class="sender-alignment-container">
                          <div class="sender-spacer"></div>
                          <span class="sender-text">{{ getSenderName(card.type, item) }}</span>
                        </div>
                        <ng-container *ngIf="!isCombinedCard(card.type)">
                          {{ getSenderName(card.type, item) }}
                        </ng-container>
                      </div>
                    </ng-template>
                  </div>

                  <div class="date-block">
                    <!-- Download icon positioned before date -->
                    <button
                      *ngIf="shouldShowDownloadButton(card.type, item.itemType)"
                      class="download-icon"
                      (click)="handleDownload($event, card.type, item)"
                      (keydown.enter)="handleDownload($event, card.type, item)"
                      tabindex="0"
                      [attr.aria-label]="getDownloadAriaLabel(card.type, item.itemType) | translate"
                    >
                      <ion-icon name="download-outline" color="success"></ion-icon>
                    </button>

                    <!-- Date text -->
                    <div class="date-text">
                      {{ getFormattedDate(card.type, item) | shortDateTime }}
                    </div>
                  </div>
                </div>
              </ion-item>

              <!-- No data state -->
              <ion-item *ngIf="card.data && card.data.length === 0" class="flex ios no-data">
                <div class="item-details">
                  <div class="item-header">
                    <span class="item-name">{{ 'MESSAGES.NO_DATA_FOUND' | translate }}</span>
                  </div>
                </div>
              </ion-item>
            </ion-list>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>
