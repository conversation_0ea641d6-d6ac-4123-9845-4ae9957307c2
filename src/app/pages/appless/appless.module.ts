import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ApplessPageRoutingModule } from './appless-routing.module';

import { ApplessPage } from './appless.page';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared.module';
import { ApplessHomeComponent } from './appless-home/appless-home.component';
import { SearchBarModule } from 'src/app/components/search-bar/search-bar.module';
import { MessagesPageModule } from '../message-center/messages/messages.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    SharedModule,
    ApplessPageRoutingModule,
    TranslateModule,
    SearchBarModule,
    MessagesPageModule
  ],
  declarations: [ApplessPage, ApplessHomeComponent]
})
export class ApplessPageModule {}
