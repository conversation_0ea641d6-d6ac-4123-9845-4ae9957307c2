export interface SiteDetailsResponse {
  data: SiteDetailsData;
}

export interface SiteDetailsData {
  getSiteDetails: GetSiteDetails;
}

export interface SiteListByUserIdResponse {
  data: SiteListByUserIdData;
}

export interface SiteListByUserIdData {
  siteListByUserId: {
    data: UserSitesResponse[];
    __typename: 'usersiteList';
  };
}

export interface UserSitesResponse {
  id: number;
  name: string;
  __typename: 'UserSites';
}

export interface GetSiteDetails {
  tenantId: number;
  name: string;
  status: string;
  siteTimeZone: string;
  logo: string;
  startTime: string;
  endTime: string;
  Address: string;
  workingDays: string;
  modifiedBy: number;
  createdAt: string;
  createdBy: number;
  modifiedAt: string;
  data: Datum[];
  configs: Config[];
  registrationId: string;
  officeEmail: string;
  contactEmail: string;
  officePhone: string;
  helplinePhone: string;
  officePhoneCountryCode: string;
  helplineCountryCode: string;
  __typename: string;
}

export interface Config {
  key: string;
  value: string;
  __typename: string;
}

export interface Datum {
  userId: number;
  displayname: string;
  __typename: string;
}

export interface FCMappingsResponse {
  data: FCMappingsData;
}

export interface FCMappingsData {
  siteFcMappings: SiteFcMapping[];
}

export interface SiteFcMapping {
  id: string;
  tenantId: string;
  siteRegistrationId: string;
  refId: string;
  refType: string;
  folderPath: string;
  folder: string;
  filingCenterSiteConfig: FilingCenterSiteConfig;
  __typename: string;
}

export interface FilingCenterSiteConfig {
  fromFilingCenter: string;
  __typename: string;
}

export interface TenantFilingCenterContent {
  data: TenantFilingData;
}

export interface TenantFilingData {
  getSiteTenantFilingCenterContent: GetSiteTenantFilingCenterContent[];
}

export interface GetSiteTenantFilingCenterContent {
  name: string;
  folder: string;
  size: number;
  type: string;
  preview: string;
  addedOn: number;
  fileId: null;
  __typename: string;
}
export interface PatientInformation {
  userId?: string | number;
  id?: number | string;
  cmisId?: number | string;
  altPatientid?: number | string;
  altPatientDob?: string;
  altPatientFirstName?: string;
  altPatientLastName?: string;
  altPatientEmail?: string;
  altPatientName?: string;
  isVirtual?: boolean;
  isAlternate?: boolean;
  altPatientMobile?: string;
  dateOfBirth?: string;
  firstName?: string;
  firstname?: string;
  lastName?: string;
  lastname?: string;
  dob?: string;
  email?: string;
  IdentityValue?: string;
  patientIdentity?: { IdentityValue?: string };
  userName?: string;
  displayName?: string;
  mobile?: string;
  gender?: string;
  alternateContact?: boolean;
  patientFirstName?: string;
  patientLastName?: string;
  patientDob?: string;
  patientDisplayName?: string;
  patientMobile?: string;
  patientEmail?: string;
  careGiver?: {
    firstName?: string;
    lastName?: string;
    dob?: string;
    displayName?: string;
    mobile?: string;
    userName?: string;
    gender: string;
  };
  alternate?: Alternate;
  alternateContacts?: Alternate;
}
export interface SignatureRequestRecipients {
  roles: string;
  searchValue: string;
  siteId: string;
  admissionId?: string;
  filterByPatientId?: number;
}
export interface PatientInformationForPrepopulation {
  patientMrn?: string;
  patientGuid?: string;
  patient?: PatientInformation;
}

export interface Alternate {
  id: string;
  roleId: number;
  status: number;
  displayName: string;
  firstName: string;
  lastName: string;
  relation: string;
  username: string;
  ESIValue: null;
  patientMrn: null;
  patientId: string;
  mobile: string;
  countryCode: string;
  password: string;
  created_at: string;
  modified_at: string;
  cmisid: string;
  patientFirstName: string;
  patientLastName: string;
  patientStatus: string;
  patientDob: Date;
  patientPassword: null;
  patientDisplayName: string;
  tenantId: number;
  tenantName: string;
  tenantRoleId: string;
  __typename: string;
}

export interface DocumentDetailedCount {
  data: DocumentDetailedCountData;
}
export interface DocumentDetailedCountData {
  mySignatureRequestCount: MySignatureRequestCount;
}
export interface MySignatureRequestCount {
  totalPendingCount: number;
  totalSignedCount: number;
  totalArchiveCount: number;
}
