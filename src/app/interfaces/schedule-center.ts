import { formatDate, isPresent } from 'src/app/utils/utils';
import { Constants } from '../constants/constants';
interface SchedeuleVisit {
  visitTask?: any;
  organization?: string;
  organization_name?: string;
  startTimezone?: string;
  isDraft?: boolean;
  locationCountryCode?: any;
  actualStartTimeVal?: string;
  actualEndTimeVal?: string;
  staff_reason_for_cancel?: string;
  patient_reason_for_cancel?: string;
  draftToUpdate?: boolean;
  draftDateChange?: boolean;
  isDelete?: boolean;
  isComplete?: boolean;
  schedulername?: any;
  visitStatus?: any;
  patientOptedNotification?: any;
  specificMedication?: string;
  therapyType?: any;
  payor?: any;
  id?: number;
  visitType?: number;
  visitLocation?: number;
  assignedToUserId?: string;
  title?: string;
  start?: string;
  end?: string;
  actualDate?: string;
  actualStartTime?: string;
  actualEndTime?: string;
  tasks?: string;
  visitTypeName?: string;
  repeatType?: number;
  visitLocName?: string;
  patientName?: string;
  patientId?: number;
  passwordExist?: boolean;
  staffReasonForCancel?: string;
  patientReasonForCancel?: string;
  ReasonForCancelVisit?: string;
  staffName?: string;
  color?: string;
  patientVisitStatus?: string;
  staffVisitStatus?: string;
  VisitSTatus?: string;
  therapyTypes?: string;
  visitTitle?: string;
  visitStartDate?: string;
  visitStartTime?: string;
  visitEndTime?: string;
  staffJobType?: string;
  patientVisitStatusId?: number;
  staffVisitStatusId?: number;
  visitStatusId?: number;
  payors?: string;
  isBillable?: string;
  subContractedSTatus?: string;
  notificationStatus?: string;
  selected_resource?: any;
  resourceName?: any;
  comments?: string;
  visitAddress?: string;
  visitAuthDates?: any;
  visitSeriesId?: number;
  fileName?: string;
  myVisitFor?: any;
  specificOrdersForReference?: string;
  currentOrders?: any;
  locationTimezone?: string;
  phoneNumber?: string;
  countryCode?: string;
  startDate?: string;
  endDate?: string;
  startTime?: string;
  endTime?: string;
  roa?: string;
  modeOfAdministration?: string;
  routeOfAdministration?: string;
  therapyId?: string;
  payorId?: string;
  taskId?: string;
  moa?: string;
  created_by?: number;
  schedulerName?: string;
  EnableStaffConfirmationVisitType?: number;
  EnablePatientConfirmationVisitType?: number;
  visitTypeKey?: number;
  locationPhoneNumber?: string;
  repeatInterval?: string;
  until?: any;
  send_patient_notification?: number;
  repeatDays?: any;
  repeatMonthType?: any;
  operation?: string;
  nonBillable?: string;
  staffConfirmation?: string;
  patientConfirmation?: string;
  repeatSchedule?: string;
  visitAssignedTo?: string;
  visitAssignedToAic?: [string];
  subcontracted?: string;
  locationTimeZone?: string;
  editVisitId?: number; // Check value
  visitId?: number; //  check value
  actualStartDate?: string;
  sendPatientNotification?: number;
  file?: string;
  reasonForCancelStaffPatient?: string;
}
export class ScheduleVisitSchemas {
  id: number;
  visitSeriesId: number;
  visitType: number;
  patientName: any;
  visitAssignedTo: string;
  visitAssignedToAic: [string];
  visitTitle: string;
  specificMedication: string;
  nonBillable: string;
  staffConfirmation: string;
  patientConfirmation: string;
  staff_reason_for_cancel?: string;
  patient_reason_for_cancel?: string;
  staffReasonForCancel?: string;
  patientReasonForCancel?: string;
  repeatSchedule: string;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  subcontracted: string;
  visitAddress: string;
  comments: string;
  visitAuthDates: any;
  locationTimeZone: string;
  currentOrders: any;
  phoneNumber: string;
  countryCode: string;
  editVisitId: number;
  visitId: number;
  visitTypeKey: number;
  actualStartDate: string;
  actualStartTime: string;
  actualEndTime: string;
  sendPatientNotification: number;
  file: string;
  reasonForCancelStaffPatient: string;
  patientId?: number;
  visitTypeName?: string;
  visitLocName?: string;
  staffName?: string;
  payors?: any;
  therapyTypes?: any;
  tasks?: any;
  roa?: any;
  specificOrdersForReference?: any;
  visitLocation?: number;
  payor?: any;
  therapyType?: any;
  routeOfAdministration?: any;
  modeOfAdministration?: any;
  visitTask?: any;
  repeatInterval?: string;
  organization?: string;
  patientOptedNotification?: any;
  visitStatus?: any;
  until?: any;
  schedulername?: any;
  isComplete?: boolean;
  startTimezone?: string;
  isDraft?: boolean;
  locationPhoneNumber?: any;
  locationCountryCode?: any;
  actualStartTimeVal?: string;
  actualEndTimeVal?: string;
  draftToUpdate?: boolean;
  draftDateChange?: boolean;
  repeatDays?: string;
  repeatMonthType?: string;
  isDelete?: boolean;
  EnableStaffConfirmationVisitType?: number;
  EnablePatientConfirmationVisitType?: number;
  constructor(opts: SchedeuleVisit = {}) {
    this.id = opts.id;
    this.visitSeriesId = opts.visitSeriesId;
    this.visitType = opts.visitType;
    this.visitTitle = opts.visitTitle;
    this.specificMedication = '';
    this.startDate = opts.startDate;
    this.startTime = opts.startTime;
    this.endDate = opts.endDate;
    this.endTime = opts.endTime;
    this.visitAddress = opts.visitAddress;
    this.comments = opts.comments;
    this.currentOrders = opts.currentOrders;
    this.phoneNumber = opts.phoneNumber;
    this.countryCode = opts.countryCode;
    this.actualStartTime = opts.actualStartTime;
    this.actualEndTime = opts.actualEndTime;
    this.visitAuthDates = opts.visitAuthDates;
    this.locationPhoneNumber = opts.locationPhoneNumber;
    this.locationCountryCode = opts.locationCountryCode;
    this.EnableStaffConfirmationVisitType = opts.EnableStaffConfirmationVisitType;
    this.EnablePatientConfirmationVisitType = opts.EnablePatientConfirmationVisitType;
    if (opts.operation === 'update') {
      this.patientName = opts.patientName; // check value
      this.visitType = opts.visitType;
      this.visitLocation = opts.visitLocation;
      this.visitTitle = opts.visitTitle;
      this.patientName = opts.patientName;
      this.payor = opts.payor;
      this.therapyType = opts.therapyType;
      this.specificMedication = opts.specificMedication;
      this.nonBillable = opts.nonBillable;
      this.routeOfAdministration = opts.routeOfAdministration;
      this.modeOfAdministration = opts.modeOfAdministration;
      this.visitTask = opts.visitTask;
      this.visitAssignedTo = opts.visitAssignedTo;
      this.visitAssignedToAic = opts.visitAssignedToAic;
      this.file = opts.file;
      this.staffConfirmation = opts.staffConfirmation;
      this.patientConfirmation = opts.patientConfirmation;
      this.repeatSchedule = opts.repeatSchedule;
      this.startDate = opts.startDate;
      this.startTime = opts.startTime;
      this.endDate = opts.endDate;
      this.endTime = opts.endTime;
      this.repeatInterval = opts.repeatInterval;
      this.comments = opts.comments;
      this.visitAuthDates = opts.visitAuthDates;
      this.subcontracted = opts.subcontracted;
      this.organization = opts.organization;
      this.patientOptedNotification = opts.patientOptedNotification;
      this.visitStatus = opts.visitStatus;
      this.until = opts.until;
      this.visitAddress = opts.visitAddress;
      this.locationTimeZone = opts.locationTimeZone;
      this.startTimezone = opts.startTimezone;
      this.isDraft = opts.isDraft;
      this.currentOrders = opts.currentOrders;
      this.phoneNumber = opts.phoneNumber;
      this.countryCode = opts.countryCode;
      this.locationPhoneNumber = opts.locationPhoneNumber;
      this.locationCountryCode = opts.locationCountryCode;
      this.actualStartDate = opts.actualStartDate;
      this.actualEndTime = opts.actualEndTime;
      this.actualStartTime = opts.actualStartTime;
      this.actualStartTimeVal = opts.actualStartTimeVal;
      this.actualEndTimeVal = opts.actualEndTimeVal;
      this.staff_reason_for_cancel = opts.staff_reason_for_cancel;
      this.patient_reason_for_cancel = opts.patient_reason_for_cancel;
      this.reasonForCancelStaffPatient = opts.reasonForCancelStaffPatient;
      this.editVisitId = opts.editVisitId;
      this.draftToUpdate = opts.draftToUpdate;
      this.draftDateChange = opts.draftDateChange;
      this.visitTypeKey = opts.visitTypeKey;
      this.repeatDays = opts.repeatDays;
      this.repeatMonthType = opts.repeatMonthType;
      this.isDelete = opts.isDelete;
      this.isComplete = opts.isComplete;
      this.visitId = opts.visitId;
      this.sendPatientNotification = opts.sendPatientNotification;
      this.schedulername = opts.schedulername;
    } else if (opts.operation === 'fetch') {
      this.patientName = opts.patientName;
      this.patientId = opts.patientId;
      this.nonBillable = opts.isBillable;
      this.staffConfirmation = opts.staffVisitStatusId.toString(); //  check value
      this.patientConfirmation = opts.patientVisitStatusId.toString(); //  check value
      this.repeatSchedule = opts.repeatInterval;
      this.visitAuthDates = opts.visitAuthDates;
      this.visitAssignedTo = opts.assignedToUserId;
      this.visitAssignedToAic = [opts.assignedToUserId];
      this.subcontracted = opts.subContractedSTatus;
      this.locationTimeZone = opts.locationTimezone;
      this.editVisitId = opts.visitSeriesId; // Check value
      this.visitId = opts.visitId || 3; //  check value
      this.visitTypeKey = opts.visitTypeKey || 3; //  check value
      this.visitLocation = opts.visitLocation || 1; //  check value
      this.actualStartDate = opts.actualDate;
      this.sendPatientNotification = opts.send_patient_notification;
      this.file = opts.fileName;
      this.reasonForCancelStaffPatient = opts.ReasonForCancelVisit; // check value
      this.staffReasonForCancel = opts.staffReasonForCancel;
      this.patientReasonForCancel = opts.patientReasonForCancel;
      this.visitTypeName = opts.visitTypeName;
      this.visitLocName = opts.visitLocName;
      this.staffName = opts.staffName;
      this.payors = opts.payors;
      this.therapyTypes = opts.therapyTypes;
      this.tasks = opts.tasks;
      this.roa = opts.roa;
      this.specificOrdersForReference = opts.specificOrdersForReference;
      this.organization = opts.organization_name;
    }
  }
}

interface CreateVisit {
  EnablePatientConfirmationVisitType?: number;
  EnableStaffConfirmationVisitType?: number;
  ReasonForCancelVisit?: string;
  actualDate?: string;
  actualEndTime?: string;
  actualStartTime?: string;
  assignedToUserId?: string;
  assignedUserName?: string;
  attachment?: string;
  comments?: string;
  countryCode?: string;
  currentOrders?: any;
  endDate?: string;
  endTime?: string;
  id?: number;
  isBillable?: number;
  locationCountryCode?: string;
  locationPhoneNumber?: string;
  locationTimezone?: string;
  modeOfAdministration?: string;
  organization_name?: string;
  patientArray?: [];
  patientId?: number;
  patientOptedNotifications?: number;
  patientReasonForCancel?: any;
  patientVisitStatusId?: number;
  payors?: any;
  phoneNumber?: string;
  repeaMonthType?: any;
  repeatDays?: any;
  repeatInterval?: string;
  repeatType?: number;
  routeOfAdministration?: any;
  schedulerName?: string;
  selectedResource?: any;
  send_patient_notification?: number;
  specificOrdersForReference?: string;
  staffName?: string;
  staffReasonForCancel?: any;
  staffVisitStatusId?: number;
  startDate?: string;
  startTime?: string;
  subContractedStatus?: number;
  tasks?: any;
  therapyTypes?: string;
  untilDate?: any;
  utcEndTimestamp?: number;
  utcStartTimestamp?: number;
  visitAddress?: string;
  visitAuthDates?: any;
  visitId?: number;
  visitLocation?: number;
  visitLocationName?: string;
  visitStatusId?: number;
  visitTitle?: string;
  visitType?: number;
  visitTypeKey?: number;
}

export class CreateVisitSchemas {
  EnablePatientConfirmationVisitType?: number;
  EnableStaffConfirmationVisitType?: number;
  ReasonForCancelVisit?: string;
  actualDate?: string;
  actualEndTime?: string;
  actualStartTime?: string;
  assignedToUserId?: string;
  assignedUserName?: string;
  attachment?: string;
  comments?: string;
  countryCode?: string;
  currentOrders?: any;
  currentOrdersGoodThrough?: string;
  endDate?: string;
  endTime?: string;
  every?: string;
  fileUpload?: string;
  id?: number;
  isBillable?: number;
  locationCountryCode?: string;
  locationPhoneNumber?: string;
  locationTimezone?: string;
  modeOfAdministration?: string;
  modeOfAdministrationIds?: string;
  monthly?: string;
  nonBillable?: boolean;
  organization?: string;
  organization_name?: string;
  patientArray?: any[];
  patientId?: number;
  patientName?: string;
  patientOptedInNotifications?: boolean;
  patientOptedNotifications?: number;
  patientReasonForCancel?: any;
  patientVisitStatus?: number;
  patientVisitStatusId?: number;
  payor?: string;
  payors?: any;
  phoneNumber?: string;
  repeaMonthType?: any;
  repeatDays?: any;
  repeatInterval?: string;
  repeatSchedule?: string;
  repeatType?: number;
  resources?: string;
  routOfAdministration?: string;
  routeOfAdministrationIds?: string;
  scheduleCadence?: string;
  schedulerName?: string;
  selectedResource?: any;
  send_patient_notification?: number;
  specificMedicineOrdered?: string;
  specificOrdersForReference?: string;
  staffID: string;
  staffName?: string;
  staffPartnerVisitStatus?: number;
  staffReasonForCancel?: any;
  staffVisitStatusId?: number;
  startDate?: string;
  startTime?: string;
  subContractedStatus?: number;
  subcontracted?: boolean;
  tasks?: any;
  therapyType?: string;
  therapyTypes?: string;
  timeZone?: string;
  until?: string;
  untilDate?: any;
  utcEndTimestamp?: number;
  utcStartTimestamp?: number;
  visitAddress?: string;
  visitAssignedTo?: string;
  visitAuthDates?: string;
  visitId?: number;
  visitLocation?: number;
  visitLocationName?: string;
  visitLocationType?: string;
  visitStatusId?: number;
  visitTask?: string;
  visitTitle?: string;
  visitationType: number;
  visitTypeKey?: number;
  weekly?: string;
  constructor(opts: CreateVisit = {}) {
    this.EnablePatientConfirmationVisitType = opts.EnablePatientConfirmationVisitType;
    this.EnableStaffConfirmationVisitType = opts.EnableStaffConfirmationVisitType;
    this.ReasonForCancelVisit = opts.ReasonForCancelVisit;
    this.actualDate = opts.actualDate;
    this.actualEndTime = opts.actualEndTime;
    this.actualStartTime = opts.actualStartTime;
    this.assignedToUserId = opts.assignedToUserId;
    this.assignedUserName = opts.assignedUserName;
    this.attachment = opts.attachment;
    this.comments = opts.comments;
    this.countryCode = opts.countryCode;
    this.currentOrders = opts.currentOrders;
    this.currentOrdersGoodThrough = isPresent(opts.currentOrders)
      ? formatDate(Number(opts.currentOrders), Constants.dateFormat.mdy)
      : undefined;
    this.endDate = isPresent(opts.endDate) ? formatDate(opts.endDate, Constants.dateFormat.mdy) : undefined;
    this.endTime =
      isPresent(opts.endDate) && isPresent(opts.endTime)
        ? formatDate(`${opts.endDate} ${opts.endTime}`, Constants.dateFormat.h1ma)
        : undefined;
    this.every = opts.repeatInterval; // check
    this.fileUpload = ''; // check
    this.id = opts.id;
    this.isBillable = opts.isBillable;
    this.locationCountryCode = opts.locationCountryCode;
    this.locationPhoneNumber = opts.locationPhoneNumber;
    this.locationTimezone = opts.locationTimezone;
    this.modeOfAdministration = '';
    this.modeOfAdministrationIds = opts.modeOfAdministration;
    this.monthly = opts.repeaMonthType; // check
    this.nonBillable = opts.isBillable === 1; // check
    this.organization = opts.organization_name;
    this.organization_name = opts.organization_name;
    this.patientArray = opts.patientArray;
    this.patientArray = opts.patientArray;
    this.patientId = opts.patientId;
    this.patientName = '';
    this.patientOptedInNotifications = Boolean(opts.patientOptedNotifications);
    this.patientOptedNotifications = opts.patientOptedNotifications;
    this.patientReasonForCancel = opts.patientReasonForCancel;
    this.patientVisitStatus = opts.patientVisitStatusId;
    this.patientVisitStatusId = opts.patientVisitStatusId;
    this.payor = '';
    this.payors = opts.payors;
    this.phoneNumber = opts.phoneNumber;
    this.repeaMonthType = opts.repeaMonthType;
    this.repeatDays = opts.repeatDays;
    this.repeatInterval = opts.repeatInterval;
    this.repeatSchedule = opts.repeatInterval; // check
    this.repeatType = opts.repeatType;
    this.resources = opts.selectedResource; // check
    this.routOfAdministration = '';
    this.routeOfAdministrationIds = opts.routeOfAdministration;
    this.scheduleCadence = opts.repeatInterval; // check
    this.schedulerName = opts.schedulerName;
    this.selectedResource = opts.selectedResource;
    this.send_patient_notification = opts.send_patient_notification;
    this.specificMedicineOrdered = opts.specificOrdersForReference; // check
    this.specificOrdersForReference = opts.specificOrdersForReference;
    this.staffID = opts.assignedToUserId;
    this.staffName = opts.staffName;
    this.staffPartnerVisitStatus = opts.staffVisitStatusId;
    this.staffReasonForCancel = opts.staffReasonForCancel;
    this.staffVisitStatusId = opts.staffVisitStatusId;
    this.startDate = isPresent(opts.startDate) ? formatDate(opts.startDate, Constants.dateFormat.mdy) : undefined;
    this.startTime =
      isPresent(opts.startDate) && isPresent(opts.startTime)
        ? formatDate(`${opts.startDate} ${opts.startTime}`, Constants.dateFormat.h1ma)
        : undefined;
    this.subContractedStatus = opts.subContractedStatus;
    this.subcontracted = Boolean(opts.subContractedStatus);
    this.tasks = opts.tasks;
    this.therapyType = opts.therapyTypes;
    this.therapyTypes = opts.therapyTypes;
    this.timeZone = opts.locationTimezone;
    this.until = opts.untilDate;
    this.untilDate = opts.untilDate;
    this.utcEndTimestamp = opts.utcEndTimestamp;
    this.utcStartTimestamp = opts.utcStartTimestamp;
    this.visitAddress = opts.visitAddress;
    this.visitAssignedTo = opts.assignedUserName;
    this.visitAuthDates = opts.visitAuthDates;
    this.visitId = opts.visitId;
    this.visitLocation = opts.visitLocation;
    this.visitLocationName = String(opts.visitLocation);
    this.visitLocationType = String(opts.visitType);
    this.visitStatusId = opts.visitStatusId;
    this.visitTask = opts.tasks;
    this.visitTitle = opts.visitTitle;
    this.visitTypeKey = opts.visitTypeKey;
    this.visitationType = opts.visitType;
    this.weekly = opts.repeatDays; // check
  }
}

export interface IStaffSearchParam {
  id?: string;
  type: string;
  siteId?: string;
  pageCount?: number;
  searchKeyword?: string;
  offset?: number;
  limit?: number;
}
export interface IcalendarData {
  color?: string;
  end: string;
  start: string;
  id: string;
  siteName?: string;
  staffVisitStatus?: string;
  status?: string;
  patientVisitStatus?: string;
  visitStatus?: string;
  visitKey?: string;
  staffName?: string;
  visitTitle?: string;
  eventId?: string;
  patientName?: string;
  title?: string;
  repeatType?: string;
  timeZone?: string;
  createdBy?: string;
  startDate?: string;
  endDate?: string;
  visitationDetails?: string;
  backgroundColor?: string;
  textColor?: string;
  borderColor?: string;
  visitTherapy?: string;
  visitTherapyName?: string;
  chairName?: string;
  visitLocationName?: string;
  clinicianNames?: string;
  visitStaffData?: [];
  visitType?: string;
}
export interface IcalendarParam {
  calendarType: string;
  startDate: string;
  endDate: string;
  status?: string;
  type?: string;
  siteId?: string;
  staffId?: string;
  patientId?: string;
  title?: string;
  visitTypeLocation?: string;
  visitChair?: string;
  colorCodingCategory?: string;
}

export interface ApiResponse<DataInterface> {
  success: boolean;
  status: { code: number; message: string };
  data: DataInterface;
}
export interface AttachmentData {
  fileKey?: string;
  filesLocation?: string;
  filesName?: string;
}
export interface VisitSubmitData {
  visitKey?: string;
}
export interface IavailabilitDeleteParam {
  action: string;
  timezone?: string;
  editType: number;
  startDate?: string;
  startTime?: string;
  endDate?: string;
  endTime?: string;
}

export interface ArrayWithCount<ResponseArray> {
  response: ResponseArray;
  totalCount: string;
}
export interface UserList {
  grp: string;
  userid: string;
  userId?: string;
  username: string;
  displayname: string;
  firstname: string;
  lastname: string;
  dob: string;
  company_nursing_agency: string;
  gender?: any;
  country_code: string;
  mobile: string;
  home_phone?: any;
  work_phone?: any;
  address?: any;
  city?: any;
  state?: any;
  country?: any;
  password: string;
  user_job_type?: any;
  status: string;
  user_type: string;
  organization: string;
}
export type TimeZones = TimeZone[]

export interface TimeZone {
  offset: string
  city: string
  name: string
  isDst: string
  current_offset: string
}

export interface Iavailability {
  availabilityKey?: string;
  availabilityType?: string;
  color?: string;
  end: string;
  eventVisitTitle?: string;
  id?: string;
  locationTimezone: string;
  reason?: string;
  repeatType?: string;
  siteName?: string;
  staffName?: string;
  staffRole?: string;
  start: string;
  title?: string;
  eventId?: string;
  eventTextColor?: string;
}
export interface IvisitFormData {
  actualTimeIn?: string;
  actualTimeOut?: string;
  additionalDetails?: string;
  assignedTo?: any;
  countryCode?: string;
  duration?: string;
  editType?: number;
  endAt: string;
  isBillable?: string;
  isSubcontracted?: string;
  locationTimezone: string;
  organization?: string;
  patientConfirmation: string;
  patientId: string;
  phoneNumber?: string;
  previousEndAt?: string;
  previousStartAt?: string;
  repeatDays?: string;
  repeatInterval?: string;
  repeatMonthType?: string;
  repeatType: string;
  siteId?: string;
  staffConfirmation: string;
  startAt: string;
  until?: string;
  visitAddress?: string;
  visitStatus?: string;
  visitTitle: string;
  visitType: string;
  visitTypeLocation?: string;
  visitChair?: string;
  visitationDetails?: string;
  staffReasonForCancel?: string;
  patientReasonForCancel?: string;
  countryIsoCode?: string;
}
export interface IvisitConfirmation {
  action?: string;
  editType?: number;
  endAt?: string;
  staffReasonForCancel?: string;
  startAt?: string;
  patientReasonForCancel?: string;
}

export interface IVisitStatus {
  value: number;
  text: string;
  disabled: boolean;
}
export interface IModifyVistFormData {
  siteName?: number;
  patientName: string;
  phoneNumber?: string;
  visitAddress?: string;
  visitTitle: string;
  visitationType?: string;
  visitationTypeLocation?: string;
  visitChair?: string;
  therapyType?: string;
  dosageAppointment?: string;
  visitDetails?: string;
  timeZone: string;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  billable: boolean;
  visitAssignedTo?: string;
  subcontracted: boolean;
  organization?: string;
  staffPartnerVisitStatus?: number;
  staffReasonForCancel?: string;
  actualDate?: string;
  actualTimeIn?: string;
  actualTimeOut?: string;
  totalMileage?: string;
  totalDriveTime?: string;
  patientVisitStatus: number;
  additionalDetails?: string;
  endOn?: string;
  repeatDays?: string;
  monthly?: string;
  requestPatientConfirmation?: boolean;
  sendPatientNotification?: boolean;
  setPatientReminder?: boolean;
  scheduleCadence: string;
  patientReasonForCancel?: string;
  recurrence?: boolean;
  weekly?: [string];
  repeatEvery?: string;
  confirmationBeforeStaffConfirm?: number;
}

export interface IVisitDataController {
  siteId?: number;
  patientId?: string;
  countryCode: string;
  phoneNumber?: string;
  visitAddress?: string;
  visitTitle?: string;
  visitationDetails?: string;
  isBillable: number;
  assignedTo?: any;
  isSubcontracted: number;
  organization?: string;
  staffConfirmation: string;
  patientConfirmation: string;
  additionalDetails?: string;
  startAt: string;
  endAt: string;
  until?: string;
  locationTimezone: string;
  repeatInterval?: number;
  repeatType?: string;
  repeatDays?: string;
  repeatMonthType?: string;
  visitStatus?: string;
  duration?: string;
  visitType?: string;
  visitTypeLocation?: string;
  visitChair?: string;
  therapyType?: string;
  dosageAppointment?: string;
  totalMileage?: string;
  totalDriveTime?: string;
  requestPatientConfirmation: number;
  sendPatientNotification: number;
  setPatientReminder: number;
  staffReasonForCancel?: string;
  actualTimeIn?: string;
  actualTimeOut?: string;
  editType?: number;
  previousEndAt?: string;
  previousStartAt?: string;
  patientReasonForCancel?: string;
  countryIsoCode?: string;
  confirmationBeforeStaffConfirm?: number;
}

export interface IClinicianList {
  address?: string;
  city?: string;
  company_nursing_agency?: string;
  country?: string;
  country_code?: string;
  displayname: string;
  dob?: string;
  firstname?: string;
  gender?: string;
  grp?: string;
  home_phone?: string;
  id?: string;
  lastname?: string;
  mobile?: string;
  name: string;
  organization?: string;
  password?: string;
  selected?: boolean;
  state?: string;
  status?: string;
  user_job_type?: string;
  user_type?: string;
  userid?: string;
  username: string;
  work_phone?: string;
}

export interface IAvailabilityParam {
  startIntervalTime: string;
  endIntervalTime: string;
  siteId?: number;
  statusFilter?: string;
  selectedUserId?: string;
  tenantTimeZoneName: string;
  timezoneDisplayName?: string;
  listView?: boolean;
  startTime?: string;
  endTime?: string;
}

export interface IVisitDelete {
  action?: string;
  editType: number;
  endAt?: string;
  startAt?: string;
}

export interface FileAttachments {
  id: string;
  name: string;
  file_path: string;
  key: string;
  size: string;
  formattedFileName: string;
}

export interface IModifyAvailability {
  id: string;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  repeatSchedule: string;
  repeatInterval?: number;
  repeatUntil?: string;
  availabilityType: string;
  repeatDays?: string;
  repeatMonthType?: string;
  nonAvailReason?: string;
  staffId?: string;
  siteId?: number;
  timezone: string;
  staffName: string;
}

export interface ICheckVisitAvailability {
  startAt: string;
  endAt: string;
  until?: string;
  repeatType: string;
  repeatInterval?: number;
  repeatDays?: string;
  repeatMonthType?: string;
  patientId: string;
  staffId?: string;
  visitKey?: string;
}
export interface IDateTime {
  date: string;
  time: string;
}
export interface IStaffData {
  displayname: string;
  user_job_type?: string;
  user_type?: string;
  organization?: string;
}
export interface IFormData {
  siteName: any;
  mobileNumber: any;
  visitTypeName: any;
  visitationDetails: any;
  billable: any;
  subcontracted: any;
  organizationName: any;
  patientNotification: any;
  additionalDetails: any;
  locationTimeZone?: any;
  assignedUserName?: string;
  startDate: string;
  startEventTime: string;
  endDate: string;
  endEventTime: string;
  weekly?: string;
  patientTimeZone: string;
  patientName: string;
  visitAddress?: string;
  eventVisitTitle: string;
  recurrenceEnd: string;
  recurrenceStart: string;
  timeInAddress?: string;
  timeInLatLong?: string;
  timeOutAddress?: string;
  timeOutLatLong?: string;
}
export interface ParsedData {
  patientName?: string;
  visitationTypes?: any;
  selectedSiteName?: string;
  clinicianName?: string;
  clinicianNames?: string;
  schedules?: any;
  title?: string;
  visitTypeLocation?: string;
  visitChair?: string;
  mySites?: any;
};
export interface VisitScheduleFilterKeys {
  clinicianId?: string;
  clinicianIds?: string;
  clinicianName?: string;
  clinicianNames?: string;
  schedules?: any;
  patientID?: string;
  mySites?: any;
  types?: any;
  patientName?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  visitTypeLocation?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  visitChair?: any;
  title?: string;
}