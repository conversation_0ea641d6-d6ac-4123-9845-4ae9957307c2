import { MessageCategory } from 'src/app/constants/constants';

export interface ChatrommUerCount {
  current_field?: any;
  field_count?: any;
  lengths?: any;
  num_rows?: any;
  type?: any;
}
export interface ChatParticipant {
  avatar?: string;
  userId: number;
  displayName: string;
}
export interface MessageTagList {
  id: number;
  name: string;
  type: string;
  bgColor: string;
  fontColor: string;
}
export interface MessagesResponse {
  loading?: boolean;
  chatAvatar: string;
  chatHeading: string;
  chatParticipantCount: number;
  chatParticipants: ChatParticipant[];
  chatSubHeading: string;
  chatSubject: string;
  chatroomFlag: number;
  chatroomId: number;
  deliveryTime?: string;
  department?: string;
  hasUnreadMessages: boolean;
  initiatorName: string;
  isAcExist: boolean;
  isPatientExist: boolean;
  isPatientInitiatedChat: boolean;
  isPdg: boolean;
  isSelfArchived: boolean;
  isSelfMessage: boolean;
  maskedSubCount: number;
  maskedUnreadCount: any;
  mentionedUsers: any;
  message: string;
  messageCategory: MessageCategory;
  messageFlag: number;
  messageForwarded: string;
  messageMentionRead: boolean;
  messageMentionUnread: boolean;
  messageOrder: string;
  messagePriorityRead: number;
  messagePriorityUnread: number;
  messageTagList: MessageTagList[];
  messageType: number;
  messageUnreadCount: number;
  messagesCount: number;
  patientId?: number;
  pinnedStatus: boolean;
  priorityId: number;
  repliedTo: any;
  unreadCount: number;
  baseChatroomId?: number;
  // Below will be removed after API changes
  maskedReplyMessages?: any;
  childExpanded?: boolean;
  checked?: boolean;
  forwardName?: string;
  // Below will be removed after API changes
  message_group_id: string; // Push deep-link payload
  createdby: string; // Push deep-link payload
  baseId: string; // masked message identifier
  invited_status: string; // archive/ restore chat payload, can be avoided if handled from API
  messageStatus?: number;
  messageDeletedTime?: string;
}

export interface MessageInboxList {
  totalUnreadMessagesCount?: number;
  messages: MessagesResponse[];
  totalChatRoomsCount: number;
}
export interface MessagethreadOnPolling {
  message: MessagesResponse;
}

export interface ChatMessageResponse {
  patient_data?: any;
  patientTagInfo?: any;
  success: boolean;
  activity: number;
  content: MessageContent[];
  chatroomid: string;
  messageType: string;
  title: string;
  subject: string;
  createdBy: string;
  baseId: string;
  messageGroupId: string;
  displayName?: string;
  appLessMode?: boolean;
  admissionId?: string;
  admissionName?: string;
}
export interface MessageInboxPayload {
  unread?: boolean;
  searchKeyword?: string;
  archived?: boolean;
  viewInventory?: boolean;
  showChatHistory?: boolean;
  pageCount?: number;
  flagValue?: number;
  filterTags?: number[];
  priorityValue?: number;
  siteIds?: number[];
  mentionUsers?: boolean;
  isShowLoader?: boolean;
  selectedDateOptions?: number;
  dateRange?: { from: string; to: string };
  chatThreadTypes?: number[];
}
export interface MessageContent {
  patientTagInfo?: { patientId: any; admissionId?: any; patient_data: any };
  checked?: boolean;
  messageType?: string;
  id?: string;
  userid: string;
  language?: string;
  message: string;
  sent: string;
  fname?: string;
  lname?: string;
  pgrp?: string;
  tenantid?: string;
  displayName: string;
  uniqueId?: string;
  caregiver_displayname?: any;
  caregiver_userid?: any;
  avatar: string;
  roleId?: string;
  forwardBy?: string;
  sign: string;
  tag?: string;
  tagSign?: string;
  patient?: string;
  tag_ids?: string;
  tag_names?: string;
  readUsers?: ReadUser[];
  pfirstname?: string;
  plastname?: string;
  pdob?: string;
  pdisplayname?: string;
  pcaregiver_displayname?: string;
  ppassword?: any;
  msg_flag?: string;
  prev_msg_flag?: string;
  msg_flag_data_id?: any;
  flag_modified_on?: string;
  tagedItems?: any;
  sender_time?: any;
  uploading?: boolean;
  priorityId?: number;
  mentionedUsers?: any[];
  messageDeletedTime?: any;
  messageStatus?: any;
  repliedTo?: any[];
  showMention?: boolean;
  showDownloadOptions?: boolean;
}

interface ReadUser {
  userid: number;
  lastactivity: number;
  displayName: string;
  avatar: string;
}

export interface DeliveredUsers {
  avatar: string;
  deliverytime: number;
  displayName: string;
  message_id: number;
  read_time: number;
  roleid: number;
  userType: number;
  userid: number;
}
export interface GetChangeNetworkStatus {
  connected: boolean;
  connectionType: 'wifi' | 'cellular' | 'none' | 'unknown';
}
export enum ConnectionStatus {
  Online,
  Offline
}
export interface MaskedRecipientListExtraParam {
  status: string;
  siteIds: string;
  recipientRoles: string;
  accessSecurityEnabled?: boolean;
  accessSecurityEsiValue?: any;
  accessSecurityIdentifierType?: any;
  accessSecurityType?: any;
}

export enum SyncFolderType {
  INCOMING = 'INCOMING',
  OUTGOING = 'OUTGOING'
}

export interface FetchUsersExtraParams {
  excludeRoleId?: boolean;
  excludeLogginedUser?: string;
  pageCount?: number;
  searchKeyword?: string;
  crossSiteOptions?: boolean;
  siteIds?: string;
  optionShow?: string;
  roleId?: number;
  userGroup?: number;
  includePDGInfo?:boolean
}
export interface SearchAction {
  do: DoOperation;
  value: string;
}
export enum DoOperation {
  search = 'search',
  reset = 'reset'
}
export interface MessageGroupParams {
  data: {
    groupId?: number;
    filter?: {
      search: string;
    };
    pagination: {
      page: number;
      limit: number;
    };
  };
  siteIds?: number[];
}
export interface PdgListParams {
  data: {
    groupId?: number;
    filter?: {
      search: string;
    };
    pagination: {
      page: number;
      limit: number;
    };
  };
  siteIds?: number[];
}
export interface MessageGroup {
  groupId: string;
  groupName: string;
  createdBy: string;
  createdAt: string;
  tenantId: string;
  isPublic: string;
  allowMultiThreadChat: string;
  branch: string;
  chatRoomId?: string;
  chatRoomCreatedBy?: string;
  topic?: string;
  isParticipant?: string;
}
export interface PatientGroup {
  patientId: string;
  firstName: string;
  lastName: string;
  DOB: string;
  MRN: string;
  groupName: string;
  createdAt: string;
  tenantId: string;
  isPublic: string;
  allowMultiThreadChat: string;
  chatRoomId?: string;
  chatRoomCreatedBy?: string;
  topic?: string;
  isParticipant: string;
  sites: string;
}
export interface MessageGroupData {
  messageGroups?: MessageGroup[];
  totalMessageGroupsCount?: string;
  patientGroups: PatientGroup[];
  totalPDGCount?: string;
}

export interface RoleList {
  roleId: number;
  roleName: string;
  tenant_id: number;
  tenant_name: string;
}
export interface UserMessagetoServer {
  data: string;
  chatroomId: string;
  type: string;
  id: string;
  uniqueId: string;
  insert: boolean;
  language: string;
  userId: string;

  displayName?: string;
  sentTimeCheck?: number;
  baseId?: number;
  messageType?: string;
  doubleVerificationStatus?: number;
  sender_time?: number;
  insertionStatus?: number;
  failed?: boolean;

  additionalDataToResend?: {
    message_group_id?: string;
    chatWithHeading?: string;
    createdby?: string;
    targetName?: string;
    selectedTenantId?: number | string;
  };
  caregiverDisplayname?: string;
  caregiverUserid?: number | string;
  avatar?: string;
  apiVersion?: string;
  includeMasterStaffs?: number;
  isMaster?: number;
  masterEnabled?: number;
  tenantId?: string;
  tenantName?: string;
  environment?: string;
  sent?: number;
  serverBaseUrl?: string;
  workingHour?: boolean;
  staffSmsNotificationEnabled?: string;
  messageConfig?: MessageConfig;
  pushData?: MessagePushData;
  masterTenantId?: string | number;
  escalationTime?: number;
  scheduleData?: any;
  scheduleInterval?: number;
  masterEscalationTime?: number;
  masterScheduleData?: any;
  masterScheduleInterval?: number;
  firstUserMessageFromChatroom?: boolean;
  chatroomPatientSiteId?: string;

  allow24HourWorking?: string;
  smsNotificationEnabled?: string;
  masterStaffSmsNotificationEnabled?: string;
  masterWorkingHour?: boolean;
  masterAllow24HourWorking?: string;
  masterMessageConfig?: MessageConfig;
  maskedMessageMainThreadMessageWithChild?: boolean;
  priorityId?: number;
  mentionedUsers?: any[];
  repliedTo?: any[];
}

export interface MessageConfig {
  messageEscalation: boolean;
  messageReminder: boolean;
  userCitusRole: string;
}
export interface Topic {
  chatRoomId: string;
  subject: string;
  groupId: string;
  createdby: string;
  isParticipant: string;
}
export interface TopicsResponse {
  messageGroupTopics?: Topic[];
  patientGroupTopics?: Topic[];
}
export interface MessagePushData {
  source?: string;
  chatRoomOrToId: string;
  environment: string;
  message: string;
  pushDeepLink: {
    activeMessage: {
      baseId: number;
      chatroomid: string;
      createdby: string;
      fromName: string;
      message_group_id: string;
      messageType: string;
      msg_flag: number;
      msg_flag_data_id: null;
      prev_msg_flag: number;
      sent: number;
      userid: string;
      selectedTenantId?: number | string;
    };
    pushType: string;
    state: string;
    stateParams: {
      targetID: string;
      targetName: string;
    };
  };
  pushNotificationUpdated: boolean;
  userId: string;
}

export interface BulkMessageResponse {
  success: boolean;
  status: BulkMessageStatus;
  data: BulkMessageResponseData[];
}

export interface BroadcastMessageData { }
export interface BulkMessageResponseData {
  userid: string;
  chatroom_id?: string;
  message_id?: string;
  configurationNotEnabled?: boolean;
}

export interface BulkMessageStatus {
  code: number;
  message: string;
}

export interface RoleParticipant {
  deletedUsersCount?: number;
  name: string;
  id: string;
}

export interface ChatRoomParticipant {
  oooInfo?: any;
  assignedRoles?: any;
  fname: string;
  lname: string;
  invited_by: string;
  country_code: string;
  dob: string;
  relation: string;
  caregiver_identityvalue: string;
  tenantid: string;
  tenantName: string;
  is_enrolled: string;
  avatar: string;
  username: string;
  userId: string;
  invited: string;
  userRoleId: string;
  defualtUser: string;
  participantType: string;
  status: string;
  mobile: string;
  roleId: string;
  displayName: string;
  caregiver_userid: string;
  participantRole: string;
  participantRoleName: string;
  caregiver_username: string;
  cgiver_fname: string;
  cgiver_passwordStatus: string;
  cgiver_lname: string;
  cgiver_grp: string;
  caregiver_displayname: string;
  caregiver_dob: string;
  roleName: string;
  siteId: string;
  messageType: string;
  IdentityValue: string;
  passwordStatus: string;
  password: string;
  naTags: string;
  naTagNames: string;
  caregiver: any[];
  deleted: string;
}

export interface ChatRoomUsersAndRolesData {
  roleParticipants: RoleParticipant[];
  chatParticipants: ChatRoomParticipant[];
  allParticipants: ChatRoomParticipant[];
}
export interface ChatRoomUsersAndRoles {
  data: ChatRoomUsersAndRolesData;
}

export interface UpdateChatroomParticipantsResponse {
  status?: number;
  room?: string;
  initiatorUserId?: string;
}

export enum UpdateChatroomParticipantStatus {
  RESTORE = 1,
  DELETE = 0
}