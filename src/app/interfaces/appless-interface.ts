import { LoginResponse } from './login';

export interface Data {
  chat_room: string;
  roomData: string | any;
  id: string;
  status: string;
}

export interface LiveTokenStatus {
  live_token_status: string;
}

export interface ApplessVideoTokenVerify {
  token: string;
  status: number;
  message: string;
  tokenExpire: boolean;
  'appless-video': boolean;
  userData: LoginResponse;
  data: Data;
  live_token_status: LiveTokenStatus;
}
