import { DeviceInfo } from '@capacitor/device';
import { ChatWithTypes } from 'src/app/constants/constants';

export interface DateSelectKeyValue {
  pickerFormat: string;
  formControlName: string;
  value: any;
}
export interface BrandConfig {
  tenantId: number;
  title: string;
  alias: string;
  theme: string;
  appName: string;
  serverUrlCustom?: string;
  machFormUrl?: string;
}

export interface DeviceInterface extends DeviceInfo {
  deviceModel: string;
  device: string;
}
export interface ExtendedScreenOrientation extends ScreenOrientation {
  lock(orientation: string): Promise<void>;
}

export interface AlternateContactsEntity {
  contactId: string;
  roleId: string;
  status: string;
  displayName: string;
  firstName: string;
  lastName: string;
  relation: string;
  email: string;
  alternateUsername: string;
  ESIValue?: string | null;
  patientId: string;
  mobile: string;
  countryCode: string;
  password: boolean;
  created_at: string;
  modified_at: string;
  cmisid: string;
  patientFirstName: string;
  patientLastName: string;
  patientStatus: string;
  patientDob: string;
  patientPassword?: null;
  patientDisplayName: string;
  tenantId: string;
  tenantName: string;
  tenantRoleId: string;
  userId: string;
  isVirtual: boolean;
  identity_value: string;
  roleName: string;
  passwordStatus?: boolean;
  patientDobFormatted: string;
  enable_sms_notifications?: string;
  enable_email_notifications?: string;
}
export interface FilterOption {
  name: string;
  iconName: string;
  value: number;
  min: number;
  max: number;
}
export interface FormMessageCountUpdateEvent {
  countType?: string;
  isPolling?: boolean;
}
export interface SurveyName {
  tagId?: string;
  id?: number;
  name?: string;
  tenant_id?: number;
  signaturePadPresent?: boolean;
  recipientRoles?: number[];
  visibleToRoles?: number[];
  stafFacing?: string;
  staffFill?: string;
  patientAssociation?: string;
  tag_name?: string;
  externalFileExchange?: boolean;
  enableSaveDraftStaff?: number;
  enableSaveDraftPatient?: number;
  externalFileDisclosePHI?: boolean;
  populatePreviousSubmission?: boolean;
  confirmActionPrefill?: boolean;
  enableMendatoryFieldChecking?: boolean;
  triggerOnPHI?: null;
  enableAppless?: boolean;
  applessDevices?: string;
  FaxQIntegration?: boolean;
  sendCompletedForm?: boolean;
}
export type SurveyNamesResponse = SurveyName[];

export interface UserEntity {
  tenantId: string;
  verificationStatus?: null;
  contactCount: string;
  tenantName: string;
  siteId: string;
  siteName: string;
  languages: string;
  password: boolean;
  dualRoles: string;
  last_login?: boolean | string;
  userid: string;
  tenantid: string;
  userId: string;
  name: string;
  displayname: string;
  email: string;
  mobile?: string | null;
  countryCode?: string | null;
  firstname: string;
  lastname: string;
  status: number | string;
  roleId: string;
  userJobType?: null;
  enable_sms_notifications?: string;
  enable_email_notifications?: string;
  dob: string;
  organization: string;
  shipping_address?: null;
  role: string;
  tenantRoleId: string;
  naTags?: null;
  naTagNames?: null;
  cmisid: string;
  signedFolderId?: null;
  IdentityValue?: string | null;
  passwordStatus: boolean;
  isCaregiverPatient: boolean;
  mobVerificationStatus: boolean;
  emailVerificationStatus: boolean;
  alternateContacts?: (AlternateContactsEntity | null)[] | null;
}
export interface UserTag {
  id: string | number;
  tenant_id?: string;
  tag_name?: string;
  tag_type_category?: string;
  tag_type_id?: string;
  tag_type?: string;
  is_deleted?: string;
  is_nursing_agency?: string;
  selectedTags?: string;
  name?: string;
  selected?: boolean;
}
export interface FormattedTag {
  tagId: number;
  tagName: string;
}
export interface Country {
  name: string;
  dialCode: string;
  code: string;
}
export type CountryPopoverCallback = (result: Country) => void;
export interface DeliveryData {
  id: number | string;
  userId: number | string;
  courierTicketNumber: string;
  courierTrackingNumber: string;
  courierTrackingURL: string;
  courierCarrier: string;
  courierDateTime: string;
  courierStatus: string;
  signatureURL: string;
  mrn: string;
  branch: string;
  emailBody: string;
  messageText: string;
  deliveyType: string;
  is_completed: number | string;
  createdOn: string;
  last_modified: string;
  userid: number | string;
  tenantId: number | string;
  firstname: string;
  lastname: string;
  mobile: string;
  tenantName: string;
}
export interface DeliveryResponse {
  totalCount: number;
  deliveryData: DeliveryData[];
}
export interface ActionButton {
  iconCustom?: boolean;
  type?: string;
  buttonType?: string;
  buttonIcon?: string;
  colorTheme?: string;
  permission?: boolean;
  label?: string;
  icon?: string;
}
export interface DataListExtraData {
  showNoDataMessage?: boolean;
  search?: { text?: string };
  image?: string;
  type?: string;
  trackBy?: string;
}
export interface FormWorkListPayload {
  roleid?: string;
  zone: string;
  isForms: boolean;
  isPrivilege: boolean;
  limit: number;
  offset: number;
  searchText: string;
  orderData: string;
  orderby: string;
  isScheduled: string;
  archived: boolean;
  pending: boolean;
  completed: boolean;
  draft: boolean;
  accessSecurityEnabled: boolean;
  startDate: string;
  endDate: string;
  filterType?: string;
  citusRoleId: string;
  enableIntegrationStatus: boolean;
  enableSftpIntegration: boolean;
  siteIds?: string;
  nursingAgencies?: string;
}
export interface DefaultSitesFilter {
  id: number;
  name: string;
}

export interface CreateChatRoomData {
  dob?: string;
  IdentityValue?: string;
  lastname?: string;
  firstname?: string;
  userId?: number;
  associatedPatient?: number;
  userIds?: number[];
  patientId?: number;
  groupId?: number;
  topic?: string;
  roleId?: number;
  allowMultiThreadChat?: string;
  displayText?: string;
  newTopicName?: any;
  isSelectTopic?: boolean;
  isParticipant?: string;
  chatRoomCreatedBy?: string;
  chatRoomId?: string;
  admissionId?: string;
}
export interface CreateChatRoomPayload {
  chatWith: ChatWithTypes;
  data: CreateChatRoomData;
  admissionId?: string;
}
interface Status {
  code: number;
  message: string;
}
export interface GeneralResponse<T> {
  data: T;
  status: Status;
  success: boolean;
}
export interface MessageDeliveredUsers {
  avatar: string;
  displayName: string;
  userid: number;
  deliveryTime: number;
  lastactivity: number;
  readTime: number;
}

export interface Page {
  size?: number;
  number?: number;
  totalElements?: number;
  totalPages?: number;
}

export interface Admission {
  status?: number;
  admissionId?: string;
  admissionName?: string;
  externalAdmissionId?: string;
  lineOfService?: string;
  siteId?: number;
  siteName?: string;
  startDate?: string;
  endDate?: string;
  mrn?: string;
  isDiscussionGroupPublic?: boolean;
  allowMultiThreadChat?: boolean;
  chatRoomId?: number;
  chatRoomCreatedBy?: number;
  topic?: string;
  isParticipant?: number;
  patientId?: number;
  citusStatus?: number;
  createdDate?: string;
  participant?: boolean;
}

export interface Pageable {
  sort?: string;
  offset?: number;
  pageNumber?: number;
  pageSize?: number;
  paged?: boolean;
  unpaged?: boolean;
}


export interface AdmissionList {
  content?: Admission[];
  pageable?: Pageable;
  totalPages?: number;
  totalElements?: number;
  last?: boolean;
  size?: number;
  number?: number;
  sort?: string;
  numberOfElements?: number;
  first?: boolean;
  empty?: boolean;
  page?: Page;
}
export interface AdmissionDetails {
  admissionId?: string;
  admissionName?: string;
  externalAdmissionId?: string;
  lineOfService?: string;
  siteId?: number;
  siteName?: string;
  startDate?: Date;
  endDate?: Date;
  mrn?: string;
  patientId?: number;
  status?: string;
  citusStatus?: number;
  createdDate?: Date;
}
