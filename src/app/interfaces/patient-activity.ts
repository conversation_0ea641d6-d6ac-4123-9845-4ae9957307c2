export interface PatientProfileDetails {
  data: PatientData;
}

export interface PatientData {
  getSessionTenant: GetSessionTenant;
}

export interface GetSessionTenant {
  patientUsers: PatientUser[];
  __typename: string;
}

export interface PatientUser {
  dateOfBirth: Date;
  gender: null;
  avatar: null;
  displayName: string;
  lastName: string;
  firstName: string;
  status: string;
  id: string;
  tenantId: number;
  userTags: UserTag[];
  activeSince: string;
  lastLogin: null;
  password: boolean;
  emails: Email[];
  comments: Comment[];
  role: Role;
  zip: string;
  associatePatient: null;
  associatePatients: null;
  associateCaregivers: null;
  mobile: string;
  countryCode: string;
  country: null;
  state: null;
  city: null;
  siteId: any;
  address: null;
  allAddresses: any[];
  patientIdentity: null;
  naTags: null;
  naTagNames: null;
  userEMverification: UserEMverification;
  alternateContacts: any[];
  externalShippingAddresses: any[];
  externalInfo: any[];
  externalDocuments: any[];
  prescriptionOrders: any[];
  patientAddress: any[];
  patientCustomFields: any[];
  enableEmailNotifications: number;
  enableSmsNotifications: number;
  __typename: string;
}

export interface Comment {
  comment: null;
  __typename: string;
}

export interface Email {
  value: string;
  type: string;
  primary: boolean;
  __typename: string;
}

export interface Role {
  roleName: string;
  id: number;
  displayName: string;
  __typename: string;
}

export interface UserEMverification {
  mobileVerified: number;
  emailVerified: number;
  __typename: string;
}

export interface UserTag {
  id: string;
  tagName: string;
  __typename: string;
}

export interface PatientDocuments {
  data: PatientDocumentsData;
}

export interface PatientDocumentsData {
  getSessionTenant: GetDocumentsSessionTenant;
}

export interface GetDocumentsSessionTenant {
  patientDocuments: PatientDocumentsClass;
  __typename: string;
}

export interface PatientDocumentsClass {
  data: DocumentDataList[];
  meta: DocumentMeta;
  __typename: string;
}

export interface DocumentDataList {
  description: string;
  createdAt: string;
  modifiedAt: string;
  docNotes: string;
  docCategory: string;
  fileName: string;
  docFileUrl: string;
  docSource: string;
  __typename: string;
}

export interface DocumentMeta {
  firstItem: number;
  lastItem: number;
  hasMorePages: boolean;
  count: number;
  total: number;
  perPage: number;
  __typename: string;
}

export interface UpdatePatientAddress {
  status: number;
  statusMessage: string;
  id: string;
}

export interface PatientAddressSearch {
  data: PatientAddressData;
}

export interface PatientAddressData {
  getSessionTenant: PatientAddressGetSessionTenant;
}

export interface PatientAddressGetSessionTenant {
  patientExternalAddress: PatientExternalAddress;
  __typename: string;
}

export interface PatientExternalAddress {
  data: PatientExternalAddressData[];
  meta: DocumentMeta;
  __typename: string;
}

export interface PatientExternalAddressData {
  id: number;
  patientId: number;
  category: number;
  line1: string;
  line2: null;
  city: string;
  district: string;
  state: string;
  country: string;
  zipCode: string;
  type: string;
  __typename: string;
}