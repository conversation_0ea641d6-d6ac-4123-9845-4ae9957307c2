import { ElementRef } from '@angular/core';
import { fakeAsync, tick } from '@angular/core/testing';
import { ContenteditableDirective } from './contenteditable.directive';

let directive: ContenteditableDirective;
it('should create an instance', () => {
  let ele: ElementRef<HTMLElement>;
  directive = new ContenteditableDirective(ele);
  expect(directive).toBeTruthy();
});

it('should return "innerHTML" if contenteditableHtml is true', () => {
  directive.contenteditableHtml = true;
  const result = directive.getProperty();
  expect(result).toBe('innerHTML');
});

it('should return "innerText" if contenteditableHtml is false', () => {
  directive.contenteditableHtml = false;
  const result = directive.getProperty();
  expect(result).toBe('innerText');
});

it('should not refresh the view if content is unchanged', () => {
  const initialContent = 'Initial Content';
  directive.contenteditableModel = initialContent;
  const elRefSpy = jasmine.createSpyObj('ElementRef', ['nativeElement']);
  elRefSpy.nativeElement = document.createElement('div');
  directive.elRef = elRefSpy;
  directive.elRef.nativeElement[directive.getProperty()] = initialContent;
  directive.refreshView();
  expect(directive.elRef.nativeElement[directive.getProperty()]).toBe(initialContent);
});

it('should refresh the view if content has changed', () => {
  const newContent = 'New Content';
  directive.contenteditableModel = newContent;
  const elRefSpy = jasmine.createSpyObj('ElementRef', ['nativeElement']);
  elRefSpy.nativeElement = document.createElement('div');
  directive.elRef = elRefSpy;
  spyOn(directive.elRef.nativeElement, directive.getProperty()).and.callThrough();
  directive.refreshView();
  expect(directive.elRef.nativeElement[directive.getProperty()]).toBe(newContent);
});

it('should not call onInput if not the first change and contenteditableModel is not empty', () => {
  const initialModel = 'Initial Content';
  const newModel = 'New Content';
  directive.contenteditableModel = initialModel;

  directive.ngOnChanges({
    contenteditableModel: {
      currentValue: newModel,
      isFirstChange: () => false,
      previousValue: initialModel,
      firstChange: false
    }
  });
  spyOn(directive, 'onInput');
  expect(directive.onInput).not.toHaveBeenCalled();
});

it('should emit contenteditableModelChange on input event without trimming', () => {
  spyOn(directive.contenteditableModelChange, 'emit');
  directive.onInput(false);
  expect(directive.contenteditableModelChange.emit).toHaveBeenCalledWith('Initial Content');
});

it('should emit contenteditableModelChange on input event with trimming', () => {
  spyOn(directive.contenteditableModelChange, 'emit');
  directive.onInput(true);
  expect(directive.contenteditableModelChange.emit).toHaveBeenCalledWith('Initial Content');
});

it('should emit contenteditableModelChange on paste event without trimming', () => {
  spyOn(directive.contenteditableModelChange, 'emit');
  directive.onPaste();
  expect(directive.contenteditableModelChange.emit).toHaveBeenCalledWith('Initial Content');
});

it('should remove pasted HTML for text-only contenteditable', fakeAsync(() => {
  directive.contenteditableHtml = false;
  directive.elRef.nativeElement.innerHTML = 'pasted <b>HTML</b>';
  spyOn(directive, 'onInput');
  directive.onPaste();
  tick();
  expect(directive.onInput).toHaveBeenCalled();
  expect(directive.elRef.nativeElement.innerHTML).toBe('pasted HTML');
}));
