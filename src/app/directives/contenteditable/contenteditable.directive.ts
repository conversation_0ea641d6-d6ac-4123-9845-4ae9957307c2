import {
	Directive, ElementRef, Input, Output, EventEmitter, SimpleChanges, OnChanges,
	HostListener
  } from '@angular/core';
  
  @Directive({
	selector: '[contenteditableModel]'
  })
  export class ContenteditableDirective implements OnChanges {
	/** Model */
	@Input() contenteditableModel: string;
	@Output() contenteditableModelChange?= new EventEmitter();
	@Input() contenteditableHtml?: boolean = false;
  
	constructor(
	  public elRef: ElementRef
	) { }
  
	ngOnChanges(changes: SimpleChanges) {
	  if (changes['contenteditableModel']) {
		// On init: if contenteditableModel is empty, read from DOM in case the element has content
		if (changes['contenteditableModel'].isFirstChange() && !this.contenteditableModel) {
		  this.onInput(true);
		}
		this.refreshView();
	  }
	}
  
	@HostListener('input') // input event would be sufficient, but isn't supported by IE
	@HostListener('blur')  // additional fallback
	@HostListener('keyup') onInput(trim = false) {
	  let value = this.elRef.nativeElement[this.getProperty()];
	  if (trim) {
		value = value.replace(/^[\n\s]+/, '');
		value = value.replace(/[\n\s]+$/, '');
	  }
	  this.contenteditableModelChange.emit(value);
	}
  
	@HostListener('paste') onPaste() {
	  this.onInput();
	  if (!this.contenteditableHtml) {
		// For text-only contenteditable, remove pasted HTML.
		// 1 tick wait is required for DOM update
		setTimeout(() => {
		  if (this.elRef.nativeElement.innerHTML !== this.elRef.nativeElement.innerText) {
			this.elRef.nativeElement.innerHTML = this.elRef.nativeElement.innerText;
		  }
		});
	  }
	}
  
	refreshView() {
	  const newContent = this.contenteditableModel;
	  // Only refresh if content changed to avoid cursor loss
	  // (as ngOnChanges can be triggered an additional time by onInput())
	  if (newContent !== this.elRef.nativeElement[this.getProperty()]) {
		this.elRef.nativeElement[this.getProperty()] = newContent;
	  }
	}
  
	getProperty(): string {
	  return this.contenteditableHtml ? 'innerHTML' : 'innerText';
	}
  }