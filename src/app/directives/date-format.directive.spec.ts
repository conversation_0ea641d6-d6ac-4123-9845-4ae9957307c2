import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { IonicModule } from '@ionic/angular';
import { DateFormatDirective } from './date-format.directive';

@Component({
  template: ' <ion-input [formControl]="testControl" appDateFormat maxlength="10" data-testid="date-input"> </ion-input> '
})
class TestComponent {
  testControl = new FormControl('');
}

describe('DateFormatDirective', () => {
  let component: TestComponent;
  let fixture: ComponentFixture<TestComponent>;
  let directive: DateFormatDirective;

  // Helper function
  function createInputEvent(
    targetValue: string,
    inputChar?: string
  ): {
    target: {
      value: string;
      selectionStart: number;
      setSelectionRange: jasmine.Spy;
    };
    data: string | undefined;
    inputType: string;
  } {
    return {
      target: {
        value: targetValue,
        selectionStart: targetValue.length,
        setSelectionRange: jasmine.createSpy('setSelectionRange')
      },
      data: inputChar,
      inputType: 'insertText'
    };
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestComponent, DateFormatDirective],
      imports: [ReactiveFormsModule, IonicModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;

    const debugElement: DebugElement = fixture.debugElement.query(By.directive(DateFormatDirective));
    directive = debugElement.injector.get(DateFormatDirective);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(directive).toBeTruthy();
  });

  describe('Auto-formatting', () => {
    it('should auto-insert slash after 2 digits', () => {
      const event = createInputEvent('010');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/0');
    });

    it('should auto-insert second slash after MM/DD', () => {
      const event = createInputEvent('01010');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/0');
    });

    it('should format complete date', () => {
      const event = createInputEvent('01012000');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2000');
    });

    it('should limit to 10 characters', () => {
      const event = createInputEvent('010120001234');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2000');
    });
  });

  describe('Manual slash entry', () => {
    it('should allow manual slash after 2 digits', () => {
      component.testControl.setValue('01');
      const event = createInputEvent('01/', '/');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/');
    });

    it('should allow manual slash after MM/DD', () => {
      component.testControl.setValue('01/01');
      const event = createInputEvent('01/01/', '/');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/');
    });
  });

  describe('Duplicate slash prevention', () => {
    it('should prevent double slashes', () => {
      const event = createInputEvent('01//');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/');
    });

    it('should prevent triple slashes', () => {
      const event = createInputEvent('01///');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/');
    });

    it('should fix multiple duplicate slashes in different positions', () => {
      const event = createInputEvent('01//01//2000');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2000');
    });
  });

  describe('Backspace functionality', () => {
    it('should detect backspace operation', () => {
      const event = {
        target: {
          value: '',
          selectionStart: 0,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: null,
        inputType: 'deleteContentBackward'
      };
      directive.onInput(event);
      // Just verify it doesn't crash and processes the backspace
      expect(component.testControl.value).toBeDefined();
    });
  });

  describe('Core functionality tests', () => {
    it('should handle mixed input with auto-formatting', () => {
      const event = createInputEvent('01a01b2000');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2000');
    });

    it('should handle partial input correctly', () => {
      const event = createInputEvent('0101');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01');
    });

    it('should maintain existing slashes when processing', () => {
      const event = createInputEvent('01/01');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01');
    });

    it('should handle empty parts in rebuild', () => {
      const event = createInputEvent('');
      directive.onInput(event);
      expect(component.testControl.value).toBe('');
    });

    it('should handle cursor positioning with auto-inserted slash', () => {
      component.testControl.setValue('01');
      const event = createInputEvent('012', '2');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/2');
    });
  });

  describe('Input validation', () => {
    it('should strip non-numeric characters except slashes', () => {
      const event = createInputEvent('01a/b01c/d2000e');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2000');
    });

    it('should handle empty input', () => {
      const event = createInputEvent('');
      directive.onInput(event);
      expect(component.testControl.value).toBe('');
    });

    it('should handle single digit', () => {
      const event = createInputEvent('1');
      directive.onInput(event);
      expect(component.testControl.value).toBe('1');
    });
  });

  describe('Edge cases', () => {
    it('should handle paste with complete date', () => {
      const event = createInputEvent('01012000');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2000');
    });

    it('should handle paste with slashes already present', () => {
      const event = createInputEvent('01/01/2000');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2000');
    });

    it('should handle mixed valid and invalid characters', () => {
      const event = createInputEvent('0!1@/0#1$/2%0^0&0*');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2000');
    });

    it('should handle null/undefined control value', () => {
      component.testControl.setValue(null);
      const event = createInputEvent('01');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01');
    });

    it('should handle null/undefined input data', () => {
      const event = {
        target: {
          value: '01',
          selectionStart: 2,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: null,
        inputType: 'insertText'
      };
      directive.onInput(event);
      expect(component.testControl.value).toBe('01');
    });
  });

  describe('Method coverage tests', () => {
    it('should test hasDuplicateSlashes method', () => {
      const event = createInputEvent('01//02');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/02');
    });

    it('should test removeDuplicateSlashes method', () => {
      const event = createInputEvent('01///02////03');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/02/03');
    });

    it('should test isBackspaceOperation method', () => {
      const event = {
        target: {
          value: '',
          selectionStart: 0,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: null,
        inputType: 'deleteContentBackward'
      };
      directive.onInput(event);
      expect(component.testControl.value).toBe('');
    });

    it('should test hasSlashAtPosition method', () => {
      const event = createInputEvent('01/');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/');
    });

    it('should test applyLengthLimit method', () => {
      const event = createInputEvent('01234567890123456789');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/23/4567');
    });

    it('should test wasSlashAutoInserted method for first slash', () => {
      component.testControl.setValue('01');
      const event = createInputEvent('012', '2');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/2');
    });

    it('should test wasSlashAutoInserted method for second slash', () => {
      component.testControl.setValue('01/01');
      const event = createInputEvent('01/012', '2');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/01/2');
    });

    it('should test rebuildValueFromParts with different lengths', () => {
      // Test with 3+ parts
      const event = createInputEvent('01/02/03/04');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01/02/03');
    });

    it('should handle null control value in directive logic', () => {
      // Test the null handling in the directive's prev value logic
      component.testControl.setValue(null);
      const event = createInputEvent('01');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01');
    });

    it('should test backspace after slash detection', () => {
      // Test the isBackspaceAfterSlash method
      component.testControl.setValue('01/');
      const event = {
        target: {
          value: '01',
          selectionStart: 3,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: null,
        inputType: 'deleteContentBackward'
      };
      directive.onInput(event);
      expect(component.testControl.value).toBe('0');
    });

    it('should test regular backspace detection', () => {
      // Test regular backspace (not after slash) - just verify it processes without error
      component.testControl.setValue('012');
      const event = {
        target: {
          value: '01',
          selectionStart: 2,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: null,
        inputType: 'deleteContentBackward'
      };
      expect(() => directive.onInput(event)).not.toThrow();
    });

    it('should test empty target value handling', () => {
      const event = {
        target: {
          value: '',
          selectionStart: 0,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: null,
        inputType: 'insertText'
      };
      directive.onInput(event);
      expect(component.testControl.value).toBe('');
    });

    it('should test all branches in rebuildValueFromParts', () => {
      // Test empty array case
      const event = createInputEvent('');
      directive.onInput(event);
      expect(component.testControl.value).toBe('');
    });

    it('should test cursor positioning without auto-inserted slash', () => {
      component.testControl.setValue('01');
      const event = createInputEvent('01a', 'a');
      directive.onInput(event);
      expect(component.testControl.value).toBe('01');
    });

    it('should handle null input data', () => {
      const event = {
        target: {
          value: '01',
          selectionStart: 2,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: null,
        inputType: 'insertText'
      };
      directive.onInput(event);
      expect(component.testControl.value).toBe('01');
    });

    it('should handle undefined input data', () => {
      const event = {
        target: {
          value: '01',
          selectionStart: 2,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: undefined,
        inputType: 'insertText'
      };
      directive.onInput(event);
      expect(component.testControl.value).toBe('01');
    });

    it('should test all branches in wasSlashAutoInserted', () => {
      // Test first condition: value.length === 3 && prev.length === 2 && inputChar !== '/'
      component.testControl.setValue('01');
      const event1 = createInputEvent('012', '2');
      directive.onInput(event1);
      expect(component.testControl.value).toBe('01/2');

      // Test second condition: value.length === 6 && prev.length === 5 && inputChar !== '/'
      component.testControl.setValue('01/01');
      const event2 = createInputEvent('01/012', '2');
      directive.onInput(event2);
      expect(component.testControl.value).toBe('01/01/2');

      // Test false condition
      component.testControl.setValue('01');
      const event3 = createInputEvent('01/', '/');
      directive.onInput(event3);
      expect(component.testControl.value).toBe('01/');
    });

    it('should handle edge case with empty target value', () => {
      const event = {
        target: {
          value: '',
          selectionStart: 0,
          setSelectionRange: jasmine.createSpy('setSelectionRange')
        },
        data: '',
        inputType: 'insertText'
      };
      directive.onInput(event);
      expect(component.testControl.value).toBe('');
    });

    it('should handle edge case with null control', () => {
      // Test when control.control is null - just verify it doesn't crash
      const event = createInputEvent('01');
      expect(() => directive.onInput(event)).not.toThrow();
    });
  });
});
