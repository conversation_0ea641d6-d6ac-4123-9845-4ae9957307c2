import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';
import { NgForTrackByIdentifierDirective } from './track-by-identifier.directive';

@Component({
  template: `
    <div *ngFor="let item of items; trackBy: trackByFn" ngForTrackByIdentifier="id">
      {{ item.name }}
    </div>
  `
})
class TestComponent {
  items = [
    { id: 1, name: 'Item 1' },
    { id: 2, name: 'Item 2' },
    { id: 3, name: 'Item 3' }
  ];

  trackByFn(index: number, item): number {
    return item.id;
  }
}

describe('NgForTrackByIdentifierDirective', () => {
  let fixture: ComponentFixture<TestComponent>;
  let component: TestComponent;
  let debugElement: DebugElement;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [NgForTrackByIdentifierDirective, TestComponent]
    });

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
    debugElement = fixture.debugElement;
    fixture.detectChanges();
  });

  it('should create an instance', () => {
    const directive = debugElement.query(By.directive(NgForTrackByIdentifierDirective)).injector.get(NgForTrackByIdentifierDirective);
    expect(directive).toBeTruthy();
  });
  it('should throw an error if property name is not defined', () => {
    const directive = debugElement.query(By.directive(NgForTrackByIdentifierDirective)).injector.get(NgForTrackByIdentifierDirective);
    directive.ngForTrackByIdentifier = undefined;
    expect(() => directive.trackBy(0, component.items[0])).toThrowError('Property name not defined');
  });

  it('should throw an error if property is undefined', () => {
    const directive = debugElement.query(By.directive(NgForTrackByIdentifierDirective)).injector.get(NgForTrackByIdentifierDirective);
    directive.ngForTrackByIdentifier = 'id';
    expect(() => directive.trackBy(0, { name: 'Item 4' })).toThrowError('Property id is undefined');
  });

  it('should return the value of the identifier property', () => {
    const directive = debugElement.query(By.directive(NgForTrackByIdentifierDirective)).injector.get(NgForTrackByIdentifierDirective);
    directive.ngForTrackByIdentifier = 'id';
    const index = 1;
    const item = component.items[index];
    const result = directive.trackBy(index, item);
    expect(result).toBe(item.id);
  });
  it('should pass ngTemplateContextGuard', () => {
    const directive = debugElement.query(By.directive(NgForTrackByIdentifierDirective)).injector.get(NgForTrackByIdentifierDirective);
    const result = NgForTrackByIdentifierDirective.ngTemplateContextGuard(directive, null);
    expect(result).toBeTrue();
  });
});
