import { NgIterable, Directive, Host, Input } from '@angular/core';
import { NgForOf } from '@angular/common';

@Directive({
  selector: '[ngForTrackByIdentifier]'
})
export class NgForTrackByIdentifierDirective<T> {
  private identifier: keyof T;

  @Input() ngForOf: NgIterable<T>;

  constructor(@Host() public ngForOfDir: NgForOf<T>) {
    this.ngForOfDir.ngForTrackBy = this.trackBy.bind(this);
  }

  trackBy(index: number, item: T) {
    if (!this.identifier) {
      throw new Error('Property name not defined');
    }
    if (typeof item[this.identifier] === 'undefined') {
      throw new Error(`Property ${this.identifier.toString()} is undefined`);
    }
    const value = item[this.identifier];
    return value;
  }

  @Input()
  set ngForTrackByIdentifier(value: keyof T) {
    this.identifier = value;
  }

  static ngTemplateContextGuard<T>(dir: NgForTrackByIdentifierDirective<T>, ctx): ctx is NgForTrackByIdentifierDirective<T> {
    return true;
  }
}
