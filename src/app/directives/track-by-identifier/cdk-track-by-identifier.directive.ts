import { NgIterable, Directive, Host, Input } from '@angular/core';
import { CdkVirtualForOf } from '@angular/cdk/scrolling';

@Directive({
  selector: '[cdkVirtualForTrackByIdentifier]'
})
export class CdkVirtualForTrackByIdentifierDirective<T> {
  private identifier: keyof T;

  @Input() cdkVirtualForOf: NgIterable<T>;

  constructor(@Host() public cdkVirtualForOfDir: CdkVirtualForOf<T>) {
    this.cdkVirtualForOfDir.cdkVirtualForTrackBy = this.trackBy.bind(this);
  }

  trackBy(index: number, item: T) {
    if (!this.identifier) {
      throw new Error('Property name not defined');
    }
    if (typeof item[this.identifier] === 'undefined') {
      throw new Error(`Property ${this.identifier.toString()} is undefined`);
    }
    const value = item[this.identifier];
    return value;
  }

  @Input()
  set cdkVirtualForTrackByIdentifier(value: keyof T) {
    this.identifier = value;
  }

  static ngTemplateContextGuard<T>(dir: CdkVirtualForTrackByIdentifierDirective<T>, ctx): ctx is CdkVirtualForTrackByIdentifierDirective<T> {
    return true;
  }
}
