import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Constants } from 'src/app/constants/constants';
import { TestBed } from '@angular/core/testing';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { IonicModule } from '@ionic/angular';
import { Keepalive } from '@ng-idle/keepalive';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { SharedModule } from 'src/app/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { CharacterLimitDirective } from './character-limit.directive';

describe('CharacterLimitDirective', () => {
  let directive: CharacterLimitDirective;
  let sharedService: SharedService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [IonicModule.forRoot(), HttpClientModule, SharedModule, HttpClientTestingModule, RouterModule.forRoot([]), TranslateModule.forRoot()],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        PermissionService,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
  });

  it('should create an instance', () => {
    directive = new CharacterLimitDirective(sharedService);
    expect(directive).toBeTruthy();
  });

  it('should not modify value if offsetHeight is greater than or equal to scrollHeight', () => {
    const event = {
      target: {
        offsetHeight: 16,
        scrollHeight: 12,
        value: 'test'
      }
    };
    directive.checkScroll(event);
    expect(event.target.value).toBe('test');
  });

  it('should set line height if maxHeight is less than Constants.paletteHeight.min', () => {
    const event = {
      target: {
        value: 'test',
        offsetHeight: 8,
        style: {
          lineHeight: ''
        }
      }
    };
    directive.onInput(event);
    expect(event.target.style.lineHeight).toBe(Constants.lineHeight.minLneHeight);
  });

  it('should set line height and letterSpacing for mobile platforms with maxHeight exceeding Constants.paletteHeight.max', () => {
    const event = {
      target: {
        value: 'test',
        offsetHeight: 150,
        style: {
          lineHeight: '',
          letterSpacing: ''
        }
      }
    };
    spyOn(directive.sharedService.platform, 'is').and.returnValue(true);
    directive.onInput(event);

    if (directive.sharedService.platform.is('android')) {
      expect(event.target.style.lineHeight).toBe(Constants.lineHeight.mobileLineHeightAndroid);
      expect(event.target.style.letterSpacing).toBe(Constants.fontSize.androidMobileLetterSpacing);
    } else {
      expect(event.target.style.lineHeight).toBe(Constants.lineHeight.mobileLineHeightiOS);
      expect(event.target.style.letterSpacing).toBeUndefined();
    }
  });

  it('should set line height for non-mobile platforms with maxHeight exceeding Constants.paletteHeight.max', () => {
    const event = {
      target: {
        value: 'test',
        offsetHeight: 150,
        style: {
          lineHeight: '',
          letterSpacing: ''
        }
      }
    };

    spyOn(directive.sharedService.platform, 'is').and.returnValue(false);
    directive.onInput(event);
    expect(event.target.style.lineHeight).toBe(Constants.lineHeight.webLineHeight);
  });

  it('should not check scroll for non-Capacitor platforms', () => {
    const event = {
      target: {
        value: 'test',
        offsetHeight: 150,
        scrollHeight: 200,
        clientWidth: 100,
        style: {
          lineHeight: '',
          letterSpacing: ''
        }
      }
    };
    spyOn(directive.sharedService.platform, 'is').and.returnValue(false);
    spyOn(directive, 'checkScroll');
    directive.onInput(event);
    expect(directive.checkScroll).not.toHaveBeenCalled();
  });

  it('should check scroll and trim value for Capacitor platform', () => {
    const event = {
      target: {
        value: 'test',
        offsetHeight: 50,
        scrollHeight: 200,
        clientWidth: 60,
        style: {
          lineHeight: '',
          letterSpacing: ''
        }
      }
    };
    spyOn(directive.sharedService.platform, 'is').and.returnValue(true);
    spyOn(directive, 'checkScroll');
    directive.onInput(event);
    expect(directive.checkScroll).toHaveBeenCalled();
  });
});
