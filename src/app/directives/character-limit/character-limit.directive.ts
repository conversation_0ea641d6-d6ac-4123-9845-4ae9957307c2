import { Constants } from 'src/app/constants/constants';
import { SharedService } from './../../services/shared-service/shared.service';
import { Directive, HostListener } from '@angular/core';

@Directive({
  selector: '[character-input-limit]'
})
export class CharacterLimitDirective {
  constructor(public sharedService: SharedService) { }
  @HostListener('input', ['$event']) onInput(e: any) {
    const textAreaNewValue = e.target.value;
    const maxHeight = e.target.offsetHeight;
    const maxWidth = e.target.clientWidth;
    if (maxHeight < Constants.paletteHeight.min) {
      e.target.style.lineHeight = Constants.lineHeight.minLneHeight;
    } else if (this.sharedService.platform.is('capacitor') || this.sharedService.platform.is('mobileweb')) {
      if (maxHeight > Constants.paletteHeight.max) {
        if (this.sharedService.platform.is('android') && this.sharedService.platform.is('capacitor')) {
          e.target.style.lineHeight = Constants.lineHeight.mobileLineHeightAndroid;
          e.target.style.letterSpacing = Constants.fontSize.androidMobileLetterSpacing;
        } else {
          e.target.style.lineHeight = Constants.lineHeight.mobileLineHeightiOS;
        }
      }
    } else if (maxHeight > Constants.paletteHeight.max) {
      e.target.style.lineHeight = Constants.lineHeight.webLineHeight;
    }
    if (this.sharedService.platform.is('capacitor')) {
      if (e.target.scrollHeight > maxHeight || e.target.scrollWidth > maxWidth) {
        e.target.value = textAreaNewValue.slice(0, -1);
        this.checkScroll(e);
      }
    }
  }
  checkScroll(e): void {
    if (e.target.offsetHeight < e.target.scrollHeight) {
      e.target.value = e.target.value.slice(0, -1);
      this.checkScroll(e);
    }
  }
}
