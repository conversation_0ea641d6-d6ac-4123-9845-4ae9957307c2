import { PatternDirective } from './pattern.directive';

describe('LongPressDirective', () => {
  let directive: PatternDirective;
  it('should create an instance', () => {
    directive = new PatternDirective();
    expect(directive).toBeTruthy();
  });
  function createKeyboardEvent(type: string, options: any = {}) {
    const event = new KeyboardEvent(type, options);
    spyOn(event, 'preventDefault');
    return event;
  }
  it('should set pattern from input', () => {
    const testPattern = /^[0-9]+$/;
    directive.pattern = testPattern;
    expect(directive.pattern).toEqual(testPattern);
  });
  it('should allow keys specified in allowedKeys array', () => {
    const allowedKey = 'ArrowUp';
    const event = createKeyboardEvent('keydown', { key: allowedKey });
    directive.onKeyDown(event);
    expect(event.defaultPrevented).toBeFalsy();
  });
  it('should allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X', () => {
    const ctrlAEvent = createKeyboardEvent('keydown', { code: 'KeyA', ctrlKey: true });
    const ctrlCEvent = createKeyboardEvent('keydown', { code: 'KeyC', ctrlKey: true });
    const ctrlVEvent = createKeyboardEvent('keydown', { code: 'KeyV', ctrlKey: true });
    const ctrlXEvent = createKeyboardEvent('keydown', { code: 'KeyX', ctrlKey: true });

    directive.onKeyDown(ctrlAEvent);
    directive.onKeyDown(ctrlCEvent);
    directive.onKeyDown(ctrlVEvent);
    directive.onKeyDown(ctrlXEvent);

    // Ensure preventDefault was not called for Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
    expect(ctrlAEvent.defaultPrevented).toBeFalsy();
    expect(ctrlCEvent.defaultPrevented).toBeFalsy();
    expect(ctrlVEvent.defaultPrevented).toBeFalsy();
    expect(ctrlXEvent.defaultPrevented).toBeFalsy();
  });
  it('should allow Cmd+A, Cmd+C, Cmd+V, Cmd+X on Mac', () => {
    const cmdAEvent = createKeyboardEvent('keydown', { code: 'KeyA', metaKey: true });
    const cmdCEvent = createKeyboardEvent('keydown', { code: 'KeyC', metaKey: true });
    const cmdVEvent = createKeyboardEvent('keydown', { code: 'KeyV', metaKey: true });
    const cmdXEvent = createKeyboardEvent('keydown', { code: 'KeyX', metaKey: true });

    directive.onKeyDown(cmdAEvent);
    directive.onKeyDown(cmdCEvent);
    directive.onKeyDown(cmdVEvent);
    directive.onKeyDown(cmdXEvent);

    // Ensure preventDefault was not called for Cmd+A, Cmd+C, Cmd+V, Cmd+X on Mac
    expect(cmdAEvent.defaultPrevented).toBeFalsy();
    expect(cmdCEvent.defaultPrevented).toBeFalsy();
    expect(cmdVEvent.defaultPrevented).toBeFalsy();
    expect(cmdXEvent.defaultPrevented).toBeFalsy();
  });

  it('should prevent default for keys not allowed', () => {
    const nonAllowedKey = '!';
    const event = createKeyboardEvent('keydown', { key: nonAllowedKey });
    directive.onKeyDown(event);
    expect(event.defaultPrevented).toBeFalsy();
  });
});
