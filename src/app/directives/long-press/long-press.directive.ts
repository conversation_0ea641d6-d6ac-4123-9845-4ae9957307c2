/* eslint-disable @angular-eslint/directive-selector */
import { EventEmitter, Directive, OnInit, Output, Input, ElementRef } from '@angular/core';
import { timer, Subscription } from 'rxjs';

@Directive({
  selector: '[dirLongPress]'
})
export class LongPressDirective implements OnInit {
  timerSub: Subscription;

  @Input() delay = 400;
  @Output() readonly longPressed: EventEmitter<any> = new EventEmitter();

  constructor(public elementRef: ElementRef<HTMLElement>) { }

  ngOnInit(): void {
    const isTouch = 'ontouchstart' in document.documentElement;
    const element = this.elementRef.nativeElement;
    element.onpointerdown = (ev) => {
      this.timerSub = timer(this.delay).subscribe(() => {
        this.longPressed.emit(ev);
      });
    };
    element.onpointerup = () => {
      this.unsub();
    };
    element.onpointercancel = () => {
      this.unsub();
    };
    if (isTouch) {
      element.onpointerleave = () => {
        this.unsub();
      };
      element.ontouchmove = () => {
        this.unsub();
      };
    } else {
      element.onpointermove = () => {
        this.unsub();
      };
    }
  }

  unsub() {
    if (this.timerSub && !this.timerSub.closed) {
      this.timerSub.unsubscribe();
    }
  }
}
