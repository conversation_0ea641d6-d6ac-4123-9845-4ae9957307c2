import { ElementRef } from '@angular/core';
import { LongPressDirective } from './long-press.directive';

describe('LongPressDirective', () => {
  let directive: LongPressDirective;
  it('should create an instance', () => {
    let ele: ElementRef<HTMLElement>;
    directive = new LongPressDirective(ele);
    expect(directive).toBeTruthy();
  });
  describe('unsub', () => {
    it('should not unsubscribe when timerSub is null', () => {
      directive.timerSub = null;
      directive.unsub();
      expect(directive.timerSub).toBeNull();
    });

    it('should not unsubscribe when timerSub is already closed', () => {
      directive.timerSub = {
        closed: true,
        unsubscribe: jasmine.createSpy('unsubscribe')
      } as any;

      directive.unsub();
      expect(directive.timerSub.unsubscribe).not.toHaveBeenCalled();
    });

    it('should unsubscribe when timerSub is not null and not closed', () => {
      directive.timerSub = {
        unsubscribe: jasmine.createSpy('unsubscribe')
      } as any;
      directive.unsub();
      expect(directive.timerSub.unsubscribe).toHaveBeenCalled();
    });
  });
  describe('ngOnInit', () => {
    it('should call ngOnInit ', () => {
      const elRefSpy = jasmine.createSpyObj('ElementRef', ['nativeElement']);
      elRefSpy.nativeElement = document.createElement('div');
      directive.elementRef = elRefSpy;
      directive.ngOnInit();
      expect(directive.ngOnInit).toBeDefined();
    });

    it('should set up event listeners on ngOnInit pointerdown and pointerup', () => {
      spyOn(directive, 'unsub');
      directive.ngOnInit();
      const pointerDownEvent = new Event('pointerdown');
      directive.elementRef.nativeElement.dispatchEvent(pointerDownEvent);
      const pointerUpEvent = new Event('pointerup');
      directive.elementRef.nativeElement.dispatchEvent(pointerUpEvent);
      expect(directive.timerSub).toBeDefined();
      expect(directive.unsub).toHaveBeenCalled();
    });

    it('should set up event listeners on ngOnInit pointercancel', () => {
      spyOn(directive, 'unsub');
      directive.ngOnInit();
      const pointerCancelEvent = new Event('pointercancel');
      directive.elementRef.nativeElement.dispatchEvent(pointerCancelEvent);
      expect(directive.timerSub).toBeDefined();
      expect(directive.unsub).toHaveBeenCalled();
    });

    it('should set up event listeners on ngOnInit pointermove and touchstart', () => {
      spyOn(directive, 'unsub');
      const touchEvent = new Event('touchstart');
      directive.elementRef.nativeElement.dispatchEvent(touchEvent);
      directive.ngOnInit();
      const pointerMoveEvent = new Event('pointermove');
      directive.elementRef.nativeElement.dispatchEvent(pointerMoveEvent);
      expect(directive.timerSub).toBeDefined();
      expect(directive.unsub).toHaveBeenCalled();
    });
  });
});
