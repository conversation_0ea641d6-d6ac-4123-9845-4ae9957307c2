import { AvatarPaths, MessageCategory, fallbackImage, userType } from 'src/app/constants/constants';
import { AvatarDirective } from './avatar.directive';

describe('AvatarDirective', () => {
  let directive: AvatarDirective;

  // Helper function to get the path portion from a URL
  const getPathFromUrl = (url: string): string => {
    try {
      // Handle both full URLs and relative paths
      return url.includes('http') ? new URL(url).pathname.slice(1) : url;
    } catch {
      return url;
    }
  };

  beforeEach(() => {
    directive = new AvatarDirective();
  });

  describe('ngOnChanges', () => {
    it('should set default PDG image for PDG message category', () => {
      directive.messageCategory = MessageCategory.PDG;
      expect(getPathFromUrl(directive.avatarSrc)).toBe(AvatarPaths.PDG.replace(/^\.\//, ''));
    });

    it('should set default message group image for MESSAGE_GROUP category', () => {
      directive.messageCategory = MessageCategory.MESSAGE_GROUP;
      expect(getPathFromUrl(directive.avatarSrc)).toBe(AvatarPaths.MESSAGE_GROUP.replace(/^\.\//, ''));
    });

    it('should set default broadcast image for BROADCAST category', () => {
      directive.messageCategory = MessageCategory.BROADCAST;
      expect(getPathFromUrl(directive.avatarSrc)).toBe(AvatarPaths.BROADCAST.replace(/^\.\//, ''));
    });

    it('should set default masked image for MASKED category', () => {
      directive.messageCategory = MessageCategory.MASKED;
      expect(getPathFromUrl(directive.avatarSrc)).toBe(AvatarPaths.MASKED.replace(/^\.\//, ''));
    });

    it('should set patient avatar for PATIENT chat avatar option', () => {
      directive.avatar = 'patient';
      expect(getPathFromUrl(directive.avatarSrc)).toContain(AvatarPaths.PATIENT.replace(/^\.\//, ''));
    });

    it('should set clinician avatar for CLINICIAN chat avatar option', () => {
      directive.avatar = 'clinician';
      expect(getPathFromUrl(directive.avatarSrc)).toContain(AvatarPaths.CLINICIAN.replace(/^\.\//, ''));
    });

    it('should set custom avatar when provided', () => {
      const customAvatar = 'custom-avatar.jpg';
      directive.avatar = customAvatar;
      expect(getPathFromUrl(directive.avatarSrc)).toBe(customAvatar);
    });
    it('should set custom thumbnail when provided', () => {
      const customAvatar = 'custom-avatar.jpg';
      directive.thumbnail = true;
      directive.hasFullAvatarUrl = false;
      directive.avatar = customAvatar;
      expect(getPathFromUrl(directive.avatarSrc)).toContain(`/thumbs/${customAvatar}`);
    });
    it('should set custom avatar for staff', () => {
      directive.userType = userType.staff;
      expect(getPathFromUrl(directive.avatarSrc)).toContain(AvatarPaths.CLINICIAN.replace(/^\.\//, ''));
    });
    it('should set custom avatar for patient', () => {
      directive.userType = userType.patient;
      expect(getPathFromUrl(directive.avatarSrc)).toContain(AvatarPaths.PATIENT.replace(/^\.\//, ''));
    });
    it('should set custom avatar for alternate contact', () => {
      directive.userType = userType.alternateContact;
      expect(getPathFromUrl(directive.avatarSrc)).toContain(AvatarPaths.PATIENT.replace(/^\.\//, ''));
    });
    it('should handle error event and update the target image source', () => {
      const mockEvent = {
        target: {
          src: 'invalid-image.jpg'
        }
      } as unknown as Event;
      directive.onImageError(mockEvent);
      expect((mockEvent.target as HTMLImageElement).src).toBe(fallbackImage);
    });
  });
});
