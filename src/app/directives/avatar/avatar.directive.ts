import { Directive, Input } from '@angular/core';
import { fallbackImage, MessageCategory, AvatarPaths, userType } from 'src/app/constants/constants';
import { isPresent } from 'src/app/utils/utils';
import { environment } from 'src/environments/environment';

@Directive({
  selector: '[appAvatar]',
  exportAs: 'avatarDirective'
})
/**
 * Directive to handle avatar image generation and error handling.
 *
 * This directive generates the source URL for avatar images based on the provided avatar type,
 * message category, and user type. It also handles image loading errors by providing a fallback image.
 */
export class AvatarDirective {
  @Input() avatar: string;
  @Input() messageCategory: MessageCategory;
  @Input() defaultImage = fallbackImage;
  @Input() userType: number;
  @Input() hasFullAvatarUrl = true;
  @Input() thumbnail = false;
  private readonly avatarPaths = {
    patient: AvatarPaths.PATIENT,
    clinician: AvatarPaths.CLINICIAN,
    [MessageCategory.BROADCAST]: AvatarPaths.BROADCAST,
    [MessageCategory.MASKED]: AvatarPaths.MASKED,
    [MessageCategory.PDG]: AvatarPaths.PDG,
    [MessageCategory.MESSAGE_GROUP]: AvatarPaths.MESSAGE_GROUP
  };

  constructor() {}

  get avatarSrc() {
    return this.getAvatarSrc(this.avatar, this.messageCategory, this.userType);
  }
  /**
   * Generates the avatar source URL based on the provided avatar type, message category, and user type.
   *
   * @param avatar - The avatar identifier, which can be a specific avatar type (e.g., 'clinician', 'patient') or a custom avatar string.
   * @param messageCategory - The category of the message, which determines if a predefined avatar path should be used.
   * @param userTypeParam - A numeric value representing the user type (e.g., patient or alternate contact).
   *
   * @returns The URL string for the avatar image. This can be a predefined path for specific message categories,
   *          a default avatar URL for known user types, or a custom avatar URL if provided.
   */
  private getAvatarSrc(avatar: string, messageCategory: MessageCategory, userTypeParam: number): string {
    if ([MessageCategory.MESSAGE_GROUP, MessageCategory.PDG, MessageCategory.BROADCAST, MessageCategory.MASKED].includes(messageCategory)) {
      return this.avatarPaths[messageCategory];
    }
    const avatarUrlDefault = `${environment.apiBasePath}/avatars/`;
    if (['clinician', 'patient'].includes(avatar)) {
      return `${avatarUrlDefault}${this.avatarPaths[avatar]}`;
    }
    if (isPresent(avatar)) {
      const avatarUrl = this.thumbnail ? `${avatarUrlDefault}thumbs/` : avatarUrlDefault;
      return `${this.hasFullAvatarUrl ? '' : avatarUrl}${avatar}`;
    }
    const defaultAvatar =
      +userTypeParam === userType.patient || +userTypeParam === userType.alternateContact ? this.avatarPaths.patient : this.avatarPaths.clinician;
    return `${avatarUrlDefault}${defaultAvatar}`;
  }
  /**
   * Fallback function to handle image loading errors.
   *
   * This function sets the source of the image to a fallback image in case of an error.
   *
   * @param event - The error event triggered when the image fails to load.
   */
  public onImageError(event: Event) {
    // eslint-disable-next-line no-param-reassign
    (event.target as HTMLImageElement).src = fallbackImage;
  }
}
