import { TestBed } from '@angular/core/testing';
import { ElementRef, Renderer2, SimpleChanges } from '@angular/core';
import { OutOfOfficeStatusDirective } from './outofoffice-status.directive';
import { CommonService } from 'src/app/services/common-service/common.service';
import { of } from 'rxjs';

describe('OutOfOfficeStatusDirective', () => {
  let directive: OutOfOfficeStatusDirective;
  let mockElementRef: ElementRef;
  let mockRenderer: Renderer2;
  let mockCommonService: CommonService;
  let oooInfo;

  beforeEach(() => {
    mockElementRef = new ElementRef(document.createElement('div'));
    mockRenderer = jasmine.createSpyObj('Renderer2', [
      'createElement',
      'addClass',
      'setStyle',
      'insertBefore',
      'setAttribute',
      'removeChild',
    ]);
    
    // Create a mock for CommonService and mock the getTranslateDataPipe method
    mockCommonService = jasmine.createSpyObj('CommonService', ['getTranslateDataPipe']);
    
    // Mock the return value of getTranslateDataPipe to return an observable
    mockCommonService.getTranslateDataPipe = jasmine.createSpy().and.returnValue(of('some translation text'));
  
    directive = new OutOfOfficeStatusDirective(mockElementRef, mockRenderer as any, mockCommonService);
  
    oooInfo = {
      isOutOfOffice: true,
      message: 'Out of Office',
      startDateTime: '2024-11-29T10:00:00Z',
      endDateTime: '2024-11-30T10:00:00Z',
      endDatePassed: false,
    };
    directive.oooInfo = oooInfo;
  });
  ;
  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  it('should update oooInfo on ngOnChanges and call addBusySymbol', () => {
    spyOn(directive, 'addBusySymbol').and.callThrough();
    const changes: SimpleChanges = {
      oooInfo: { currentValue: oooInfo, previousValue: null, firstChange: true, isFirstChange: () => true },
    };
    directive.ngOnChanges(changes);
    expect(directive.oooInfo).toEqual(oooInfo);
    expect(directive.addBusySymbol).toHaveBeenCalled();
  });

  it('should call removeBusySymbol', () => {
    const mockElement = document.createElement('div');
    const mockParent = document.createElement('div');
    mockParent.appendChild(mockElement);
    mockElementRef.nativeElement = mockElement;

    spyOn(directive, 'removeBusySymbol').and.callThrough();

    directive.removeBusySymbol(mockElement);

    expect(directive.removeBusySymbol).toHaveBeenCalledWith(mockElement);
  });

  it('should emit badgeStatusInfo in createBusySymbol if returnOnly is true', () => {
    directive.returnOnly = true;
    spyOn(directive.badgeStatusInfo, 'emit');

    const badgeStatusInfo = { status: true, color: 'red', message: 'Out of Office' };
    directive.createBusySymbol(mockElementRef.nativeElement, badgeStatusInfo);

    expect(directive.badgeStatusInfo.emit).toHaveBeenCalledWith(badgeStatusInfo);
  });

});