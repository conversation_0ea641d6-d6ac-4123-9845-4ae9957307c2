import { Directive, ElementRef, Renderer2, Input, OnInit, OnChanges, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { isBlank, getOooStatusBasedOnTime, isPresent } from 'src/app/utils/utils';
import { OutOfOfficeInfo } from 'src/app/interfaces/login';
import { CommonService } from 'src/app/services/common-service/common.service';

@Directive({
  selector: '[outOfOfficeStatus]'
})
export class OutOfOfficeStatusDirective implements OnChanges {
  @Input() oooInfo: OutOfOfficeInfo;
  @Input() customClass = '';
  @Input() returnOnly = false;
  @Output() badgeStatusInfo = new EventEmitter<any>();
  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private commonService: CommonService
  ) {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.oooInfo?.currentValue) {
      this.oooInfo = changes.oooInfo.currentValue;
      this.removeBusySymbol(this.el.nativeElement);
      this.addBusySymbol();
    }
  }

  addBusySymbol() {
    const avatarContainer = this.el.nativeElement;
    if (avatarContainer && isPresent(this.oooInfo)) {
      const badgeStatusInfo = getOooStatusBasedOnTime(this.oooInfo);
      if (this.oooInfo && (this.oooInfo.isOutOfOffice || !isBlank(this.oooInfo.message)) && badgeStatusInfo.status) {
        this.createBusySymbol(avatarContainer, badgeStatusInfo);
        if (badgeStatusInfo.timeToEndOutOfOfficeInfo > 0) {
          setTimeout(() => {
            this.removeBusySymbol(avatarContainer);
          }, badgeStatusInfo.timeToEndOutOfOfficeInfo);
        }
      } else if (badgeStatusInfo.timeToStartOutOfOfficeInfo > 0) {
        setTimeout(() => {
          this.addBusySymbol();
        }, badgeStatusInfo.timeToStartOutOfOfficeInfo);
      }
    }
  }
  createBusySymbol(container: HTMLElement, badgeStatusInfo) {
    if (this.returnOnly) {
      this.badgeStatusInfo.emit(badgeStatusInfo);
      return;
    }
    this.removeBusySymbol(container);
    const oooBadge = this.renderer.createElement('ion-icon');
    this.renderer.setAttribute(oooBadge, 'name', 'remove-circle');
    if (this.customClass) {
      this.renderer.addClass(oooBadge, this.customClass);
    } else {
      this.renderer.addClass(oooBadge, 'red-badge');
    }
    this.renderer.setStyle(oooBadge, 'color', badgeStatusInfo.color);
    this.renderer.insertBefore(container.parentElement, oooBadge, container.nextSibling);
    this.commonService.getTranslateDataPipe(badgeStatusInfo.message).subscribe((translatedMessage) => {
      this.renderer.setAttribute(oooBadge, 'title', translatedMessage);
    });
  }
  removeBusySymbol(container: HTMLElement) {
    if (!container || !container.parentElement) {
      return;
    }
    const ionIcon = Array.from(container.parentElement.querySelectorAll('ion-icon')).find((icon) => icon.getAttribute('name') === 'remove-circle');
    if (ionIcon) {
      this.renderer.removeChild(container.parentElement, ionIcon);
    }
  }
}
