import { Directive, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';

interface InputTarget {
  value: string;
  selectionStart: number;
  setSelectionRange: (start: number, end: number) => void;
}

interface InputEvent {
  target: InputTarget;
  data: string;
  inputType: string;
}

@Directive({
  selector: '[appDateFormat]'
})
export class DateFormatDirective {
  private static readonly MAX_DATE_LENGTH = 10;

  constructor(private readonly control: NgControl) {}

  @HostListener('ionInput', ['$event'])
  onInput(event: InputEvent): void {
    this.formatDateInput(event);
  }

  private formatDateInput(event: InputEvent): void {
    const value = event.target.value ?? '';
    const cursor = event.target.selectionStart;
    const prev = this.control.value ?? '';
    const inputChar = event.data;

    // Handle duplicate slashes
    if (this.hasDuplicateSlashes(value)) {
      const cleanedValue = this.removeDuplicateSlashes(value);
      const cursorAdjustment = cleanedValue.length - value.length;
      const newCursorPosition = cursor + cursorAdjustment;
      this.updateValueAndCursor(cleanedValue, newCursorPosition, event.target);
      return;
    }

    // Handle backspace operations
    if (this.isBackspaceOperation(event)) {
      this.handleBackspace(event, prev, cursor);
      return;
    }

    // Process and format the input
    this.processInput(value, prev, cursor, inputChar, event.target);
  }

  private hasDuplicateSlashes(value: string): boolean {
    return value.includes('//');
  }

  private removeDuplicateSlashes(value: string): string {
    return value.replace(/\/+/g, '/');
  }

  private isBackspaceOperation(event: InputEvent): boolean {
    return event.inputType === 'deleteContentBackward';
  }

  private handleBackspace(event: InputEvent, prev: string, cursor: number): void {
    let value: string;
    let newCursor: number;

    if (this.isBackspaceAfterSlash(prev, cursor)) {
      // Remove slash and previous digit
      value = prev.slice(0, cursor - 2) + prev.slice(cursor);
      newCursor = cursor - 2;
    } else {
      // Regular backspace
      value = prev.slice(0, cursor - 1) + prev.slice(cursor);
      newCursor = cursor - 1;
    }

    this.updateValueAndCursor(value, newCursor, event.target);
  }

  private isBackspaceAfterSlash(prev: string, cursor: number): boolean {
    return prev[cursor - 1] === '/';
  }

  private processInput(value: string, prev: string, cursor: number, inputChar: string, target: InputTarget): void {
    // Clean and format the input
    const formattedValue = this.formatValue(value);

    // Apply length limit
    const finalValue = this.applyLengthLimit(formattedValue);

    // Update control value
    this.updateControlValue(finalValue);

    // Handle cursor positioning
    this.handleCursorPosition(finalValue, prev, cursor, inputChar, target);
  }

  private formatValue(value: string): string {
    const cleanedValue = this.cleanInput(value);
    return this.autoInsertSlashes(cleanedValue);
  }

  private cleanInput(value: string): string {
    const parts = value.split('/');
    const cleanParts = parts.map((part: string) => part.replace(/\D/g, ''));
    return this.rebuildValueFromParts(cleanParts);
  }

  private rebuildValueFromParts(cleanParts: string[]): string {
    if (cleanParts.length === 1) {
      return cleanParts[0];
    }
    if (cleanParts.length === 2) {
      return `${cleanParts[0]}/${cleanParts[1]}`;
    }
    if (cleanParts.length >= 3) {
      return `${cleanParts[0]}/${cleanParts[1]}/${cleanParts[2]}`;
    }
    return '';
  }

  private autoInsertSlashes(value: string): string {
    let result = value;

    if (result.length > 2 && !this.hasSlashAtPosition(result, 2)) {
      result = `${result.slice(0, 2)}/${result.slice(2)}`;
    }

    if (result.length > 5 && !this.hasSlashAtPosition(result, 5)) {
      result = `${result.slice(0, 5)}/${result.slice(5)}`;
    }

    return result;
  }

  private hasSlashAtPosition(value: string, position: number): boolean {
    return value.charAt(position) === '/';
  }

  private applyLengthLimit(value: string): string {
    return value.slice(0, DateFormatDirective.MAX_DATE_LENGTH);
  }

  private updateControlValue(value: string): void {
    this.control.control?.setValue(value);
  }

  private updateValueAndCursor(value: string, cursorPosition: number, target: InputTarget): void {
    this.updateControlValue(value);
    setTimeout(() => target.setSelectionRange(cursorPosition, cursorPosition));
  }

  private handleCursorPosition(value: string, prev: string, cursor: number, inputChar: string, target: InputTarget): void {
    setTimeout(() => {
      const newPos = this.calculateNewCursorPosition(value, prev, cursor, inputChar);
      target.setSelectionRange(newPos, newPos);
    });
  }

  private calculateNewCursorPosition(value: string, prev: string, cursor: number, inputChar: string): number {
    if (this.wasSlashAutoInserted(value, prev, inputChar)) {
      return cursor + 1;
    }
    return cursor;
  }

  private wasSlashAutoInserted(value: string, prev: string, inputChar: string): boolean {
    return (value.length === 3 && prev.length === 2 && inputChar !== '/') || (value.length === 6 && prev.length === 5 && inputChar !== '/');
  }
}
