import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[appNumericOnly]'
})
export class NumericOnlyDirective {

  constructor(private el: ElementRef) { }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    // Allow: Delete, Backspace, Tab, Escape, Enter, navigation keys (except up/down arrows)
    if (
      event.key === 'Delete' ||
      event.key === 'Backspace' ||
      event.key === 'Tab' ||
      event.key === 'Escape' ||
      event.key === 'Enter' ||
      event.key === 'Home' ||
      event.key === 'End' ||
      event.key === 'ArrowLeft' ||
      event.key === 'ArrowRight'
    ) {
      return true;
    }

    // Block up/down arrow keys
    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      event.preventDefault();
      return false;
    }

    // Allow only numbers (0-9)
    const isNumber = /^[0-9]$/.test(event.key);
    if (!isNumber) {
      event.preventDefault();
      return false;
    }

    return true;
  }

  // For Android and iOS mobile support
  @HostListener('input', ['$event'])
  onInput(event: InputEvent) {
    const input = event.target as HTMLInputElement;
    const sanitized = input.value.replace(/[^0-9]/g, '');
    
    if (sanitized !== input.value) {
      input.value = sanitized;
      const inputEvent = new Event('input', { bubbles: true });
      input.dispatchEvent(inputEvent);
    }
  }

  // Optional: You can add paste event handling to strip non-numeric characters
  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent) {
    event.preventDefault();
    
    const pastedText = event.clipboardData?.getData('text/plain');
    
    if (pastedText) {
      const sanitized = pastedText.replace(/[^0-9]/g, '');
      
      document.execCommand('insertText', false, sanitized);
    }
  }
}