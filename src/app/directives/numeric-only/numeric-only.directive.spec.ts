/* eslint-disable @typescript-eslint/no-explicit-any */
import { ElementRef } from '@angular/core';
import { NumericOnlyDirective } from './numeric-only.directive';

describe('NumericOnlyDirective', () => {
  let directive: NumericOnlyDirective;
  let mockElementRef: ElementRef;

  beforeEach(() => {
    mockElementRef = new ElementRef(document.createElement('input'));
    directive = new NumericOnlyDirective(mockElementRef);
  });

  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  describe('onKeyDown', () => {
    it('should allow Delete, Backspace, Tab, Escape, Enter, Home, End, ArrowLeft, ArrowRight keys', () => {
      const allowedKeys = ['Delete', 'Backspace', 'Tab', 'Escape', 'Enter', 'Home', 'End', 'ArrowLeft', 'ArrowRight'];

      allowedKeys.forEach((key) => {
        // Create a simple mock event object with only the properties we need
        const event = {
          key,
          preventDefault: jasmine.createSpy('preventDefault')
        };

        const result = directive.onKeyDown(event as any);
        expect(result).toBe(true);
        expect(event.preventDefault).not.toHaveBeenCalled();
      });
    });

    it('should block ArrowUp and ArrowDown keys', () => {
      const blockedKeys = ['ArrowUp', 'ArrowDown'];

      blockedKeys.forEach((key) => {
        const event = {
          key,
          preventDefault: jasmine.createSpy('preventDefault')
        };

        const result = directive.onKeyDown(event as any);
        expect(result).toBe(false);
        expect(event.preventDefault).toHaveBeenCalled();
      });
    });

    it('should allow numeric keys (0-9)', () => {
      const numericKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

      numericKeys.forEach((key) => {
        const event = {
          key,
          preventDefault: jasmine.createSpy('preventDefault')
        };

        const result = directive.onKeyDown(event as any);
        expect(result).toBe(true);
        expect(event.preventDefault).not.toHaveBeenCalled();
      });
    });

    it('should block non-numeric keys', () => {
      const nonNumericKeys = ['a', 'Z', '@', '.', '-', 'F1', ' '];

      nonNumericKeys.forEach((key) => {
        const event = {
          key,
          preventDefault: jasmine.createSpy('preventDefault')
        };

        const result = directive.onKeyDown(event as any);
        expect(result).toBe(false);
        expect(event.preventDefault).toHaveBeenCalled();
      });
    });
  });

  describe('onPaste', () => {
    it('should always prevent default and sanitize pasted content', () => {
      const pastedText = '123abc456';
      const event = {
        preventDefault: jasmine.createSpy('preventDefault'),
        clipboardData: {
          getData: jasmine.createSpy('getData').and.returnValue(pastedText)
        }
      };

      // Mock document.execCommand
      spyOn(document, 'execCommand');

      directive.onPaste(event as any);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(event.clipboardData.getData).toHaveBeenCalledWith('text/plain');
      expect(document.execCommand).toHaveBeenCalledWith('insertText', false, '123456');
    });

    it('should handle empty clipboard data safely', () => {
      const event = {
        preventDefault: jasmine.createSpy('preventDefault'),
        clipboardData: {
          getData: jasmine.createSpy('getData').and.returnValue(null)
        }
      };

      // Mock document.execCommand
      spyOn(document, 'execCommand');

      directive.onPaste(event as any);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(document.execCommand).not.toHaveBeenCalled();
    });
  });

  describe('onInput', () => {
    it('should sanitize input to only allow numbers', () => {
      const input = document.createElement('input');
      input.value = '123abc456';

      const dispatchSpy = spyOn(input, 'dispatchEvent');

      const event = {
        target: input
      };

      directive.onInput(event as any);

      expect(input.value).toBe('123456');
      expect(dispatchSpy).toHaveBeenCalled();
    });

    it('should not modify input if it contains only numbers', () => {
      const input = document.createElement('input');
      input.value = '123456';

      const dispatchSpy = spyOn(input, 'dispatchEvent');

      const event = {
        target: input
      };

      directive.onInput(event as any);

      expect(input.value).toBe('123456');
      expect(dispatchSpy).not.toHaveBeenCalled();
    });
  });
});
