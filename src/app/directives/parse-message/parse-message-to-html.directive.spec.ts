import { ParseMessageToHtmlDirective } from './parse-message-to-html.directive';
import { ElementRef } from '@angular/core';

describe('ParseMessageToHtmlDirective', () => {
  let directive: ParseMessageToHtmlDirective;
  it('should create an instance', () => {
    const ele: ElementRef<HTMLElement> = { nativeElement: { innerHTML: '' } } as any;
    directive = new ParseMessageToHtmlDirective(ele);
    expect(directive).toBeTruthy();
  });

  it('should call on change: image', () => {
    const ele: ElementRef<HTMLElement> = { nativeElement: { innerHTML: '' } } as any;
    directive = new ParseMessageToHtmlDirective(ele);
    directive.dirParseMessageToHtml = '<img data-mediaType="image"';
    directive?.ngOnChanges();
    expect(directive?.ngOnChanges).toBeTruthy();
  });
  it('should call on change: video', () => {
    const ele: ElementRef<HTMLElement> = { nativeElement: { innerHTML: '' } } as any;
    directive = new ParseMessageToHtmlDirective(ele);
    directive.dirParseMessageToHtml = '<img data-mediaType="video"';
    directive?.ngOnChanges();
    expect(directive?.ngOnChanges).toBeTruthy();
  });
  it('should call on change: pdf', () => {
    const ele: ElementRef<HTMLElement> = { nativeElement: { innerHTML: '' } } as any;
    directive = new ParseMessageToHtmlDirective(ele);
    directive.dirParseMessageToHtml = '<img data-mediaType="pdf"';
    directive?.ngOnChanges();
    expect(directive?.ngOnChanges).toBeTruthy();
  });
  it('should call on change: document', () => {
    const ele: ElementRef<HTMLElement> = { nativeElement: { innerHTML: '' } } as any;
    directive = new ParseMessageToHtmlDirective(ele);
    directive.dirParseMessageToHtml = '<img data-mediaType="document"';
    directive?.ngOnChanges();
    expect(directive?.ngOnChanges).toBeTruthy();
  });
  it('should call on change: audio', () => {
    const ele: ElementRef<HTMLElement> = { nativeElement: { innerHTML: '' } } as any;
    directive = new ParseMessageToHtmlDirective(ele);
    directive.dirParseMessageToHtml = '<audio controls><source src=/';
    directive?.ngOnChanges();
    expect(directive?.ngOnChanges).toBeTruthy();
  });
});
