/* eslint-disable @angular-eslint/directive-selector */
import { Directive, Input, ElementRef, OnChanges } from '@angular/core';

@Directive({
  selector: '[dirParseMessageToHtml]'
})
export class ParseMessageToHtmlDirective implements OnChanges {
  @Input() dirParseMessageToHtml: string;
  constructor(private elRef: ElementRef) {}
  ngOnChanges(): void {
    if (this.dirParseMessageToHtml) {
      if (
        this.dirParseMessageToHtml.match(/<img data-mediaType='image'/i) ||
        this.dirParseMessageToHtml.match(/<img data-mediaType="image"/i) ||
        this.dirParseMessageToHtml.match(/<img src=/i)
      ) {
        this.dirParseMessageToHtml = '<ion-icon name="image-sharp"></ion-icon> Image';
      } else if (
        this.dirParseMessageToHtml.match(/<img data-mediaType='video'/i) ||
        this.dirParseMessageToHtml.match(/<img data-mediaType="video"/i) ||
        this.dirParseMessageToHtml.match(/<video ><source src =/i)
      ) {
        this.dirParseMessageToHtml = '<ion-icon name="videocam-sharp"></ion-icon> Video';
      } else if (
        this.dirParseMessageToHtml.match(/<img data-mediaType='pdf'/i) ||
        this.dirParseMessageToHtml.match(/<img data-mediaType="pdf"/i)
      ) {
        this.dirParseMessageToHtml = '<ion-icon name="document-sharp"></ion-icon> Pdf';
      } else if (
        this.dirParseMessageToHtml.match(/<img data-mediaType='excel'/i) ||
        this.dirParseMessageToHtml.match(/<img data-mediaType="excel"/i)
      ) {
        this.dirParseMessageToHtml = '<ion-icon name="document-text-sharp"></ion-icon> Excel';
      } else if (
        this.dirParseMessageToHtml.match(/<img data-mediaType='document'/i) ||
        this.dirParseMessageToHtml.match(/<img data-mediaType="document"/i) ||
        this.dirParseMessageToHtml.match(/<img data-mediaType=/i) ||
        this.dirParseMessageToHtml.match(/<img data-src=/i)
      ) {
        this.dirParseMessageToHtml = '<ion-icon name="document-text-sharp"></ion-icon> Document';
      } else if (this.dirParseMessageToHtml.match(/<audio controls><source src=/i)) {
        this.dirParseMessageToHtml = '<ion-icon name="document-sharp"></ion-icon> Audio';
      }
      this.elRef.nativeElement.innerHTML = this.dirParseMessageToHtml;
    }
  }
}
