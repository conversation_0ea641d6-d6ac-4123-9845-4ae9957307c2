import { Directive, HostListener, ElementRef, Renderer2, ChangeDetectorRef } from '@angular/core';

@Directive({
  selector: '[appTrimSpaces]'
})
export class TrimSpacesDirective {
  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private cdr: ChangeDetectorRef
  ) {}
  @HostListener('ionBlur')
  onBlur(): void {
    const trimmedValue = this.el.nativeElement.value.trim();
    this.renderer.setProperty(this.el.nativeElement, 'value', trimmedValue);
    this.el.nativeElement.dispatchEvent(new Event('ionInput'));
    this.cdr.detectChanges();
  }
}
