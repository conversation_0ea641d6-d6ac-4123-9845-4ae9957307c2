import { ElementRef, Renderer2, ChangeDetectorRef, Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { TrimSpacesDirective } from 'src/app/directives/trim-spaces/trim-spaces.directive';

@Component({
  template: '<ion-input type="text" appTrimSpaces></ion-input>'
})
class TestComponent {}

describe('TrimSpacesDirective', () => {
  let fixture: ComponentFixture<TestComponent>;
  let inputEl: ElementRef;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [TestComponent, TrimSpacesDirective],
      providers: [Renderer2, ChangeDetectorRef]
    });
    fixture = TestBed.createComponent(TestComponent);
    inputEl = fixture.debugElement.query(By.directive(TrimSpacesDirective));
    fixture.detectChanges();
  });

  it('should trim spaces when ionBlur event is triggered', () => {
    const el = inputEl.nativeElement as HTMLInputElement;
    el.value = '  some text with spaces  ';
    inputEl.nativeElement.dispatchEvent(new Event('ionBlur'));
    expect(el.value).toBe('some text with spaces');
  });

  it('should dispatch ionInput event after trimming', () => {
    const el = inputEl.nativeElement as HTMLInputElement;
    const eventSpy = spyOn(el, 'dispatchEvent');
    el.value = '  another test  ';
    inputEl.nativeElement.dispatchEvent(new Event('ionBlur'));
    expect(eventSpy).toHaveBeenCalledWith(new Event('ionInput'));
  });
});
