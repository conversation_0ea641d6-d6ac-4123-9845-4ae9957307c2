<div class="common-search-box">
    <ion-grid>
        <ion-row class="ion-align-items-center">
            <div class="search-container">
                <form [formGroup]="searchForm" id="search-form" class="common-start-padding">
                    <!-- TODO! CHP-3596 -->
                    <ion-input type="search" id="search-recipients"
                        placeholder="{{ searchPleaseHolder ? (searchPleaseHolder | translate) : ('PLACEHOLDERS.SEARCH_HERE' | translate) }}"
                        formControlName="search" name="search" class="ion-input-style set-border-bg-color"
                        [(ngModel)]="searchValue" mode="md" autocapitalize="on">
                    </ion-input>
                </form>
            </div>
            <div class="action-buttons">
                <ion-button expand="block" (click)="search()" id="staffroles-search"
                    [disabled]="!searchValue||searchValue.length===0">
                    <ion-icon class="search-icon" color="white" src="assets/icon/material-svg/magnify.svg"></ion-icon>
                </ion-button>
                <ion-button expand="block" (click)="reset()" id="refresh-btn">
                    <ion-icon class="refresh-icon" color="white" src="assets/icon/material-svg/refresh.svg"></ion-icon>
                </ion-button>
                <ion-button *ngIf="addPatient" expand="block" (click)="add()" id="add-patient-btn" [disabled]="disableAddPatient">
                    <ion-icon class="add-patient-icon" color="white" src="assets/icon/material-svg/account-plus.svg">
                    </ion-icon>
                </ion-button>
            </div>
        </ion-row>
    </ion-grid>
</div>