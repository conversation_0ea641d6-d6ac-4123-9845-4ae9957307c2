import { SharedService } from './../../services/shared-service/shared.service';
import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { Subscription, Observable } from 'rxjs';

@Component({
  selector: 'app-search-bar-recipients',
  templateUrl: './search-bar-recipients.component.html',
  styleUrls: ['./search-bar-recipients.component.scss']
})
export class SearchBarRecipientsComponent implements OnInit {
  searchForm: UntypedFormGroup;
  @Output() readonly searchAction = new EventEmitter();
  @Input() readonly addPatient: boolean;
  @Input() readonly disableAddPatient: boolean;
  private eventsSubscription: Subscription;
  @Input() events: Observable<void>;
  @Input() readonly tabActive: boolean;
  @Input() searchValue: string;
  @Input() searchPleaseHolder: string;
  constructor(private readonly formBuilder: UntypedFormBuilder, public sharedService: SharedService) {
    this.searchForm = this.formBuilder.group({
      search: [null]
    });
  }

  ngOnInit(): void {
    if (this.tabActive) {
      this.eventsSubscription = this.events.subscribe(() => this.reset());
    }
  }
  search(): void {
    if (this.searchForm.value.search) {
      this.searchAction.emit({ do: 'search', value: this.searchForm.value.search });
    }
  }

  reset(): void {
    this.searchForm.reset();
    this.searchAction.emit({ do: 'reset', value: '' });
  }

  add(): void {
    this.searchAction.emit({ do: 'add', value: '' });
  }
}
