import { SharedService } from './../../services/shared-service/shared.service';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { SearchBarRecipientsComponent } from './search-bar-recipients.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';

describe('SearchBarRecipientsComponent', () => {
  let component: SearchBarRecipientsComponent;
  let fixture: ComponentFixture<SearchBarRecipientsComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SearchBarRecipientsComponent],
      imports: [
        IonicModule.forRoot(),
        ReactiveFormsModule,
        TranslateModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        SharedService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(SearchBarRecipientsComponent);
    component = fixture.componentInstance;
    Object.defineProperty(component, 'tabActive', { value: true });
    Object.defineProperty(component, 'events', { value: of(null) });
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should define search', () => {
    Object.defineProperty(component, 'searchForm', { value: { value: { search: 'abc' } } });
    component.search();
    expect(component.search).toBeTruthy();
  });

  it('should define reset', () => {
    component.reset();
    expect(component.reset).toBeTruthy();
  });

  it('should define add', () => {
    component.add();
    expect(component.add).toBeTruthy();
  });
});
