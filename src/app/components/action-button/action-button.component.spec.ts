import { NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { NgxPermissionsService } from 'ngx-permissions';
import { AuthService } from './../../services/auth-service/auth.service';
import { AuthGuard } from './../../services/auth-guard/auth.guard';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AngularDelegate, IonicModule, ModalController, PopoverController } from '@ionic/angular';
import { TestConstants } from 'src/app/constants/test-constants';
import { ActionButtonComponent } from './action-button.component';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { PermissionService } from 'src/app/services/permission-service/permission.service';

describe('ActionButtonComponent', () => {
  const modalSpy = TestConstants.modalSpy;
  let component: ActionButtonComponent;
  let fixture: ComponentFixture<ActionButtonComponent>;
  let sharedService: SharedService;
  let modalController: ModalController;
  let permissionService: PermissionService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [ActionButtonComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        SharedModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      providers: [
        SharedService,
        AuthGuard,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        PermissionService,
        Idle,
        IdleExpiry,
        ModalController,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        PopoverController,
        AngularDelegate
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    permissionService = TestBed.inject(PermissionService);
    spyOn(permissionService, 'showNewMessage').and.returnValue(true);
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    sharedService = TestBed.inject(SharedService);
    sharedService.userPermissions = '';
    Object.defineProperty(sharedService, 'userData', { value: TestConstants.userData });
    fixture = TestBed.createComponent(ActionButtonComponent);
    component = fixture.componentInstance;
    component.button = { type: '', permission: true, buttonType: '' };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('Define action click', () => {
    component.button.type = 'new-chat';
    component.onActionBtnClick();
    expect(component.onActionBtnClick).toBeTruthy();
  });
  it('action click default action', () => {
    component.button.type = 'documents';
    component.onActionBtnClick();
    expect(component.onActionBtnClick).toBeTruthy();
  });
  it('execute onInit for new chat', () => {
    component.button.type = 'new-chat';
    sharedService.configValuesUpdated.next(null);
    component.ngOnInit();
    expect(component.ngOnInit).toBeTruthy();
  });
});
