<div class="common-green-button-icon {{button.customClass}}" *ngIf="button.permission && button.buttonType !== 'fab-button'" [ngClass]="{'chat-with-content':button.type === 'new-chat'}">
    <button id="chatwith-button-footer" type="button" (click)="onActionBtnClick()" tappable>
        <span><i><img src="../../assets/icon/button/{{button.icon}}" [alt]="button.label"></i>
            <label class="action-btn-label" translate>{{button.label}}</label>
        </span>
    </button>
</div>
<!-- TODO! CHP-3597 -->
<ion-fab vertical="bottom" horizontal="end" slot="fixed"
    *ngIf="button.permission && button?.buttonType === 'fab-button'" [class]="button?.customClass">
    <ion-fab-button [color]="button?.colorTheme || 'de-york'" (click)="onActionBtnClick()">
        <ion-icon [name]="button?.buttonIcon || 'add'" *ngIf="!button?.iconCustom"></ion-icon>
        <ion-icon [src]="button?.buttonIcon" *ngIf="button?.iconCustom"></ion-icon>
    </ion-fab-button>
</ion-fab>