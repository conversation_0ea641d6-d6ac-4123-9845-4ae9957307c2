import { Component, EventEmitter, OnInit, Input, Output } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { ChooseRecipientsPage } from 'src/app/pages/message-center/choose-recipients/choose-recipients.page';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { ChoosePatientComponent } from 'src/app/pages/message-center/choose-patient/choose-patient.component';
import { AdmissionComponent } from '../admission/admission.component';

@Component({
  selector: 'app-action-button',
  templateUrl: './action-button.component.html',
  styleUrls: ['./action-button.component.scss']
})
export class ActionButtonComponent implements OnInit {
  @Input() button: any = {};
  @Output() readonly buttonClick: EventEmitter<any> = new EventEmitter();
  constructor(private readonly sharedService: SharedService, private readonly permissionService: PermissionService) {}

  ngOnInit(): void {
    // TODO! CHP-3597
    if (this.button.type === 'new-chat') {
      this.button = {
        ...this.button,
        type: 'new-chat',
        label: 'BUTTONS.START_NEW_CHAT',
        icon: 'new-chat-icon.png',
        permission: this.permissionService.showNewMessage()
      };
    }
    this.sharedService.configValuesUpdated.subscribe(() => {
      if (this.button.type === 'new-chat') {
        this.button.permission = this.permissionService.showNewMessage();
      }
    });
  }
  onActionBtnClick(): any {
    if (this.button.type === 'new-chat') {
      this.sharedService.newMessageAction(ChooseRecipientsPage, ChoosePatientComponent, AdmissionComponent);
    } else {
      this.buttonClick.emit();
    }
  }
}
