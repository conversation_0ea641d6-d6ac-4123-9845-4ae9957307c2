<ion-menu side="start" menuId="first" id="first" contentId="main" class="menu-theme">
  <ion-content>
    <div class="left-menu-user-details-wrap">
      <div class="left-menu-user-image" *ngIf="sharedService.userData">
        <img #profileImage [attr.src]="sharedService.userData?.profileImageThumbUrl" alt="profile-image"
          (error)="profileImage.src = errorImage" outOfOfficeStatus [oooInfo]="sharedService.userData.oooInfo"/>
      </div>
      <div class="left-menu-user-details">
        <h2>{{ sharedService.userData?.displayName || "" }}</h2>
        <h3>{{ sharedService.userData?.roleName || "" }}</h3>
      </div>
    </div>

    <ion-list>
      <ion-menu-toggle mode="md">
        <ion-item detail="false" lines="none" button id="home" (click)="navigatePage(homeRoute)" mode="md"
          class="set-border-top">
          <ion-icon src="assets/icon/material-svg/home-outline.svg" slot="start"></ion-icon>
          {{ "MENU.HOME" | translate }}
          <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
        </ion-item>
      </ion-menu-toggle>
      <!-- TODO! CHP-3576 -->
      <ion-accordion-group class="set-border-top" mode="md" *ngIf="enableMsgCenter" value="MessageCenter"
        #messageCenterAccordionGroup>
        <ion-accordion value="{{ 'MENU.MESSAGE_CENTER' | translate }}" mode="md">
          <ion-item slot="header" class="setAccordionIconColor" mode="md" id="message-center">
            <ion-icon src="assets/icon/material-svg/email.svg" slot="start"></ion-icon>
            <ion-label>{{ "MENU.MESSAGE_CENTER" | translate }}</ion-label>
          </ion-item>

          <ion-list slot="content" class="ion-no-padding sub-menu-bg">
            <ion-list class="ion-no-padding sub-menu-bg">
              <ion-item *ngIf="newMessgeLinkShow" tappable (click)="newMessage()" detail="false" lines="none" id="new-message"
                class="sub-menu-bg">
                <ion-icon src="assets/icon/material-svg/message-text.svg" slot="start"></ion-icon>
                {{ "MENU.NEW_MESSAGE" | translate }}
                <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
              </ion-item>

              <ion-item tappable detail="false" lines="none" id="active-message"
                (click)="navigatePage(activeMessagesRoute); closeAccordion();" class="sub-menu-bg">
                <ion-icon src="assets/icon/material-svg/message.svg" slot="start"></ion-icon>
                {{ "MENU.ACTIVE_MESSAGES" | translate }}
                <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
              </ion-item>

            </ion-list>
            <ion-list *ngxPermissionsOnly="[permissions.sendBroadcaseMessage]" class="ion-no-padding sub-menu-bg">
              <ion-item detail="false" lines="none" id="broadcast-message" tappable
                (click)="closeAccordion(); navigatePage(broadCastMessagesRoute);" class="sub-menu-bg">
                <ion-icon src="assets/icon/material-svg/message-text-outline.svg" slot="start"></ion-icon>
                <ion-label style="white-space: break-spaces;">{{ "MENU.SEND_BROADCAST_MESSAGES" | translate }}</ion-label>
                <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
              </ion-item>
            </ion-list>
            <span *ngIf="enableMaskedMsg">
              <ion-list *ngxPermissionsOnly="[permissions.sendMaskedMessage]" class="ion-no-padding sub-menu-bg">
                <ion-item (click)="navigatePage(maskedMessagesRoute); closeAccordion();" detail="false" lines="none"
                  id="masked-message" class="sub-menu-bg" tappable>
                  <ion-icon src="assets/icon/material-svg/forum.svg" slot="start"></ion-icon>
                  <ion-label style="white-space: break-spaces;">{{ "MENU.SEND_MASKED_MESSAGES" | translate }}</ion-label>
                  <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
                </ion-item>
              </ion-list>
            </span>
            <ion-list *ngxPermissionsOnly="[permissions.showArchive]" class="ion-no-padding sub-menu-bg">
              <ion-item detail="false" lines="none" id="archived-message" tappable
                (click)="navigatePage(archivedMessageRoute); closeAccordion();" class="sub-menu-bg">
                <ion-icon src="assets/icon/material-svg/delete-variant.svg" slot="start"></ion-icon>
                <ion-label style="white-space: break-spaces;">{{ "MENU.ARCHIVED_MESSAGES" | translate }}</ion-label>
                <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
              </ion-item>
            </ion-list>
          </ion-list>
        </ion-accordion>
      </ion-accordion-group>
      <!-- TODO! CHP-3576 -->
      <ion-accordion-group class="set-border-top" mode="md" *ngIf="enableUserCenter && !nursingAgencyUser"
        value="UserCenter" #userCenterAccordionGroup>
        <ion-accordion value="{{ 'MENU.USER_CENTER' | translate }}" mode="md" *ngxPermissionsOnly="[
                  permissions.addVirtualPatientUsers,
                  permissions.addVirtualEnrolledStaffUsers,
                  permissions.addVirtualPartnerUsers,
                  permissions.allowPatientEnrollment,
                  permissions.allowStaffEnrollment,
                  permissions.allowPartnerEnrollment
                ]">
          <ion-item slot="header" class="setAccordionIconColor" mode="md" tappable id="user-center">
            <ion-icon src="assets/icon/material-svg/account-multiple.svg" slot="start"></ion-icon>
            <ion-label>{{ "MENU.USER_CENTER" | translate }}</ion-label>
          </ion-item>

          <ion-list slot="content" class="ion-no-padding">
            <ion-list *ngxPermissionsOnly="[permissions.allowAddUser]" class="ion-no-padding sub-menu-bg">
              <ion-item *ngxPermissionsOnly="[
                        permissions.addVirtualPatientUsers,
                        permissions.addVirtualEnrolledStaffUsers,
                        permissions.addVirtualPartnerUsers
                      ]" detail="false" lines="none" id="add-user" tappable
                (click)="closeAccordion(); navigatePage(addUserRoute);" class="sub-menu-bg">
                <ion-icon src="assets/icon/material-svg/account-plus.svg" slot="start"></ion-icon>
                {{ "MENU.ADD_USERS" | translate }}
                <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
              </ion-item>
            </ion-list>
            <ion-list class="ion-no-padding sub-menu-bg">
              <ion-item detail="false" lines="none" tappable *ngxPermissionsOnly="[
                        permissions.allowPatientEnrollment,
                        permissions.allowStaffEnrollment,
                        permissions.allowPartnerEnrollment
                      ]" id="invite-user" (click)="navigatePage(inviteUserRoute); closeAccordion();"
                class="sub-menu-bg">
                <img class="icon-png" src="assets/icon/menu/invite-user.png" slot="start" alt="invite" />
                {{ "MENU.INVITE_USERS" | translate }}
                <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
              </ion-item>
            </ion-list>
          </ion-list>
        </ion-accordion>
      </ion-accordion-group>

      <ion-menu-toggle mode="md">
        <ion-item *ngIf="enableDocCenter" class="set-border-top" (click)="navigatePage(documentCenterRoute);"
          detail="false" lines="none" id="document-center" tappable>
          <img class="icon-png" src="assets/icon/menu/signing-head-icon-white.png" slot="start" alt="doument-center" />
          {{ "MENU.DOCUMENT_CENTER" | translate }}
          <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
        </ion-item>
        <ion-item *ngIf="enableDeliveryCenter && userData?.group === constants?.patientGroupId.toString()"
          class="set-border-top" (click)="navigatePage(deliveryCenterRoute);" detail="false" lines="none"
          id="document-center" tappable>
          <ion-icon src="assets/icon/material-svg/truck.svg" slot="start"></ion-icon>
          {{ "MENU.DELIVERY_CENTER" | translate }}
          <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
        </ion-item>
        <ion-item *ngIf="enableFormCenter" class="set-border-top" (click)="navigatePage(formCenterRoute);"
          detail="false" lines="none" id="form-center" tappable>
          <ion-icon src="assets/icon/material-svg/note-text.svg" slot="start"></ion-icon>
          {{ "MENU.FORM_CENTER" | translate }}
          <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
        </ion-item>
      </ion-menu-toggle>
      <!-- TODO! CHP-3576 -->
      <ion-accordion-group mode="md" class="set-border-top" *ngIf="enableEduCenter" value="EducationCenter"
        #educationCenterAccordionGroup>
        <ion-accordion value="{{ 'MENU.EDUCATION_CENTER' | translate }}" mode="md">
          <ion-item slot="header" class="setAccordionIconColor" mode="md" tappable id="education-center">
            <ion-icon src="assets/icon/material-svg/book.svg" slot="start"></ion-icon>
            <ion-label>{{ "MENU.EDUCATION_CENTER" | translate }}</ion-label>
          </ion-item>

          <ion-list slot="content" class="ion-no-padding sub-menu-bg">
            <ion-item detail="false" lines="none" id="education-material" tappable
              (click)="closeAccordion(); navigatePage(educationCenterMaterialRoute);" class="sub-menu-bg">
              <ion-icon src="assets/icon/material-svg/eye-regular.svg" slot="start"></ion-icon>
              {{ "MENU.VIEW_EDUCATION_MAT" | translate }}
              <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
            </ion-item>
          </ion-list>
        </ion-accordion>
      </ion-accordion-group>
      <!-- TODO! CHP-3576 -->
      <ion-accordion-group mode="md" class="set-border-top" *ngIf="enableScheduleCenter" value="ScheduleCenter"
        #scheduleCenterAccordionGroup>
        <ion-accordion value="{{ 'MENU.SCHEDULE_CENTER' | translate }}" mode="md">
          <ion-item slot="header" class="setAccordionIconColor" mode="md" tappable id="schedule-center">
            <ion-icon src="assets/icon/material-svg/calendar.svg" slot="start"></ion-icon>
            <ion-label>{{ "MENU.SCHEDULE_CENTER" | translate }}</ion-label>
          </ion-item>

          <ion-list slot="content" class="ion-no-padding">
            <ion-menu-toggle auto-hide="false">
              <ion-list class="sub-menu-bg">
                <ion-item detail="false" lines="none" id="view-visit" tappable id="view-visit"
                  (click)="closeAccordion(); navigatePage(scheduleCenterVisitsRoute);" class="sub-menu-bg">
                  <ion-icon src="/assets/icon/material-svg/schedule.svg" slot="start"></ion-icon>
                  {{ "MENU.VISITS" | translate }}
                  <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
                </ion-item>
                <ion-item *ngIf="scheduleCenterPermissions.manage" detail="false" lines="none" id="manage-availability"
                  tappable (click)="closeAccordion(); navigatePage(scheduleCenterManageRoute);" class="sub-menu-bg">
                  <ion-icon class="" src="assets/icon/material-svg/pen.svg" slot="start"></ion-icon>
                  {{ "MENU.MANAGE_AVAILABILITY" | translate }}
                  <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
                </ion-item>
              </ion-list>
            </ion-menu-toggle>
          </ion-list>
        </ion-accordion>
      </ion-accordion-group>

      <ion-menu-toggle mode="md">
        <ion-item class="set-border-top" *ngIf="enablePAH" (click)="navigatePage(patientActivityRoute);" detail="false"
          lines="none" tappable id="patient-activity-hub">
          <ion-icon src="assets/icon/material-svg/account-switch.svg" slot="start"></ion-icon>
          {{ "TITLES.PATIENT_ACTIVITY_HUB" | translate }}
          <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
        </ion-item>
        <ion-item class="set-border-top" (click)="navigatePage(profileUserRoute);" detail="false" lines="none" tappable
          id="profile">
          <ion-icon src="assets/icon/material-svg/account-outline.svg" slot="start"></ion-icon>
          {{ "MENU.MY_PROFILE" | translate }}
          <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
        </ion-item>
        <ion-item class="set-border-top" (click)="navigatePage(supportRoute);" detail="false" lines="none" tappable
          id="app-support" *ngIf="showSupportAndFeedback">
          <ion-icon src="assets/icon/material-svg/comment-text-outline.svg" slot="start"></ion-icon>
          {{ "MENU.APP_SUPPORT" | translate }}
          <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
        </ion-item>
      </ion-menu-toggle>
      <ion-menu-toggle mode="md">
        <ion-item class="set-border-top" (click)="logout()" button detail="false" lines="none" id="logout">
          <ion-icon src="assets/icon/material-svg/logout.svg" slot="start"></ion-icon>
          {{ "MENU.LOGOUT" | translate }}
          <ion-icon src="assets/icon/material-svg/chevron-right.svg" slot="end"></ion-icon>
        </ion-item>
      </ion-menu-toggle>
    </ion-list>
  </ion-content>
  <span class="app-version">{{ "GENERAL.VERSION" | translate }}: {{ appVersion }}</span>
</ion-menu>