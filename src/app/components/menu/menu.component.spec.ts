import { SharedService } from 'src/app/services/shared-service/shared.service';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, RouterModule } from '@angular/router';
import { IonicModule, MenuController, ModalController } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { MenuComponent } from './menu.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { OrderByPipe } from 'src/app/pipes/order-by/order-by.pipe';
import { TestConstants } from 'src/app/constants/test-constants';
import { RouterTestingModule } from '@angular/router/testing';
import { LoginPage } from 'src/app/pages/auth/login/login.page';

describe('MenuComponent', () => {
  let component: MenuComponent;
  let fixture: ComponentFixture<MenuComponent>;
  let modalController: ModalController;
  const { modalSpy } = TestConstants;
  let router: Router;
  let sharedService: SharedService;
  let menuControllerMock: MenuController;

  beforeEach(() => {
    menuControllerMock = jasmine.createSpyObj('MenuController', ['close']);
    TestBed.configureTestingModule({
      declarations: [MenuComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterTestingModule.withRoutes([{ path: 'login', component: LoginPage }])
      ],
      providers: [
        OrderByPipe,
        MenuController,
        ModalController,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        PermissionService,
        SharedService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        { provide: MenuController, useValue: menuControllerMock }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    router = TestBed.inject(Router);
    spyOn(router, 'navigateByUrl').and.stub();
    spyOn(router, 'navigate').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(MenuComponent);
    component = fixture.componentInstance;
    component.isUserCenter = 1;
    component.isMessageCenter = 1;
    component.isEducationCenter = 1;
    component.isScheduleCenter = 1;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.isMessageCenter).toBeDefined(1);
    expect(component.isUserCenter).toBeDefined(1);
    expect(component.isEducationCenter).toBeDefined(1);
    expect(component.isScheduleCenter).toBeDefined(1);
  });

  it('should check config value', () => {
    expect(component.checkConfigValues).toBeTruthy();
  });

  it('click on logout', () => {
    component.logout();
    expect(component.logout).toBeTruthy();
  });

  it('click on new message', () => {
    component.newMessage();
    expect(component.newMessage).toBeTruthy();
  });

  describe('toggleMenuItem', () => {
    it('toggle menu item should message isMessageCenter = 1', () => {
      component.toggleMenuItem('message');
      expect(component.isMessageCenter).toEqual(0);
    });

    it('toggle menu item should message isMessageCenter = 0', () => {
      component.isMessageCenter = 0;
      component.toggleMenuItem('message');
      expect(component.isMessageCenter).toEqual(1);
    });

    it('toggle menu item should user isUserCenter = 1', () => {
      component.toggleMenuItem('user');
      expect(component.isUserCenter).toEqual(0);
    });

    it('toggle menu item should user isUserCenter = 0', () => {
      component.isUserCenter = 0;
      component.toggleMenuItem('user');
      expect(component.isUserCenter).toEqual(1);
    });

    it('toggle menu item should education isEducationCenter = 1', () => {
      component.toggleMenuItem('education');
      expect(component.isEducationCenter).toEqual(0);
    });

    it('toggle menu item should education isEducationCenter = 0', () => {
      component.isEducationCenter = 0;
      component.toggleMenuItem('education');
      expect(component.isEducationCenter).toEqual(1);
    });

    it('toggle menu item should schedule isScheduleCenter = 1', () => {
      component.toggleMenuItem('schedule');
      expect(component.isScheduleCenter).toEqual(0);
    });

    it('toggle menu item should schedule isScheduleCenter = 0', () => {
      component.isScheduleCenter = 0;
      component.toggleMenuItem('schedule');
      expect(component.isScheduleCenter).toEqual(1);
    });
  });

  it('should page navigation', () => {
    component.navigatePage('/');
    expect(component.navigatePage).toBeTruthy();
  });

  it('should page navigation with schedule-center', () => {
    component.navigatePage('/schedule-center');
    expect(component.navigatePage).toBeTruthy();
  });

  describe('closeAccordion', () => {
    it('should close the menu', () => {
      component.closeAccordion();
      expect(menuControllerMock.close).toHaveBeenCalled();
    });
    it('should reset messageCenterAccordionGroup value if available', () => {
      component.messageCenterAccordionGroup = {
        nativeElement: { value: 'someValue' }
      } as any;
      component.closeAccordion();
      expect(component.messageCenterAccordionGroup.nativeElement.value).toBeUndefined();
    });

    it('should reset userCenterAccordionGroup value if available', () => {
      component.userCenterAccordionGroup = {
        nativeElement: { value: 'someValue' }
      } as any;
      component.closeAccordion();
      expect(component.userCenterAccordionGroup.nativeElement.value).toBeUndefined();
    });

    it('should reset educationCenterAccordionGroup value if available', () => {
      component.educationCenterAccordionGroup = {
        nativeElement: { value: 'someValue' }
      } as any;
      component.closeAccordion();
      expect(component.educationCenterAccordionGroup.nativeElement.value).toBeUndefined();
    });

    it('should reset scheduleCenterAccordionGroup value if available', () => {
      component.scheduleCenterAccordionGroup = {
        nativeElement: { value: 'someValue' }
      } as any;
      component.closeAccordion();
      expect(component.scheduleCenterAccordionGroup.nativeElement.value).toBeUndefined();
    });
  });
});
