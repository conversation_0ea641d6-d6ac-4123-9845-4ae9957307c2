import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { MenuComponent } from './menu.component';
import { SharedModule } from 'src/app/shared.module';
import { RouterModule } from '@angular/router';
import { ChooseRecipientsPageModule } from 'src/app/pages/message-center/choose-recipients/choose-recipients.module';
import { ChoosePatientComponentModule } from 'src/app/pages/message-center/choose-patient/choose-patient.module';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        SharedModule,
        ReactiveFormsModule,
        TranslateModule,
        RouterModule,
        ChooseRecipientsPageModule,
        ChoosePatientComponentModule
    ],
    declarations: [MenuComponent],
    exports: [MenuComponent]
})
export class MenuComponentModule { }
