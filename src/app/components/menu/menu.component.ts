import { CommonService } from './../../services/common-service/common.service';
import { Router } from '@angular/router';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { SharedService } from '../../services/shared-service/shared.service';
import { ChooseRecipientsPage } from 'src/app/pages/message-center/choose-recipients/choose-recipients.page';
import { Urls } from 'src/app/constants/urls';
import { Permissions } from '../../constants/permissions';
import { PermissionService } from '../../services/permission-service/permission.service';
import { Config } from '../../constants/config';
import { ChoosePatientComponent } from 'src/app/pages/message-center/choose-patient/choose-patient.component';
import { ConfigValues } from 'src/assets/config/config';
import { PageRoutes } from 'src/app/constants/page-routes';
import { MenuController } from '@ionic/angular';
import { Constants } from 'src/app/constants/constants';
import { AdmissionComponent } from '../admission/admission.component';

@Component({
  selector: 'app-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss']
})
export class MenuComponent {
  @ViewChild('messageCenterAccordionGroup', { read: ElementRef })
  messageCenterAccordionGroup: ElementRef; // TODO! CHP-3576
  @ViewChild('userCenterAccordionGroup', { read: ElementRef })
  userCenterAccordionGroup: ElementRef; // TODO! CHP-3576
  @ViewChild('educationCenterAccordionGroup', { read: ElementRef })
  educationCenterAccordionGroup: ElementRef; // TODO! CHP-3576
  @ViewChild('scheduleCenterAccordionGroup', { read: ElementRef })
  scheduleCenterAccordionGroup: ElementRef; // TODO! CHP-3576
  isMessageCenter: number;
  isEducationCenter: number;
  isScheduleCenter: number;
  isUserCenter: number;
  errorImage = Urls.noImage;
  permissions: any;
  userData: any;
  newMessgeLinkShow: boolean;
  patientTopicCount: number;
  nursingAgencyUser: boolean;
  enableMsgCenter: boolean;
  enableUserCenter: boolean;
  enableMaskedMsg: boolean;
  enableDocCenter: boolean;
  enableFormCenter: boolean;
  enableEduCenter: boolean;
  enableScheduleCenter: boolean;
  enablePAH: boolean;
  enableDeliveryCenter: boolean;
  showSupportAndFeedback: boolean;
  scheduleCenterPermissions = { create: false, manage: false };
  appVersion: string;
  homeRoute = PageRoutes.home;
  activeMessagesRoute = PageRoutes.activeMessages;
  broadCastMessagesRoute = PageRoutes.broadCastMessage;
  maskedMessagesRoute = PageRoutes.maskedMessage;
  archivedMessageRoute = PageRoutes.archivedMessage;
  addUserRoute = PageRoutes.addUser;
  inviteUserRoute = PageRoutes.inviteUser;
  documentCenterRoute = PageRoutes.documentCenter;
  formCenterRoute = PageRoutes.formCenter;
  deliveryCenterRoute = PageRoutes.deliveryCenter;
  educationCenterMaterialRoute = PageRoutes.educationCenterMaterial;
  scheduleCenterVisitsRoute = PageRoutes.scheduleCenterVisits;
  scheduleCenterManageRoute = PageRoutes.scheduleCenterManage;
  patientActivityRoute = PageRoutes.patientActivity;
  profileUserRoute = PageRoutes.profileUser;
  supportRoute = PageRoutes.support;
  createVisit = PageRoutes.createVisit;
  constants: any = {};
  constructor(
    public sharedService: SharedService,
    private readonly permissionService: PermissionService,
    private menu: MenuController,
    private router: Router,
    private common: CommonService
  ) {
    this.permissions = Permissions;
    this.userData = this.sharedService.userData;
    this.constants = Constants;
    if (this.userData) {
      this.checkConfigValues();
      this.sharedService.configValuesUpdated.subscribe(() => {
        this.checkConfigValues();
      });
    }
    const fullVersion = ConfigValues.appVersion.split('-');
    this.appVersion = fullVersion[0];
  }

  checkConfigValues(): void {
    this.newMessgeLinkShow = this.permissionService.showNewMessage();
    this.nursingAgencyUser = this.permissionService.isNursingAgencyUser();
    this.enableMsgCenter = this.sharedService.isEnableConfig(Config.enableMsgCenter);
    this.enableMaskedMsg = this.sharedService.isEnableConfig(Config.enableMaskedMsg);
    this.enableUserCenter = this.sharedService.isEnableConfig(Config.enableUserCenter);
    this.enableDocCenter = this.sharedService.isEnableConfig(Config.enableDocCenter);
    this.enableDeliveryCenter = this.sharedService.isEnableConfig(Config.enableDeliveryCenter);
    this.enableFormCenter = this.sharedService.isEnableConfig(Config.enableFormCenter);
    this.enableEduCenter = this.sharedService.isEnableConfig(Config.enableEduCenter);
    this.enableScheduleCenter = this.sharedService.isEnableConfig(Config.enableScheduleCenter);
    this.showSupportAndFeedback = this.sharedService.isEnableConfig(Config.showSupportAndFeedback);
    this.enablePAH =
      this.sharedService.isEnableConfig(Config.enableMobilePAH) &&
      this.permissionService.userHasPermission(Permissions.enablePatientActivityHub) &&
      !this.sharedService.loggedUserIsPatient();
    this.scheduleCenterPermissions.create =
      this.permissionService.userHasPermission(Permissions.allowStaffToScheduleForThemselves) ||
      this.permissionService.userHasPermission(Permissions.manageVisitSchedule);
    this.scheduleCenterPermissions.manage = !this.sharedService.loggedUserIsPatient();
  }

  logout(): void {
    setTimeout(() => {
      this.sharedService.logout();
    }, 500);
  }

  newMessage(): void {
    this.sharedService.newMessageAction(ChooseRecipientsPage, ChoosePatientComponent, AdmissionComponent);
    this.closeAccordion();
  }

  toggleMenuItem(item: string): void {
    switch (item) {
      case 'message':
        this.isMessageCenter === 1 ? (this.isMessageCenter = 0) : (this.isMessageCenter = 1);
        break;
      case 'user':
        this.isUserCenter === 1 ? (this.isUserCenter = 0) : (this.isUserCenter = 1);
        break;
      case 'education':
        this.isEducationCenter === 1 ? (this.isEducationCenter = 0) : (this.isEducationCenter = 1);
        break;
      case 'schedule':
        this.isScheduleCenter === 1 ? (this.isScheduleCenter = 0) : (this.isScheduleCenter = 1);
        break;
    }
  }

  // TODO! CHP-3576
  closeAccordion() {
    this.menu.close();
    if (this.messageCenterAccordionGroup?.nativeElement) {
      this.messageCenterAccordionGroup.nativeElement.value = undefined;
    }
    if (this.userCenterAccordionGroup?.nativeElement) {
      this.userCenterAccordionGroup.nativeElement.value = undefined;
    }
    if (this.educationCenterAccordionGroup?.nativeElement) {
      this.educationCenterAccordionGroup.nativeElement.value = undefined;
    }
    if (this.scheduleCenterAccordionGroup?.nativeElement) {
      this.scheduleCenterAccordionGroup.nativeElement.value = undefined;
    }
  }

  navigatePage(pageRoute: string) {
    //TODO: Need to figureout the solution for clearing page when taking from the menu component
    // For now we are clearing only for visit schedule. This can't be added for all routers as there is some issue with back button
    this.sharedService.branchSwitched = true;
    this.sharedService.resetSelectedDateFilterData();
    if (pageRoute.includes('/schedule-center')) {
      this.router.navigateByUrl(pageRoute, { replaceUrl: true });
    } else {
      this.router.navigateByUrl(pageRoute);
    }
  }
}
