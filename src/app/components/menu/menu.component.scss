.menu-theme {
    --background: var(--ion-color-skin-main);
    --ion-item-background: var(--ion-color-skin-main);

    ion-content {
        --background: var(--ion-color-skin-main);
    }

    ion-item {
        --color: #cde7f3;
        font-size: 18px;
    }
    .set-border-top {
        border-top: 1px solid rgb(255 255 255 / 10%);
    }
    .sub-menu-bg {
        --background: var(--ion-color-skin-main-light);
        background: var(--ion-color-skin-main-light);
    }
    .sub-menu ion-list {
        --ion-item-background: var(--ion-color-skin-main-light);
    }
    .left-menu-user-details-wrap {
        padding-top: calc(25px + constant(safe-area-inset-bottom));
        padding-top: calc(25px + env(safe-area-inset-bottom));
        text-align: center;
        margin-bottom: 20px;

        .left-menu-user-image img {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: #e4e5e5;
            object-fit: cover;
        }

        .left-menu-user-details h2 {
            font-size: 18px;
            color: #ffffff;
            margin: 1px 0px 0px;
            text-transform: capitalize;
        }

        .left-menu-user-details h3 {
            color: #e2e2e2;
            font-size: 15px;
            margin: 2px 0px;
        }
    }

    .icon-png {
        left: 15px;
        font-size: 20px;
        top: 16px;
        width: 8%;
    }

    ion-icon {
        color: #cde7f3;
    }

    .app-version {
        color: #fff;
        text-align: center;
        padding: 10px 0px;
        font-style: italic;
    }
}
