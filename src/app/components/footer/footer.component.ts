import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Route, Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { PageRoutes } from 'src/app/constants/page-routes';
import { SharedService } from 'src/app/services/shared-service/shared.service';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent {
  @Input() showBackButton = true;
  @Input() backButtonLink: string;
  @Input() blockNavigation = false;
  @Output() readonly backAck: EventEmitter<any> = new EventEmitter();
  constructor(private readonly navCtrl: NavController, private readonly sharedService: SharedService, private readonly router: Router) {}

  goBack(): void {
    if (this.sharedService.isAppLessHomeLoggedIn()) {
      this.router.navigate([`.${PageRoutes.appLess}/home`]);
    } else if (this.backButtonLink) {
      this.navCtrl.navigateBack(this.backButtonLink);
    } else if (this.blockNavigation) {
      this.backAck.emit(true);
    } else {
      this.navCtrl.back();
    }
  }
}
