import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavController } from '@ionic/angular';
import { HttpClientModule } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgIdleModule } from '@ng-idle/core';
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';

import { FooterComponent } from './footer.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('FooterComponent', () => {
  let component: FooterComponent;
  let fixture: ComponentFixture<FooterComponent>;
  let navCtrl: NavController;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [FooterComponent],
      imports: [
        IonicModule.forRoot(), 
        RouterModule.forRoot([]),
        HttpClientModule,
        TranslateModule.forRoot(),
        NgxPermissionsModule.forRoot(),
        NgIdleModule.forRoot(),
        NgIdleKeepaliveModule.forRoot()
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [NavController]
    }).compileComponents();
    navCtrl = TestBed.inject(NavController);
    fixture = TestBed.createComponent(FooterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  describe('goBack', () => {
    it('should navigate back using navCtrl if backButtonLink is not provided', () => {
      spyOn(navCtrl, 'back');
      component.goBack();
      expect(navCtrl.back).toHaveBeenCalled();
    });

    it('should navigate to backButtonLink using navCtrl if backButtonLink is provided', () => {
      const backButtonLink = '/home';
      spyOn(navCtrl, 'navigateBack');
      component.backButtonLink = backButtonLink;
      component.goBack();
      expect(navCtrl.navigateBack).toHaveBeenCalledWith(backButtonLink);
    });

    it('should emit backAck event if blockNavigation is true', () => {
      spyOn(component.backAck, 'emit');
      component.blockNavigation = true;
      component.goBack();
      expect(component.backAck.emit).toHaveBeenCalledWith(true);
    });
  });
});
