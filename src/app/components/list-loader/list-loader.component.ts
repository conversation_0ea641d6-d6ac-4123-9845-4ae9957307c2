import { Component, Input } from '@angular/core';

declare type SpinnerType =
  | 'bubbles'
  | 'circles'
  | 'circular'
  | 'crescent'
  | 'dots'
  | 'lines'
  | 'lines-sharp'
  | 'lines-sharp-small'
  | 'lines-small'
  | undefined;
@Component({
  selector: 'app-list-loader',
  templateUrl: './list-loader.component.html',
  styleUrls: ['./list-loader.component.scss']
})
export class ListLoaderComponent {
  @Input() loaderText: string;
  @Input() customClass: string;
  @Input() spinnerType: SpinnerType = 'bubbles';
}
