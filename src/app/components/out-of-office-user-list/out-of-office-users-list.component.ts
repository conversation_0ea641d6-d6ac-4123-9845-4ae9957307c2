import { Component, Input, SimpleChanges, OnChanges } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { getOooStatusBasedOnTime, isBlank } from 'src/app/utils/utils';
import { CommonService } from 'src/app/services/common-service/common.service';

@Component({
  selector: 'app-out-of-office',
  templateUrl: './out-of-office-users-list.component.html',
  styleUrls: ['./out-of-office.users-list.component.scss']
})
export class OutOfOfficeUserListComponent implements OnChanges {
  @Input() chatParticipants: any[] = [];
  oooUsers: any[] = [];
  presentingElement = null;
  showOooMessage = false;
  oooMessage: any;
  isMultipleOfflineUser: boolean;
  offlineStatus: boolean = false;

  constructor(
    private modalController: ModalController,
    private sharedService: SharedService,
    private commonService: CommonService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.oooInfo) {
      this.chatParticipants = changes.chatParticipants.currentValue;
    }
    this.showOutOfOfficeMessage();
  }

  closeOooStatusNotification() {
    this.showOooMessage = false;
  }

  goBack(val: any): void {
    this.modalController.dismiss(val);
  }

  showOutOfOfficeMessage() {
    this.oooUsers = this.chatParticipants?.filter(
      (data) =>
        getOooStatusBasedOnTime(data.oooInfo).status &&
        (data.oooInfo.isOutOfOffice || !isBlank(data.oooInfo.message)) &&
        +data.userId !== +this.sharedService.userData.userId
    );
    this.oooMessage = '';
    if (this.oooUsers?.length) {
      const offlineUsers = this.oooUsers?.filter((data) => data.oooInfo.isOutOfOffice);
      this.showOooMessage = true;
      if (this.oooUsers.length === 1) {
        this.isMultipleOfflineUser = false;
        if (offlineUsers.length === 1) {
          this.oooMessage = !isBlank(offlineUsers[0].oooInfo.message)
            ? `${this.oooUsers[0].displayName} : ${offlineUsers[0].oooInfo.message}`
            : this.commonService.getTranslateDataWithParam('LABELS.OUT_OF_OFFICE_SINGLE_USER_MSG', { displayName: offlineUsers[0].displayName });
        } else {
          this.oooMessage = `${this.oooUsers[0].displayName} : ${this.oooUsers[0].oooInfo.message}`;
        }
        this.offlineStatus = offlineUsers.length === 1;
      } else {
        this.isMultipleOfflineUser = true;
        this.oooMessage =
          offlineUsers.length === this.oooUsers.length
            ? this.commonService.getTranslateData('LABELS.OUT_OF_OFFICE_ON_MULTIPLE_USER_MSG')
            : this.commonService.getTranslateData('LABELS.OUT_OF_OFFICE_OFF_MULTIPLE_USER_MSG');
        this.offlineStatus = offlineUsers.length > 0;
      }
    } else {
      this.showOooMessage = false;
    }
  }
}
