import { ComponentFixture, TestBed } from '@angular/core/testing';
import { OutOfOfficeUserListComponent } from './out-of-office-users-list.component';
import { ModalController } from '@ionic/angular';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import * as Utils from 'src/app/utils/utils';
import { of } from 'rxjs';

describe('OutOfOfficeUserListComponent', () => {
  let component: OutOfOfficeUserListComponent;
  let fixture: ComponentFixture<OutOfOfficeUserListComponent>;
  let modalController: ModalController;
  let sharedService: SharedService;
  let commonService: CommonService;

  beforeEach(async () => {
    const utilsMock = {
      getOooStatusBasedOnTime: jasmine.createSpy('getOooStatusBasedOnTime').and.returnValue({ status: true }),
      isBlank: jasmine.createSpy('isBlank').and.returnValue(false),
    };

    await TestBed.configureTestingModule({
      declarations: [OutOfOfficeUserListComponent],
      providers: [
        { provide: ModalController, useValue: jasmine.createSpyObj('ModalController', ['dismiss']) },
        {
          provide: SharedService,
          useValue: { userData: { userId: 1 } },
        },
        {
          provide: CommonService,
          useValue: {
            getTranslateData: jasmine.createSpy('getTranslateData').and.returnValue(of('Translated Multiple Users Message')),
            getTranslateDataWithParam: jasmine.createSpy('getTranslateDataWithParam').and.returnValue(of('Translated Single User Message')),
          },
        },
        { provide: Utils, useValue: utilsMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(OutOfOfficeUserListComponent);
    component = fixture.componentInstance;
    modalController = TestBed.inject(ModalController);
    sharedService = TestBed.inject(SharedService);
    commonService = TestBed.inject(CommonService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnChanges', () => {
    it('should update chatParticipants and call showOutOfOfficeMessage', () => {
      component.chatParticipants = [{ userId: 2, oooInfo: { isOutOfOffice: true, message: 'On leave' } }];
      fixture.detectChanges(); 

      spyOn(component, 'showOutOfOfficeMessage'); 

      const changes = {
        chatParticipants: {
          previousValue: [],
          currentValue: [{ userId: 2, oooInfo: { isOutOfOffice: true, message: 'On leave' } }],
          firstChange: true,
          isFirstChange: () => true,
        },
      };
      
      component.ngOnChanges(changes); 
      expect(component.chatParticipants).toEqual(changes.chatParticipants.currentValue);
      expect(component.showOutOfOfficeMessage).toHaveBeenCalled();
    });
  });

  describe('showOutOfOfficeMessage', () => {
    it('should correctly identify and display OOO users with messages', () => {
      component.chatParticipants = [
        { userId: 2, displayName: 'User A', oooInfo: { isOutOfOffice: true, message: 'On leave' } },
        { userId: 3, displayName: 'User B', oooInfo: { isOutOfOffice: false, message: '' } },
      ];
      component.showOutOfOfficeMessage();
      expect(component.oooUsers.length).toBe(1);
      expect(component.showOooMessage).toBeTrue();
      expect(component.oooMessage).toContain('User A : On leave');
    });

    it('should handle multiple OOO users correctly', () => {
      component.chatParticipants = [
        { userId: 2, displayName: 'User A', oooInfo: { isOutOfOffice: true, message: '' } },
        { userId: 3, displayName: 'User B', oooInfo: { isOutOfOffice: true, message: '' } },
      ];

      component.showOutOfOfficeMessage();
      expect(commonService.getTranslateData).toHaveBeenCalledWith('LABELS.OUT_OF_OFFICE_ON_MULTIPLE_USER_MSG');
    });

    it('should hide OOO message if no users are OOO', () => {
      component.chatParticipants = [
        { userId: 2, displayName: 'User A', oooInfo: { isOutOfOffice: false, message: '' } },
      ];

      component.showOutOfOfficeMessage();
      expect(component.showOooMessage).toBeFalse();
    });
  });

  describe('closeOooStatusNotification', () => {
    it('should set showOooMessage to false', () => {
      component.showOooMessage = true;
      component.closeOooStatusNotification();
      expect(component.showOooMessage).toBeFalse();
    });
  });

  describe('goBack', () => {
    it('should call modalController.dismiss with provided value', () => {
      const val = 'test';
      component.goBack(val);
      expect(modalController.dismiss).toHaveBeenCalledWith(val);
    });
  });
});
