<div>
  @if (showOooMessage) {
    <div class="modal-btn-container" [ngClass]="{'bg-grey': !offlineStatus}">
      <ion-text>
        <p>
          <span class="clickable-text" id="open-modal" expand="block" *ngIf="isMultipleOfflineUser">{{ 'LABELS.TWO_OR_MORE' | translate }}</span>
          <span class="status-msg"> {{ oooMessage }}</span>
        </p>
      </ion-text>
      <ion-icon name="close-outline" (click)="closeOooStatusNotification()"></ion-icon>
    </div>
    <ion-modal #modal trigger="open-modal" [presentingElement]="presentingElement">
      <ng-template>
        <ion-header>
          <ion-toolbar>
            <app-header-plain headerTitle="LABELS.OUT_OF_OFFICE_TITLE" (close)="goBack('')" [type]="'rightCloseIcon'"></app-header-plain>
          </ion-toolbar>
        </ion-header>
        <ion-content>
          <ion-text>
            <p class="ion-text-right ion-padding-end">
              <ion-icon name="alert-circle-outline"></ion-icon> {{ 'LABELS.OUT_OF_OFFICE_INFO' | translate }}
            </p>
          </ion-text>
          <ion-list class="styled-list">
            @for (item of oooUsers; track i; let i = $index) {
              <ion-item class="custom-item" [ngClass]="{ 'no-color': !item?.oooInfo?.isOutOfOffice }">
                <ion-label>
                  <h2 class="item-title">{{ item?.displayName }}</h2>
                  <p class="item-content">{{ item?.oooInfo?.message || 'LABELS.OUT_OF_OFFICE' | translate }}</p>
                  <p class="item-dates">
                    @if (item?.oooInfo?.startDateTime) {
                      <strong>{{ 'LABELS.START_DATE' | translate }} :</strong> {{ item?.oooInfo?.startDateTime | date: 'dd/MM/yyyy hh:mm a' }}
                    }
                    <br />
                    @if (item?.oooInfo?.endDateTime) {
                      <strong>{{ 'LABELS.END_DATE' | translate }} :</strong> {{ item?.oooInfo?.endDateTime | date: 'dd/MM/yyyy hh:mm a' }}
                    }
                  </p>
                </ion-label>
              </ion-item>
            }
          </ion-list>
        </ion-content>
      </ng-template>
    </ion-modal>
  }
</div>
