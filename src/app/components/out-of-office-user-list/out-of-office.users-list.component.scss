.styled-list {
  margin: 20px;
  padding: 2px;
}

.custom-item {
  border-radius: 10px;
  margin: 10px 0;
  --ion-item-background: #f0a8b0;
  border: 1px solid #f0a8b0;
  box-shadow: 0 2px 4px #f0a8b0;
}

.no-color {
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  margin: 10px 0;
  --ion-item-background: #f9f9f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bg-grey {
  background: #ededed !important;
  border: 1px solid #ededed !important;
  color: #484747 !important;
}

.item-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.item-content {
  font-size: 14px;
  color: #666;
  margin: 5px 0;
}

.item-dates {
  font-size: 12px;
  color: #555;
  margin-top: 8px;
}

.modal-btn-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f0a8b0;
  border: 1px solid #f0a8b0;
  border-radius: 5px;
  padding: 3px 5px;
  color: #000;
  white-space: nowrap;
  font-weight: lighter
}

.clickable-text {
  text-decoration: underline;
  cursor: pointer;
}

.status-msg{
  text-wrap: wrap;
}