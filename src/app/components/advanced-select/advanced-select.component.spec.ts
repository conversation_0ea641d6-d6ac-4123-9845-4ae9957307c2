import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormArray } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { AngularDelegate, IonicModule, ModalController, NavParams, PopoverController } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { AuthGuard } from 'src/app/services/auth-guard/auth.guard';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { AdvancedSelectComponent } from 'src/app/components/advanced-select/advanced-select.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';

describe('AdvancedSelectComponent', () => {
  let component: AdvancedSelectComponent;
  let fixture: ComponentFixture<AdvancedSelectComponent>;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  const formBuilder: UntypedFormBuilder = new UntypedFormBuilder();

  const mockData = [
    {
      id: 26596,
      tenant_id: '558',
      tag_name: 'CitusTestPatTag-2',
      tag_type_category: null,
      tag_type_id: '1829',
      tag_type: '2',
      is_deleted: '0',
      is_nursing_agency: '0',
      selectedTags: '',
      name: 'CitusTestPatTag-2',
      selected: false,
      meta: '{"outgoingFilingCenter":"","fileSaveFormat":"","summarizeOutcomeMeasure":false,"approvalRequired":true,"patientFacing":true}'
    },
    {
      id: 26595,
      tenant_id: '558',
      tag_name: 'CitusTestPatTag-1',
      tag_type_category: null,
      tag_type_id: '1829',
      tag_type: '2',
      is_deleted: '0',
      is_nursing_agency: '0',
      selectedTags: '',
      name: 'CitusTestPatTag-1',
      selected: true,
      meta: '{"outgoingFilingCenter":"","fileSaveFormat":"","summarizeOutcomeMeasure":false,"approvalRequired":true,"patientFacing":true}'
    }
  ];
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [AdvancedSelectComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateTestingModule,
        ReactiveFormsModule
      ],
      providers: [
        NavParams,
        AuthGuard,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        PopoverController,
        AngularDelegate,
        ModalController,
        { provide: UntypedFormBuilder, useValue: formBuilder }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    fixture = TestBed.createComponent(AdvancedSelectComponent);
    component = fixture.componentInstance;

    component.optionsForm = formBuilder.group({
      options: formBuilder.array([
        formBuilder.group({
          name: 'Option 1',
          selected: false
        }),
        formBuilder.group({
          name: 'Option 2',
          selected: false
        }),
        formBuilder.group({
          name: 'Option 3',
          selected: false
        })
      ])
    });
    modalSpy.present.and.stub();
    fixture.detectChanges();
  });

  it('should create', () => {
    component.options = mockData;
    component.optionsFiltered = mockData;
    if (component.options.length > 0) {
      const controls = mockData.map((value) => component.createItem(value));
      component.optionsForm = formBuilder?.group({
        options: new UntypedFormArray(controls)
      });
    }
    expect(component.options).toBeDefined();
    expect(component).toBeDefined();
  });

  it('should call done', () => {
    component.done();
    fixture.detectChanges();
    expect(component.done).toBeDefined();
  });

  it('should call goBack', () => {
    component.goBack();
    expect(component.goBack).toBeTruthy();
  });

  it('should filter the list based on the search term', () => {
    const event = {
      srcElement: {
        value: 'CitusTestPatTag'
      }
    };
    component.options = mockData;
    component.optionsFiltered = mockData;
    component.filterList(event);

    expect(component.optionsFiltered.length).toBe(2);
    expect(component.optionsFiltered[0].name).toBe('CitusTestPatTag-2');
    expect(component.optionsFiltered[1].name).toBe('CitusTestPatTag-1');
  });

  it('should not filter the list if search term is empty', () => {
    const event = {
      srcElement: {
        value: ''
      }
    };
    component.options = mockData;
    component.optionsFiltered = mockData;
    component.filterList(event);
    expect(component.optionsFiltered.length).toBe(2);
    expect(component.optionsFiltered).toEqual(component.options);
  });

  it('should patch all options with the provided value', () => {
    const value = true;
    component.patchOptionValue(value);
    const options = component.optionsForm.get('options') as UntypedFormArray;
    options.controls.forEach((option) => {
      expect(option.get('selected').value).toBe(value);
    });
  });

  it('should return true if no option is selected', () => {
    expect(component.isDisabled).toBe(true);
  });

  it('should return false if at least one option is selected', () => {
    const options = component.optionsForm.get('options') as UntypedFormArray;
    options.controls[0].get('selected').setValue(true);
    expect(component.isDisabled).toBe(false);
  });

  it('should update the selection and dismiss the modal', () => {
    const data = 1;
    const event = {
      detail: {
        checked: true
      }
    };
    component.options = [
      { id: 1, selected: false },
      { id: 2, selected: true }
    ];

    component.selection(data, event);
    expect(component.options[0].selected).toBe(false);
    expect(component.options[1].selected).toBe(true);
  });

  it('should update the selection and dismiss the modal hideCheckbox=false and singleSelect= true', () => {
    component.hideCheckbox = false;
    component.singleSelect = true;
    const data = 1;
    const event = {
      detail: {
        checked: true
      }
    };
    component.options = [
      { id: 1, selected: false },
      { id: 2, selected: true }
    ];

    component.selection(data, event);
    expect(component.options[0].selected).toBe(true);
    expect(component.options[1].selected).toBe(false);
  });

  it('should update the selection and dismiss the modal hideCheckbox=true and singleSelect= true', () => {
    component.hideCheckbox = true;
    component.singleSelect = true;
    const data = 1;
    const event = {
      detail: {
        checked: true
      }
    };
    component.options = [
      { id: 1, selected: false },
      { id: 2, selected: true }
    ];

    component.selection(data, event);
    expect(component.options[0].selected).toBe(true);
    expect(component.options[1].selected).toBe(false);
  });

  it('should update the selection and dismiss the modal hideCheckbox=true and singleSelect= true and checked false', () => {
    component.hideCheckbox = false;
    component.singleSelect = false;
    const data = 1;
    const event = {
      detail: {
        checked: false
      }
    };
    component.options = [
      { id: 1, selected: false },
      { id: 2, selected: true }
    ];

    component.selection(data, event);
    expect(component.options[0].selected).toBe(false);
    expect(component.options[1].selected).toBe(true);
  });
});
