<app-header-plain [headerTitle]="headerTitle" (close)="goBack()"></app-header-plain>
<ion-text color="black" class="ion-padding-horizontal ion-padding-top" *ngIf="infoMsg"><ion-icon class="pos-absolute" name="information-circle-outline" size="small"></ion-icon>&nbsp;<span class="ml-rem-1">{{ infoMsg }}</span></ion-text>
<ion-searchbar id="ion-search" *ngIf="showSearch" (ionInput)="filterList($event)"></ion-searchbar>
<ion-content>
    <form name="optionsForm" id="select-recipient-form" [formGroup]="optionsForm">
        <div class="common-list">
            <ion-item lines="none" formArrayName="options" *ngFor="let option of optionsFiltered; let i = index"
                [id]="'item-'+i" (click)="selection(option?.id)"
                [class.highlight]="option.selected && singleSelect && hideCheckbox"
                [ngClass]="{'text-center':singleSelect && hideCheckbox}" [class.ion-hide]="option?.isHide === true">
                <ion-label class="ion-text-wrap">{{option?.name}}</ion-label>
                <ng-container [formGroupName]="i" *ngIf="!singleSelect">
                    <ion-checkbox formControlName="selected" [id]="'checkbox-'+i" class="common-checkbox" slot="end"
                        mode='ios'>
                    </ion-checkbox>
                </ng-container>
                <ng-container [formGroupName]="i" *ngIf="singleSelect && !hideCheckbox">
                    <ion-checkbox formControlName="selected" [id]="'checkbox-'+i" class="common-checkbox" slot="end"
                        mode='ios' (ionChange)="selection(option?.id,$event)">
                    </ion-checkbox>
                </ng-container>
            </ion-item>
        </div>
    </form>
</ion-content>
<ion-footer *ngIf="!singleSelect">
    <ion-row>
        <!-- TODO! CHP-3595 -->
        <ion-col>
            <ion-button (click)="patchOptionValue(true)" id="select-all" expand="block" *ngIf="showButtons?.selectAll"
                class="ion-text-capitalize">
                {{'BUTTONS.SELECT_ALL' | translate}}
            </ion-button>
        </ion-col>
        <ion-col>
            <ion-button (click)="patchOptionValue(false)" id="clear-all" expand="block" *ngIf="showButtons?.clearAll"
                [disabled]="isDisabled" class="ion-text-capitalize">
                {{'BUTTONS.CLEAR_ALL' | translate}}
            </ion-button>
        </ion-col>
        <ion-col>
            <ion-button (click)="done()" expand="block" id="done" *ngIf="showButtons?.done" class="ion-text-capitalize">
                {{'BUTTONS.DONE' | translate}}
            </ion-button>
        </ion-col>
    </ion-row>
</ion-footer>