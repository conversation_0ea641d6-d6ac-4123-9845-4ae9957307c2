import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdvancedSelectComponent } from './advanced-select.component';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { SearchBarRecipientsModule } from 'src/app/components/search-bar-recipients/search-bar-recipients.module';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        IonicModule,
        SharedModule,
        TranslateModule,
        HeaderPlainModule,
        SearchBarRecipientsModule
    ],
    declarations: [AdvancedSelectComponent],
    exports: [AdvancedSelectComponent]
})
export class AdvancedSelectComponentModule { }
