import { Component } from '@angular/core';
import { Constants } from 'src/app/constants/constants';
import { ModalController, NavParams } from '@ionic/angular';
import { UntypedFormGroup, UntypedFormBuilder, UntypedFormArray } from '@angular/forms';
import { isPresent } from 'src/app/utils/utils';

@Component({
  selector: 'app-advanced-select',
  templateUrl: './advanced-select.component.html',
  styleUrls: ['./advanced-select.component.scss']
})
export class AdvancedSelectComponent {
  usersList = [];
  userTypes = Constants.userTypes;
  pageCount = 0;
  optionsForm: UntypedFormGroup;
  options = [];
  optionsFiltered: any = [];
  headerTitle = '';
  showSearch = false;
  infoMsg = '';
  showButtons: any;
  singleSelect: boolean;
  hideCheckbox = false;
  constructor(
    private readonly modalController: ModalController,
    private readonly navParams: NavParams,
    private readonly formBuilder: UntypedFormBuilder
  ) {
    if (this.navParams.get('options')) {
      this.options = this.navParams.get('options');
      this.optionsFiltered = this.navParams.get('options');
    }
    this.headerTitle = this.navParams.get('headerTitle');
    this.showSearch = this.navParams.get('showSearch');
    this.infoMsg = this.navParams.get('infoMsg');
    this.showButtons = this.navParams.get('buttons');
    this.singleSelect = this.navParams.get('singleSelect') ? false : false;
    if (this.options.length > 0) {
      const controls = this.options?.map((value: any) => this.createItem(value));
      this.optionsForm = this.formBuilder.group({
        options: new UntypedFormArray(controls)
      });
    }
  }

  createItem(controlValue: any): UntypedFormGroup {
    return this.formBuilder.group({
      id: [controlValue.id],
      name: [controlValue.name],
      meta: [controlValue.meta],
      selected: [controlValue.selected ? controlValue.selected : false],
      isHide: [controlValue.isHide || false]
    });
  }

  goBack(): void {
    this.modalController.dismiss();
  }

  done(): void {
    this.modalController.dismiss({
      selectedData: this.optionsForm?.value?.options
    });
  }

  filterList(event: any): boolean {
    this.optionsFiltered = this.options;
    const searchTerm = event.srcElement.value;
    if (!searchTerm) {
      return;
    }
    this.optionsFiltered = this.options.filter((form: any) => {
      const valueKey = form.name;
      if (valueKey && searchTerm) {
        if (valueKey.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1) {
          return true;
        }
        return false;
      }
    });
  }

  patchOptionValue(value: boolean): void {
    const array = this.optionsForm.value.options;
    const selected = array.map((option: any) => ({
      ...option,
      selected: value
    }));
    this.optionsForm.patchValue({ options: selected });
  }

  get isDisabled(): boolean {
    return this.optionsForm.value.options.map((item: any) => item.selected).indexOf(true) === -1;
  }

  selection(data: any, event?: any): void {
    if ((isPresent(event) && this.singleSelect && !this.hideCheckbox) || (this.singleSelect && this.hideCheckbox)) {
      this.options = this.options.filter((item) => {
        if (this.hideCheckbox && this.singleSelect) {
          item.selected = item.id === data;
        } else if (item.id === data) {
          item.selected = event.detail.checked ? true : false;
        } else {
          item.selected = false;
        }
        return item;
      });
      this.modalController.dismiss({
        selectedData: this.options
      });
    }
  }
}
