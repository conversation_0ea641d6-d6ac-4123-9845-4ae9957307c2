import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { IonicModule, ModalController } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { Constants } from 'src/app/constants/constants';
import { AdmissionList, Admission } from 'src/app/interfaces/common-interface';
import { DoOperation, SearchAction } from 'src/app/interfaces/messages';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { isBlank, isPresent } from 'src/app/utils/utils';
import { Config } from 'src/app/constants/config';
import { AdmissionService } from 'src/app/services/admission-service/admission.service';
import { HeaderPlainModule } from '../header-plain/header-plain.module';
import { SearchBarRecipientsModule } from '../search-bar-recipients/search-bar-recipients.module';
import { SitesModule } from '../sites/sites.module';

@Component({
  selector: 'app-admission',
  templateUrl: './admission.component.html',
  styleUrls: ['./admission.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, TranslateModule, HeaderPlainModule, SearchBarRecipientsModule, SitesModule]
})
export class AdmissionComponent implements OnInit {
  @Input() from;
  @Input() admissionID;
  @Input() patientID;
  @Input() siteIds;
  @Input() initialAdmissions;
  admissionSearchForm: UntypedFormGroup;
  cancel = Constants.cancel;
  admissionList = [];
  pagination = {
    pageCount: 0,
    offset: Constants.offset
  };
  showSiteSelection = false;
  showLoadMore = true;
  siteLabel = 'LABELS.ADMISSION_SITES';
  constants = Constants;
  constructor(
    private admissionService: AdmissionService,
    private modalController: ModalController,
    private readonly formBuilder: UntypedFormBuilder,
    public readonly sharedService: SharedService
  ) {}

  ngOnInit() {
    const isEnableMultiSite = this.sharedService.isEnableConfig(Config.enableMultiSite);
    this.showSiteSelection = isEnableMultiSite && this.sharedService?.userData?.mySites?.length > 1 && this.from !== 'PatientStartNewChat';
    this.admissionSearchForm = this.formBuilder.group({
      searchText: ['']
    });
    if (isPresent(this.initialAdmissions)) {
      this.admissionList = this.initialAdmissions;
    } else {
      this.getAdmissionList();
    }
  }

  dismiss(val: any): void {
    this.modalController.dismiss(val);
  }

  getAdmissionList(event?: any): void {
    if (!event) {
      this.sharedService.isLoading = true;
    }
    if (this.pagination.pageCount === 0) {
      this.admissionList = [];
    }
    this.admissionService
      .getAdmissionList({
        currentPage: this.pagination.pageCount,
        patientId: this.patientID,
        searchKey: this.admissionSearchForm.value.searchText,
        from: this.from,
        siteIds: this.siteIds,
        showSiteSelection: this.showSiteSelection
      })
      .subscribe((response: AdmissionList) => {
        this.sharedService.isLoading = false;
        response.content = response.content.map((admission: Admission) => ({
          ...admission,
          isParticipant: admission?.participant ? 1 : 0
        }));
        this.admissionList = this.pagination.pageCount === 0 ? response.content : [...this.admissionList, ...response.content];
        this.showLoadMore = this.admissionList.length !== response.page.totalElements;
        if (event) {
          event.target.complete();
        }
      });
  }

  loadData(event): void {
    this.pagination.pageCount += Constants.pageCount;
    this.getAdmissionList(event);
  }

  selectAdmission(admission: Admission): void {
    this.dismiss(admission);
  }

  filterSitesData(data: any[]): void {
    this.siteIds = data;
    this.pagination.pageCount = 0;
    this.getAdmissionList();
  }

  searchOperations(searchAction: SearchAction): void {
    if (searchAction.do === DoOperation.reset) {
      this.admissionSearchForm.reset();
    }
    this.pagination.pageCount = 0;
    this.admissionList = [];
    if (searchAction.do === DoOperation.search) {
      this.admissionSearchForm.patchValue({
        searchText: searchAction.value
      });
    }
    this.getAdmissionList();
  }

  onLoadSites(event: any[]): void {
    if (this.siteIds.length > 1 || isBlank(this.siteIds)) {
      this.siteIds = event;
    }
  }
}
