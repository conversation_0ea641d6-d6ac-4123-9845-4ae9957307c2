import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AngularDelegate, IonicModule, ModalController, NavParams } from '@ionic/angular';

import { Constants } from 'src/app/constants/constants';
import { Admission } from 'src/app/interfaces/common-interface';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { RouterModule } from '@angular/router';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { TranslateModule } from '@ngx-translate/core';
import { DoOperation, SearchAction } from 'src/app/interfaces/messages';
import { AdmissionComponent } from './admission.component';

describe('AdmissionComponent', () => {
  let component: AdmissionComponent;
  let fixture: ComponentFixture<AdmissionComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        TranslateTestingModule
      ],
      providers: [
        NavParams,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        AngularDelegate,
        ModalController
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AdmissionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('#selectAdmission', () => {
    it('should call dismiss with the correct admission', () => {
      const admission: Admission = {
        admissionId: "6b0fc86e-193e-48d3-a90a-745309e19a32",
        siteId: 64075,
        siteName: "Test Site 1",
        admissionName: "07/15/2024 - Pre-Registration - Hospice - 5822321 - Test Site 1"
      };
      spyOn(component, 'dismiss');
      component.selectAdmission(admission);
      expect(component.dismiss).toHaveBeenCalledWith(admission);
    });

    it('should handle null admission gracefully', () => {
      spyOn(component, 'dismiss');
      component.selectAdmission(null);
      expect(component.dismiss).toHaveBeenCalledWith(null);
    });
  });

  describe('#loadData', () => {
    it('increment page count', () => {
      const initialPageCount = component.pagination.pageCount;
      const event = {};
      component.loadData(event);
      expect(component.pagination.pageCount).toBe(initialPageCount + Constants.pageCount);
    });
    it('call getAdmissionList function after increment page count', () => {
      const event = {};
      spyOn(component, 'getAdmissionList');
      component.loadData(event);
      expect(component.getAdmissionList).toHaveBeenCalledWith(event);
    });
  });
  it('dismiss admission modal while click on back', () => {
    component.dismiss('');
    expect(component.dismiss).toBeDefined();
  });

  it('search admission list', () => {
    const action: SearchAction = {
      do: DoOperation.search,
      value: 'test'
    };
    component.admissionList = [];
    component.searchOperations(action);
    expect(component.searchOperations).toBeDefined();
  });
});
