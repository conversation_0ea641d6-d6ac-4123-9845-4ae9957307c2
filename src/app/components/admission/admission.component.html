<ion-header>
  <app-header-plain headerTitle="TITLES.ADMISSIONS" (close)="dismiss('')"></app-header-plain>
  @if (from === 'PatientStartNewChat') {
    <ion-row class="multiple-admission-notes">
      <ion-col size="12" class="ion-text-center">
        {{ 'MESSAGES.SELECT_AN_ADMISSION_TO_START_CHAT' | translate }}
      </ion-col>
    </ion-row>
  }
</ion-header>
@if (showSiteSelection) {
  <app-sites
    (filterSites)="filterSitesData($event)"
    [selectedSiteIds]="siteIds"
    (onLoadSites)="onLoadSites($event)"
    [siteLabel]="siteLabel"
  ></app-sites>
}
<app-search-bar-recipients (searchAction)="searchOperations($event)"></app-search-bar-recipients>

<ion-content>
  @if (!admissionList?.length && !sharedService.isLoading) {
    <ion-list class="user-list hide-border">
      <ion-item class="ion-text-center">
        <ion-label>{{ 'MESSAGES.NO_ITEM_FOUND' | translate }}</ion-label>
      </ion-item>
    </ion-list>
  } @else {
    <ion-list>
      @for (admission of admissionList; track admission.admissionId; let i = $index) {
        @if (from === 'PatientStartNewChat') {
          <ion-card mode="md" [class.highlight]="admission.admissionId === admissionID" (click)="selectAdmission(admission)">
            <ion-row>
              <ion-col size="12" class="lineHeight1_5">
                <div> {{'ADMISSION.LABELS.ADMISSION_START_DATE' | translate }}: {{admission?.startDate | date: constants?.dateFormat?.mmddyyyy}}</div>
                <div> {{'ADMISSION.LABELS.LINE_OF_BUSINESS' | translate }}: {{admission?.lineOfService}}</div>
                <div> {{'LABELS.SITE' | translate }}: {{admission?.siteName}}</div>
                <div> {{'GENERAL.STATUS' | translate }}: {{admission?.status}}</div>
              </ion-col>
            </ion-row>
          </ion-card>
        }@else {
          <ion-item
          class="user-list-item ion-text-center"
          [class.highlight]="admission.admissionId === admissionID"
          (click)="selectAdmission(admission)"
          [id]="admission.admissionId">
            <ion-label>{{ admission?.admissionName }}</ion-label>
          </ion-item>
        }
      }
    </ion-list>
    @if (showLoadMore) {
      <ion-infinite-scroll threshold="100px" (ionInfinite)="loadData($event)" id="infinit-scroll">
        <ion-infinite-scroll-content loadingSpinner="dots"> </ion-infinite-scroll-content>
      </ion-infinite-scroll>
    }
  }
</ion-content>
