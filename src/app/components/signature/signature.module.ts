import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { SignatureComponent } from 'src/app/components/signature/signature.component';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { SignaturePadModule } from 'angular16-signaturepad';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, SharedModule, TranslateModule, HeaderPlainModule, SignaturePadModule],
  declarations: [SignatureComponent],
  exports: [SignatureComponent]
})
export class SignatureComponentModule {}
