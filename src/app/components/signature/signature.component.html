<ion-header class="common-plain-header">
  <ion-toolbar>
    <ion-buttons slot="start" class="header-menu left">
      <ion-button (click)="cancel()" id="cancel" class="ion-text-capitalize">{{ 'BUTTONS.CANCEL' | translate }} </ion-button>
    </ion-buttons>
    <ion-buttons slot="end" class="header-menu right">
      <ion-button (click)="accept(sign)" id="accept" class="ion-text-capitalize">{{ 'BUTTONS.ACCEPT' | translate }} </ion-button>
    </ion-buttons>
    <h1 class="header-title">{{ 'TITLES.DRAW_YOUR_SIGNATURE' | translate }}</h1>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="sign-pad-container">
    <signature-pad #sign class="common-sign-pad" [options]="signaturePadOptions" id="sign-pad"></signature-pad>
    <ion-button color="light" (click)="clear(sign)" id="clear" class="ion-text-capitalize" *ngIf="!isDevice">{{
      'BUTTONS.CLEAR' | translate
    }}</ion-button>
  </div>
</ion-content>
<ion-footer *ngIf="isDevice">
  <ion-row>
    <ion-col size="12" class="ion-text-center">
      <ion-button color="light" (click)="clear(sign)" id="clear" class="ion-text-capitalize">{{ 'BUTTONS.CLEAR' | translate }}</ion-button>
    </ion-col>
  </ion-row>
</ion-footer>
