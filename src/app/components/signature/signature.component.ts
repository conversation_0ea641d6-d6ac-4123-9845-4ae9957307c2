import { Component } from '@angular/core';
import { ModalController, Platform } from '@ionic/angular';
import { Constants } from 'src/app/constants/constants';
import { CommonService } from 'src/app/services/common-service/common.service';

@Component({
  selector: 'app-signature',
  templateUrl: './signature.component.html',
  styleUrls: ['./signature.component.scss']
})
export class SignatureComponent {
  signaturePadOptions = {
    canvasWidth: Constants.canVasHeightAndWidth.canvasWidth,
    canvasHeight: Constants.canVasHeightAndWidth.canvasHeight
  };
  isDevice = false;
  constructor(
    private readonly modalController: ModalController,
    private readonly common: CommonService,
    private readonly platform: Platform
  ) { }

  ionViewDidEnter() {
    const canvas = document.getElementsByTagName('canvas');
    setTimeout(() => {
      const setCanvasWidth = window.innerWidth - 40;
      const setCanvasHeight = window.innerHeight - 150;
      if (canvas) {
        if (canvas.length === 4) {
          if (canvas[3].parentElement.tagName.toLowerCase() === 'signature-pad') {
            if (this.platform.is('capacitor') && !this.platform.is('ipad') && !this.platform.is('tablet')) {
              this.isDevice = true;
              canvas[3].width = setCanvasWidth;
              canvas[3].height =
                setCanvasHeight < Constants.canVasHeightAndWidth.canvasHeight ? Constants.canVasHeightAndWidth.canvasHeight : setCanvasHeight;
            } else if (!this.platform.is('ipad') && !this.platform.is('tablet')) {
              canvas[3].width =
                setCanvasWidth > Constants.canVasHeightAndWidth.maxCanvasWidth ? Constants.canVasHeightAndWidth.maxCanvasWidth : setCanvasWidth;
            }
          }
        }
      }
    }, 200);
  }

  cancel(): void {
    this.modalController.dismiss();
  }

  accept(sign: any): void {
    if (!sign.isEmpty()) {
      this.modalController.dismiss({
        imageData: sign.toDataURL()
      });
    } else {
      this.common.showMessage(this.common.getTranslateData('VALIDATION_MESSAGES.SIGN_REQUIRED'));
    }
  }

  clear(sign: any): void {
    sign.clear();
  }
}
