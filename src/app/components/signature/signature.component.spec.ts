import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController, Platform } from '@ionic/angular';
import { SignatureComponent } from './signature.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonService } from 'src/app/services/common-service/common.service';
import { RouterModule } from '@angular/router';

describe('SignatureComponent', () => {
  let component: SignatureComponent;
  let fixture: ComponentFixture<SignatureComponent>;
  let modalController: ModalController;
  let platformMock: jasmine.SpyObj<Platform>;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SignatureComponent],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), TranslateModule.forRoot()],
      providers: [CommonService],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(SignatureComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('execute call cancel method', () => {
    component.cancel();
    expect(component.cancel).toBeTruthy();
  });

  it('should call the clear method', () => {
    const signMock = jasmine.createSpyObj('sign', ['clear']);
    component.clear(signMock);
    expect(signMock.clear).toHaveBeenCalled();
  });

  it('should dismiss modal if sign is not empty', () => {
    const signMock = jasmine.createSpyObj('sign', ['isEmpty', 'toDataURL']);
    signMock.isEmpty.and.returnValue(false);
    signMock.toDataURL.and.returnValue('data:image/png;base64,...');
    component.accept(signMock);
    expect(signMock.isEmpty).toHaveBeenCalled();
    expect(signMock.toDataURL).toHaveBeenCalled();
    expect(modalController.dismiss).toHaveBeenCalledWith({ imageData: 'data:image/png;base64,...' });
  });

  it('should show message if sign is empty', () => {
    const signMock = jasmine.createSpyObj('sign', ['isEmpty']);
    signMock.isEmpty.and.returnValue(true);
    component.accept(signMock);
    expect(signMock.isEmpty).toHaveBeenCalled();
    expect(modalController.dismiss).not.toHaveBeenCalled();
  });
  it('should not set isDevice or resize canvas if not running on Capacitor or if running on iPad', (done) => {
    platformMock?.is('capacitor') ? true : false;
    component.ionViewDidEnter();
    component.isDevice = false;
    fixture.detectChanges();
    setTimeout(() => {
      expect(component.isDevice).toBeFalse();

      const canvasMock = document.createElement('canvas');
      const signaturePadParentMock = document.createElement('signature-pad');
      const canvasParentMock = document.createElement('div');
      canvasParentMock.appendChild(canvasMock);
      signaturePadParentMock.appendChild(canvasParentMock);
      document.body.appendChild(signaturePadParentMock);

      expect(canvasMock.width).toBe(300);
      expect(canvasMock.height).toBe(150);

      done();
    }, 300);
  });

  it('should set isDevice to true and resize canvas if running on Capacitor and not on iPad', (done) => {
    platformMock?.is('ipad') ? true : false;
    component.isDevice = true;
    component.ionViewDidEnter();
    fixture.detectChanges();
    setTimeout(() => {
      expect(component.isDevice).toBeTruthy();
      const canvasMock = document.createElement('canvas');
      const signaturePadParentMock = document.createElement('signature-pad');
      const canvasParentMock = document.createElement('div');
      canvasParentMock.appendChild(canvasMock);
      signaturePadParentMock.appendChild(canvasParentMock);
      document.body.appendChild(signaturePadParentMock);

      expect(canvasMock.width).toBeDefined(window.innerWidth - 40);
      expect(canvasMock.height).toBeDefined(window.innerHeight - 150 < 180 ? 180 : window.innerHeight - 150);

      done();
    }, 300);
  });
});
