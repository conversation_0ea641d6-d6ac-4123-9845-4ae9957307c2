import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ModalController } from '@ionic/angular';

declare let google;
@Component({
  selector: 'app-map-view',
  templateUrl: './map-view.component.html',
  styleUrls: ['./map-view.component.scss']
})
export class MapViewComponent implements OnInit {
  @Input() mapLocation: any;
  @ViewChild('javascriptMap', { static: true }) javascriptMapRef: ElementRef;
  constructor(private readonly modalController: ModalController) { }

  ngOnInit() {
    this.loadJavaScriptMap();
  }

  loadJavaScriptMap() {
    const latLng = new google.maps.LatLng(this.mapLocation[0].lat, this.mapLocation[0].lng);
    const mapOptions = {
      center: latLng,
      zoom: 16,
      mapTypeId: google.maps.MapTypeId.ROADMAP
    };
    const map = new google.maps.Map(this.javascriptMapRef.nativeElement, mapOptions);
    const marker = new google.maps.Marker({
      position: latLng
    });
    marker.setMap(map);
  }

  goBack(): void {
    this.modalController.dismiss();
  }
}
