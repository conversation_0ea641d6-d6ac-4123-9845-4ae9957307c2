import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';

import { TestConstants } from 'src/app/constants/test-constants';
import { MapViewComponent } from './map-view.component';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Keepalive } from '@ng-idle/keepalive';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared.module';
import { Idle, IdleExpiry } from '@ng-idle/core';

describe('MapViewComponent', () => {
  let component: MapViewComponent;
  let fixture: ComponentFixture<MapViewComponent>;
  let modalController: ModalController;
  const { modalSpy } = TestConstants;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [MapViewComponent],
      imports: [IonicModule.forRoot(), HttpClientModule, SharedModule, HttpClientTestingModule, TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        ModalController,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    (<any>window).google = {
      maps: {
        LatLng: jasmine.createSpy('LatLng'),
        Map: jasmine.createSpy('Map'),
        Marker: jasmine.createSpy('Marker').and.returnValue({
          setMap: jasmine.createSpy('setMap')
        }),
        MapTypeId: {
          ROADMAP: 'roadmap'
        }
      }
    };

    fixture = TestBed.createComponent(MapViewComponent);
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    component = fixture.componentInstance;
    component.mapLocation = [
      {
        lat: '23.0355105',
        long: '72.5042686'
      }
    ];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call goBack', () => {
    component.goBack();
    expect(component.goBack).toBeDefined();
  });

  it('should create map with correct options', () => {
    component.loadJavaScriptMap();
    expect(component.loadJavaScriptMap).toBeDefined();
  });
});
