.overlay {
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 39999;
}
.video-main-screen {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center;
}
.fullscreen-mute {
    position: absolute;
    margin: -55px 20px;
    color: orangered;
    z-index: 99999;
}
.user-wait {
    position: absolute;
    top: 40%;
    left: 40%;
    font-size: 15px;
}
.remote-tiles-conatiner {
    display: flex;
    .video-tile-wrap {
        display: inline-block;
        margin-left: 15px;
        .video-tile-sml {
            margin: -50px;
            width: 100px;
            height: 70px;
            margin: 10px auto;
            border: 3px solid lightgrey;
            border-radius: 3px;
            margin-bottom: 32px;
            cursor: pointer;
            background-size: cover;
            .remote-tile-mute {
                position: absolute;
                z-index: 1;
                color: orangered;
                font-size: 27px;
            }
        }
    }
}
.display-flex {
    display: flex;
}

.setBottomButton {
    position: absolute;
    bottom: 50px;
    width: 100%;
}

.hide-overlay {
    display: none;
}
.show-overlay {
    display: block;
}

.show-message {
    position: absolute;
    width: 100%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #1c1c1c7d;
    color: #ccc;
    font-size: 16px;
    z-index: 999;
}
.setBottomButton{
    --ion-color-danger: #eb445a !important;
}
