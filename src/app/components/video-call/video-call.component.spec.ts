import { Apollo } from 'apollo-angular';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AlertController, IonicModule } from '@ionic/angular';

import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';
import { VideoCallComponent } from './video-call.component';
import { VideoCallService } from 'src/app/services/video-call/video-call.service';
import { VideoCall } from 'src/app/constants/video-call';

describe('VideoCallComponent', () => {
  let component: VideoCallComponent;
  let fixture: ComponentFixture<VideoCallComponent>;
  let alertController: AlertController;
  const { alertSpy } = TestConstants;
  let videoCallService: VideoCallService
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [VideoCallComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), HttpClientModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        Apollo,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(VideoCallComponent);
    videoCallService = TestBed.inject(VideoCallService);
    component = fixture.componentInstance;
    alertController = TestBed.inject(AlertController);
    spyOn(alertController, 'create').and.callFake(() => {
      return alertSpy;
    });
    alertSpy.present.and.stub();
    spyOn(alertController, 'dismiss').and.stub();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call toggleMute', () => {
    component.toggleMute();
    expect(component.microphonePrivacy).toBe(true);
  });

  it('should call toggleCamera', () => {
    component.toggleCamera();
    expect(component.cameraPrivacy).toBe(true);
  });

  it('should call toggleCameraView', () => {
    component.toggleCameraView();
    expect(component.toggleCameraView).toBeDefined();
  });

  it('should call resetValue', () => {
    component.resetValue();
    expect(component.resetValue).toBeDefined();
  });

  it('should call unSubscribeVideoEvents', () => {
    component.unSubscribeVideoEvents();
    expect(component.unSubscribeVideoEvents).toBeDefined();
  });

  it('should call showVideoAlert', () => {
    component.showVideoAlert('All users left this conference');
    expect(component.showVideoAlert).toBeDefined();
  });

  describe('endVideoCall', () => {
    it('should delete room if participant is not present', () => {
      videoCallService.participant = null;
      spyOn(videoCallService, 'deleteRoom').and.stub();
      component.endVideoCall();
      expect(videoCallService.deleteRoom).toHaveBeenCalled();
    });

    it('should call updateToAppLessVideo if participant is not present and isAppLessWorkFlow is true', () => {
      videoCallService.participant = null;
      videoCallService.isAppLessWorkFlow = true;
      component.endVideoCall();
      expect(videoCallService.isAppLessWorkFlow).toBe(false);
    });

    it('should not call updateToAppLessVideo if participant is present', () => {
      spyOn(videoCallService.sharedService, 'updateToAppLessVideo').and.stub();
      videoCallService.participant = true;
      component.endVideoCall();
      expect(videoCallService.sharedService.updateToAppLessVideo).not.toHaveBeenCalled();
    });

    it('should not call updateToAppLessVideo if isAppLessWorkFlow is false', () => {
      spyOn(videoCallService.sharedService, 'updateToAppLessVideo').and.stub();
      videoCallService.participant = false;
      videoCallService.isAppLessWorkFlow = false;
      component.endVideoCall();
      expect(videoCallService.sharedService.updateToAppLessVideo).not.toHaveBeenCalled();
    });
  });
});
