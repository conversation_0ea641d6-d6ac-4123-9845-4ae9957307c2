import { Component } from '@angular/core';
import { isBlank } from 'src/app/utils/utils';
import { VideoCall } from 'src/app/constants/video-call';
import { Subscription } from 'rxjs';
import { VideoCallService } from '../../services/video-call/video-call.service';
import { CommonService } from '../../services/common-service/common.service';
import { Constants } from '../../constants/constants';

@Component({
  selector: 'app-video-call',
  templateUrl: './video-call.component.html',
  styleUrls: ['./video-call.component.scss']
})
export class VideoCallComponent {
  microphonePrivacy = false;
  cameraPrivacy = false;
  videoTiles = [];
  allMembers = [];
  videoChanged = null;
  videoCallEndSubs = null;
  vidyoClientSubscribe = null;
  connectionMessage: string;
  private userLeftSubs: Subscription;
  constructor(
    public videoCallService: VideoCallService,
    private common: CommonService
  ) {
    this.connectionMessage = 'VIDEO_MESSAGES.CONNECTING';
    this.commonSubscribeCode();
  }

  commonSubscribeCode() {
    if (
      this.videoCallService.sharedService.platform.is('android') ||
      this.videoCallService.sharedService.platform.is('mobileweb') ||
      this.videoCallService.sharedService.platform.is('desktop')
    ) {
      this.videoCallService.videoNotFound.subscribe((data) => {
        if (isBlank(data.avatar)) {
          data.avatar = Constants.avatarImage;
        }
      });

      if (isBlank(this.videoChanged) || (this.videoChanged && this.videoChanged.closed)) {
        this.videoChanged = this.videoCallService.videoChanged.subscribe((eventData) => {
          this.allMembers = eventData[0];
          this.videoTiles = eventData[1];
          this.videoCallService.playAudioForCall.emit({
            playSound: false,
            action: ''
          });
          if (eventData && eventData[0] === VideoCall.noRemoteParticipant && !this.videoCallService.participant) {
            this.showVideoAlert(this.common.getTranslateData('VIDEO_MESSAGES.ALL_USER_LEFT'));
          }
        });
      }

      if (isBlank(this.videoCallEndSubs) || (this.videoCallEndSubs && this.videoCallEndSubs.closed)) {
        this.videoCallEndSubs = this.videoCallService.videoCallEnd.subscribe((eventData) => {
          this.showVideoAlert(eventData.data.title);
        });
      }

      if (isBlank(this.vidyoClientSubscribe) || (this.vidyoClientSubscribe && this.vidyoClientSubscribe.closed)) {
        this.vidyoClientSubscribe = this.videoCallService.vidyoClient.subscribe((data) => {
          if (data && data.assignConnectionData) {
            this.connectionMessage = 'VIDEO_MESSAGES.WAITING';
          }
          if (data && data.playAudio) {
            if (!this.videoCallService.joinChat) {
              this.videoCallService.playAudioForCall.emit({
                playSound: true,
                action: VideoCall.forCall
              });
            }
          }
        });
      }

      this.videoCallService.waitingMessage.subscribe();

      this.userLeftSubs = this.videoCallService.userLeft.subscribe((data) => {
        if (data[0].participents === Constants.noParticipents) {
          this.showVideoAlert(this.common.getTranslateData('VIDEO_MESSAGES.ALL_USER_LEFT'));
        }
      });
    }
  }

  unSubscribeVideoEvents(): void {
    this.videoCallService.callInitiatedRoomId = '';
    if (this.vidyoClientSubscribe) {
      this.vidyoClientSubscribe.unsubscribe();
    }
    if (this.videoCallEndSubs) {
      this.videoCallEndSubs.unsubscribe();
    }
    if (this.videoChanged) {
      this.videoChanged.unsubscribe();
    }
  }

  showVideoAlert(title: string): void {
    this.videoCallService.waitingAlertMsg = true;
    const buttons = [
      {
        text: this.common.getTranslateData('BUTTONS.WAIT'),
        confirm: true,
        class: 'wait-btn'
      },
      {
        text: this.common.getTranslateData('BUTTONS.CANCEL'),
        confirm: false,
        class: 'cancel-btn'
      }
    ];
    const header = title;
    const alertData = {
      message: this.common.getTranslateData('VIDEO_MESSAGES.CANCEL_MESSAGE'),
      header,
      buttons,
      cssClass: VideoCall.videoClass.commonSweetAlert,
      alertId: VideoCall.alertMsgId,
      backDrop: false
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (!confirmation) {
        this.videoCallService.initiatorEnd = true;
        this.videoCallService.joinChat = false;
        this.videoCallService.playAudio(false);
        this.videoCallService.disconnectVideo();
        this.common.closeAllAlert();
      } else {
        this.videoCallService.waitingAlertMsg = false;
        this.videoCallService.playAudioForCall.emit({
          playSound: false,
          action: VideoCall.forCall
        });
      }
    });
  }

  toggleMute(): void {
    this.microphonePrivacy = !this.microphonePrivacy;
    this.videoCallService.toggleMute(this.microphonePrivacy);
  }

  toggleCamera(): void {
    this.cameraPrivacy = !this.cameraPrivacy;
    this.videoCallService.changeCameraPrivacy(this.cameraPrivacy);
  }

  disconnect(): void {
    if (this.videoCallService.sharedService.userData?.appLessSession) {
      this.common.showAlert({ message: this.common.getTranslateData('VIDEO_MESSAGES.CANCEL_MESSAGE_APPLESS') }).then((response) => {
        if (response) {
          this.endVideoCall();
          this.unSubscribeVideoEvents();
          this.resetValue();
          this.videoCallService.disconnectApplessClient();
          const data = {
            action: VideoCall.sendAppLessAction.participantJoin,
            userid: this.videoCallService.sharedService.userData.userId,
            token: this.videoCallService.sharedService.userData.authenticationToken
          };
          this.videoCallService.sharedService.updateToAppLessVideo(data).subscribe();
        }
      });
    } else {
      this.endVideoCall();
      this.videoCallService.disconnectVideo();
      this.unSubscribeVideoEvents();
      this.resetValue();
    }
  }

  toggleCameraView(): void {
    this.videoCallService.cycleCamera();
  }

  endVideoCall() {
    if (!this.videoCallService.participant) {
      this.videoCallService.deleteRoom();
      if (this.videoCallService.isAppLessWorkFlow) {
        const data = {
          action: VideoCall.sendAppLessAction.initiatorLeft,
          chatroom: this.videoCallService.currentChatroomId,
          status: VideoCall.leftStatus
        };
        this.videoCallService.sharedService.updateToAppLessVideo(data).subscribe();
        this.videoCallService.isAppLessWorkFlow = false;
      }
    }
  }
  ionViewDidLeave() {
    this.userLeftSubs.unsubscribe();
    this.resetValue();
  }
  resetValue() {
    this.microphonePrivacy = false;
    this.cameraPrivacy = false;
  }
}
