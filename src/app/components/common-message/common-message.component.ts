import { Component } from '@angular/core';
import { PopoverController, NavParams } from '@ionic/angular';

@Component({
  selector: 'app-common-message',
  templateUrl: './common-message.component.html',
  styleUrls: ['./common-message.component.scss']
})
export class CommonMessageComponent {
  message = '';
  constructor(
    private readonly popoverController: PopoverController,
    private readonly navParams: NavParams
  ) {
    this.message = this.navParams.get('message');
  }

  close(): void {
    this.popoverController.dismiss();
  }
}
