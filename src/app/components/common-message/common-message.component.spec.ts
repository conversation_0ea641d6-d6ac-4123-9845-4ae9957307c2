import { SafePipe } from './../../pipes/safe.pipe';
import { SharedModule } from 'src/app/shared.module';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, NavParams, PopoverController } from '@ionic/angular';
import { CommonMessageComponent } from './common-message.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';

describe('CommonMessageComponent', () => {
  let component: CommonMessageComponent;
  let fixture: ComponentFixture<CommonMessageComponent>;
  let popoverController: PopoverController;
  const popoverSpy = TestConstants.popoverSpy;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [CommonMessageComponent, SafePipe],
      imports: [IonicModule.forRoot(), SharedModule],
      providers: [NavParams, SafePipe, PopoverController],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popoverSpy;
    });
    popoverSpy.present.and.stub();
    spyOn(popoverController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(CommonMessageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should close', () => {
    component.close();
    expect(component.close).toBeTruthy();
  });
});
