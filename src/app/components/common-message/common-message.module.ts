import { VideoCallComponent } from './../video-call/video-call.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { CommonMessageComponent } from './common-message.component';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared.module';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, TranslateModule, SharedModule],
  declarations: [CommonMessageComponent],
  exports: [CommonMessageComponent]
})
export class CommonMessageComponentModule {}
