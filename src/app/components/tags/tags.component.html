<div class="thread-tag-wrap">
  <ng-container *ngTemplateOutlet="tagTemplate; context: { tags: displayTags }"></ng-container>
  <div class="align-self-center">
    <ion-badge slot="start" class="overflow-count count-badge" *ngIf="hiddenTags.length && maxAllowedTags !== 0" (click)="toggleTagList(true)">
      +{{ hiddenTags.length }}
    </ion-badge>
    <ion-badge slot="start" class="overflow-count count-badge" *ngIf="hiddenTags.length && maxAllowedTags === 0" (click)="toggleTagList(true)">
      <ion-icon name="pricetags-outline"></ion-icon>
      {{ 'BUTTONS.TAGS' | translate }}
    </ion-badge>
  </div>
</div>

<ion-modal
  #modal
  [initialBreakpoint]="0.4"
  [breakpoints]="[0, 0.4, 0.6, 0.8]"
  handleBehavior="cycle"
  [isOpen]="isTagListOpened"
  (didDismiss)="toggleTagList(false)"
  mode="ios"
>
  <ng-template>
    <ion-content class="ion-padding">
      <ion-list>
        <ion-item-group class="mt-1" *ngIf="threadLevelTags.length">
          <ion-item-divider>
            <ion-label> {{ 'LABELS.THREAD' | translate }} </ion-label>
          </ion-item-divider>
          <ion-item lines="none">
            <div class="thread-tag-wrap">
              <ng-container *ngTemplateOutlet="tagTemplate; context: { tags: threadLevelTags }"></ng-container>
            </div>
          </ion-item>
        </ion-item-group>
        <ion-item-group class="mt-1" *ngIf="messageLevelTags.length">
          <ion-item-divider>
            <ion-label> {{ 'LABELS.MESSAGE' | translate }} </ion-label>
          </ion-item-divider>
          <ion-item lines="none">
            <div class="thread-tag-wrap">
              <ng-container *ngTemplateOutlet="tagTemplate; context: { tags: messageLevelTags }"></ng-container>
            </div>
          </ion-item>
        </ion-item-group>
      </ion-list>
    </ion-content>
  </ng-template>
</ion-modal>

<ng-template #tagTemplate let-tags="tags">
  <div class="thread-tag-element" *ngFor="let tag of tags" [class.dynamic-bg-color]="true" [style]="colorTag(tag)">
    <span
      class="tag"
      [ngClass]="{
        shorter: !isTagListOpened,
        'mr-20': !hideDelete && ((isThread && isThreadLevel(tag)) || (!isThread && !isThreadLevel(tag))) && !tag.enableIntegration
      }"
      [class.dynamic-text-color]="true"
      [style]="colorTag(tag)"
    >
      {{ tag.name }}
    </span>
    <span
      *ngIf="!hideDelete && ((isThread && isThreadLevel(tag)) || (!isThread && !isThreadLevel(tag))) && !tag.enableIntegration"
      (click)="$event.stopPropagation(); removeTagsFromMessage(tag)"
      class="tag-close"
    >
      X
    </span>
  </div>
</ng-template>
