import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { IonicModule, ModalController } from '@ionic/angular';
import { TagsComponent } from 'src/app/components/tags/tags.component';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { RouterModule, UrlSerializer } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TestConstants } from 'src/app/constants/test-constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of } from 'rxjs';
import { SharedService } from 'src/app/services/shared-service/shared.service';

describe('TagsComponent', () => {
  let component: TagsComponent;
  let fixture: ComponentFixture<TagsComponent>;
  let modalController: ModalController;
  let commonService: CommonService;
  let sharedService: SharedService;
  let httpService: HttpService;
  const { modalSpy } = TestConstants;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [TagsComponent],
      imports: [
        IonicModule.forRoot(),
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateTestingModule
      ],
      providers: [
        UrlSerializer,
        NgxPermissionsService,
        Idle,
        IdleExpiry,
        Keepalive,
        NgxPermissionsStore,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ]
    });
    modalController = TestBed.inject(ModalController);
    commonService = TestBed.inject(CommonService);
    httpService = TestBed.inject(HttpService);
    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    fixture = TestBed.createComponent(TagsComponent);
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    component = fixture.componentInstance;
  });

  it('should create the TagsComponent', () => {
    expect(component).toBeTruthy();
  });

  it('should set displayTags and hiddenTags correctly when maxAllowedTags is 0', () => {
    component.tags = [
      { name: 'Tag1', id: 1 },
      { name: 'Tag2', id: 2 },
      { name: 'Tag3', id: 3 }
    ];
    component.maxAllowedTags = 0;
    component.setTags();

    expect(component.displayTags.length).toBe(0);
    expect(component.hiddenTags.length).toBe(3);
  });

  it('should set displayTags and hiddenTags correctly when maxAllowedTags is greater than the number of tags', () => {
    component.tags = [
      { name: 'Tag1', id: 1 },
      { name: 'Tag2', id: 2 },
      { name: 'Tag3', id: 3 }
    ];
    component.maxAllowedTags = 5;
    component.setTags();

    expect(component.displayTags.length).toBe(3);
    expect(component.hiddenTags.length).toBe(0);
  });

  it('should set displayTags and hiddenTags correctly when maxAllowedTags is less than the number of tags', () => {
    component.tags = [
      { name: 'Tag1', id: 1 },
      { name: 'Tag2', id: 2 },
      { name: 'Tag3', id: 3 }
    ];
    component.maxAllowedTags = 1;
    component.setTags();

    expect(component.displayTags.length).toBe(1);
    expect(component.hiddenTags.length).toBe(2);
  });

  it('should toggle tag list correctly when isOpen is true', () => {
    component.isTagListOpened = false;
    component.hiddenTags = [{ name: 'Tag1' }, { name: 'Tag2' }];
    component.toggleTagList(true);

    expect(component.isTagListOpened).toBe(true);
    expect(component.allTagsInThread).toEqual(component.hiddenTags);
  });

  it('should toggle tag list correctly when isOpen is false', () => {
    component.isTagListOpened = true;
    component.hiddenTags = [{ name: 'Tag1' }, { name: 'Tag2' }];
    component.toggleTagList(false);

    expect(component.isTagListOpened).toBe(false);
    expect(component.allTagsInThread).toEqual([]);
  });

  it('should generate the correct CSS style for a tag with valid colors', () => {
    const tag = {
      bgColor: 'red',
      fontColor: 'white'
    };
    const cssStyle = component.colorTag(tag);
    expect(cssStyle).toBe('background: red; border-right-color: red; color: white');
  });
  it('should execute removeTagsFromMessage', fakeAsync(() => {
    const tag = {
      id: 32423
    };
    spyOn(commonService, 'showAlert').and.resolveTo(true);
    spyOn(httpService, 'doPost').and.returnValue(of(''));
    spyOn(modalController, 'getTop').and.callFake(() => {
      return modalSpy;
    });
    component.removeTagsFromMessage(tag);
    tick(1000);
    expect(component.removeTagsFromMessage).toBeTruthy();
  }));

  it('should handle tags with meta property', () => {
    const tags = [
      {
        id: 1,
        name: 'Tag1',
        meta: JSON.stringify({ patientFacing: true, enableIntegration: true })
      }
    ];
    component.tags = tags;
    component.setTags();

    expect(component.displayTags[0].enableIntegration).toBe(true);
  });

  it('should handle tags with metaData property', () => {
    const tags = [
      {
        id: 1,
        name: 'Tag1',
        metaData: JSON.stringify({ patientFacing: true, enableIntegration: true })
      }
    ];
    component.tags = tags;
    component.setTags();

    expect(component.displayTags[0].enableIntegration).toBe(true);
  });

  it('should handle both meta and metaData properties, preferring meta', () => {
    const tags = [
      {
        id: 1,
        name: 'Tag1',
        meta: JSON.stringify({ enableIntegration: false }),
        metaData: JSON.stringify({ enableIntegration: true })
      }
    ];
    component.tags = tags;
    component.setTags();

    expect(component.displayTags[0].enableIntegration).toBe(true);
  });

  it('should use deepParseJSON to safely parse meta data', () => {
    const tags = [
      {
        id: 1,
        name: 'Tag1',
        meta: JSON.stringify({ enableIntegration: true })
      }
    ];
    component.tags = tags;
    component.setTags();
    expect(component.displayTags[0].enableIntegration).toBe(true);
  });

  it('should handle invalid JSON in meta data', () => {
    const tags = [
      {
        id: 1,
        name: 'Tag1',
        meta: '{invalid json'
      }
    ];
    component.tags = tags;
    component.setTags();
    // Should not throw an error and should set enableIntegration to false (default)
    expect(component.displayTags[0].enableIntegration).toBe(false);
  });

  it('should handle null meta data', () => {
    const tags = [
      {
        id: 1,
        name: 'Tag1',
        meta: null
      }
    ];
    component.tags = tags;
    component.setTags();

    // Should not throw an error and should set enableIntegration to false (default)
    expect(component.displayTags[0].enableIntegration).toBe(false);
  });
});
