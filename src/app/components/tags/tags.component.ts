import { Component, Input, Output, EventEmitter } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { Activity } from 'src/app/constants/activity';
import { APIs } from 'src/app/constants/apis';
import { TagType } from 'src/app/constants/constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { deepParseJSON, isBlank, isPresent } from 'src/app/utils/utils';

@Component({
  selector: 'app-tags',
  templateUrl: './tags.component.html',
  styleUrls: ['./tags.component.scss']
})
export class TagsComponent {
  _tags = [];
  displayTags = [];
  hiddenTags = [];
  allTagsInThread = [];
  threadLevelTags = [];
  messageLevelTags = [];
  isTagListOpened = false;
  @Output() removeMessageTags = new EventEmitter<number>();
  @Input() isThread = true;
  @Input() chatRoomId;
  @Input() messageId;
  @Input() hideDelete = false;
  @Input() threadLevel = true;
  @Output() emitTagOpenStatus = new EventEmitter<boolean>();
  _maxAllowedTags = 1;
  @Input() set maxAllowedTags(max: any) {
    this._maxAllowedTags = max;
    this.setTags();
  }
  get maxAllowedTags() {
    return this._maxAllowedTags;
  }
  @Input() set tags(tags: any) {
    this._tags = tags;
    this.setTags();
  }

  get tags() {
    return this._tags;
  }

  constructor(
    private readonly common: CommonService,
    private readonly sharedService: SharedService,
    private readonly httpService: HttpService,
    private modalCtrl: ModalController
  ) {}

  colorTag(tag) {
    return `background: ${tag.bgColor}; border-right-color: ${tag.bgColor}; color: ${tag.fontColor}`
  }
  isThreadLevel(tag): boolean {
    if (isPresent(tag?.type)) {
      return tag?.type === TagType.THREAD;
    }
    return tag?.threadLevel;
  }

  setTags() {
    this.displayTags = [];
    this.hiddenTags = [];
    this.threadLevelTags = [];
    this.messageLevelTags = [];
    if (this.tags) {
      const tags = [];
      this.tags.forEach((tag) => {
        if (!tags.find((item) => item.id === tag.id && !this.isThreadLevel(item))) {
          const metaString = tag?.metaData ? tag?.metaData : tag?.meta;
          const metaData = !isBlank(metaString) ? deepParseJSON(metaString) : {};
          tags.push({
            ...tag,
            enableIntegration: !isBlank(metaData) && !isBlank(metaData.enableIntegration) && Boolean(metaData.enableIntegration)
          });
        }
      });
      if (this.maxAllowedTags === 0) {
        this.hiddenTags = tags;
      } else if (tags.length > this.maxAllowedTags) {
        this.displayTags = tags.slice(0, this.maxAllowedTags);
        this.hiddenTags = tags.slice(this.maxAllowedTags);
      } else {
        this.displayTags = tags;
      }
    }
    if (this.hiddenTags.length) {
      this.hiddenTags.forEach((tag) => {
        if (this.isThreadLevel(tag)) this.threadLevelTags.push(tag);
        else this.messageLevelTags.push(tag);
      });
    }
  }

  toggleTagList(isOpen: boolean) {
    this.isTagListOpened = isOpen;
    this.emitTagOpenStatus.emit(isOpen);
    if (isOpen) {
      this.allTagsInThread = this.hiddenTags;
    } else {
      this.allTagsInThread = [];
    }
  }

  removeTagsFromMessage(tag: any) {
    const alertData = {
      message: 'MESSAGES.GOING_TO_REMOVE_TAG',
      header: 'MESSAGES.ARE_YOU_SURE'
    };
    this.common.showAlert(alertData).then((confirmation) => {
      if (confirmation) {
        const data = {
          tagid: tag.id,
          tagInfo: tag,
          chatroomId: this.chatRoomId,
          messageId: this.messageId ? this.messageId : 0,
          userId: this.sharedService.userData.userId,
          type: 0,
          apId: '',
          tenantId: this.sharedService.userData.tenantId,
          user: this.sharedService.userData.displayName,
          createdBy: this.sharedService.userData.userId
        };
        this.httpService
          .doPost({
            endpoint: APIs.deleteMessageTag,
            payload: data
          })
          .subscribe({
            next: (response: any) => {
              if(response.success){
                this.common.showToast({ message: this.common.getTranslateData('MESSAGES.TAGS_REMOVED_SUCCESSFULLY') });
                this.closeModal();
                this.removeMessageTags.emit(tag.id);
                this.sharedService.trackActivity({
                  type: Activity.messaging,
                  name: Activity.removeTagMessage,
                  des: {
                    data: {
                      displayName: this.sharedService.userData.displayName,
                      messageId: this.messageId ? this.messageId : '',
                      chatroomId: this.chatRoomId
                    },
                    desConstant: this.messageId ? Activity.removeTagMessageFromMessageDes : Activity.removeTagMessageFromThreadDes
                  }
                });
              } else if(this.sharedService.isMultiAdmissionsEnabled && !response.success){
                this.common.showToast({ message: this.common.getTranslateData('VALIDATION_MESSAGES.NO_ACCESS_TO_ADMISSION'), color: 'danger' });
              }
            },
            error: (errorResponse: any) => {
              let message = this.common.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
              if(errorResponse.status.code === 422 && this.sharedService.isMultiAdmissionsEnabled){
                message =  this.common.getTranslateData('VALIDATION_MESSAGES.NO_ACCESS_TO_ADMISSION');
              } else if(errorResponse.data.errors){
                message = errorResponse.data.errors[0].message || message;
              }
              this.common.showToast({ message, color: 'danger' });
            }
          });
      }
    });
  }

  async closeModal() {
    const modal = await this.modalCtrl.getTop();
    if (modal) {
      modal.dismiss();
    }
  }
}
