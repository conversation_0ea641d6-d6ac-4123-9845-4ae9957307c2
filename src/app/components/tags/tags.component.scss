
.thread-tag-wrap {
    padding: 10px 0px 5px 10px;
    display: flex;
    gap: 10px 15px;
    flex-flow: wrap;
    .align-self-center {
        align-self: center;
        .count-badge {
            font-size: 13px;
            padding: 4px;
        }
    }

    .thread-tag-element {
        position: relative;
        background: rgb(136, 155, 160);
        border-right-color: rgb(136, 155, 160);
        color: rgb(255, 255, 255);
        display: inline-block;
        border-radius: 0px 5px 5px 0px;
        color: #FFFFFF;
        padding: 5px 6px 5px 12px;
        z-index: 100;
        margin-bottom: 5px;

        .tag {
            display: block;
            overflow: hidden;
            margin: 0px;
            float: left;
            white-space: nowrap;
            font-size: 12px;
            &.shorter {
                max-width: 100px;
                text-overflow: ellipsis;
            }
        }

        &:before {
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #FFFFFF;
            top: 9px;
            left: 0px;
            z-index: 9999;
            content: " ";
        }

        &:after {
            right: 100%;
            top: 0;
            border: solid transparent;
            content: " ";
            height: 0;
            width: 0;
            position: absolute;
            pointer-events: none;
            border-right-color: inherit;
            border-width: 12px;
            margin-top: 0;
        }
    }
}

.overflow-count {
    width: 40px;
    border: 1px solid #7f8fa5;
    color: #3a68a7;
    background-color: #e4e9f0;
    border-radius: 5px;
    padding: 0.5px 8px;
    font-size: 13px;
    margin-left: -5px;
    &.margin-right-5px {
      margin-left: 5px;
    }
    &:hover {
      color: #3975c7;
      background-color: #c4d6ea;
    }
  }

  .mt-1 {
    margin-top: 1rem;
  }

  .mr-20 {
    margin-right: 1.25rem !important;
  }

  .tag-close {
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 20px;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: center;
    align-items: center;
    color: #fff !important;
  }
  
  .tag-close:hover {
    background: #5d686c;
  }
  