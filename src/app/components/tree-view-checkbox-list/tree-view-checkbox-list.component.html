<app-header-plain [headerTitle]="title" (close)="goBack()"></app-header-plain>
<ion-content>
  <div class="common-list">
    <div *ngFor="let group of listFiltered; let i = index">
      <ion-item lines="none">
        <ion-icon [name]="group.expanded?'remove-circle-outline':'add-circle-outline'" slot="start" (click)="expand(i)"
          tappable></ion-icon>
        <ion-label>{{group.name}}</ion-label>
        <ion-checkbox class="common-checkbox" (ionChange)="groupSelect(i)" [(ngModel)]="group.isSelected" slot="end"
          mode='ios' id="group-{{i}}">
        </ion-checkbox>
      </ion-item>
      <div *ngIf="group.expanded" class="child-list">
        <ion-item lines="none" *ngFor="let item of group.childList; let id = index">
          {{item.loc_name}}
          <ion-checkbox class="common-checkbox" slot="end" mode='ios' [(ngModel)]="item.isSelected"
            (ionChange)="selectItem()">
          </ion-checkbox>
        </ion-item>
      </div>
    </div>
  </div>
</ion-content>
<ion-footer>
  <ion-row>
    <ion-col size="6">
      <ion-button (click)="clearAll()" expand="block" color="de-york" [disabled]="disableClearAll"
        class="ion-text-capitalize">
        {{'BUTTONS.CLEAR_ALL' | translate}}
      </ion-button>
    </ion-col>
    <ion-col size="6">
      <ion-button (click)="getSelected()" expand="block" color="de-york" class="ion-text-capitalize">
        {{'BUTTONS.DONE' | translate}}
      </ion-button>
    </ion-col>
  </ion-row>
</ion-footer>