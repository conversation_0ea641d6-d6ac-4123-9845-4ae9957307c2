import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SharedModule } from 'src/app/shared.module';
import { TreeViewCheckboxListComponent } from './tree-view-checkbox-list.component';
import { HeaderPlainModule } from '../header-plain/header-plain.module';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    HeaderPlainModule
  ],
  declarations: [TreeViewCheckboxListComponent],
  exports: [TreeViewCheckboxListComponent]
})
export class TreeViewCheckboxListModule { }
