import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-tree-view-checkbox-list',
  templateUrl: './tree-view-checkbox-list.component.html',
  styleUrls: ['./tree-view-checkbox-list.component.scss'],
})
export class TreeViewCheckboxListComponent implements OnInit {
  title: any;
  listFiltered = [];
  disableClearAll: boolean = false;
  constructor(
    private readonly modalController: ModalController,
  ) { }
  ngOnInit() {
  }

  /**close modal by pressing back */
  goBack(): void {
    this.modalController.dismiss();
  }

  /**expand() for expand child checkbox list items */
  expand(i: number): void {
    this.listFiltered[i].expanded = !this.listFiltered[i].expanded;
  }

  /**groupSelect() for select all child item under the parent checkbox */
  groupSelect(i: number): void {
    this.listFiltered[i].childList.map((item) => (item.isSelected = this.listFiltered[i].isSelected));
    this.selectItem();
  }

  /**selectItem() for disable clear all button and it checks any child or parent is checked*/
  selectItem() {
    let parentCheck = this.listFiltered.every(e => e.isSelected == true);
    let childCheck = this.listFiltered.find(e => e.childList.find(x => x.isSelected == true));
    if (parentCheck === true || childCheck) {
      this.disableClearAll = false;
    }
    else {
      this.disableClearAll = true;
    }
  }
  /**get all value with it's changes on done button click */
  getSelected() {
    this.modalController.dismiss({
      selectedData: this.listFiltered
    });
  }

  /**clear all selected item from the list */
  clearAll() {
    this.listFiltered.map((item) => {
      item.isSelected = false;
      item.childList.map((item) => {
        item.isSelected = false;
      });
    });
    this.selectItem();
  }
}
