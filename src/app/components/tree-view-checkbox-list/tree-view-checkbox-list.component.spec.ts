import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { TreeViewCheckboxListComponent } from './tree-view-checkbox-list.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';

describe('TreeViewCheckboxListComponent', () => {
  const mockFilterData = [
    {
      id: 1,
      isSelected: false,
      expanded: 1,
      childList: [{ id: 1 }, { id: 2 }]
    }
  ];
  const mockData = 0;
  let component: TreeViewCheckboxListComponent;
  let fixture: ComponentFixture<TreeViewCheckboxListComponent>;
  let modalController: ModalController;
  const modalSpy = TestConstants.modalSpy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [TreeViewCheckboxListComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot()],
      providers: [NavParams, ModalController]
    }).compileComponents();
    jasmine.createSpyObj('SharedService', ['userData']).userData.and.returnValue(TestConstants.userData);
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(TreeViewCheckboxListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('goBack function should close modal', () => {
    component.goBack();
    expect(component.goBack).toBeTruthy();
  });

  it('expand function should be call', function () {
    component.listFiltered = mockFilterData;
    component.expand(mockData);
    fixture.detectChanges();
    expect(component.expand).toBeTruthy();
  });

  it('groupSelect function should be defined and child list should be greaterthan or equal to 0', function () {
    component.listFiltered = mockFilterData;
    component.groupSelect(mockData);
    expect(component.listFiltered[0].childList.length).toBeGreaterThanOrEqual(0);
    expect(component.groupSelect).toBeDefined();
  });

  it('selectItem function should be defined', function () {
    component.selectItem();
    expect(component.selectItem).toBeTruthy();
  });

  it('getSelected function should close modal', () => {
    component.getSelected();
    expect(component.getSelected).toBeTruthy();
  });

  it('clearAll function should be called,set IsSelect as false', () => {
    component.listFiltered = mockFilterData;
    component.clearAll();
    expect(component.listFiltered[0].isSelected).toEqual(false);
    expect(component.clearAll).toBeDefined();
  });
});
