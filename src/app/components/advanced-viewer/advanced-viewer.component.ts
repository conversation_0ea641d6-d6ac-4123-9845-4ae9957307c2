import { Component, Input } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Constants } from 'src/app/constants/constants';
import { CommonService } from 'src/app/services/common-service/common.service';

@Component({
  selector: 'app-advanced-viewer',
  templateUrl: './advanced-viewer.component.html',
  styleUrls: ['./advanced-viewer.component.scss']
})
export class AdvancedViewerComponent {
  @Input() url: string;
  @Input() downloadUrl: string;
  @Input() type = Constants.documentTypes.image;
  @Input() name: string;
  @Input() id: number;
  constants = Constants;
  retryCount = 0;
  iframeUrl: string;
  isMobileWeb = false;
  constructor(
    private readonly modalController: ModalController,
    public readonly sharedService: SharedService,
    private common: CommonService
  ) { }

  cancel(): void {
    this.modalController.dismiss();
  }
  ionViewWillEnter() {
    if (!this.sharedService.platform.is('capacitor') && this.sharedService.platform.is('mobileweb')) {
      this.isMobileWeb = true;
    }
    if (this.type === Constants.documentTypes.pdf) {
      this.iframeUrl = `${this.url}#scrollbar=0`;
      if (!this.isMobileWeb) {
        this.openURL();
      }
    }
  }
  /**
   * Turn off app-loader after content loading completion
   * @returns url
   */
  openURL(): void {
    if (this.retryCount === 0) {
      this.sharedService.isLoading = true;
    }
    if (this.url) {
      if (this.sharedService.isLoading) {
        const iframe = document.getElementById('frame') as HTMLIFrameElement;
        if (this.retryCount === 0 && iframe) {
          iframe.addEventListener('load', () => {
            if (iframe && (!iframe.contentDocument || iframe.src.includes('blob'))) {
              this.sharedService.isLoading = false;
            }
          });
        }
        this.retryCount += 1;
        if (this.retryCount === 500) {
          this.retry(iframe);
        }
        setTimeout(() => {
          this.openURL();
        }, 2000);
      }
    }
  }
  /**
   * Retry loading document alert popover
   * @param iframe
   */
  retry(iframe): void {
    if (this.sharedService.isLoading) {
      const buttons = [
        {
          text: this.common.getTranslateData('BUTTONS.CANCEL'),
          confirm: false
        },
        {
          text: this.common.getTranslateData('BUTTONS.RETRY'),
          confirm: true
        }
      ];
      const alertData = {
        header: 'MESSAGES.FAILED_TO_LOAD_DOCUMENT',
        message: '',
        buttons
      };
      this.sharedService.isLoading = false;
      this.common.showAlert(alertData).then((confirmation) => {
        if (confirmation && iframe && iframe.contentDocument) {
          this.sharedService.isLoading = true;
          this.retryCount = 0;
        }
      });
    }
  }
}
