import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SharedModule } from 'src/app/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdvancedViewerComponent } from 'src/app/components/advanced-viewer/advanced-viewer.component';
import { HeaderPlainModule } from 'src/app/components/header-plain/header-plain.module';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { SignaturePadModule } from 'angular16-signaturepad';
import { PinchZoomModule } from '@meddv/ngx-pinch-zoom';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SharedModule,
    TranslateModule,
    HeaderPlainModule,
    SignaturePadModule,
    PdfViewerModule,
    PinchZoomModule,
    SignaturePadModule,
    PinchZoomModule
  ],
  declarations: [AdvancedViewerComponent],
  exports: [AdvancedViewerComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AdvancedViewerComponentModule {}
