<ion-header class="common-plain-header">
  <ion-toolbar>
    <ion-buttons slot="end" class="header-menu">
      <ion-button fill="clear" (click)="cancel()" id="cancel">
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="container" *ngIf="type === constants.documentTypes.image">
    <!-- Reference Link https://github.com/drozhzhin-n-e/ngx-pinch-zoom -->
    <pinch-zoom [autoHeight]="true" [backgroundColor]="'rgba(0, 0, 255, 0)'" class="set-height">
      <ion-img [src]="url" class="set-min-width" alt="..."></ion-img>
    </pinch-zoom>
  </div>
  <div class="container" *ngIf="type === constants.documentTypes.pdf">
    <iframe id="frame" width="100%" height="100%" [src]="iframeUrl | safe: 'resourceUrl'" *ngIf="!isMobileWeb"></iframe>
    <pdf-viewer [src]="iframeUrl" [render-text]="true" [original-size]="false" style="width: 100%; height: 100%"
      *ngIf="isMobileWeb"></pdf-viewer>
  </div>
</ion-content>