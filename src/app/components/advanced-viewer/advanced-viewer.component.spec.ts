import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>roller, PopoverController, IonicModule, NavParams } from '@ionic/angular';
import { AuthGuard } from 'src/app/services/auth-guard/auth.guard';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { AdvancedViewerComponent } from 'src/app/components/advanced-viewer/advanced-viewer.component';
import { AuthService } from 'src/app/services/auth-service/auth.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { TestConstants } from 'src/app/constants/test-constants';

describe('AdvancedViewerComponent', () => {
  let component: AdvancedViewerComponent;
  let fixture: ComponentFixture<AdvancedViewerComponent>;
  let sharedService: SharedService;
  let common: CommonService;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [AdvancedViewerComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateTestingModule,
        ReactiveFormsModule
      ],
      providers: [
        NavParams,
        AuthGuard,
        AuthService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        PopoverController,
        ModalController,
        AngularDelegate
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    fixture = TestBed.createComponent(AdvancedViewerComponent);
    sharedService = TestBed.inject(SharedService);
    common = TestBed.inject(CommonService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute openURL', () => {
    component.url = 'http://www.link.com';
    component.type = 'pdf';
    component.retryCount = 499;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.openURL();
    expect(component.openURL).toBeTruthy();
  });
  it('execute retry', () => {
    sharedService.isLoading = true;
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.retry({ contentDocument: 'abc' });
    expect(component.retry).toBeTruthy();
  });
  it('execute cancel', () => {
    component.cancel();
    expect(component.cancel).toBeTruthy();
  });

  it('execute ionViewWillEnter', () => {
    component.type = 'pdf';
    component.url = 'blob://test.pdf';
    component.ionViewWillEnter();
    expect(component.ionViewWillEnter).toBeTruthy();
  });

  it('execute openURL', () => {
    component.type = 'pdf';
    component.url = 'blob://test.pdf';
    component.retryCount = 0;
    const iframe = document.createElement('iframe');
    iframe.id = 'frame';
    iframe.src = component.url;
    document.body.appendChild(iframe);
    spyOn(document, 'getElementById').and.returnValue(iframe);
    spyOn(common, 'showAlert').and.resolveTo(true);
    component.openURL();
    iframe.dispatchEvent(new Event('load'));
    expect(component.openURL).toBeTruthy();
  });
});
