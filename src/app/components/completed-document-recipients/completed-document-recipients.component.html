<ion-header class="common-plain-header">
  <ion-toolbar>
    <ion-buttons slot="start" class="header-menu left">
      <ion-button (click)="goBack()">
        {{ 'BUTTONS.BACK' | translate }}
      </ion-button>
    </ion-buttons>
    <ion-title class="header-ion-title">
      <h1 class="header-title" translate>
        {{ pageName === copyDocumentPageName.documentCenter ? 'TITLES.RECIPIENTS_OF_COMPLETED_DOCUMENT' : 'TITLES.RECIPIENTS_OF_COMPLETED_FORM' }}
      </h1>
    </ion-title>
    <ion-buttons slot="end" class="header-menu right" *ngIf="isSkipButtonShow">
      <ion-button (click)="goBack('SKIP')">
        {{ 'BUTTONS.SKIP' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content>
  <form class="common-list" [formGroup]="listForm" id="list-form" *ngIf="recipients?.length">
    <div formArrayName="list" *ngFor="let user of recipients; let i = index">
      <ion-item lines="none">
        <span class="common-capitalize-text">{{ user?.displayName }}</span>
        <ion-checkbox class="common-checkbox" slot="end" mode="ios" [formControlName]="i" id="user-{{ i }}"> </ion-checkbox>
      </ion-item>
    </div>
  </form>
  <div class="common-no-items" *ngIf="errorMessage">{{ errorMessage }}</div>
</ion-content>

<ion-footer *ngIf="!errorMessage">
  <ion-row>
    <ion-col size="6">
      <ion-button class="ion-text-capitalize set-button-bg-color" (click)="clearAll()" expand="block" id="clear-all" [disabled]="!listForm.valid">
        {{ 'BUTTONS.CLEAR_ALL' | translate }}
      </ion-button>
    </ion-col>
    <ion-col size="6">
      <ion-button
        class="ion-text-capitalize set-button-bg-color"
        (click)="sendFormRecipients()"
        id="next"
        expand="block"
        [disabled]="!listForm.valid"
      >
        {{ 'BUTTONS.NEXT' | translate }}
      </ion-button>
    </ion-col>
  </ion-row>
</ion-footer>
