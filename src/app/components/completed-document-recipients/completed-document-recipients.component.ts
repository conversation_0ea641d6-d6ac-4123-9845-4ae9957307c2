import { Constants } from 'src/app/constants/constants';
import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ModalController } from '@ionic/angular';
import { APIs } from 'src/app/constants/apis';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { atLeastOneCheckboxCheckedValidator } from 'src/app/utils/utils';

@Component({
  selector: 'app-completed-document-recipients',
  templateUrl: './completed-document-recipients.component.html',
  styleUrls: ['./completed-document-recipients.component.scss'],
})
export class CompletedDocumentRecipientsComponent implements OnInit {

  @Input() formFacing: string;
  @Input() recipient: any;
  @Input() pageName: string;
  @Input() isSkipButtonShow = false;
  @Input() admissionId: string;
  recipients: any = [];
  userData: any;
  errorMessage: string = '';
  listForm: UntypedFormGroup;
  copyDocumentPageName = Constants.copyDocumentPageName;
  constructor(
    private readonly httpService: HttpService,
    private readonly sharedService: SharedService,
    private readonly modalController: ModalController,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly common: CommonService
  ) {
    this.userData = this.sharedService.userData;
    this.listForm = this.formBuilder.group({
      list: new UntypedFormArray([])
    });
  }

  ngOnInit() {
    this.listAlternateCaregiverPatientsContacts();
  }

  goBack(items?: any): void {
    this.modalController.dismiss(items);
  }

  listAlternateCaregiverPatientsContacts(): void {
    const body = {
      tenantId: this.recipient?.tenantId || this.userData?.tenantId,
      patientId: this.recipient?.userid || this.recipient?.userId || this.userData?.patientId,
      isActive: 1,
      chatRoomInvite: true,
      completedFormsRecipients: true,
      admissionId: this.sharedService.isMultiAdmissionsEnabled ? this.admissionId : undefined
    };
    this.sharedService.isLoading = true;
    this.httpService.doGet({ endpoint: APIs.getAlternateContactsCaregiverForPatient, extraParams: body }).subscribe(
      (response) => {
        this.sharedService.isLoading = false;
        if (response.data.length === 0) {
          this.recipients = [];
          this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
          return;
        }
        this.errorMessage = '';
        let alternateData = [];
        let otherUser = [];
        let allNewData;
        if (response.data.patient) {
          response.data.patient['roleName'] = Constants.patientValue
          otherUser.push(response.data.patient);
        }
        if (response.data.caregiver) {
          response.data.caregiver['roleName'] = Constants.userCaregiver;
          otherUser.push(response.data.caregiver);
        }

        if (response.data.alternateContacts) {
          alternateData = response.data.alternateContacts;
        }
        allNewData = [...alternateData, ...otherUser];
        this.recipients = allNewData;
        this.createForm(allNewData);
      },
      (error) => {
        this.sharedService.errorHandler(error);
        this.errorMessage = this.common.getTranslateData('MESSAGES.NO_ITEM_FOUND');
      }
    );
  }

  createForm(userList: any[]): void {
    const controls = userList.map((c, index) => {
      const checkedStatus = this.listForm.value.list[index] || false;
      return new UntypedFormControl(checkedStatus);
    });
    this.listForm = this.formBuilder.group({
      list: new UntypedFormArray(controls, atLeastOneCheckboxCheckedValidator())
    });
  }

  async sendFormRecipients(): Promise<void> {
    const selectedStaffRoles = await this.listForm.value.list
      .map((v, i: number) => (v ? this.recipients[i].userId : null))
      .filter((v) => v !== null);

    if (this.pageName === Constants.copyDocumentPageName.documentCenter) {
      const selectedStaff = await this.listForm.value.list
        .map((v, i: number) => (v ? this.recipients[i] : null))
        .filter((v) => v !== null);
      this.goBack({ selectedStaffRoles, selectedStaff });
    } else {
      this.goBack(selectedStaffRoles);
    }
  }

  clearAll(): void {
    this.listForm.reset();
  }

}
