import { CompletedDocumentRecipientsComponent } from './completed-document-recipients.component';
import { ComponentFixture, TestBed, fakeAsync } from '@angular/core/testing';
import { IonicModule, ModalController, NavParams } from '@ionic/angular';

import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { UntypedFormArray, UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { HttpService } from 'src/app/services/http-service/http.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { TestConstants } from 'src/app/constants/test-constants';
import { of, throwError } from 'rxjs';
import { Constants } from 'src/app/constants/constants';

describe('CompletedDocumentRecipientsComponent', () => {
  let component: CompletedDocumentRecipientsComponent;
  let fixture: ComponentFixture<CompletedDocumentRecipientsComponent>;
  let sharedService: SharedService;
  let common: CommonService;
  let httpService: HttpService;
  const modalSpy = TestConstants.modalSpy;
  let modalController: ModalController;
  let formBuilder: UntypedFormBuilder;
  beforeEach((() => {
    TestBed.configureTestingModule({
      declarations: [CompletedDocumentRecipientsComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([]),
        ReactiveFormsModule
      ],
      providers: [
        ModalController,
        HttpService,
        CommonService,
        NavParams,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    httpService = TestBed.inject(HttpService);
    sharedService = TestBed.inject(SharedService);
    sharedService.localConfig = TestConstants.localConfig;
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    spyOn(sharedService, 'trackActivity').and.stub();

    common = TestBed.inject(CommonService);
    spyOn(common, 'showMessage').and.stub();
    spyOn(common, 'redirectToPage').and.stub();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    spyOn(common, 'showAlert').and.resolveTo(true);
    fixture = TestBed.createComponent(CompletedDocumentRecipientsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    formBuilder = TestBed.inject(UntypedFormBuilder);
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('listAlternateCaregiverPatientsContacts', () => {
    it('should set recipients and call createForm when response has complete data', fakeAsync(() => {
      const mockResponse = {
        data: {
          patient: { userId: '123', firstName: 'John', lastName: 'Doe' },
          caregiver: { userId: '456', firstName: 'Jane', lastName: 'Smith' },
          alternateContacts: [{ userId: '789', firstName: 'Alt', lastName: 'Contact' }]
        },
        status: 'success'
      };

      spyOn(httpService, 'doGet').and.returnValue(of(mockResponse));
      spyOn(component, 'createForm').and.callThrough();
      spyOn(common, 'getTranslateData').and.returnValue('No items found');

      component.listAlternateCaregiverPatientsContacts();

      expect(sharedService.isLoading).toBeFalse();
      expect(component.errorMessage).toBe('');
      expect(component.recipients.length).toBe(3);

      // Verify patient data is transformed correctly
      expect(component.recipients).toContain(
        jasmine.objectContaining({
          userId: '123',
          firstName: 'John',
          lastName: 'Doe',
          roleName: Constants.patientValue
        })
      );

      // Verify caregiver data is transformed correctly
      expect(component.recipients).toContain(
        jasmine.objectContaining({
          userId: '456',
          firstName: 'Jane',
          lastName: 'Smith',
          roleName: Constants.userCaregiver
        })
      );
      // Verify alternateContacts are included
      expect(component.recipients).toContain(
        jasmine.objectContaining({
          userId: '789',
          firstName: 'Alt',
          lastName: 'Contact'
        })
      );
      expect(component.createForm).toHaveBeenCalledWith(component.recipients);
    }));

    it('should handle empty response data', fakeAsync(() => {
      const mockResponse = {
        data: [],
        status: 'success'
      };

      spyOn(httpService, 'doGet').and.returnValue(of(mockResponse));
      spyOn(common, 'getTranslateData').and.returnValue('No items found');

      component.listAlternateCaregiverPatientsContacts();

      expect(sharedService.isLoading).toBeFalse();
      expect(component.recipients).toEqual([]);
      expect(component.errorMessage).toBe('No items found');
    }));

    it('should handle response with only patient data', fakeAsync(() => {
      const mockResponse = {
        data: {
          patient: { userId: '123', firstName: 'John', lastName: 'Doe' }
        },
        status: 'success'
      };

      spyOn(httpService, 'doGet').and.returnValue(of(mockResponse));
      spyOn(component, 'createForm').and.callThrough();

      component.listAlternateCaregiverPatientsContacts();

      expect(component.recipients.length).toBe(1);
      expect(component.recipients[0].roleName).toBe(Constants.patientValue);
      expect(component.createForm).toHaveBeenCalledWith(component.recipients);
    }));

    it('should handle response with only caregiver data', fakeAsync(() => {
      const mockResponse = {
        data: {
          caregiver: { userId: '456', firstName: 'Jane', lastName: 'Smith' }
        },
        status: 'success'
      };

      spyOn(httpService, 'doGet').and.returnValue(of(mockResponse));
      spyOn(component, 'createForm').and.callThrough();

      component.listAlternateCaregiverPatientsContacts();

      expect(component.recipients.length).toBe(1);
      expect(component.recipients[0].roleName).toBe(Constants.userCaregiver);
      expect(component.createForm).toHaveBeenCalledWith(component.recipients);
    }));

    it('should handle response with only alternateContacts data', fakeAsync(() => {
      const mockResponse = {
        data: {
          alternateContacts: [{ userId: '789', firstName: 'Alt', lastName: 'Contact' }]
        },
        status: 'success'
      };

      spyOn(httpService, 'doGet').and.returnValue(of(mockResponse));
      spyOn(component, 'createForm').and.callThrough();

      component.listAlternateCaregiverPatientsContacts();

      expect(component.recipients.length).toBe(1);
      expect(component.createForm).toHaveBeenCalledWith(component.recipients);
    }));

    it('should handle error response and set error message', fakeAsync(() => {
      spyOn(httpService, 'doGet').and.returnValue(throwError('Network error'));
      spyOn(sharedService, 'errorHandler').and.callThrough();
      spyOn(common, 'getTranslateData').and.returnValue('No items found');

      component.listAlternateCaregiverPatientsContacts();

      expect(sharedService.errorHandler).toHaveBeenCalledWith('Network error');
      expect(component.errorMessage).toBe('No items found');
    }));

    it('should include admissionId in request params when multi admissions is enabled', fakeAsync(() => {
      const mockResponse = { data: [], status: 'success' };

      spyOn(httpService, 'doGet').and.returnValue(of(mockResponse));

      sharedService.userData.config.enable_multi_admissions = '1';
      component.admissionId = '123456';

      component.listAlternateCaregiverPatientsContacts();

      expect(httpService.doGet).toHaveBeenCalledWith(
        jasmine.objectContaining({
          extraParams: jasmine.objectContaining({
            admissionId: '123456'
          })
        })
      );
    }));

    it('should not include admissionId in request params when multi admissions is disabled', fakeAsync(() => {
      const mockResponse = { data: [], status: 'success' };

      spyOn(httpService, 'doGet').and.returnValue(of(mockResponse));

      sharedService.userData.config.enable_multi_admissions = '0';
      component.admissionId = '123456';

      component.listAlternateCaregiverPatientsContacts();

      expect(httpService.doGet).toHaveBeenCalledWith(
        jasmine.objectContaining({
          extraParams: jasmine.objectContaining({
            admissionId: undefined
          })
        })
      );
    }));
  });

  describe('clearAll', () => {
    it('should remove all controls from the list FormArray', () => {
      const form = component.listForm;
      const listArray = form.get('list') as UntypedFormArray;
      listArray.push(formBuilder.control('value1'));
      listArray.push(formBuilder.control('value2'));
      component.clearAll();
      expect(listArray.length).toBe(2);
    });
  });

  describe('goBack', () => {
    it('should dismiss the modal with items', () => {
      component.goBack({ item1: 'value1', item2: 'value2' });
      expect(modalController.dismiss).toHaveBeenCalledWith({ item1: 'value1', item2: 'value2' });
    });

    it('should dismiss the modal without items', () => {
      component.goBack();
      expect(modalController.dismiss).toHaveBeenCalledWith(undefined);
    });
  });

  describe('sendFormRecipients', () => {
    it('should call goBack with selected staff roles', async () => {
      component.recipients = [
        {
          contactId: '2233476',
          roleId: '3',
          status: '1',
          displayName: 'aaa cccccc',
          firstName: 'aaa',
          lastName: 'cccccc',
          relation: 'servant',
          email: '<EMAIL>',
          alternateUsername: '<EMAIL>',
          ESIValue: '43252',
          patientId: '2233475',
          mobile: '',
          countryCode: '+1',
          password: true,
          created_at: '2022-11-02 09:10:11',
          modified_at: '2023-04-12 09:31:49',
          cmisid: '2261295',
          patientFirstName: 'aaa',
          patientLastName: 'aaaaa',
          patientStatus: '1',
          patientDob: '2000-11-12',
          patientPassword: null,
          patientDisplayName: 'aaa aaaaa',
          tenantId: '91',
          tenantName: 'Citus QA Regression Branch 2',
          tenantRoleId: '6138',
          userId: '2233476',
          isVirtual: false,
          identity_value: '',
          roleName: 'Alternate Contact',
          patientDobFormatted: '11/12/2000'
        },
        {
          patientId: 2233475,
          firstName: 'aaa',
          lastName: 'aaaaa',
          email: '<EMAIL>',
          mobile: '',
          userId: '2233475',
          roleName: 'Patient'
        }
      ];
      spyOn(component, 'goBack');
      const selectedStaffRoles = ['2233476'];
      component.listForm = formBuilder.group({
        list: formBuilder.array([true, false])
      });
      component.listForm.setControl('list', formBuilder.array([true, false]));
      await component.sendFormRecipients();
      expect(component.goBack).toHaveBeenCalledWith(selectedStaffRoles);
    });

    it('should call goBack without selected staff roles', async () => {
      spyOn(component, 'goBack');
      component.listForm = formBuilder.group({
        list: formBuilder.array([false, false, false])
      });
      component.listForm.setControl('list', formBuilder.array([false, false, false]));
      await component.sendFormRecipients();
      expect(component.goBack).toHaveBeenCalledWith([]);
    });

    it('should call goBack with selectedStaffRoles and selectedStaff when pageName is documentCenter', async () => {
      component.pageName = Constants.copyDocumentPageName.documentCenter;
      component.recipients = [
        { userId: 1 },
        { userId: 2 }
      ];
      const goBackSpy = spyOn(component, 'goBack');
      component.listForm = formBuilder.group({
        list: formBuilder.array([true, true, false])
      });
      component.listForm.setControl('list', formBuilder.array([true, true, false]));
      await component.sendFormRecipients();

      expect(goBackSpy).toHaveBeenCalledWith({
        selectedStaffRoles: [1, 2],
        selectedStaff: [component.recipients[0], component.recipients[1]]
      });
    });
  });

  it('should create listForm with correct FormArray and default checked status', () => {
    const userList = [
      { userId: 1 },
      { userId: 2 },
      { userId: 3 },
    ];
    component.createForm(userList);
    const listForm = component.listForm;
    expect(listForm.get('list')).toBeInstanceOf(UntypedFormArray);
    userList.forEach((user, index) => {
      const checkedStatus = component.listForm.value.list[index];
      expect(checkedStatus).toBeFalse();
    });
  });
});
