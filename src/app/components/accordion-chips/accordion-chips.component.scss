// Reusable accordion chip styling (based on message center pattern)
.accordion-group {
  margin: 0;

  ion-accordion {
    .user-chip {
      padding: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      overflow-y: auto;
      overflow-x: hidden;
      width: 100%;

      ion-chip {
        .chip-label {
          max-width: 180px;
          white-space: normal;
          word-wrap: break-word;
          overflow-wrap: break-word;
          line-height: 1.2;
        }
      }

      ion-icon[name='person-circle-outline'], ion-icon[name='people-outline'], ion-icon[name='pricetag-outline'] {
        color: var(--ion-color-de-york);
      }

      ion-icon[name='close'] {
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }
    }
  }
}
