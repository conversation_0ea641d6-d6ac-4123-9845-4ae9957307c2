import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';

export interface AccordionChipItem {
  id: string;
  displayName: string;
  isUserTag?: boolean;
  isAlternateContact?: boolean;
  recipientType?: 'tag' | 'patient' | 'staff' | 'alternate contact';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any; // Allow additional properties
}

export interface RemoveItemEvent {
  item: AccordionChipItem;
  index: number;
}

@Component({
  selector: 'app-accordion-chips',
  standalone: true,
  imports: [CommonModule, IonicModule, TranslateModule],
  templateUrl: './accordion-chips.component.html',
  styleUrls: ['./accordion-chips.component.scss']
})
export class AccordionChipsComponent {
  @Input() items: AccordionChipItem[] = [];
  @Input() headerLabel: string = 'LABELS.SELECTED_RECIPIENTS';
  @Input() showCount: boolean = true;
  @Input() maxHeight: string = '150px';
  @Input() chipIdPrefix: string = 'chip';
  @Output() removeItem = new EventEmitter<RemoveItemEvent>();

  /**
   * Get the appropriate icon for the chip based on item type
   */
  getChipIcon(item: AccordionChipItem): string {
    if (item.isUserTag) {
      return 'pricetag-outline';
    }
    if (item.isAlternateContact) {
      return 'people-outline';
    }
    return 'person-circle-outline';
  }

  /**
   * Handle chip removal
   */
  onRemoveChip(item: AccordionChipItem, index: number): void {
    this.removeItem.emit({ item, index });
  }
}
