<ion-accordion-group class="accordion-group">
  <ion-accordion>
    <ion-item slot="header" [id]="chipIdPrefix + '-header'">
      <ion-label class="dark-text">
        {{ headerLabel | translate }}
        @if (showCount) {
          ({{ items.length }})
        }
      </ion-label>
    </ion-item>
    <div slot="content" class="user-chip" [style.max-height]="maxHeight">
      @for (item of items; track item.id; let i = $index) {
        <ion-chip [id]="chipIdPrefix + '-' + i">
          <ion-icon [name]="getChipIcon(item)"></ion-icon>
          <ion-label class="dark-text wrap-ellipsis chip-label">{{ item.displayName }}</ion-label>
          <ion-icon name="close" (click)="onRemoveChip(item, i)" [id]="chipIdPrefix + '-close-' + i" tappable> </ion-icon>
        </ion-chip>
      }
    </div>
  </ion-accordion>
</ion-accordion-group>
