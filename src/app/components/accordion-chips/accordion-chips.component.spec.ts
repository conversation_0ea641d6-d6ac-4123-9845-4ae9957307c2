import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { AccordionChipsComponent, AccordionChipItem } from './accordion-chips.component';

describe('AccordionChipsComponent', () => {
  let component: AccordionChipsComponent;
  let fixture: ComponentFixture<AccordionChipsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AccordionChipsComponent, IonicModule.forRoot(), TranslateModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(AccordionChipsComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('getChipIcon', () => {
    it('should return pricetag-outline for user tags', () => {
      expect(component.getChipIcon({ isUserTag: true } as AccordionChipItem)).toBe('pricetag-outline');
    });

    it('should return people-outline for alternate contacts', () => {
      expect(component.getChipIcon({ isAlternateContact: true } as AccordionChipItem)).toBe('people-outline');
    });

    it('should return person-circle-outline for default items', () => {
      expect(component.getChipIcon({} as AccordionChipItem)).toBe('person-circle-outline');
    });
  });

  describe('onRemoveChip', () => {
    it('should emit removeItem event with correct data', () => {
      const mockItem = { id: '1', displayName: 'Test' } as AccordionChipItem;
      spyOn(component.removeItem, 'emit');

      component.onRemoveChip(mockItem, 0);

      expect(component.removeItem.emit).toHaveBeenCalledWith({ item: mockItem, index: 0 });
    });
  });
});
