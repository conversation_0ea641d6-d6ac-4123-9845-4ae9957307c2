<ion-header>
  <app-header-plain [headerTitle]="headerTitle | translate" (close)="dismiss()"></app-header-plain>
  <form [formGroup]="itemForm">
    <ion-row class="ion-align-items-center common-search-box">
      <div class="search-container">
        <ion-input
          type="search"
          id="search-key"
          formControlName="searchText"
          name="search"
          class="ion-input-style set-border-bg-color"
          placeholder="{{ 'PLACEHOLDERS.SEARCH_HERE' | translate }}"
          mode="md"
          autocapitalize="on"
        >
        </ion-input>
      </div>
      <div class="action-buttons">
        <ion-button expand="block" id="search" (click)="loadItemsOnSearch()">
          <ion-icon class="search-icon" color="white" src="assets/icon/material-svg/magnify.svg"></ion-icon>
        </ion-button>
        <ion-button expand="block" id="reset" (click)="onCancel()">
          <ion-icon class="refresh-icon" color="white" src="assets/icon/material-svg/refresh.svg"></ion-icon>
        </ion-button>
      </div>
    </ion-row>
  </form>
</ion-header>
<ion-content>
  <form name="itemsForm" id="select-items-form" [formGroup]="itemsForm" *ngIf="itemsForm">
    <div class="common-list">
      <ion-list class="item-list hide-border" *ngIf="!itemsList?.length">
        <ion-item class="ion-text-center">
          <ion-label>{{ 'MESSAGES.NO_ITEM_FOUND' | translate }}</ion-label>
        </ion-item>
      </ion-list>
      <div formArrayName="options">
        <ion-item
          lines="none"
          *ngFor="let item of itemsList; let i = index"
          [formGroupName]="i"
          [id]="'item-' + i"
          (click)="selectItem(item)"
          [class.highlight]="item.selected && singleSelect"
          [ngClass]="{ 'text-center': singleSelect }"
        >
          <ion-label class="ion-text-wrap">{{ item[displayKey] }}</ion-label>
          <ion-checkbox
            formControlName="selected"
            [id]="'checkbox-' + i"
            class="common-checkbox"
            slot="end"
            mode="ios"
            (ionChange)="selectItem(item, $event)"
          >
          </ion-checkbox>
        </ion-item>
      </div>
    </div>
  </form>
  <ion-infinite-scroll threshold="100px" (ionInfinite)="loadData($event)" *ngIf="itemsList?.length" id="infinit-scroll">
    <ion-infinite-scroll-content loadingSpinner="dots"></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
<ion-footer>
  <ion-row *ngIf="!singleSelect">
    <ion-col>
      <ion-button
        (click)="patchOptionValue(true)"
        id="select-all"
        expand="block"
        *ngIf="showButtons?.selectAll"
        class="ion-text-capitalize"
        color="de-york"
      >
        {{ 'BUTTONS.SELECT_ALL' | translate }}
      </ion-button>
    </ion-col>
    <ion-col>
      <ion-button
        (click)="patchOptionValue(false)"
        id="clear-all"
        expand="block"
        *ngIf="showButtons?.clearAll"
        [disabled]="isDisabled"
        class="ion-text-capitalize"
        color="de-york"
      >
        {{ 'BUTTONS.CLEAR_ALL' | translate }}
      </ion-button>
    </ion-col>
    <ion-col>
      <ion-button (click)="submit()" id="done" expand="block" color="de-york" class="ion-text-capitalize">
        {{ 'BUTTONS.DONE' | translate }}
      </ion-button>
    </ion-col>
  </ion-row>
  <ion-row *ngIf="singleSelect">
    <ion-col size="6">
      <ion-button (click)="resetSelection()" expand="block" id="reset" color="de-york" class="ion-text-capitalize">
        {{ 'BUTTONS.RESET' | translate }}
      </ion-button>
    </ion-col>
    <ion-col size="6">
      <ion-button (click)="submit()" id="done" expand="block" color="de-york" class="ion-text-capitalize">
        {{ 'BUTTONS.DONE' | translate }}
      </ion-button>
    </ion-col>
  </ion-row>
</ion-footer>
