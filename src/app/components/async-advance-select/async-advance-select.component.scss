.item-list {
    display: flex;
    flex-direction: column;
    width: 100%;

    &-item {
        cursor: pointer;
    }
}

.hide-border {
    border-style: none;
    opacity: .4;
}

.highlight {
    --background: #64c28d;
    color: white;
}

ion-item {
    --padding-start: 0px;
}

ion-item ion-label {
    padding-left: 20px;
}

.select-staff {
    border: 1px solid #e2e2e2;
}

.select-staff-div {
    width: 100%;
    margin-right: 2%;
    margin-left: 2%;
}

::ng-deep .wider-popover {
    --width: 70%;
}

ion-title {
    font-size: 17px;
}

.action-buttons {
    display: flex;
    flex-direction: row;
    margin-left: 2px;

    ion-button {
        --background: var(--ion-color-fountain-blue);
        --border-color: transparent;
        --box-shadow: none;
        --padding-start: 0;
        --padding-end: 0;
        min-height: 44px;

        .refresh-icon {
            color: #b2b2b2;
        }
    }
}

.common-search-box {
    display: flex;
    margin-left: 6px;
    margin-right: 4px;
}