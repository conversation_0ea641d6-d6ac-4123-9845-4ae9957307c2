import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, UntypedFormArray, UntypedFormGroup } from '@angular/forms';
import { ModalController } from '@ionic/angular';
import { Constants } from 'src/app/constants/constants';
import { VisitScheduleConstants } from 'src/app/constants/visit-schedule-constants';
import { HttpService } from 'src/app/services/http-service/http.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { isBlank } from 'src/app/utils/utils';

interface ItemData {
  id: string;
  selected: boolean;
  [key: string]: any;
}

@Component({
  selector: 'app-async-advance-select',
  templateUrl: './async-advance-select.component.html',
  styleUrls: ['./async-advance-select.component.scss']
})
export class AsyncAdvanceSelectComponent implements OnInit {
  itemsList: ItemData[] = [];
  searchParams: any = VisitScheduleConstants.commonSearchParam;
  itemForm: FormGroup;
  itemsForm: UntypedFormGroup;
  selectedItems: ItemData[] = [];
  loadDataFunction: any;
  displayKey: string;
  headerTitle = 'TITLES.SELECT_ITEM';
  singleSelect = true;
  showButtons = { selectAll: true, clearAll: true, done: true };

  constructor(
    private modalController: ModalController,
    private readonly formBuilder: FormBuilder,
    public sharedService: SharedService,
    private httpService: HttpService
  ) {}

  ngOnInit() {
    this.itemForm = this.formBuilder.group({
      searchText: [this.searchParams.searchString]
    });
    this.loadAllItems();
  }

  loadAllItems(event?: { target: { disabled: boolean; complete: () => void } }): void {
    this.sharedService.isLoading = true;
    if (this.loadDataFunction) {
      this.loadDataFunction(this.searchParams).subscribe(
        (resp) => {
          if (!isBlank(resp.content)) {
            const ids = (this.selectedItems && this.selectedItems.map((item) => item.id)) || [];

            // Format items for multi-select functionality
            const formattedItems = resp.content.map((item) => ({
              ...item,
              selected: ids.includes(item.id)
            }));

            this.itemsList = this.searchParams.currentPage === 0 ? formattedItems : [...this.itemsList, ...formattedItems];

            // Create form controls for the items
            this.createItemsForm();
          }
          if (resp.last && event) {
            // Create a copy instead of modifying the parameter directly
            const eventCopy = event;
            setTimeout(() => {
              eventCopy.target.disabled = true;
            });
          }

          this.sharedService.isLoading = false;
        },
        () => {
          // Handle error silently
          this.itemsList = [];
          this.itemsForm = this.formBuilder.group({
            options: []
          });
          this.sharedService.isLoading = false;
        }
      );
    }
  }

  createItemsForm(): void {
    if (this.itemsList.length > 0) {
      const controls = this.itemsList.map((value: ItemData) => this.createItemControl(value));
      this.itemsForm = this.formBuilder.group({
        options: new UntypedFormArray(controls)
      });
    }
  }

  createItemControl(controlValue: ItemData): UntypedFormGroup {
    return this.formBuilder.group({
      id: [controlValue.id],
      selected: [controlValue.selected || false],
      // Include the display key value for easier access
      [this.displayKey]: [controlValue[this.displayKey]]
    });
  }

  loadItemsOnSearch() {
    if (this.displayKey === 'therapyTypeName' || this.displayKey === 'therapyName') {
      this.searchParams.searchParams[this.displayKey] = this.itemForm.controls.searchText.value ? `%${this.itemForm.controls.searchText.value}%` : '%%';
    } else {
      this.searchParams.searchString = `%${this.itemForm.controls.searchText.value}%`;
    }
    this.searchParams.currentPage = 0;
    this.itemsList = [];
    this.loadAllItems();
  }

  loadData(event: any): void {
    setTimeout(() => {
      event.target.complete();
      this.searchParams.currentPage++;
      this.loadAllItems(event);
    }, 500);
  }

  onCancel() {
    this.searchParams.searchString = '';
    if (this.searchParams.searchParams) {
      this.searchParams.searchParams[this.displayKey] = `%%`;
    }
    this.searchParams.currentPage = 0;
    this.itemsList = [];
    this.selectedItems = [];
    this.loadAllItems();
    this.itemForm.reset();
  }

  selectItem(item: ItemData, event?: { detail: { checked: boolean } }): void {
    if (this.singleSelect) {
      this.itemsList = this.itemsList.map((it) => ({
        ...it,
        selected: false
      }));

      if (this.itemsForm) {
        const formArray = this.itemsForm.get('options') as UntypedFormArray;
        formArray.controls.forEach((control) => control.get('selected').setValue(false));
      }
    }
    this.itemsList = this.itemsList.map((listItem) => {
      const updatedItem = { ...listItem };
      if (updatedItem.id === item.id) {
        updatedItem.selected = event ? event.detail.checked : !updatedItem.selected;
      }
      return updatedItem;
    });

    // Update form controls if needed
    if (this.itemsForm) {
      const formArray = this.itemsForm.get('options') as UntypedFormArray;
      const index = this.itemsList.findIndex((listItem) => listItem.id === item.id);
      if (index >= 0 && index < formArray.length) {
        formArray.at(index).get('selected').setValue(this.itemsList[index].selected);
      }
    }

    if (this.singleSelect) {
      const selectedItem = this.itemsList.find((i) => i.selected);
      if (selectedItem) {
        this.submit();
      }
    }
  }

  patchOptionValue(value: boolean): void {
    if (this.itemsForm) {
      const array = this.itemsForm.value.options;
      const selected = array.map((option) => ({
        ...option,
        selected: value
      }));
      this.itemsForm.patchValue({ options: selected });
    }

    // Update the items array to match
    this.itemsList = this.itemsList.map((item) => ({
      ...item,
      selected: value
    }));
  }

  get isDisabled(): boolean {
    return !this.itemsList.some((item) => item.selected);
  }

  resetSelection(): void {
    this.selectedItems = [];
    this.patchOptionValue(false);
    this.itemForm.reset();
  }

  submit(): void {
    const selectedItems = this.itemsList.filter((item) => item.selected);
    const selectedIds = selectedItems.map((item) => item.id);
    const selectedNames = selectedItems.map((item) => item[this.displayKey]);

    this.modalController.dismiss({
      selectedItems,
      selectedNames,
      selectedIds
    });
  }

  dismiss() {
    if (!this.itemsList.length && !this.selectedItems.length) {
      this.modalController.dismiss({
        selectedItem: {},
        selectedName: '',
        selectedId: ''
      });
    } else {
      this.modalController.dismiss();
    }
  }
}
