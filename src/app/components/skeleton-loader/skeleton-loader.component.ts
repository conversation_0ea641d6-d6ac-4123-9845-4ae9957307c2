import { Component, Input, OnInit } from '@angular/core';
import { SkeletonLoaderTypes } from 'src/app/constants/constants';

@Component({
  selector: 'app-skeleton-loader',
  templateUrl: './skeleton-loader.component.html',
  styleUrls: ['./skeleton-loader.component.scss']
})
export class SkeletonLoaderComponent implements OnInit {
  @Input() count = 1;
  @Input() type = SkeletonLoaderTypes.LIST;
  @Input() avatar = false;
  @Input() skeletonWidths = [80, 60, 40];
  items = [];
  skeletonLoaderTypes = SkeletonLoaderTypes;
  ngOnInit(): void {
    this.items = [...Array(this.count).keys()];
  }
}
