<ion-list class="skeleton-loader" *ngFor="let item of items">
  <ng-container *ngIf="type === skeletonLoaderTypes.CHAT">
    <ion-item lines="none">
      <ion-avatar slot="start">
        <ion-skeleton-text animated></ion-skeleton-text>
      </ion-avatar>
      <ion-label>
        <ion-skeleton-text animated class="left-sk" slot="start"></ion-skeleton-text>
      </ion-label>
    </ion-item>
    <ion-item lines="none">
      <ion-avatar slot="end">
        <ion-skeleton-text animated></ion-skeleton-text>
      </ion-avatar>
      <ion-label>
        <ion-skeleton-text animated class="right-sk"></ion-skeleton-text>
      </ion-label>
    </ion-item>
  </ng-container>
  <ng-container *ngIf="type === skeletonLoaderTypes.LIST">
    <ion-item lines="none" class="list-item">
      <ion-avatar slot="start" *ngIf="avatar">
        <ion-skeleton-text animated></ion-skeleton-text>
      </ion-avatar>
      <ion-label>
        <ion-skeleton-text *ngFor="let width of skeletonWidths; let i = index" animated [style.width.%]="width"></ion-skeleton-text>
      </ion-label>
    </ion-item>
  </ng-container>
</ion-list>
