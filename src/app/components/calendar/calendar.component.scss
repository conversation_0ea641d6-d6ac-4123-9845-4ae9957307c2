full-calendar {
    color: black;
    width: 100%;

    .fc-button {
        background-color: #e6e6e6;
        color: black;
    }
}

::ng-deep {
    .fc-header-toolbar.fc-toolbar {
        flex-direction: column !important;
        line-height: 52px;
    }

    .fc-direction-ltr {

        // Small devices (landscape phones, 576px and up)
        @media (min-width: 320px) {
            height: calc(100% - 10px);
        }

        // Medium devices (tablets, 768px and up)
        @media (min-width: 768px) {
            height: calc(100% - 10px);
        }

        // Large devices (desktops, 992px and up)
        @media (min-width: 992px) {
            height: 100%;
        }

        // Extra large devices (large desktops, 1200px and up)
        @media (min-width: 1200px) {
            height: 100%;
        }
    }

    .fc {
        .fc-daygrid-day.fc-day-today {
            background-color: #82fafb;
            background-color: var(--fc-today-bg-color, #82fafb) !important;
        }

        .fc-col-header-cell-cushion {
            color: black;
        }

        .fc-daygrid-event-dot {
            display: none;
        }

        .fc-toolbar-title {
            font-size: 1.5em !important;
        }

        .fc-list-event {
            display: block;
            padding: 0px 0px;
        }


        .fc-daygrid-event {
            font-size: .65em;
            width: 98%;
        }

        .fc-timegrid-event {
            font-size: .65em;
            padding: 0px 4px;
        }

        button.fc-button-active {
            background-color: var(--ion-color-skin-secondary-bright) !important;
            border: 1px solid var(--ion-color-skin-secondary-bright) !important;
            box-shadow: none;
        }

        button.fc-button {
            background-color: #a9b4bc;
            border: 1px solid #a9b4bc;
        }

        .fc-button-primary:hover {
            background-color: var(--ion-color-skin-secondary-bright) !important;
            border: 1px solid var(--ion-color-skin-secondary-bright) !important;
            box-shadow: none;
        }

        .fc-button {
            box-shadow: none !important;
        }

        .fc-list-item-time {
            display: none;
        }

        .fc-v-event .fc-event-main-frame {
            height: auto;
            flex-direction: column-reverse;
        }

        .fc table {
            font-size: .9em;
        }

        .fc-timegrid-more-link {
            background: var(--ion-color-skin-secondary-bright);
        }

        .fc-daygrid-day-number {
            color: #514d6a;
        }

        .fc-daygrid-more-link {
            color: #514d6a;
        }

        button.fc-button.fc-setDefaultViewButton-button {
            background: var(--ion-color-skin-secondary-bright);
            margin-left: 5px !important;
            border-top-left-radius: 0.25em !important;
            border-bottom-left-radius: 0.25em !important;
        }

        .fc-scrollgrid table tr {
            height: 48px;
        }

        .fc-scrollgrid table thead tr {
            height: 26px;
        }


    }

}