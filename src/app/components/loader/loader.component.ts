import { Component } from '@angular/core';
import { SessionService } from 'src/app/services/session-service/session.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';

@Component({
  selector: 'app-loader',
  templateUrl: './loader.component.html',
  styleUrls: ['./loader.component.scss']
})
export class LoaderComponent {
  constructor(public sharedService: SharedService, public sessionService: SessionService) {}
}
