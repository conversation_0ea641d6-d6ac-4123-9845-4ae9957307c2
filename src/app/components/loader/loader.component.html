<div class="overlay" [ngClass]="{'overlay-background':sharedService.loaderMessage}"
    *ngIf="sharedService.isLoading|| sharedService.consoloLoader||sessionService.sessionLoader|| sharedService.loaderMessage">
    <div class="overlay__inner">
        <div class="overlay__content overlay-wait" *ngIf="sharedService.loaderMessage">
            {{sharedService.loaderMessage| translate}}
        </div>
        <div class="overlay__content">
            <span class="spinner"></span>
        </div>
    </div>
</div>