.overlay {
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    position: fixed;
    background: #ffffff30;
    z-index: 39999;
}

.overlay-background {
    background: #000000c7;
}

.overlay__inner {
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    position: absolute;
}

.overlay__content {
    left: calc(50% - 25px);
    position: absolute;
    top: calc(50% - 25px);
    transform: translate(-50%, -50%);
}

.overlay-wait {
    top: calc(50% - 80px);
    left: calc(50% + 0px);
    color: #fff;
    text-align: center;
    line-height: 22px;
    width: 90%;
}

.spinner {
    height: 0;
    width: 0;
    padding: 15px;
    border: 6px solid #ccc;
    border-right-color: #888;
    border-radius: 22px;
    -webkit-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear;
    /* left, top and position just for the demo! */
    position: absolute;
    left: 50%;
    top: 50%;
}

@keyframes spin {
    100% {
        transform: rotate(360deg);
    }
}