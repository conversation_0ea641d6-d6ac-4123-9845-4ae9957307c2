import { Component } from '@angular/core';
import { SessionService } from 'src/app/services/session-service/session.service';
import { PopoverController } from '@ionic/angular';

@Component({
  selector: 'app-session-timeout',
  templateUrl: './session-timeout.component.html',
  styleUrls: ['./session-timeout.component.scss']
})
export class SessionTimeoutComponent {
  constructor(public readonly sessionService: SessionService, private readonly popoverController: PopoverController) {}

  continueSession(status: boolean): void {
    this.popoverController.dismiss(status);
  }
}
