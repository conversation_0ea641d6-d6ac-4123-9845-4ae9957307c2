<ion-row class="session-popup">
    <ion-col class="ion-text-center">
        {{ 'MESSAGES.SESSION_EXPIRE' | translate : { min: sessionService.min, sec: sessionService.sec} }}
    </ion-col>
</ion-row>
<ion-row class="session-popup-buttons">
    <ion-col>
        <ion-button (click)="continueSession(false)" class="ion-text-capitalize" expand="block" id="logout-session"
            fill="clear">
            <ion-label>{{ 'MENU.LOGOUT' | translate }}</ion-label>
        </ion-button>
    </ion-col>
    <ion-col>
        <ion-button (click)="continueSession(true)" class="ion-text-capitalize" expand="block" id="continue-session"
            fill="clear">
            <ion-label>{{ 'LABELS.CONTINUE_SESSION' | translate }}</ion-label>
        </ion-button>
    </ion-col>
</ion-row>