<ng-container *ngIf="isDateRange">
  <ion-header>
    <ion-toolbar mode="ios">
      <ion-buttons slot="start">
        <ion-button color="white" (click)="selectOptions('')">
          {{ 'BUTTONS.CANCEL' | translate }}
        </ion-button>
      </ion-buttons>
      <ion-title mode="ios" color="white">{{ 'LABELS.CALENDAR' | translate }}</ion-title>
      <ion-buttons slot="end">
        <ion-button color="white" (click)="selectOptions(dateRange)">
          {{ 'BUTTONS.DONE' | translate }}
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  <ion-content>
    <ion-row>
      <ion-col size="12">
        <ion-item>
          <ion-label position="floating" class="ion-text-capitalize">{{ 'LABELS.FROM' | translate }}</ion-label>
          <ion-input
            type="text"
            [value]="dateRange.from"
            [placeholder]="'PLACEHOLDERS.ENTER_FROM_DATE' | translate"
            (ionBlur)="enteredDate($event, 'from')"
            [maskito]="dateMask"
            [maskitoElement]="maskPredicate"
          ></ion-input>
        </ion-item>
        <ion-item>
          <ion-label position="floating">{{ 'LABELS.TO' | translate }}</ion-label>
          <ion-input
            type="text"
            [value]="dateRange.to"
            [placeholder]="'PLACEHOLDERS.ENTER_TO_DATE' | translate"
            (ionBlur)="enteredDate($event, 'to')"
            [maskito]="dateMask"
            [maskitoElement]="maskPredicate"
          ></ion-input>
        </ion-item>
      </ion-col>
    </ion-row>
    <ion-calendar [(ngModel)]="dateRange" [options]="optionsRange" [type]="type" (change)="onChange($event)"></ion-calendar>
  </ion-content>
</ng-container>
<ng-container *ngIf="!isDateRange">
  <ion-list class="set-list-margin">
    <ion-item
      lines="none"
      class="ion-no-padding"
      [class.selected-list]="selected === filterSelectedData[monthType]"
      (click)="selected = filterSelectedData[monthType]"
      *ngFor="let monthType of monthTypes"
    >
      <ion-label>{{ filterData[monthType] | translate }}</ion-label>
    </ion-item>
  </ion-list>
  <ion-row>
    <ion-col size="6" class="ion-text-end">
      <ion-button size="small" mode="ios" class="custom-buttons apply-button" (click)="selectOptions(selected)">
        {{ 'BUTTONS.APPLY' | translate }}
      </ion-button>
    </ion-col>
    <ion-col size="6">
      <ion-button size="small" color="medium" mode="ios" class="ion-text-end" (click)="selectOptions()">
        {{ 'BUTTONS.CANCEL' | translate }}
      </ion-button>
    </ion-col>
  </ion-row>
</ng-container>
