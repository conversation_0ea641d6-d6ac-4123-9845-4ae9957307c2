import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CalendarModule } from 'ion7-calendar';
import { TranslateModule } from '@ngx-translate/core';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MaskitoDirective } from '@maskito/angular';
import { DateFilterPopupComponent } from './date-filter-popup.component';

@NgModule({
  declarations: [DateFilterPopupComponent],
  imports: [CommonModule, IonicModule, TranslateModule, FormsModule, ReactiveFormsModule, CalendarModule, MaskitoDirective],
  exports: [DateFilterPopupComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DateFilterPopupModule {}
