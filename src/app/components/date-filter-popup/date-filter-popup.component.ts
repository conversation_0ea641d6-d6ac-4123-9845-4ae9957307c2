import { CalendarComponentOptions } from 'ion7-calendar';
import { Component, Input } from '@angular/core';
import { PopoverController, ModalController } from '@ionic/angular';
import * as moment from 'moment';
import { Constants } from 'src/app/constants/constants';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';

@Component({
  selector: 'app-date-filter-popup',
  templateUrl: './date-filter-popup.component.html',
  styleUrls: ['./date-filter-popup.component.scss']
})
export class DateFilterPopupComponent {
  @Input() selected;
  @Input() isDateRange = false;
  type: 'string';
  optionsRange: CalendarComponentOptions = {
    pickMode: 'range',
    from: new Date(1971, 0, 1),
    to: 0,
    color: 'light',
    weekStart: 1 
  };
  @Input() dateRange = {
    from: '',
    to: ''
  };
  @Input() monthTypes = Constants.monthTypes.default;
  filterData = Constants.filterOptions;
  filterSelectedData = Constants.filterSelectedOptions;

  constructor(
    private readonly popoverController: PopoverController,
    private readonly modalController: ModalController
  ) {}

  selectOptions(value = undefined): void {
    if (this.isDateRange) {
      this.modalController.dismiss(value);
    } else {
      this.dateRange = JSON.parse(JSON.stringify(Constants.resetSelectedDateRange));
      this.popoverController.dismiss(value);
    }
  }

  onChange(event): void {
    this.dateRange.from = moment(event.from).format(Constants.dateFormat.mdy);
    this.dateRange.to = moment(event.to).format(Constants.dateFormat.mdy);
  }

  enteredDate(event, type: string): void {
    const isValidDate = moment(event.target.value, Constants.dateFormat.mdy).isValid();
    this.dateRange[type] = !isValidDate ? '' : moment(event.target.value).format(Constants.dateFormat.mdy);
  }

  readonly dateMask: MaskitoOptions = {
    mask: [...Array(2).fill(/\d/), '/', ...Array(2).fill(/\d/), '/', ...Array(4).fill(/\d/)]
  };

  readonly maskPredicate: MaskitoElementPredicate = async (el) => (el as HTMLIonInputElement).getInputElement();
}
