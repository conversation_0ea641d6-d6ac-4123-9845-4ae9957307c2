import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { IonicModule, PopoverController, ModalController } from '@ionic/angular';

import { TestConstants } from 'src/app/constants/test-constants';
import { TranslateModule } from '@ngx-translate/core';
import { DateFilterPopupComponent } from './date-filter-popup.component';

describe('DateFilterPopupComponent', () => {
  let component: DateFilterPopupComponent;
  let fixture: ComponentFixture<DateFilterPopupComponent>;
  const { popoverSpy, modalSpy } = TestConstants;
  let popoverController: PopoverController;
  let modalController: ModalController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DateFilterPopupComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(DateFilterPopupComponent);
    component = fixture.componentInstance;

    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popoverSpy;
    });
    popoverSpy.present.and.stub();
    spyOn(popoverController, 'dismiss').and.stub();

    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call select filter options and close popup', () => {
    component.selectOptions();
    expect(popoverController.dismiss).toHaveBeenCalled();
  });

  it('should call select filter options and close modal', () => {
    component.isDateRange = true;
    component.selectOptions();
    expect(modalController.dismiss).toHaveBeenCalled();
  });

  it('should call onChange to set date', () => {
    const event = {
      from: '12/18/2023',
      to: '12/21/2023'
    };
    component.onChange(event);
    expect(component.onChange).toBeDefined();
  });

  it('should call enteredDate to set date', () => {
    component.enteredDate({ target: { value: '' } }, 'from');
    expect(component.enteredDate).toBeDefined();
  });

  it('should call enteredDate to set from date', () => {
    component.enteredDate({ target: { value: '12/12/2023' } }, 'from');
    expect(component.dateRange.from).toBe('12/12/2023');
  });

  it('should call enteredDate to set to date', () => {
    component.enteredDate({ target: { value: '12/20/2023' } }, 'to');
    expect(component.dateRange.to).toBe('12/20/2023');
  });
});
