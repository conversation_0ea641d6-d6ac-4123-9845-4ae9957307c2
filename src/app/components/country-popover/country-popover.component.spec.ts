import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule, PopoverController } from '@ionic/angular';
import { CountryPopoverComponent } from './country-popover.component';

describe('CountryPopoverComponent', () => {
  let component: CountryPopoverComponent;
  let fixture: ComponentFixture<CountryPopoverComponent>;

  let popoverControllerSpy: jasmine.SpyObj<PopoverController>;

  beforeEach(waitForAsync(() => {
    const popoverControllerMock = jasmine.createSpyObj('PopoverController', ['dismiss']);
    TestBed.configureTestingModule({
      declarations: [CountryPopoverComponent],
      imports: [IonicModule.forRoot()],
      providers: [{ provide: PopoverController, useValue: popoverControllerMock }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    popoverControllerSpy = TestBed.inject(PopoverController) as jasmine.SpyObj<PopoverController>;

    fixture = TestBed.createComponent(CountryPopoverComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should dismiss popover controller with data', () => {
    component.sendCode({ name: 'Australia', dialCode: '+61', code: 'AU' });
    expect(popoverControllerSpy.dismiss).toBeDefined();
  });

  it('should filter the countries list based on the search term', () => {
    component.countries = [
      { name: 'USA', dialCode: '+1', code: 'US' },
      { name: 'Canada', dialCode: '+1', code: 'US' },
      { name: 'Australia', dialCode: '+61', code: 'AU' }
    ];
    const event = {
      target: {
        value: 'ca'
      }
    } as any;
    component.filterList(event);
    expect(component.countriesList).toEqual([{ name: 'Canada', dialCode: '+1', code: 'US' }]);
  });

  it('should not filter the countries list when search term is empty', () => {
    component.countries = [
      { name: 'USA', dialCode: '+1', code: 'US' },
      { name: 'Canada', dialCode: '+1', code: 'US' },
      { name: 'Australia', dialCode: '+61', code: 'AU' }
    ];
    const event = {
      target: {
        value: ''
      }
    } as any;
    component.filterList(event);
    expect(component.countriesList).toEqual(component.countries);
  });
});
