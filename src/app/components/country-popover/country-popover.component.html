<div class="users-popover">
  <ion-searchbar (ionInput)="filterList($event)" id="search-country" class="static-search"></ion-searchbar>
  <ul class="popover-list">
    <li *ngFor="let country of countriesList" class="user-list-section" (click)="sendCode(country)" id="{{ country.dialCode }}">
      <div><span class="fi fi-{{ country.code }}"></span> {{ country.name }} {{ country.dialCode }}</div>
    </li>
  </ul>
</div>
