import { Component, OnInit } from '@angular/core';
import { countryDialCodes } from 'src/app/constants/country-dial-codes';
import { InputCustomEvent, PopoverController } from '@ionic/angular';
import { Country } from 'src/app/interfaces/common-interface';

@Component({
  selector: 'app-country-popover',
  templateUrl: './country-popover.component.html',
  styleUrls: ['./country-popover.component.scss']
})
export class CountryPopoverComponent implements OnInit {
  countries: Country[] = [];
  countriesList: Country[];
  constructor(private readonly popoverController: PopoverController) {}

  ngOnInit(): void {
    this.getCountry();
  }
  getCountry(): void {
    countryDialCodes.map((e) => {
      e.code = e.code.toLowerCase();
      return e.code;
    });
    this.countries = countryDialCodes;
    this.countriesList = countryDialCodes;
  }
  sendCode(data: Country): void {
    this.popoverController.dismiss(data);
  }
  filterList(event: InputCustomEvent): boolean {
    this.countriesList = this.countries;
    const searchTerm = <string>event?.target?.value;
    if (!searchTerm) {
      return;
    }
    this.countriesList = this.countriesList.filter(
      (country) => country?.name && searchTerm && country.name.toLowerCase().indexOf(searchTerm.toLowerCase()) > -1
    );
  }
}
