import { Component, Input } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Constants } from 'src/app/constants/constants';

@Component({
  selector: 'app-show-message',
  templateUrl: './show-message.component.html',
  styleUrls: ['./show-message.component.scss']
})
export class ShowMessageComponent {
  @Input() messageData: any = {};
  userData: any;
  constants: any = {};
  constructor(public shared: SharedService) {
    this.userData = this.shared.userData;
    this.constants = Constants;
  }
}
