<ion-grid *ngIf="messageData">
    <ion-row>
        <ion-col>
            <div class="empty-list-items">
                <div class="no-items-icon">
                    <img src="../assets/images/no-{{messageData.type}}.png" alt="no-{{messageData.type}}" />
                </div>
                <div class="empty-text" *ngIf="!(messageData?.search?.text || messageData?.search?.date)">
                    {{ 'MESSAGES.NO_LIST_DATA' | translate : { listType: messageData.type } }}
                </div>
                <div class="empty-text" *ngIf="messageData?.search?.text && !messageData?.search?.date">
                    {{ 'MESSAGES.NO_LIST_DATA_SEARCH' | translate : { searchText: messageData?.search?.text } }}
                </div>
                <div class="empty-text" *ngIf="!messageData?.search?.text && messageData?.search?.date">
                    {{ 'MESSAGES.NO_LIST_DATA_SEARCH_DATE' | translate : { searchDate: messageData?.search?.date } }}
                </div>
                <div class="empty-text" *ngIf="messageData?.search?.text && messageData?.search?.date">
                    {{ 'MESSAGES.NO_LIST_DATA_SEARCH_CRITIRIA' | translate }}
                </div>
                <div class="start-newchat-text" *ngIf="userData?.group.toString() === constants?.patientGroupId.toString()">
                    {{ 'MESSAGES.NO_LIST_DATA_START_CHAT' | translate : {type: messageData?.type} }}
                </div>
            </div>
        </ion-col>
    </ion-row>
</ion-grid>