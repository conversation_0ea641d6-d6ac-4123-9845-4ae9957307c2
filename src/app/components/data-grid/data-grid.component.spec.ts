import { USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsStore } from 'ngx-permissions';
import { NgxPermissionsService } from 'ngx-permissions';
import { RouterModule } from '@angular/router';
import { TranslateTestingModule } from './../../services/translate-testing.module';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { DataGridComponent } from './data-grid.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('DataGridComponent', () => {
  let component: DataGridComponent;
  let fixture: ComponentFixture<DataGridComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DataGridComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateTestingModule
      ],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(DataGridComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit itemBtnClick event with correct parameters', () => {
    const item = { id: 1, name: 'Test Item' };
    const index = 0;
    const expectedAction = 'click';

    spyOn(component.itemBtnClick, 'emit');

    component.selectItem(item, index);

    expect(component.itemBtnClick.emit).toHaveBeenCalledWith({
      action: expectedAction,
      item: item,
      index: index
    });
  });
});
