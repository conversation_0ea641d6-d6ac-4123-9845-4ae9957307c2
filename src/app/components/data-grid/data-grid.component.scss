.grid-col {
    float: left;
    text-align: center;
    min-height: 100px;
    border: 1px solid #eaeaea;
    margin-right: -1px;
    margin-bottom: -1px;
    padding-top: 15px;
    background-color: #f2f2f2;

    .grid-name {
        color: #58595b;
        text-overflow: ellipsis;
        font-size: 14px;
        line-height: 16px;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
    }

    .grid-icon img {
        width: 40px;
    }
}

.grid-col.item-not-read {
    background-color: #ffffff;
}

.data-grid-title {
    font-weight: 600;
    color: #404040;
    font-size: 14px;
    font-family: 'Open Sans', sans-serif;
    line-height: 20px;
}

.font-weight-lite {
    font-weight: 400;
}