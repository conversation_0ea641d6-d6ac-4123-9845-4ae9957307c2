import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { DataGridComponent } from './data-grid.component';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared.module';
import { ShowMessageModule } from '../show-message/show-message.module';


@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        TranslateModule,
        SharedModule,
        ShowMessageModule
    ],
    declarations: [DataGridComponent],
    exports: [DataGridComponent]
})
export class DataGridModule { }
