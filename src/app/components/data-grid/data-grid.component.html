<ion-grid>
    <ion-row *ngIf="extraData.type !== constants?.delivery">
        <ion-col align-self-start *ngFor="let item of gridData;let i = index" class="grid-col"
            [ngClass]="{'item-not-read':!item.isRead}" size="4" tappable (click)="selectItem(item, i)" id="item-{{i}}">
            <div class="grid-item">
                <div class="grid-icon">
                    <img src="../../assets/{{extraData.image}}" class="grid-icon-background" [alt]="item.displayLabel">
                </div>
                <div class="grid-name">
                    <label>{{item.displayLabel}}</label>
                </div>
            </div>
        </ion-col>
    </ion-row>
    <ion-row *ngIf="extraData.type === constants?.delivery">
        <ion-col align-self-start *ngFor="let item of gridData;let i = index" class="grid-col" size="4" tappable
            (click)="selectItem(item, i)" id="item-{{i}}">
            <div class="data-grid-title">
                <div class="data-grid-title">{{ 'LABELS.TICKET_NO' | translate }} :{{item.courierTicketNumber}}</div>
            </div>
            <div class="data-grid-title font-weight-lite">
                <ion-icon name="time-outline" class="set-align-center"></ion-icon>
                {{item.courierDateTime* 1000 | shortDateTime}}
            </div>
        </ion-col>
    </ion-row>
</ion-grid>
<app-show-message *ngIf="extraData.showNoDataMessage" [messageData]="extraData"></app-show-message>