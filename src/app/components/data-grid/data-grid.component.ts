import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Constants } from 'src/app/constants/constants';
import { SharedService } from 'src/app/services/shared-service/shared.service';

@Component({
  selector: 'app-data-grid',
  templateUrl: './data-grid.component.html',
  styleUrls: ['./data-grid.component.scss']
})
export class DataGridComponent {
  @Input() gridData: any;
  @Input() extraData: any = {};
  @Output() readonly itemBtnClick: EventEmitter<any> = new EventEmitter();
  userData: any;
  constants: any = {};
  constructor(public shared: SharedService) {
    this.constants = Constants;
    this.userData = this.shared.userData;
  }

  selectItem(item: any, index: number): void {
    const action = 'click';
    this.itemBtnClick.emit({ action, item, index });
  }
}
