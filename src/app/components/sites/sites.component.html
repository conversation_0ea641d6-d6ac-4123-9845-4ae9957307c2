<div class="site-header" [ngClass]="{'disable-filter':disableFilter}" *ngIf="multisiteFilter">
    <ion-grid>
        <ion-row class="ion-align-items-center">
            <ion-col size="2.5" class="place-holder" *ngIf="!isHomeSiteEnabled && showSitesTitle">
                <b>{{siteLabel | translate}}</b>
                <b *ngIf="mandatory"> *</b>
            </ion-col>
            <ion-col class="drop-down-place-holder" (click)="toggle()" id="site-dropdown">
                <ion-icon name="caret-down" class="arrow" *ngIf="!dropDown"></ion-icon>
                <ion-icon name="caret-up" class="arrow" *ngIf="dropDown"></ion-icon>
                <label>{{mySitesPlaceHolder}}</label>
            </ion-col>
        </ion-row>
    </ion-grid>
    <ion-grid class="drop-down-row">
        <ion-row *ngIf="dropDown" class="ion-align-items-center">
            <ion-col size="2.5" *ngIf="showSitesTitle">
            </ion-col>
            <ion-col class="drop-down">
                <ion-searchbar class="searchbar" [(ngModel)]="searchKey" (ionChange)="onInput($event)"
                    (ionCancel)="onCancel($event)">
                </ion-searchbar>
                <ion-list class="list">
                    <div *ngFor="let sites of mySites;let i=index" class="list-item"
                        [ngClass]="siteIds.includes(sites.id)?'list-item-checked':'list-item-unchecked'"
                        (click)="toggleSite(sites)">
                        <label>{{sites.name}}</label>
                        <p class="tick-mark" *ngIf="siteIds.includes(sites.id)!==false">&#10003;</p>
                    </div>
                </ion-list>
                <ion-list lines="none">
                    <ion-row class="site-buttons">
                        <!-- TODO! CHP-3595 -->
                        <ion-col size="5" class="ion-text-end ion-no-padding" *ngIf="!singleSiteSelection">
                            <ion-button (click)="allMySites()" size="small" class="ion-text-capitalize">
                                {{'BUTTONS.ALL_MY_SITES' | translate}}
                            </ion-button>
                        </ion-col>
                        <ion-col size="3.5" class="ion-text-center ion-no-padding">
                            <ion-button (click)="doneSites()" size="small" class="ion-text-capitalize">
                                {{'BUTTONS.DONE' | translate}}
                            </ion-button>
                        </ion-col>
                        <ion-col size="3.5" class="ion-no-padding">
                            <ion-button (click)="resetSites()" size="small" class="ion-text-capitalize">
                                {{'BUTTONS.RESET' | translate}}
                            </ion-button>
                        </ion-col>
                    </ion-row>
                </ion-list>
            </ion-col>
        </ion-row>
    </ion-grid>
</div>
<div class="site-header-padding" *ngIf="!multisiteFilter"></div>