import { CommonService } from 'src/app/services/common-service/common.service';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, ModalController, ActionSheetController } from '@ionic/angular';
import { SitesComponent } from 'src/app/components/sites/sites.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TestConstants } from 'src/app/constants/test-constants';
import { RouterTestingModule } from '@angular/router/testing';
import { LoginPage } from 'src/app/pages/auth/login/login.page';

describe('SitesComponent', () => {
  let component: SitesComponent;
  let fixture: ComponentFixture<SitesComponent>;
  let sharedService: SharedService;
  const { modalSpy } = TestConstants;
  let modalController: ModalController;
  const mysiteMock = [
    { id: 1, name: 'a' },
    { id: 2, name: 'b' },
    { id: 3, name: 'c' },
    { id: 4, name: 'd' }
  ];
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SitesComponent],
      imports: [
        IonicModule.forRoot(),
        HttpClientModule,
        HttpClientTestingModule,
        ReactiveFormsModule,
        RouterModule.forRoot([]),
        TranslateModule.forRoot(),
        RouterTestingModule.withRoutes([{ path: 'login', component: LoginPage }])
      ],
      providers: [
        CommonService,
        SharedService,
        NgxPermissionsService,
        NgxPermissionsStore,
        ActionSheetController,
        Idle,
        IdleExpiry,
        ModalController,
        Keepalive,

        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    modalSpy.onDidDismiss.and.resolveTo({ data: 'fakeData' });
    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    sharedService.userData.mySites = mysiteMock;
    fixture = TestBed.createComponent(SitesComponent);
    component = fixture.componentInstance;
    component.mySites = [];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('execute toggle', () => {
    component.dropDown = true;
    component.toggle();
    expect(component.dropDown).toBeFalse();
  });
  it('execute toggleSite: pop', () => {
    component.dropDown = true;
    component.siteIds = [1, 2, 3];
    component.mySitesAll = mysiteMock;
    component.mySites = JSON.parse(JSON.stringify(component.mySitesAll));
    component.toggleSite({ id: 1 });
    expect(component.siteIds).toEqual([2, 3]);
  });
  it('execute toggleSite: singleSelection', () => {
    component.dropDown = true;
    component.singleSiteSelection = true;
    component.siteIds = [1, 2, 3];
    component.mySitesAll = mysiteMock;
    component.mySites = JSON.parse(JSON.stringify(component.mySitesAll));
    component.toggleSite({ id: 4 });
    expect(component.siteIds).toEqual([4]);
  });
  it('execute toggleSite: push', () => {
    component.dropDown = true;
    component.singleSiteSelection = false;
    component.siteIds = [1, 2, 3];
    component.mySitesAll = mysiteMock;
    component.mySites = JSON.parse(JSON.stringify(component.mySitesAll));
    component.toggleSite({ id: 4 });
    expect(component.siteIds).toEqual([1, 2, 3, 4]);
  });
  it('execute allMySites', () => {
    component.siteIdsAll = [1, 2, 3, 4];
    component.mySitesAll = mysiteMock;
    component.allMySites();
    expect(component.siteIds).toEqual(component.siteIdsAll);
  });
  it('execute resetSites', () => {
    component.siteIdsAll = [1, 2, 3, 4];
    component.mySitesAll = mysiteMock;
    component.resetSites();
    expect(component.siteIds).toEqual([]);
  });
  it('execute doneSites', () => {
    component.siteIds = [];
    component.doneSites();
    expect(component.doneSites).toBeTruthy();
  });
  it('execute onInput', () => {
    component.searchKey = 'a';
    component.mySitesAll = mysiteMock;
    component.onInput({});
    expect(component.mySites).toEqual([mysiteMock[0]]);
  });
  it('execute onInput: no search key', () => {
    component.searchKey = '';
    component.mySitesAll = mysiteMock;
    component.onInput({});
    expect(component.mySites).toEqual(mysiteMock);
  });
  it('execute onCancel', () => {
    component.mySitesAll = mysiteMock;
    component.onCancel({});
    expect(component.mySites).toEqual(mysiteMock);
  });
  it('execute ngOnInit', () => {
    component.mySitesAll = mysiteMock;
    component.singleSiteSelection = true;
    component.selectedSiteIds = [1, 2];
    component.ngOnInit();
    expect(component.siteIds).toEqual([1, 2]);
  });
  it('execute ngOnInit isHomeSiteEnabled true', () => {
    spyOn(component, 'changePlaceHolder').and.stub();
    component.isHomeSiteEnabled = true;
    component.ngOnInit();
    expect(component.mySites).toEqual([]);
    expect(component.mySitesPlaceHolder).toEqual('');
    expect(component.changePlaceHolder).toHaveBeenCalled();
  });
  it('execute ngOnChanges', () => {
    component.isHomeSiteEnabled = true;
    component.listHomeSiteIds = [];
    component.ngOnChanges({ selectedSiteIds: { currentValue: [1, 2] } as any });
    expect(component.siteIds).toEqual([]);
  });
  it('execute ngOnChanges reset value', () => {
    spyOn(component, 'resetSites').and.stub();
    component.isReset = true;
    component.ngOnChanges({ isReset: { currentValue: true } as any });
    expect(component.isReset).toBe(false);
    expect(component.resetSites).toHaveBeenCalled();
  });
  it('execute onInput with isHomeSiteEnabled true and searchKey blank', () => {
    component.isHomeSiteEnabled = true;
    component.searchKey = '';
    component.onInput({});
    expect(component.mySites).toEqual(undefined);
  });

  it('execute onInput with isHomeSiteEnabled true and searchKey with a', () => {
    component.isHomeSiteEnabled = true;
    component.searchKey = 'a';
    component.homeSites = mysiteMock;
    component.onInput({});
    const data = mysiteMock[0];
    expect(component.mySites).toEqual([data]);
  });

  it('ngOnInit should initialize siteIds with getSelectedSites', () => {
    spyOn(component, 'getSelectedSites').and.returnValue([1, 2, 3]);
    component.ngOnInit();
    expect(component.siteIds).toEqual([1, 2, 3]);
  });

  it('ngOnInit should handle singleSiteSelection', () => {
    component.singleSiteSelection = true;
    spyOn(component, 'changePlaceHolder').and.stub();
    component.ngOnInit();
    expect(component.siteIds).toEqual([]);
    expect(component.mySitesPlaceHolder).toEqual('');
    expect(component.changePlaceHolder).toHaveBeenCalled();
  });

  it('ngOnInit should handle selectedSiteIds', () => {
    component.selectedSiteIds = [1, 2];
    spyOn(component, 'changePlaceHolder').and.stub();
    component.ngOnInit();
    expect(component.siteIds).toEqual([1, 2]);
    expect(component.changePlaceHolder).toHaveBeenCalled();
  });

  it('ngOnInit should handle isHomeSiteEnabled', () => {
    component.isHomeSiteEnabled = true;
    spyOn(component, 'changePlaceHolder').and.stub();
    component.ngOnInit();
    expect(component.mySites).toEqual([]);
    expect(component.mySitesPlaceHolder).toEqual('');
    expect(component.changePlaceHolder).toHaveBeenCalled();
  });

  it('ngOnInit should emit onLoadSites with getSelectedSites', () => {
    spyOn(component.onLoadSites, 'emit').and.stub();
    spyOn(component, 'getSelectedSites').and.returnValue([1, 2, 3]);
    component.ngOnInit();
    expect(component.onLoadSites.emit).toHaveBeenCalledWith([1, 2, 3]);
  });

  it('ngOnInit should handle crossSiteCommunication', () => {
    component.crossSiteCommunication = true;
    component.sharedService.userData.enabledCrosssites = [
      { id: '1', name: 'Site 1' },
      { id: '2', name: 'Site 2' }
    ];
    spyOn(component, 'changePlaceHolder').and.stub();
    spyOn(component.onLoadSites, 'emit').and.stub();
    component.ngOnInit();
    expect(component.mySites).toEqual([
      { id: 1, name: 'Site 1' },
      { id: 2, name: 'Site 2' }
    ]);
    expect(component.siteIds).toEqual([1, 2]);
    expect(component.mySitesAll).toEqual([
      { id: 1, name: 'Site 1' },
      { id: 2, name: 'Site 2' }
    ]);
    expect(component.changePlaceHolder).toHaveBeenCalled();
    expect(component.onLoadSites.emit).toHaveBeenCalledWith([1, 2]);
  });

  it('ngOnChanges should update siteIds when selectedSiteIds changes and isHomeSiteEnabled is true', () => {
    component.isHomeSiteEnabled = true;
    component.listHomeSiteIds = [1, 2];
    component.sharedService.userData.mySites = mysiteMock;
    component.siteIds = [3];
    component.ngOnChanges({ selectedSiteIds: { currentValue: [1, 2] } as any });
    expect(component.mySites).toEqual([
      { id: 1, name: 'a' },
      { id: 2, name: 'b' }
    ]);
    expect(component.homeSites).toEqual([
      { id: 1, name: 'a' },
      { id: 2, name: 'b' }
    ]);
    expect(component.siteIds).toEqual([]);
  });

  it('ngOnChanges should update siteIds when selectedSiteIds changes and isHomeSiteEnabled is false', () => {
    component.isHomeSiteEnabled = false;
    component.selectedSiteIds = [1, 2];
    component.ngOnChanges({ selectedSiteIds: { currentValue: [1, 2] } as any });
    expect(component.siteIds).toEqual([1, 2]);
  });

  it('ngOnChanges should update siteIds and call setSites when availableSites changes', () => {
    spyOn(component, 'setSites').and.stub();
    component.selectedSiteIds = [1, 2];
    component.ngOnChanges({ availableSites: { currentValue: mysiteMock } as any });
    expect(component.siteIds).toEqual([1, 2]);
    expect(component.setSites).toHaveBeenCalledWith(mysiteMock);
  });

  it('ngOnChanges should call resetSites and set isReset to false when isReset changes', () => {
    spyOn(component, 'resetSites').and.stub();
    component.isReset = true;
    component.ngOnChanges({ isReset: { currentValue: true } as any });
    expect(component.isReset).toBe(false);
    expect(component.resetSites).toHaveBeenCalled();
  });

  it('ngOnChanges should call changePlaceHolder and detectChanges', () => {
    spyOn(component, 'changePlaceHolder').and.stub();
    component.ngOnChanges({});
    expect(component.changePlaceHolder).toHaveBeenCalled();
  });
});
