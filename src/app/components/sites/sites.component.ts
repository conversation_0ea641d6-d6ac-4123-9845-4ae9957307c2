import { Component, OnInit, Output, EventEmitter, Input, OnChanges, ChangeDetectorRef, SimpleChanges } from '@angular/core';
import { ModalController, ActionSheetController } from '@ionic/angular';
import { Constants } from 'src/app/constants/constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { getValueFromLocalStorage } from 'src/app/utils/storage-utils';
import { isBlank, isPresent } from 'src/app/utils/utils';

@Component({
  selector: 'app-sites',
  templateUrl: './sites.component.html',
  styleUrls: ['./sites.component.scss']
})
export class SitesComponent implements OnInit, OnChanges {
  @Output() readonly filterSites = new EventEmitter();
  @Output() readonly onLoadSites = new EventEmitter();
  @Input() disableFilter = false;
  @Input() selectedSiteIds: any;
  @Input() listHomeSiteIds = [];
  @Input() singleSiteSelection = false;
  @Input() mandatory = false;
  @Input() isHomeSiteEnabled = false;
  @Input() showSitesTitle = true;
  @Input() isReset = false;
  @Input() siteLabel = 'LABELS.SITES';
  @Input() availableSites = [];
  @Input() crossSiteCommunication = false;
  mySites = [];
  mySitesOpted = [];
  mySitesPlaceHolder = '';
  siteIds = [];
  siteIdsAll = [];
  dropDown = false;
  shouldShowCancel = true;
  constants = Constants;
  searchKey = '';
  multisiteFilter = true;
  mySitesAll: any[];
  homeSites: any[];
  constructor(
    public modalCtrl: ModalController,
    public actionSheetController: ActionSheetController,
    private readonly commonService: CommonService,
    public readonly sharedService: SharedService,
    private cd: ChangeDetectorRef
  ) {
    const { userData } = this.sharedService;
    this.multisiteFilter = sharedService.userData.config.enable_multisite === this.constants.enableMultiSite;
    this.setSites(userData.mySites);
  }
  setSites(mySites) {
    this.mySites = mySites;
    this.mySitesAll = this.mySites;
    this.mySites?.forEach((element) => {
      this.siteIdsAll.push(element.id);
    });
    if (this.siteIds.length === 0) {
      this.siteIds = this.getSelectedSites();
    }
    this.changePlaceHolder();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.selectedSiteIds?.currentValue) {
      const data = this.listHomeSiteIds;
      if (this.isHomeSiteEnabled) {
        this.mySites = this.sharedService.userData.mySites.filter((mySites) => data.includes(mySites.id));
        this.homeSites = this.mySites;
        if (isBlank(data) || !data.includes(this.siteIds[0])) {
          this.siteIds = [];
        }
      } else {
        this.siteIds = this.selectedSiteIds;
      }
    }
    if (changes?.availableSites?.currentValue) {
      if (isPresent(this.selectedSiteIds)) {
        this.siteIds = this.selectedSiteIds;
      }
      this.setSites(changes?.availableSites?.currentValue);
    }
    if (changes?.isReset?.currentValue) {
      this.resetSites();
      this.isReset = false;
    }
    this.changePlaceHolder();
    this.cd.detectChanges();
  }
  ngOnInit(): void {
    this.siteIds = this.getSelectedSites();
    if (this.singleSiteSelection) {
      this.siteIds = [];
      this.mySitesPlaceHolder = '';
      this.changePlaceHolder();
    }
    if (this.selectedSiteIds) {
      this.siteIds = this.selectedSiteIds;
      this.changePlaceHolder();
    }
    if (this.isHomeSiteEnabled) {
      this.mySites = [];
      this.mySitesPlaceHolder = '';
      this.changePlaceHolder();
    }
    this.onLoadSites.emit(this.getSelectedSites());
    if (this.crossSiteCommunication && isPresent(this.sharedService?.userData?.enabledCrosssites)) {
      this.mySites = this.sharedService.userData.enabledCrosssites.map((item) => ({
        id: +item.id,
        name: item.name
      }));
      this.siteIds = this.mySites.map((site) => site.id);
      this.mySitesAll = this.mySites;
      this.changePlaceHolder();
      this.onLoadSites.emit(this.siteIds);
    }
  }

  toggle(): void {
    this.dropDown = !this.dropDown;
    if (!this.dropDown) {
      this.doneSites();
    }
  }
  toggleSite(site: any): void {
    if (this.siteIds.includes(site.id)) {
      this.siteIds = this.siteIds.filter((x) => x !== site.id);
    } else if (this.singleSiteSelection) {
      this.siteIds = [];
      this.siteIds.push(site.id);
      this.dropDown = false;
      this.filterSites.emit(this.siteIds);
    } else {
      this.siteIds.push(site.id);
    }
    this.changePlaceHolder();
  }
  allMySites(): void {
    this.siteIds = this.siteIdsAll;
    this.changePlaceHolder();
  }
  resetSites(): void {
    this.siteIds = [];
    this.mySitesPlaceHolder = '';
    this.changePlaceHolder();
  }
  doneSites(): void {
    this.dropDown = false;
    this.filterSites.emit(this.siteIds);
  }
  onInput(event: any): void {
    if (!this.isHomeSiteEnabled) {
      if (this.searchKey.length > 0) {
        this.mySites = this.mySitesAll.filter((x) => String(x.name.toUpperCase()).includes(this.searchKey.toUpperCase()));
      } else {
        this.mySites = this.mySitesAll;
      }
    } else {
      if (this.searchKey.length > 0) {
        this.mySites = this.homeSites.filter((x) => String(x.name.toUpperCase()).includes(this.searchKey.toUpperCase()));
      } else {
        this.mySites = this.homeSites;
      }
    }
  }
  onCancel(event: any): void {
    this.mySites = this.mySitesAll;
  }
  changePlaceHolder(): void {
    const i = this.siteIds.length;
    const { displayedSites, remainingCount } = this.siteIds.reduce(
      (acc, id) => {
        const siteName = this.mySitesAll.find((site) => +site.id === +id)?.name;
        if (siteName) {
          if (acc.displayedSites.length < 2) {
            acc.displayedSites.push(siteName);
          } else {
            acc.remainingCount += 1;
          }
        }
        return acc;
      },
      { displayedSites: [] as string[], remainingCount: 0 }
    );
    this.mySitesPlaceHolder = displayedSites.join(', ') + (remainingCount > 0 ? `, ...(${remainingCount})` : '');
    if (i === 0) {
      if (this.isHomeSiteEnabled) {
        this.mySitesPlaceHolder = this.commonService.getTranslateData('LABELS.SELECT_HOME_SITE');
      } else {
        this.mySitesPlaceHolder = this.commonService.getTranslateData('LABELS.SELECT_SITES');
      }
    }
  }

  getSelectedSites() {
    const selectedSites = getValueFromLocalStorage(Constants.storageKeys.selectedSites);
    return isPresent(selectedSites) && !this.singleSiteSelection ? JSON.parse(selectedSites) : this.siteIdsAll;
  }
}
