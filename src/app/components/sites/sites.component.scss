.site-header {
    border: none;
    position: relative;
    .place-holder {
        color: #58595b;
        font-size: 14px;
        text-align: center;
    }
    .drop-down-place-holder {
        float: right;
        right: 5px;
        font-size: 14px;
        background: #ffffff;
        color: #58595b;
        cursor: pointer;
        border: 1px solid #58595bcc;
        padding: 12px 10px;
        text-align: center;
    }
    .arrow {
        color: var(--ion-color-skin-secondary-bright);
        float: right;
        right: 0;
    }
    .drop-down-row {
        float: left;
        left: 0;
        position: absolute;
        width: 100%;
    }
    .drop-down {
        z-index: 999;
        right: 5px;
        padding: 0 4px;
        border: 1px solid var(--ion-color-skin-main);
        flex-direction: column;
        background: white;
        .searchbar {
            background: white;
        }
        .list {
            overflow-y: scroll;
            height: 210px;
            @media (max-height: 640px) {
                height: 75px;
            }
            @media (min-height: 641px) and (max-height: 680px) {
                height: 110px;
            }
            @media (min-height: 681px) and (max-height: 720px) {
                height: 140px;
            }
            @media (min-height: 721px) and (max-height: 750px) {
                height: 160px;
            }
            padding: 0px 0px;
            margin: 0px 9px 0px 9px;
            .list-item {
                padding: 12px 12px;
                cursor: pointer;
            }
            .tick-mark {
                float: right;
                line-height: 0;
                color: white;
            }
            .list-item-checked {
                background: var(--ion-color-site-selected);
                color: white;
                padding: 12px 12px;
            }
            .list-item-unchecked {
                background: white;
                color: #58595b;
                padding: 12px 12px;
            }
        }
        .buttons {
            height: auto;
            display: flex;
            flex-direction: column;
            padding: 7px 0px;
            button {
                margin: 0 3px;
                background: var(--ion-color-skin-secondary-bright);
                color: white;
                border-radius: 9px;
                padding: 12px 2px;
                flex: 1;
            }
        }
    }
    .site-buttons {
        ion-col {
            ion-button {
                --background: var(--ion-color-de-york);
            }
            .ion-activated {
                --background-activated: var(--ion-color-de-york);
            }
        }
    }
}

/* width */
::-webkit-scrollbar {
    width: 6px;
}

/* Track */
::-webkit-scrollbar-track {
    background: #1c62721f;
    border-radius: 20%;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: var(--ion-color-scrollbar-thumb);
    border-radius: 20%;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: var(--ion-color-skin-secondary-bright);
}
.disable-filter {
    pointer-events: none;
    opacity: 0.4;
}
