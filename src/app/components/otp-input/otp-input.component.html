<ion-row lines="none" id="otp" size="12" class="ion-justify-content-center">
  <form [ngClass]="otpConfig.classList?.container" class="otp-input-container">
    <div class="ion-justify-content-center" *ngFor="let control of ngxOtpArray.controls; let i = index"
      [ngClass]="otpConfig.classList?.inputBox" class="">
      <ion-col [attr.aria-label]="ariaLabels[i]">
        <ion-input #otpInputElement type="number" [id]="'otp-input-' + i" [formControl]="control" [ngxOtpPattern]="otpConfig.pattern"
          [ngClass]="styles?.length > 0 ? styles[i] : null"
          (keyup)="handleKeyUp(i, $event)" (focus)="handleFocus(i)"  (ionInput)="handleChange($event)" class="otp-input" maxlength="1" [maskito]="otpMask"
          autocapitalize="off" spellcheck="false"></ion-input>
      </ion-col>
    </div>
  </form>
</ion-row>