import { HttpClientModule } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA, SimpleChange, SimpleChanges } from '@angular/core';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ActionSheetController, IonicModule, ModalController, PopoverController } from '@ionic/angular';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { TranslateModule } from '@ngx-translate/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Constants } from 'src/app/constants/constants';
import { TestConstants } from 'src/app/constants/test-constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { formatDate, getMonthRangeData } from 'src/app/utils/utils';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of } from 'rxjs';
import { SearchBarComponent, getIonFlagClass, getPriority } from './search-bar.component';
import { DateFilterPopupComponent } from '../date-filter-popup/date-filter-popup.component';


describe('SearchBarComponent', () => {
  let component: SearchBarComponent;
  let fixture: ComponentFixture<SearchBarComponent>;
  let actionSheet: ActionSheetController;
  let modalController: ModalController;
  let popoverController: PopoverController;
  const { modalSpy, actionSheetSpy, popoverSpy } = TestConstants;
  let searchForm: UntypedFormGroup;
  let sharedService: SharedService;
  const formBuilder: UntypedFormBuilder = new UntypedFormBuilder();
  let commonService: CommonService;
  let httpService: HttpService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SearchBarComponent],
      imports: [
        IonicModule.forRoot(),
        ReactiveFormsModule,
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [
        CommonService,
        ActionSheetController,
        ModalController,
        PopoverController,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        PermissionService,
        SharedService,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        { provide: UntypedFormBuilder, useValue: formBuilder }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    sharedService = TestBed.inject(SharedService);
    commonService = TestBed.inject(CommonService);
    httpService = TestBed.inject(HttpService);
    actionSheet = TestBed.inject(ActionSheetController);
    modalController = TestBed.inject(ModalController);
    popoverController = TestBed.inject(PopoverController);
    searchForm = new UntypedFormGroup({
      date: new UntypedFormControl()
    });
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    spyOn(actionSheet, 'create').and.callFake(() => {
      return actionSheetSpy;
    });
    actionSheetSpy.present.and.stub();
    spyOn(actionSheet, 'dismiss').and.stub();
    spyOn(modalController, 'create').and.callFake(() => {
      return modalSpy;
    });
    modalSpy.present.and.stub();
    spyOn(modalController, 'dismiss').and.stub();
    spyOn(popoverController, 'create').and.callFake(() => {
      return popoverSpy;
    });
    popoverSpy.present.and.stub();
    spyOn(popoverController, 'dismiss').and.stub();
    fixture = TestBed.createComponent(SearchBarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('setSelectedDate', () => {
    it('should set selectedDate to formatted date when date control has a value', () => {
      const dateValue = new Date(2023, 6, 10);
      searchForm.controls.date.setValue(dateValue);
      component.setSelectedDate();
      expect(component.selectedDate).toBeDefined();
    });
    it('should set selectedDate to formatted undefined date when date control is empty', () => {
      searchForm.controls.date.setValue(null);
      component.setSelectedDate();
      const expectedDate = formatDate(undefined, Constants.dateFormat.yyyMMDDT);
      expect(component.selectedDate).toBe(expectedDate);
    });
  });
  describe('checkKeyBoardKey', () => {
    it('should call search() method when the event charCode matches Constants.enterKeyCode', () => {
      const event = { charCode: Constants.enterKeyCode };
      spyOn(component, 'search');
      component.checkKeyBoardKey(event);
      expect(component.search).toHaveBeenCalled();
    });
    it('should not call search() method when the event charCode does not match Constants.enterKeyCode', () => {
      const event = { charCode: 65 };
      spyOn(component, 'search');
      component.checkKeyBoardKey(event);
      expect(component.search).not.toHaveBeenCalled();
    });
  });
  describe('getIonFlagClass', () => {
    it('should return "green" when flag is 0', () => {
      const result = getIonFlagClass(0);
      expect(result).toBe(Constants.flagColor.green);
    });
    it('should return "blue" when flag is 1', () => {
      const result = getIonFlagClass(1);
      expect(result).toBe(Constants.flagColor.blue);
    });
    it('should return "orange" when flag is 2', () => {
      const result = getIonFlagClass(2);
      expect(result).toBe(Constants.flagColor.orange);
    });
    it('should return "red" when flag is 3', () => {
      const result = getIonFlagClass(3);
      expect(result).toBe(Constants.flagColor.red);
    });
    it('should return an empty string when flag is not 0, 1, 2, or 3', () => {
      const result = getIonFlagClass(4);
      expect(result).toBe(Constants.flagColor.green);
    });
  });
  describe('filterMessageByPriority', () => {
    it('should not set priority or emit filterAction event when priority is null', () => {
      const filterData = null;
      component.filterMessageByPriority('flag', filterData, null);
      expect(component.messagePriority).toBe(0);
    });

    it('should set priority and emit filterAction event when priority is not null', () => {
      const filterData = 1;
      component.filterMessageByPriority('priority', filterData, null);
      expect(component.messagePriority).toBe(1);
    });

    it('should not set ionFlag or emit filterAction event when filterData is null', () => {
      const filterData = null;
      component.filterMessageByPriority('flag', null, filterData);
      expect(component.ionFlag).toBe('ion-flag-green');
    });

    it('should set ionFlag and emit filterAction event when filterData is not null', () => {
      const filterData = 1;
      component.filterMessageByPriority('flag', null, filterData);
      expect(component.ionFlag).toBe('ion-flag-blue');
    });
  });

  it('execute search', () => {
    component.search();
    expect(component.search).toBeDefined();
  });

  it('execute presentActionSheet with flag', () => {
    component.presentActionSheet('flag');
    expect(component.presentActionSheet).toBeDefined();
  });
  describe('close', () => {
    it('should set expandSearch to false', () => {
      component.expandSearch = true;
      component.close();
      expect(component.expandSearch).toBeFalse();
    });
    it('should reset the searchForm', () => {
      component.searchForm = new UntypedFormGroup({
        searchTerm: new UntypedFormControl('example')
      });
      component.close();
      expect(component.searchForm.value).toEqual({ searchTerm: null });
    });
    it('should emit the closeSearch event', () => {
      spyOn(component.closeSearch, 'emit');
      component.close();
      expect(component.closeSearch.emit).toHaveBeenCalled();
    });
  });
  it('should return true if filteredTags is not blank, messagePriority is not 0, and flagFilter is not noFlag', () => {
    component.filteredTags = ['tag1', 'tag2']; // Set some sample values
    component.messagePriority = 1;
    component.flagFilter = Constants.flagTypes.low;
    const result = component.isFiltered();
    expect(result).toBeTrue();
  });
  it('should return false if filteredTags is blank, messagePriority is 0, and flagFilter is noFlag', () => {
    component.filteredTags = [];
    component.messagePriority = 0;
    component.flagFilter = Constants.flagTypes.noFlag;
    const result = component.isFiltered();
    expect(result).toBeFalse();
  });
  it('should call filterTags', () => {
    component.filterTags();
    expect(component.filterTags).toBeDefined();
  });
  describe('showPopup', () => {
    it('select preset options: Select All', fakeAsync(() => {
      component.selectedDateOptions = 3;
      popoverSpy.onDidDismiss.and.resolveTo({ data: 0 });
      spyOn(component, 'setDateFilterText');
      component.showPopup({});
      tick(1000);
      expect(component.showPopup).toBeDefined();
      expect(component.selectedDateOptions).toBe(0);
      expect(component.setDateFilterText).toHaveBeenCalled();
    }));
    it('select custom option', fakeAsync(() => {
      popoverSpy.onDidDismiss.and.resolveTo({ data: 2 });
      spyOn(component, 'showCalendar');
      component.showPopup({});
      tick(1000);
      expect(component.showPopup).toBeDefined();
      expect(component.selectedDateOptions).toBe(2);
      expect(component.showCalendar).toHaveBeenCalled();
    }));
  });
  it('should call showCalendar', fakeAsync(() => {
    const getMonthData = getMonthRangeData(Constants.dateFormat.mdy);
    const returnValue = { role: 'someRole', data: { from: getMonthData.previousMonthDate, to: getMonthData.todaysDate } };
    modalSpy.onDidDismiss.and.resolveTo(returnValue);
    component.showCalendar(1);
    expect(modalController.create).toHaveBeenCalledWith({
      component: DateFilterPopupComponent,
      cssClass: 'custom-calendar-class',
      componentProps: {
        dateRange: { from: component.dateRange.from, to: component.dateRange.to },
        isDateRange: true,
        monthTypes: Constants.monthTypes.default
      }
    });
    expect(component.dateFilterSelectedValue).toBe(`${returnValue.data.from} - ${returnValue.data.to}`);
    expect(component.dateRange.from).toBe('');
    expect(component.dateRange.to).toBe('');
  }));

  it('should set dateFilterSelectedValue to "All" and show a warning toast for selectedDateOptions 0', () => {
    spyOn(commonService, 'showToast');
    component.selectedDateOptions = 0;
    component.setDateFilterText();
    expect(component.dateFilterSelectedValue).toBe('DATE_FILTER.LABELS.ALL');
    expect(commonService.showToast).toHaveBeenCalledWith({
      message: 'MESSAGES.MORE_TIME_REQUIRED',
      color: 'warning'
    });
  });

  it('should set dateFilterSelectedValue to the correct date range for selectedDateOptions 1', () => {
    const getMonthData = getMonthRangeData(Constants.dateFormat.mdy);
    const expectedDateRange = `${getMonthData.previousMonthDate} - ${getMonthData.todaysDate}`;
    component.selectedDateOptions = 1;
    component.setDateFilterText();
    expect(component.dateFilterSelectedValue).toBe(expectedDateRange);
  });

  it('should set dateFilterSelectedValue to the correct date range for selectedDateOptions 2', () => {
    component.dateRange.from = '12/12/2023';
    component.dateRange.to = '12/20/2023';
    component.selectedDateOptions = 2;
    component.setDateFilterText();
    expect(component.dateFilterSelectedValue).toBe(`${component.dateRange.from} - ${component.dateRange.to}`);
  });

  it('should call filterMessageByUser', () => {
    component.filteredTags = [{ id: '1', selected: true }];
    component.filterMessageByUser(true);
    expect(component.filterMessageByUser).toBeDefined();
  });

  it('should call filter by UnreadMessages is true', () => {
    component.filterUnreadMessages(true);
    expect(component.filterUnreadMessages).toBeDefined();
  });

  it('should show a warning message when the difference in months is less than -6', () => {
    spyOn(commonService, 'showToast');
    component.showMessage('2022-01-01', '2023-06-30');

    expect(commonService.showToast).toHaveBeenCalledWith({
      message: 'MESSAGES.MORE_TIME_REQUIRED',
      color: 'warning'
    });
  });
  it('should call showTagFilter', () => {
    expect(component.showTagFilter).toBeDefined();
  });
  it('should call setDateValue with value blank', () => {
    const data = {
      pickerFormat: 'date',
      formControlName: 'date',
      value: ''
    };
    component.setDateValue(data);
    expect(component.setDateValue).toBeDefined();
  });
  it('should call setDateValue with value', () => {
    const data = {
      pickerFormat: 'date',
      formControlName: 'date',
      value: '12/12/2023'
    };
    component.setDateValue(data);
    expect(component.setDateValue).toBeDefined();
  });
  it('should call filterTags with get data', async () => {
    const response = {
      data: [
        {
          contactId: '1746067',
          id: '3'
        }
      ],
      status: 'success'
    };
    const returnValue = {
      role: 'someRole',
      data: [
        {
          selected: true,
          id: '3'
        }
      ]
    };
    component.allTags = [{ id: '1', selected: true }];
    modalSpy.onDidDismiss.and.resolveTo(returnValue);
    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.filterTags();
    expect(component.filterTags).toBeDefined();
  });
  describe('getPriority', () => {
    it('should return "alert-fill bg-default" when flag is 0', () => {
      const result = getPriority(0);
      expect(result).toBe('alert-fill bg-default');
    });
    it('should return "alert-fill danger" when Message Priority HIGH', () => {
      const result = getPriority(1);
      expect(result).toBe('alert-fill danger');
    });
    it('should return "chatbox-outline" when Message Priority NORMAL', () => {
      const result = getPriority(2);
      expect(result).toBe('chatbox-outline');
    });
    it('should return "arrowdown-fill primary" when Message Priority LOW', () => {
      const result = getPriority(3);
      expect(result).toBe('arrowdown-fill primary');
    });
  });
  it('execute presentActionSheet with priority', () => {
    component.presentActionSheet('priority');
    expect(component.presentActionSheet).toBeDefined();
  });

  it('should update searchForm and expandSearch when searchText changes', () => {
    const changes: SimpleChanges = {
      searchText: new SimpleChange('', 'test search', true)
    };
    component.ngOnChanges(changes);
    fixture.detectChanges();
    expect(component.searchForm.controls.search.value).toBe('test search');
    expect(component.expandSearch).toBeTrue();
  });

  it('should update searchForm and dateRange when dateRange changes', () => {
    const changes: SimpleChanges = {
      dateRange: {
        currentValue: { from: '2023-01-01', to: '2023-01-31' },
        previousValue: '',
        firstChange: true,
        isFirstChange: () => true
      }
    };
    component.ngOnChanges(changes);
    expect(component.searchForm.controls.date.value.date).toEqual(changes.dateRange.currentValue);
    expect(component.dateRange).toEqual(changes.dateRange.currentValue);
  });

  it('should update filteredTags when filteredTags changes', () => {
    const changes: SimpleChanges = {
      filteredTags: {
        currentValue: [1, 2, 3],
        previousValue: [],
        firstChange: true,
        isFirstChange: () => true
      }
    };
    component.ngOnChanges(changes);
    fixture.detectChanges();
    expect(component.filteredTags).toEqual([{ id: 1 }, { id: 2 }, { id: 3 }]);
  });

  it('should update mentionUsers when showMentionMessages changes', () => {
    const changes: SimpleChanges = {
      showMentionMessages: {
        currentValue: true,
        previousValue: false,
        firstChange: true,
        isFirstChange: () => true
      }
    };
    component.ngOnChanges(changes);
    expect(component.mentionUsers).toBeTrue();
  });

  it('should update chatThreadTypeOptions when chatThreadTypeSelected changes', () => {
    const changes: SimpleChanges = {
      chatThreadTypeSelected: {
        currentValue: [1, 2],
        previousValue: [],
        firstChange: true,
        isFirstChange: () => true
      }
    };
    component.ngOnChanges(changes);
    expect(
      component.chatThreadTypeOptions.every((item) => item.selected === changes.chatThreadTypeSelected.currentValue.includes(Number(item.id)))
    ).toBeTrue();
  });

  it('should set all chatThreadTypeOptions to not selected if chatThreadTypeSelected is empty', () => {
    component.chatThreadTypeSelected = [];
    component.chatThreadTypeOptions = [
      { id: 1, selected: true },
      { id: 2, selected: true }
    ];
    component.cancelChatThreadType();
    expect(component.chatThreadTypeOptions.every((option) => !option.selected)).toBeTrue();
  });

  it('should set chatThreadTypeOptions to selected based on chatThreadTypeSelected', () => {
    component.chatThreadTypeSelected = [1];
    component.chatThreadTypeOptions = [
      { id: 1, selected: false },
      { id: 2, selected: true }
    ];
    component.cancelChatThreadType();
    expect(component.chatThreadTypeOptions[0].selected).toBeTrue();
    expect(component.chatThreadTypeOptions[1].selected).toBeFalse();
  });
});
