<div class="mail-inbox-head">
  <ion-grid>
    <form [formGroup]="searchForm" id="search-form" class="common-start-padding">
      <ion-row class="ion-align-items-center">
        <div class="left-sec" *ngIf="isActiveMessagesPage">
          <ion-icon class="filter-icon" [name]="isFiltered() ? 'filter-circle' : 'filter-circle-outline'" id="filter-button"></ion-icon>
        </div>
        <ion-popover trigger="filter-button" [dismissOnSelect]="true" mode="ios">
          <ng-template>
            <ion-content class="mail-inbox-head">
              <ion-list>
                <ion-item [button]="true" id="show-tag-filter" [detail]="false" (click)="filterUnreadMessages(!showUnreadMessages)">
                  <ion-icon
                    tappable
                    class="ion-margin-end ion-icon-filter"
                    [name]="showUnreadMessages ? 'mail-unread-outline' : 'mail-outline'"
                    name=""
                  ></ion-icon>
                  <ion-label>{{ 'LABELS.FILTER_BY_UNREAD' | translate }}</ion-label>
                  <ion-icon name="checkmark-outline" color="primary" *ngIf="showUnreadMessages"></ion-icon>
                </ion-item>
                <ion-item [button]="true" id="show-tag-filter" [detail]="false" (click)="filterTags()" *ngIf="showTagFilter">
                  <ion-icon
                    tappable
                    class="ion-margin-end ion-icon-filter"
                    [name]="filteredTags.length ? 'pricetags' : 'pricetags-outline'"
                  ></ion-icon>
                  <ion-label>{{ 'LABELS.FILTER_BY_TAGS' | translate }}</ion-label>
                  <ion-icon name="checkmark-outline" color="primary" *ngIf="filteredTags.length"></ion-icon>
                </ion-item>
                <ion-item [button]="true" id="show-filter" [detail]="false" (click)="presentActionSheet(TYPE.PRIORITY)">
                  <ion-icon
                    tappable
                    class="ion-margin-end"
                    class="{{ getPriority(messagePriority) }}"
                    name="{{ getPriority(messagePriority) }}"
                  ></ion-icon>
                  <ion-label>{{ 'LABELS.FILTER_BY_PRIORITY' | translate }}</ion-label>
                  <ion-icon name="checkmark-outline" color="primary" *ngIf="messagePriority !== 0"></ion-icon>
                </ion-item>
                <ion-item [button]="true" [detail]="false" (click)="filterMessageByUser(!showMentionMessages)">
                  <ion-icon id="filter-users" class="ion-margin-end ion-user-mention margin-end" tappable name="at"></ion-icon>
                  <ion-label>{{ 'LABELS.MY_MENTIONS' | translate }}</ion-label>
                  <ion-icon name="checkmark-outline" color="primary" *ngIf="showMentionMessages"></ion-icon>
                </ion-item>
                <ion-item [button]="true" [detail]="false" (click)="presentActionSheet(TYPE.FLAG)">
                  <ion-icon id="filter" class="ion-margin-end" [ngClass]="ionFlag" tappable src="assets/icon/material-svg/filter_sort.svg"></ion-icon>
                  <ion-label>{{ 'BUTTONS.FLAG' | translate }}</ion-label>
                  <ion-icon name="checkmark-outline" color="primary" *ngIf="flagFilter !== constants.flagTypes.noFlag"></ion-icon>
                </ion-item>
                <ion-item [button]="true" id="nested-trigger" *ngIf="!isUserPatient">
                  <ion-icon id="filter" class="ion-margin-end" name="chatbox-outline"></ion-icon>
                  <ion-label>{{ 'LABELS.MESSAGE_TYPE' | translate }}</ion-label>
                  <ion-icon name="checkmark-outline" color="primary" *ngIf="chatThreadTypeSelected.length"></ion-icon>
                </ion-item>
                <ion-popover #messageTypes trigger="nested-trigger" [dismissOnSelect]="false" side="end" mode="ios" (ionPopoverWillDismiss)="cancelChatThreadType()">
                  <ng-template>
                    <ion-content>
                      <ion-list>
                        <ion-item [button]="true" [detail]="false" *ngFor="let option of chatThreadTypeOptions" (click)="option.selected = !option.selected">
                          {{ option.label }}
                          <ion-icon name="checkmark-outline" color="primary" *ngIf="option.selected"></ion-icon>
                        </ion-item>
                        <ion-item>
                          <ion-label role="button" (click)="filterWithChatThreadType();messageTypes.dismiss()">{{ 'BUTTONS.DONE' | translate }}</ion-label>
                          <ion-label role="button" (click)="cancelChatThreadType();messageTypes.dismiss()">{{ 'BUTTONS.CANCEL' | translate }}</ion-label>
                        </ion-item>
                      </ion-list>
                    </ion-content>
                  </ng-template>
                </ion-popover>
              </ion-list>
            </ion-content>
          </ng-template>
        </ion-popover>

        <ion-col size="1.5" size-md="1" *ngIf="expandSearch">
          <ion-icon id="search-close" tappable (click)="close()" src="assets/icon/material-svg/close.svg"> </ion-icon>
        </ion-col>

        <ion-col size="7.5" size-md="9" size-lg="10" class="sub-title" *ngIf="expandSearch">
          <ion-item>
            <ion-input
              type="search"
              placeholder="{{ 'PLACEHOLDERS.SEARCH' | translate }}"
              formControlName="search"
              id="search-bar-input"
              (keypress)="checkKeyBoardKey($event)"
              enterkeyhint="search"
              autocapitalize="on"
            >
            </ion-input>
          </ion-item>
        </ion-col>
        <ion-col *ngIf="!expandSearch && isDateFilter">
          <div class="date-filter" (click)="showPopup($event)">
            <ion-badge mode="md">{{ dateFilterSelectedValue }}</ion-badge>
            <ion-icon name="caret-down" class="arrow"></ion-icon>
          </div>
        </ion-col>
        <ion-col id="show-search-bar" class="ion-text-end" *ngIf="showSearch">
          <ion-icon id="search" tappable (click)="search()" src="assets/icon/material-svg/magnify.svg"> </ion-icon>
        </ion-col>
      </ion-row>
    </form>
  </ion-grid>
</div>
