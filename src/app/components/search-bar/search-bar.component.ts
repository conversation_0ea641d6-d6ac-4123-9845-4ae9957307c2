import { Component, OnInit, Output, EventEmitter, Input, OnChanges, SimpleChanges } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder } from '@angular/forms';
import { ModalController, ActionSheetController, PopoverController } from '@ionic/angular';
import { chatThreadTypes, Constants, MessagePriority } from 'src/app/constants/constants';
import { Router } from '@angular/router';
import { PageRoutes } from 'src/app/constants/page-routes';
import { CommonService } from 'src/app/services/common-service/common.service';
import { DateSelectKeyValue } from 'src/app/interfaces/common-interface';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { APIs } from 'src/app/constants/apis';
import { HttpService } from 'src/app/services/http-service/http.service';
import { PermissionService } from 'src/app/services/permission-service/permission.service';
import { Config } from 'src/app/constants/config';
import { Permissions } from 'src/app/constants/permissions';
import { DateFilterPopupComponent } from 'src/app/components/date-filter-popup/date-filter-popup.component';
import * as moment from 'moment';
import { AdvancedSelectComponent } from '../advanced-select/advanced-select.component';
import { isBlank, formatDate, getMonthRangeData, getKeyByValue } from '../../utils/utils';

export const getPriority = (priorityFilterValue: number): string => {
  let className = 'alert-fill bg-default';
  if (priorityFilterValue === MessagePriority.HIGH) {
    className = 'alert-fill danger';
  } else if (priorityFilterValue === MessagePriority.NORMAL) {
    className = 'chatbox-outline';
  } else if (priorityFilterValue === MessagePriority.LOW) {
    className = 'arrowdown-fill primary';
  }

  return className;
};

export const getIonFlagClass = (flag: number): string => {
  let className = '';
  switch (flag) {
    case 1:
      className = Constants.flagColor.blue;
      break;
    case 2:
      className = Constants.flagColor.orange;
      break;
    case 3:
      className = Constants.flagColor.red;
      break;
    default:
      className = Constants.flagColor.green;
  }
  return className;
};

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss']
})
export class SearchBarComponent implements OnInit, OnChanges {
  expandSearch = false;
  ionFlag = 'ion-flag-green';
  flagPriority = 0;
  @Input() messagePriority = 0;
  searchForm: UntypedFormGroup;
  @Input() filteredTags = [];
  @Input() flagFilter = Constants.flagTypes.noFlag;
  allTags = [];
  mentionUsers = false;
  isUserPatient = false;
  isUserPartner = false;
  @Output() readonly seachAction = new EventEmitter();
  @Output() readonly closeSearch = new EventEmitter();
  @Output() readonly filterAction = new EventEmitter();
  @Input() readonly isDateFilter = false;
  @Input() monthTypes = Constants.monthTypes.default;
  selectedDate = '';
  constants: any = Constants;
  @Input() showMentionMessages = false;
  enableTagging = false;
  getPriority = getPriority;
  isActiveMessagesPage = false;
  @Input() showUnreadMessages = false;
  TYPE = {
    PRIORITY: 'priority',
    FLAG: 'flag',
    TAG: 'tag',
    USER: 'user'
  };
  @Input() selectedDateOptions;
  @Input() searchText = '';
  @Output() readonly closeDateFilter = new EventEmitter();
  @Input() dateFilterSelectedValue = 'All';
  @Input() dateRange = {
    from: '',
    to: ''
  };
  @Input() chatThreadType = [];
  chatThreadTypeOptions: any = chatThreadTypes;
  @Input() chatThreadTypeSelected = [];
  @Input() showSearch = true;
  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public modalCtrl: ModalController,
    public actionSheetController: ActionSheetController,
    private router: Router,
    private readonly commonService: CommonService,
    public sharedService: SharedService,
    private readonly httpService: HttpService,
    private readonly permissionService: PermissionService,
    public readonly popoverController: PopoverController
  ) {

    this.searchForm = this.formBuilder.group({
      search: [null],
      date: ''
    });
    this.chatThreadTypeOptions = chatThreadTypes;
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.searchText && changes.searchText.currentValue) {
      this.searchForm.patchValue({ search: changes.searchText.currentValue });
      this.searchForm.updateValueAndValidity();
      this.expandSearch = !!changes.searchText.currentValue;
    }
    if (changes.dateRange && changes.dateRange.currentValue) {
      this.searchForm.controls.date.patchValue({ date: changes.dateRange.currentValue });
      this.dateRange = changes.dateRange.currentValue;
      this.searchForm.updateValueAndValidity();
    }
    this.setDateFilterText();
    if (changes.filteredTags) {
      if (Array.isArray(changes.filteredTags.currentValue) && changes.filteredTags.currentValue.every((tag) => typeof tag === 'number')) {
        this.filteredTags = changes.filteredTags.currentValue.map((tag) => ({ id: tag }));
      }
    }
    if (changes.showMentionMessages) {
      this.mentionUsers = changes.showMentionMessages.currentValue;
    }
    if (changes.chatThreadTypeSelected) {
      this.chatThreadTypeOptions = this.chatThreadTypeOptions.map((item) => {
        return {
          ...item,
          selected: changes.chatThreadTypeSelected.currentValue.includes(Number(item.id))
        };
      });
    }
  }
  get showTagFilter() {
    return (!this.isUserPatient && !this.isUserPartner) || this.enableTagging;
  }

  ngOnInit(): void {
    this.isUserPatient = this.sharedService.loggedUserIsPatient();
    this.isUserPartner = this.sharedService.loggedUserIsPartner();
    this.isActiveMessagesPage = this.router.url === PageRoutes.activeMessages;
    this.sharedService.configValuesUpdated.subscribe(() => {
      this.enableTagging =
        this.sharedService?.isEnableConfig(Config.enableMessageTagging) && this.permissionService?.userHasPermission(Permissions.tagMessage);
    });
    this.setDateFilterText();
  }
  search(): void {
    this.expandSearch = true;
    if (this.searchForm.value.search || this.searchForm.value.date) {
      this.seachAction.emit({
        text: this.searchForm.value.search,
        date: this.searchForm.value.date
      });
    }
  }
  close(): void {
    this.expandSearch = false;
    this.searchForm.reset();
    this.closeSearch.emit();
  }

  async presentActionSheet(type: string): Promise<void> {
    const actionSheet = await this.actionSheetController.create({
      header: this.commonService.getTranslateData(
        type === this.TYPE.PRIORITY ? 'TITLES.FILTER_MESSAGES_BY_PRIORITY' : 'TITLES.FILTER_MESSAGES_BY_FLAG'
      ),
      mode: 'ios',
      buttons: [
        {
          text: this.commonService.getTranslateData('OPTIONS.ALL_MESSAGES'),
          handler: () => {
            if (type === this.TYPE.PRIORITY) {
              this.messagePriority = 0;
            } else {
              this.flagPriority = Constants.flagTypes.noFlag;
            }
            this.filterMessageByPriority(type, this.messagePriority, this.flagPriority);
          }
        },
        {
          text: this.commonService.getTranslateData(type === this.TYPE.PRIORITY ? 'PRIORITIES.HIGH' : 'OPTIONS.HIGH'),
          handler: () => {
            if (type === this.TYPE.PRIORITY) {
              this.messagePriority = MessagePriority.HIGH;
            } else {
              this.flagPriority = Constants.flagTypes.high;
            }
            this.filterMessageByPriority(type, this.messagePriority, this.flagPriority);
          }
        },
        {
          text: this.commonService.getTranslateData(type === this.TYPE.PRIORITY ? 'PRIORITIES.NORMAL' : 'OPTIONS.MEDIUM'),
          handler: () => {
            if (type === this.TYPE.PRIORITY) {
              this.messagePriority = MessagePriority.NORMAL;
            } else {
              this.flagPriority = Constants.flagTypes.medium;
            }
            this.filterMessageByPriority(type, this.messagePriority, this.flagPriority);
          }
        },
        {
          text: this.commonService.getTranslateData(type === this.TYPE.PRIORITY ? 'PRIORITIES.LOW' : 'OPTIONS.LOW'),
          handler: () => {
            if (type === this.TYPE.PRIORITY) {
              this.messagePriority = MessagePriority.LOW;
            } else {
              this.flagPriority = Constants.flagTypes.low;
            }
            this.filterMessageByPriority(type, this.messagePriority, this.flagPriority);
          }
        },
        {
          text: this.commonService.getTranslateData('BUTTONS.CANCEL'),
          role: 'cancel',
          data: {
            action: 'cancel'
          }
        }
      ]
    });
    await actionSheet.present();
    await actionSheet.onDidDismiss();
  }

  filterMessageByPriority(type, priorityFilterData: number, filterData: any): void {
    if (type === this.TYPE.FLAG && filterData !== null) {
      this.ionFlag = `ion-flag-${getIonFlagClass(filterData)}`;
      this.flagFilter = filterData;
    }

    if (type === this.TYPE.PRIORITY && priorityFilterData !== null) {
      this.messagePriority = priorityFilterData;
    }

    if (filterData !== null || priorityFilterData !== null) {
      this.emitFilters();
    }
  }

  filterUnreadMessages(showUnreadMessages: boolean) {
    this.showUnreadMessages = showUnreadMessages;
    this.emitFilters();
  }

  setDateValue(val: DateSelectKeyValue) {
    if (isBlank(val.value)) {
      // TODO: Click on clear reset selected date value
      this.searchForm.controls[val.formControlName].reset();
    } else {
      this.searchForm.controls[val.formControlName].setValue(formatDate(val.value, Constants.dateFormat.mdy));
    }
  }

  /**
   *
   * @param event key press event
   */
  checkKeyBoardKey(event: any) {
    if (Constants.enterKeyCode === +event?.charCode) {
      this.search();
    }
  }

  setSelectedDate() {
    this.selectedDate = this.searchForm.controls.date.value
      ? formatDate(this.searchForm.controls.date.value, Constants.dateFormat.yyyMMDDT)
      : formatDate(undefined, Constants.dateFormat.yyyMMDDT);
  }

  filterTags(): void {
    const url = APIs.getAlltags;
    const params = {
      tag_type: 1,
      patientTenantId: this.sharedService.userData.tenantId
    };
    this.httpService.doGet({ endpoint: url, extraParams: params }).subscribe(async (response) => {
      this.allTags = response;
      const modal: any = await this.modalCtrl.create({
        component: AdvancedSelectComponent,
        componentProps: {
          options: this.sharedService.mergeSelectedData(this.filteredTags, this.allTags),
          expandSearch: true,
          buttons: { selectAll: true, clearAll: true, done: true },
          headerTitle: this.commonService.getTranslateData('TITLES.FILTER_THREAD_BY_TAGS')
        },
        cssClass: 'common-advanced-select',
        animated: true
      });
      modal.onDidDismiss().then(({ data }) => {
        if (data) {
          const filteredData = this.allTags.filter((tag) => {
            return data.selectedData.find((row) => row.selected && tag.id === row.id);
          });
          this.filteredTags = filteredData;
          this.emitFilters();
        }
      });
      await modal.present();
    });
  }

  isFiltered() {
    return !(
      isBlank(this.filteredTags) &&
      this.messagePriority === 0 &&
      this.flagFilter === Constants.flagTypes.noFlag &&
      !this.showMentionMessages &&
      !this.showUnreadMessages &&
      !this.chatThreadTypeSelected.length
    );
  }

  filterMessageByUser(showMentionMessages): void {
    this.showMentionMessages = showMentionMessages;
    this.mentionUsers = showMentionMessages;
    this.emitFilters();
  }

  emitFilters() {
    this.filterAction.emit({
      filter: {
        mentionUsers: this.mentionUsers,
        priorityValue: this.messagePriority,
        flagValue: this.flagFilter,
        unread: this.showUnreadMessages,
        tags: this.filteredTags.map((tag) => tag.id).join(','),
        chatThreadTypes: this.chatThreadTypeSelected
      }
    });
  }

  async showPopup(ev: any): Promise<void> {
    const popup = await this.popoverController.create({
      component: DateFilterPopupComponent,
      cssClass: 'custom-class-date-filter',
      event: ev,
      mode: 'ios',
      componentProps: { selected: this.selectedDateOptions, isDateRange: false, monthTypes: this.monthTypes }
    });
    await popup.present();
    await popup.onDidDismiss().then((value) => {
      if (value.data !== undefined) {
        if (value.data === Constants.filterSelectedOptions.custom) {
          this.selectedDateOptions = value.data;
          this.showCalendar(value.data);
        } else if (Object.values(Constants.filterSelectedOptions).includes(value.data) && this.selectedDateOptions !== value.data) {
          this.selectedDateOptions = value.data;
          this.dateRange.from = '';
          this.dateRange.to = '';
          this.setDateFilterText();
          this.closeDateFilter.emit({
            text: value.data
          });
        }
      }
    });
  }

  async showCalendar(value): Promise<void> {
    const selectedOption = [Constants.filterSelectedOptions.lastMonth, Constants.filterSelectedOptions.lastThreeMonth];
    if (selectedOption.includes(this.sharedService.selectedDateOptions)) {
      this.dateRange = JSON.parse(JSON.stringify(Constants.resetSelectedDateRange));
    }

    const myCalendar = await this.modalCtrl.create({
      component: DateFilterPopupComponent,
      cssClass: 'custom-calendar-class',
      componentProps: { dateRange: { from: this.dateRange.from, to: this.dateRange.to }, isDateRange: true, monthTypes: this.monthTypes }
    });
    await myCalendar.present();
    await myCalendar.onDidDismiss().then((returnValue: any) => {
      if (returnValue.role !== 'cancel' && returnValue.data) {
        this.dateFilterSelectedValue = `${returnValue.data.from} - ${returnValue.data.to}`;
        this.dateRange.from = returnValue.data.from;
        this.dateRange.to = returnValue.data.to;
        this.showMessage(returnValue.data.from, returnValue.data.to);
        this.closeDateFilter.emit({
          text: value,
          dates: returnValue.data
        });
      }
    });
  }

  setDateFilterText(): void {
    const key = getKeyByValue(Constants.filterSelectedOptions, this.selectedDateOptions);
    const getMonthData = getMonthRangeData(Constants.dateFormat.mdy, Constants.monthRange[key], Constants.monthRangeType[key]);
    switch (this.selectedDateOptions) {
      case Constants.filterSelectedOptions.all:
        this.dateFilterSelectedValue = this.commonService.getTranslateData(Constants.filterOptions.all);
        break;
      case Constants.filterSelectedOptions.lastMonth:
      case Constants.filterSelectedOptions.lastThreeMonth:
      case Constants.filterSelectedOptions.lastSixMonth:
        this.dateFilterSelectedValue = `${getMonthData.previousMonthDate} - ${getMonthData.todaysDate}`;
        break;
      case Constants.filterSelectedOptions.custom:
        this.dateFilterSelectedValue = `${this.dateRange.from} - ${this.dateRange.to}`;
        break;
      default:
        this.dateFilterSelectedValue = `${getMonthData.previousMonthDate} - ${getMonthData.todaysDate}`;
        break;
    }
    this.showMessage(this.dateRange?.from, this.dateRange?.to);
  }

  showMessage(fromDate, toDate): void {
    if (this.selectedDateOptions === Constants.filterSelectedOptions.all || moment(fromDate).diff(moment(toDate), 'months') < -6) {
      this.commonService.showToast({ message: this.commonService.getTranslateData('MESSAGES.MORE_TIME_REQUIRED'), color: 'warning' });
    }
  }

  filterWithChatThreadType(): void {
    this.chatThreadTypeSelected = this.chatThreadTypeOptions.filter((item) => item.selected).map((i) => Number(i.id));
    this.emitFilters();
  }

  cancelChatThreadType(): void {
    if (!this.chatThreadTypeSelected.length) {
      this.chatThreadTypeOptions = this.chatThreadTypeOptions.map((item) => ({
        ...item,
        selected: false
      }));
    } else {
      this.chatThreadTypeOptions = this.chatThreadTypeOptions.map((item) => ({
        ...item,
        selected: this.chatThreadTypeSelected.includes(Number(item.id))
      }));
    }
  }
}
