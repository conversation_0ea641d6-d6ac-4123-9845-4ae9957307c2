import { CustomDatepickerComponentModule } from './../custom-datepicker/custom-datepicker.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SearchBarComponent } from './search-bar.component';
import { TranslateModule } from '@ngx-translate/core';
import { DateFilterPopupModule } from '../date-filter-popup/date-filter-popup.module';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, ReactiveFormsModule, TranslateModule, CustomDatepickerComponentModule, DateFilterPopupModule],
  declarations: [SearchBarComponent],
  exports: [SearchBarComponent]
})
export class SearchBarModule { }
