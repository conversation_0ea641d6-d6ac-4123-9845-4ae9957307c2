.help-icon {
    @extend .seColorMarginBottom;
    ion-icon {
        @extend .setBGColorRadius;
        font-size: 23px;
    }
}
.socket-icon {
    @extend .seColorMarginBottom;
    ion-img {
        width: 23px;
        height: 23px;
        padding: 4px;
        @extend .setBGColorRadius;
    }
    transform: translateX(10px);
    .disconnect {
        background-color: var(--ion-color-socket-bg-disconnect);
    }
}

.setBGColorRadius {
    background-color: var(--ion-color-socket-bg);
    border-radius: 50%;
}
.seColorMarginBottom {
    color: white;
    margin-bottom: 7px;
}
.hp-count {
    background: var(--ion-color-count-bright-bg);
    width: 25px;
    height: 25px;
    border-radius: 50%;
    color: var(--ion-color-count-bright-color);
    position: absolute;
    left: 23px;
    top: 23px;
    font-weight: bold;
    font-size: 12px;
    z-index: 9999;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.header-image {
    height: calc(35px + env(safe-area-inset-top));
    margin-top: 16px;
    max-height: 40px;
}
