import { TranslateModule } from '@ngx-translate/core';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule, PopoverController } from '@ionic/angular';
import { HeaderComponent } from './header.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestConstants } from 'src/app/constants/test-constants';
import Elevio from 'elevio/lib/client';
import { fakeAsync, tick } from '@angular/core/testing';
import { Config } from 'src/app/constants/config';
import { Constants, UserGroup } from 'src/app/constants/constants';
import { InAppBrowser, InAppBrowserObject } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Activity } from 'src/app/constants/activity';
import { of } from 'rxjs';

describe('HeaderComponent', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;
  let sharedService: SharedService;
  let popoverController: PopoverController;
  const { popoverSpy } = TestConstants;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [HeaderComponent],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateModule.forRoot()],
      providers: [
        SharedService,
        SocketService,
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        InAppBrowser,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    sharedService = TestBed.inject(SharedService);
    Object.defineProperties(sharedService, TestConstants.sharedServiceTestProperties);
    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;

    // Initialize component properties
    component.tutorialLink = '';
    component['tlmsBrowser'] = jasmine.createSpyObj('InAppBrowserObject', ['close']);
    component['tlmsPollingInterval'] = null;

    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popoverSpy;
    });
    spyOn(popoverController, 'dismiss').and.stub();
    popoverSpy.present.and.stub();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return false and set hideMessageCount to true if pathCheck is true', () => {
    const result = component.checkPath;
    component.sharedService.hideMessageCount = true;
    expect(result).toBe(true);
  });

  it('should return true if pathCheck is false', () => {
    component['pathCheck'] = true;
    const result = component.checkPath;

    expect(result).toBe(true);
    expect(sharedService.hideMessageCount).toBeDefined();
  });

  it('should set scrollTitle to false if the title does not overflow', () => {
    component.checkTitleScroll();
    expect(component.scrollTitle).toBe(false);
  });

  it('should call socketPopover', () => {
    component.socketPopover();
    expect(component.socketPopover).toBeDefined();
  });

  it('should set showTutorialButton and tutorialLink on configValuesUpdated', () => {
    sharedService.configValuesUpdated.next(true);
    expect(component.showTutorialButton).toBe(component.showTutorial);
    expect(component.tutorialLink).toBe(sharedService.getConfigValue(TestConstants.talentlmsUrl));
  });

  it('should initialize Elevio if showElevio is true', () => {
    spyOn(component, 'initializeElevio');
    spyOnProperty(component, 'showElevio', 'get').and.returnValue(true);
    component.ngOnInit();
    expect(component.initializeElevio).toHaveBeenCalled();
  });

  it('should not initialize Elevio if showElevio is false', () => {
    spyOn(component, 'initializeElevio');
    spyOnProperty(component, 'showElevio', 'get').and.returnValue(false);
    component.ngOnInit();
    expect(component.initializeElevio).not.toHaveBeenCalled();
  });

  it('should not set up event listeners on init', () => {
    spyOn(window, 'addEventListener');
    component.ngOnInit();
    expect(window.addEventListener).not.toHaveBeenCalled();
  });
  
  it('should set scrollTitle to true if the title overflows', () => {
    const titleInputElement = component.titleInput.nativeElement;
    spyOnProperty(titleInputElement, 'offsetWidth', 'get').and.returnValue(100);
    spyOnProperty(titleInputElement, 'scrollWidth', 'get').and.returnValue(200);
    component.checkTitleScroll();
    expect(component.scrollTitle).toBe(true);
  });

  describe('showTutorial getter', () => {
    beforeEach(() => {
      localStorage.clear();
      sessionStorage.clear();
    });

    afterEach(() => {
      localStorage.clear();
      sessionStorage.clear();
    });

    it('should return true when all conditions are met with OKTA login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });
      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      expect(component.showTutorial).toBe(true);
    });

    it('should return true when all conditions are met with SSO login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });
      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'true');
      expect(component.showTutorial).toBe(true);
    });

    it('should return true when both OKTA and SSO are enabled', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });
      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      sessionStorage.setItem('isSSOLogin', 'true');
      expect(component.showTutorial).toBe(true);
    });

    it('should return false when TalentLMS tutorial is disabled', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return false;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      expect(component.showTutorial).toBe(false);
    });

    it('should return false when user is a patient', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });
      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: UserGroup.PATIENT.toString() },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      expect(component.showTutorial).toBe(false);
    });

    it('should return false when neither OKTA nor SSO login is present', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      expect(component.showTutorial).toBeFalsy();
    });

    it('should return false when OKTA login is empty but IDM is disabled', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');
      expect(component.showTutorial).toBeFalsy();
    });

    it('should return true when OKTA login is empty but SSO is enabled', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return false when IDM authentication is disabled and no SSO', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');

      expect(component.showTutorial).toBe(false);
    });

    it('should throw error when userData is undefined', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: undefined,
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');

      expect(() => component.showTutorial).toThrowError();
    });

    it('should return false when multiple conditions are not met', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return false;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: UserGroup.PATIENT.toString() },
        writable: true,
        configurable: true
      });

      expect(component.showTutorial).toBe(false);
    });

    it('should return true for partner user with OKTA login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: UserGroup.PARTNER.toString() },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return true for partner user with SSO login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: UserGroup.PARTNER.toString() },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return true for staff user (group 1) with OKTA login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return true;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '1' },
        writable: true,
        configurable: true
      });
      localStorage.setItem(Constants.storageKeys.oktaLogin, 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return true for staff user (group 1) with SSO login', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '1' },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'true');

      expect(component.showTutorial).toBe(true);
    });

    it('should return false when SSO login is not true', () => {
      spyOn(sharedService, 'isEnableConfig').and.callFake((config: string) => {
        if (config === Config.showTalentlmsTutorial) return true;
        if (config === Config.enableIDM) return false;
        return false;
      });

      Object.defineProperty(sharedService, 'userData', {
        value: { ...TestConstants.userData, group: '2' },
        writable: true,
        configurable: true
      });
      sessionStorage.setItem('isSSOLogin', 'false');

      expect(component.showTutorial).toBe(false);
    });
  });

  describe('openTLMS', () => {
    beforeEach(() => {
      component.tutorialLink = 'https://example.com/tutorial';
      spyOn(component.sharedService, 'trackActivity');
      spyOn(component as any, 'openTLMSInAppBrowser');
    });

    it('should open TLMS in app browser when platform is capacitor', () => {
      spyOn(component.sharedService.platform, 'is').and.returnValue(true);

      component.openTLMS();

      expect((component as any).openTLMSInAppBrowser).toHaveBeenCalled();
      expect(component.sharedService.trackActivity).toHaveBeenCalledWith({
        type: Activity.openHelpCenter,
        name: Activity.helpCenter,
        des: {
          data: {
            firstName: component.sharedService.userData?.firstName,
            secondName: component.sharedService.userData?.secondName,
            userName: component.sharedService.userData?.userName,
            tutorialLink: component.tutorialLink
          },
          desConstant: Activity.helpCenterDes
        }
      });
    });

    it('should open TLMS in new window when platform is not capacitor', () => {
      spyOn(component.sharedService.platform, 'is').and.returnValue(false);
      spyOn(window, 'open');

      component.openTLMS();

      expect(window.open).toHaveBeenCalledWith(component.tutorialLink, '_blank');
      expect(component.sharedService.trackActivity).toHaveBeenCalled();
    });
  });

  describe('openTLMSInAppBrowser', () => {
    let mockBrowser: jasmine.SpyObj<InAppBrowserObject>;

    beforeEach(() => {
      component.tutorialLink = 'https://example.com/tutorial';
      mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['on', 'close', 'executeScript']);
      mockBrowser.on.and.returnValue(of({} as any));
      spyOn(component['inAppBrowser'], 'create').and.returnValue(mockBrowser);
      spyOn(component as any, 'startTLMSPolling');
      spyOn(component as any, 'injectTLMSEnhancements');
      spyOn(component as any, 'stopTLMSPolling');
      spyOn(window, 'open');
    });

    it('should create in-app browser with correct options', () => {
      (component as any).openTLMSInAppBrowser();

      expect(component['inAppBrowser'].create).toHaveBeenCalledWith(
        component.tutorialLink,
        '_blank',
        jasmine.objectContaining({
          location: 'no',
          toolbar: 'yes',
          clearcache: 'no',
          zoom: 'no',
          closebuttoncolor: Constants.whiteColor,
          closebuttoncaption: 'Back to App',
          navigationbuttoncolor: Constants.whiteColor
        })
      );
    });

    it('should start polling and set up event listeners', () => {
      (component as any).openTLMSInAppBrowser();

      expect((component as any).startTLMSPolling).toHaveBeenCalled();
      expect(mockBrowser.on).toHaveBeenCalledWith('loadstop');
      expect(mockBrowser.on).toHaveBeenCalledWith('loaderror');
      expect(mockBrowser.on).toHaveBeenCalledWith('exit');
    });

    it('should handle loadstop event by injecting enhancements', () => {
      mockBrowser.on.and.callFake((event) => {
        if (event === 'loadstop') {
          return of({} as any);
        }
        return of({} as any);
      });

      (component as any).openTLMSInAppBrowser();

      expect((component as any).injectTLMSEnhancements).toHaveBeenCalledWith(mockBrowser);
    });

    it('should handle loaderror event by closing browser', () => {
      mockBrowser.on.and.callFake((event) => {
        if (event === 'loaderror') {
          return of({} as any);
        }
        return of({} as any);
      });

      (component as any).openTLMSInAppBrowser();

      expect(mockBrowser.close).toHaveBeenCalled();
    });

    it('should handle exit event by cleaning up and tracking activity', () => {
      spyOn(component.sharedService, 'trackActivity');
      mockBrowser.on.and.callFake((event) => {
        if (event === 'exit') {
          return of({} as any);
        }
        return of({} as any);
      });

      (component as any).openTLMSInAppBrowser();

      expect(component['tlmsBrowser']).toBeNull();
      expect((component as any).stopTLMSPolling).toHaveBeenCalled();
      expect(component.sharedService.trackActivity).toHaveBeenCalledWith({
        type: Activity.openHelpCenter,
        name: Activity.helpCenter,
        des: {
          data: {
            action: 'TLMS_BROWSER_CLOSED',
            tutorialLink: component.tutorialLink
          },
          desConstant: 'User closed TLMS browser'
        }
      });
    });

    it('should fallback to window.open on error', () => {
      spyOn(console, 'error');
      (component['inAppBrowser'].create as jasmine.Spy).and.throwError('Browser creation failed');

      (component as any).openTLMSInAppBrowser();

      expect(window.open).toHaveBeenCalledWith(component.tutorialLink, '_blank');
    });
  });

  describe('startTLMSPolling', () => {
    let mockBrowser: jasmine.SpyObj<InAppBrowserObject>;
    let setIntervalSpy: jasmine.Spy;

    beforeEach(() => {
      mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['executeScript']);
      component['tlmsBrowser'] = mockBrowser;
      spyOn(window, 'clearInterval');
      setIntervalSpy = spyOn(window, 'setInterval').and.returnValue(123 as any);
      spyOn(component, 'closeTLMSBrowser');
      spyOn(component as any, 'stopTLMSPolling');
    });

    it('should clear existing interval before starting new one', () => {
      component['tlmsPollingInterval'] = 456 as any;

      (component as any).startTLMSPolling();

      expect(clearInterval).toHaveBeenCalledWith(456);
    });

    it('should return early if no browser exists', () => {
      component['tlmsBrowser'] = null;

      (component as any).startTLMSPolling();

      expect(setIntervalSpy).not.toHaveBeenCalled();
    });

    it('should set up polling interval', () => {
      (component as any).startTLMSPolling();

      expect(setIntervalSpy).toHaveBeenCalledWith(jasmine.any(Function), 1000);
      expect(component['tlmsPollingInterval']).toBe(123 as any);
    });

    it('should stop polling if browser becomes null', () => {
      (component as any).startTLMSPolling();
      const intervalCallback = setIntervalSpy.calls.mostRecent().args[0];
      
      component['tlmsBrowser'] = null;
      intervalCallback();

      expect((component as any).stopTLMSPolling).toHaveBeenCalled();
    });
  });

  describe('stopTLMSPolling', () => {
    beforeEach(() => {
      spyOn(window, 'clearInterval');
    });

    it('should clear interval and reset to null', () => {
      component['tlmsPollingInterval'] = 123 as any;

      (component as any).stopTLMSPolling();

      expect(clearInterval).toHaveBeenCalledWith(123);
      expect(component['tlmsPollingInterval']).toBeNull();
    });

    it('should handle null interval gracefully', () => {
      component['tlmsPollingInterval'] = null;

      (component as any).stopTLMSPolling();

      expect(clearInterval).not.toHaveBeenCalled();
    });
  });

  describe('closeTLMSBrowser', () => {
    let mockBrowser: jasmine.SpyObj<InAppBrowserObject>;

    beforeEach(() => {
      mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['close']);
      spyOn(component as any, 'stopTLMSPolling');
    });

    it('should close browser and stop polling', () => {
      component['tlmsBrowser'] = mockBrowser;

      component.closeTLMSBrowser();

      expect(mockBrowser.close).toHaveBeenCalled();
      expect(component['tlmsBrowser']).toBeNull();
      expect((component as any).stopTLMSPolling).toHaveBeenCalled();
    });

    it('should handle browser close errors gracefully', () => {
      component['tlmsBrowser'] = mockBrowser;
      mockBrowser.close.and.throwError('Close failed');
      spyOn(console, 'warn');

      expect(() => component.closeTLMSBrowser()).not.toThrow();
      expect(console.warn).toHaveBeenCalledWith('Error closing TLMS browser:', jasmine.any(Error));
      expect(component['tlmsBrowser']).toBeNull();
      expect((component as any).stopTLMSPolling).toHaveBeenCalled();
    });

    it('should handle null browser gracefully', () => {
      component['tlmsBrowser'] = null;

      component.closeTLMSBrowser();

      expect((component as any).stopTLMSPolling).toHaveBeenCalled();
    });


  });

  describe('injectTLMSEnhancements', () => {
    let mockBrowser: jasmine.SpyObj<InAppBrowserObject>;

    beforeEach(() => {
      mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['executeScript']);
      mockBrowser.executeScript.and.returnValue(Promise.resolve());
    });

    it('should inject mobile styles and back button functionality', () => {
      (component as any).injectTLMSEnhancements(mockBrowser);

      expect(mockBrowser.executeScript).toHaveBeenCalledTimes(2);
      
      // Check first call (mobile styles)
      const firstCall = mockBrowser.executeScript.calls.argsFor(0)[0];
      expect(firstCall.code).toContain('mobileStyle');
      expect(firstCall.code).toContain('innerHTML');
      
      // Check second call (back button)
      const secondCall = mockBrowser.executeScript.calls.argsFor(1)[0];
      expect(secondCall.code).toContain('backButton');
      expect(secondCall.code).toContain('addEventListener');
    });

    it('should handle script injection errors gracefully', () => {
      mockBrowser.executeScript.and.returnValue(Promise.reject('Script error'));

      expect(() => (component as any).injectTLMSEnhancements(mockBrowser)).not.toThrow();
    });
  });

  describe('ngOnDestroy cleanup', () => {
    let mockBrowser: jasmine.SpyObj<InAppBrowserObject>;

    beforeEach(() => {
      mockBrowser = jasmine.createSpyObj('InAppBrowserObject', ['close']);
      spyOn(component as any, 'stopTLMSPolling');
    });

    it('should stop polling and close browser on destroy', () => {
      component['tlmsBrowser'] = mockBrowser;

      component.ngOnDestroy();

      expect((component as any).stopTLMSPolling).toHaveBeenCalled();
      expect(mockBrowser.close).toHaveBeenCalled();
    });

    it('should handle browser close errors on destroy', () => {
      component['tlmsBrowser'] = mockBrowser;
      mockBrowser.close.and.throwError('Close failed');
      spyOn(console, 'warn');

      expect(() => component.ngOnDestroy()).not.toThrow();
      expect(console.warn).toHaveBeenCalledWith('Error closing TLMS browser:', jasmine.any(Error));
      expect((component as any).stopTLMSPolling).toHaveBeenCalled();
    });


  });
});
