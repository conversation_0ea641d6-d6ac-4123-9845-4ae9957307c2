import { TranslateModule } from '@ngx-translate/core';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AngularDelegate, IonicModule, PopoverController } from '@ionic/angular';

import { HeaderPlainComponent } from './header-plain.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { Keepalive } from '@ng-idle/keepalive';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('HeaderPlainComponent', () => {
  let component: HeaderPlainComponent;
  let fixture: ComponentFixture<HeaderPlainComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [HeaderPlainComponent],
      imports: [
        IonicModule.forRoot(),
        RouterModule.forRoot([]),
        HttpClientModule,
        HttpClientTestingModule,
        TranslateModule.forRoot()
      ],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        PopoverController,
        AngularDelegate
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(HeaderPlainComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should reset the scroll correctly', () => {
    const scrollElement = document.createElement('div');
    scrollElement.setAttribute('id', 'plain-header');
    scrollElement.scrollLeft = 100;
    scrollElement.style.textOverflow = 'clip';
    spyOn(document, 'getElementById').and.returnValue(scrollElement);
    component.resetScroll();
    expect(scrollElement.scrollLeft).toBe(0);
    expect(scrollElement.style.textOverflow).toBe('ellipsis');
  });

  it('should emit the close event when goBack() is called', () => {
    spyOn(component.close, 'emit');
    component.goBack();
    expect(component.close.emit).toHaveBeenCalled();
  });

  it('should set textOverflow to "unset" when startScroll() is called', () => {
    const scrollElement = document.createElement('div');
    scrollElement.setAttribute('id', 'plain-header');
    scrollElement.style.textOverflow = 'ellipsis';
    spyOn(document, 'getElementById').and.returnValue(scrollElement);
    component.startScroll();
    expect(scrollElement.style.textOverflow).toBe('unset');
  });

  it('should set textOverflow to "unset" and update scrollLeft when wheel() is called', () => {
    const scrollElement = document.createElement('div');
    scrollElement.setAttribute('id', 'plain-header');
    scrollElement.style.textOverflow = 'ellipsis';
    scrollElement.scrollLeft = 0;
    const deltaY = 50;
    const wheelEvent = new WheelEvent('wheel', {
      deltaY: deltaY
    });
    spyOn(document, 'getElementById').and.returnValue(scrollElement);
    component.wheel(wheelEvent);
    expect(scrollElement.style.textOverflow).toBe('unset');
    expect(scrollElement.scrollLeft).toBe(0);
  });
});
