<ion-header class="common-plain-header" [ngClass]="customClass">
    <ion-toolbar>
        <ion-buttons slot="start" class="header-menu left" *ngIf="type===types.leftCloseButton && showButton">
            <ion-button id="back" fill="clear" (click)="goBack()" translate>BUTTONS.BACK
            </ion-button>
        </ion-buttons>
        <ion-title class="header-ion-title">
        <h1 #titleClick class="header-title"
            (touchend)="resetScroll()" (mouseleave)="resetScroll()" (mouseup)="resetScroll()" 
            (touchstart)="startScroll()" (mouseenter)="startScroll()" (mousedown)="startScroll()"
            (wheel)="wheel($event)"
            id="plain-header">{{ headerTitle | translate }}</h1>
        </ion-title>
        <ion-buttons slot="end" class="header-menu right" *ngIf="type===types.rightCloseIcon && showButton">
            <ion-button id="close" fill="clear" (click)="goBack()">
                <ion-icon name="close"></ion-icon>
            </ion-button>
        </ion-buttons>
    </ion-toolbar>
</ion-header>