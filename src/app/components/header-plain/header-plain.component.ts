import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { isBlank } from '../../utils/utils';
import { SharedService } from '../../services/shared-service/shared.service';
import { Activity } from '../../constants/activity';

@Component({
  selector: 'app-header-plain',
  templateUrl: './header-plain.component.html',
  styleUrls: ['./header-plain.component.scss']
})
export class HeaderPlainComponent implements OnInit {
  types = {
    leftCloseButton: 'leftCloseButton',
    rightCloseIcon: 'rightCloseIcon'
  };
  @Input() headerTitle: string;
  @Input() customClass = '';
  @Input() type = this.types.leftCloseButton;
  @Input() showButton = true;
  @Output() public readonly close = new EventEmitter();
  constructor(private readonly route: ActivatedRoute, private readonly sharedService: SharedService) {}
  ngOnInit(): void {
    this.sharedService.pageAccess(this.headerTitle);
  }
  wheel(event: WheelEvent): void {
    const scroll = document.getElementById('plain-header');
    scroll.style.textOverflow = 'unset';
    scroll.scrollLeft += event.deltaY;
  }
  goBack(): void {
    this.close.emit();
  }

  startScroll(): void {
    const scroll = document.getElementById('plain-header');
    scroll.style.textOverflow = 'unset';
  }
  resetScroll(): void {
    const scroll = document.getElementById('plain-header');
    scroll.scrollLeft = 0;
    scroll.style.textOverflow = 'ellipsis';
  }
}
