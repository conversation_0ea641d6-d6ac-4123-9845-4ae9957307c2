import { Component, OnInit } from '@angular/core';
import { PopoverController } from '@ionic/angular';
import * as moment from 'moment';
import { Constants } from 'src/app/constants/constants';
import { SocketService } from 'src/app/services/socket-service/socket.service';

@Component({
  selector: 'app-socket-popover',
  templateUrl: './socket-popover.component.html',
  styleUrls: ['./socket-popover.component.scss']
})
export class SocketPopoverComponent implements OnInit {
  socketStatus = false;
  lastActive: string;
  iconPath: string;
  dateFormat = Constants.dateFormat;
  constructor(private socketService: SocketService, private popoverController: PopoverController) {
    this.setSocketParams();
  }
  ngOnInit(): void {
    this.socketService.socketStatusUpdated.subscribe(() => {
      this.setSocketParams();
    });
  }
  setSocketParams(): void {
    this.socketStatus = this.socketService.status;
    const lastActive = this.socketService.timeStamp;
    if (lastActive) {
      this.lastActive = moment(lastActive).format(this.dateFormat.mmddyyhma);
    }
  }
  close(): void {
    this.popoverController.dismiss();
  }
}
