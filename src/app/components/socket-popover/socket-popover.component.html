<ion-content>
  <ion-fab vertical="top" horizontal="end" slot="fixed" (click)="close()">
    <ion-fab-button class="close-socket">
      <ion-icon name="close" [ngClass]="{ disconnect: !socketStatus }"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <ion-row>
    <ion-col class="socket-image">
      <ion-img [ngClass]="{ disconnect: !socketStatus }" src="/assets/images/connection-icon.png"
        alt="connection-image"></ion-img>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col>
      <ion-label *ngIf="socketStatus" class="title"><b>{{ 'LABELS.YOU_ARE_CONNECTED' | translate }}</b></ion-label>
      <ion-label *ngIf="!socketStatus" class="title"><b>{{ 'LABELS.YOU_ARE_DISCONNECTED' | translate }}</b></ion-label>
    </ion-col>
  </ion-row>
  <ion-row *ngIf="!socketStatus">
    <ion-col>
      <ion-label class="content">{{ 'LABELS.RETRYING' | translate }}</ion-label>
    </ion-col>
  </ion-row>
  <ion-row *ngIf="lastActive">
    <ion-col>
      <ion-label class="content">{{
        'LABELS.LAST_CONNECTED_AT' | translate
        }}</ion-label>
      <ion-label class="content">{{ lastActive }}</ion-label>
    </ion-col>
  </ion-row>
</ion-content>