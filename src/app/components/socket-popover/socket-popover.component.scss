ion-content {
  --overflow: hidden;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none;
  }
  .close-socket {
    width: 16px;
    height: 16px;
    ion-icon {
      background-color: var(--ion-color-socket-bg);
    }
    .disconnect {
      background-color: var(--ion-color-socket-bg-disconnect);
    }
  }
  ion-row {
    ion-col {
      text-align: center;
      ion-img {
        padding: 13%;
        background-color: var(--ion-color-socket-bg);
        border-radius: 50%;
        width: 150px;
        display: inline-block;
        margin: 26px;
      }
      .disconnect {
        background-color: var(--ion-color-socket-bg-disconnect);
      }
      .title {
        color: #565656;
        font-size: 18px;
        font-weight: 600;
        margin-top: 15px;
      }
      .content {
        color: #969696;
        font-size: 15px;
        font-weight: 400;
        margin-top: 15px;
      }
    }
    .socket-image {
      padding-bottom: 0px;
      margin-bottom: -16px;
    }
  }
}
