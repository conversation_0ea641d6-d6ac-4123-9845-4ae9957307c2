import { TranslateModule } from '@ngx-translate/core';
import { SocketService } from 'src/app/services/socket-service/socket.service';
import { ComponentFixture, TestBed, fakeAsync } from '@angular/core/testing';
import { IonicModule, PopoverController } from '@ionic/angular';
import { SocketPopoverComponent } from './socket-popover.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TestConstants } from 'src/app/constants/test-constants';

describe('SocketPopoverComponent', () => {
  let component: SocketPopoverComponent;
  let fixture: ComponentFixture<SocketPopoverComponent>;
  let popoverController: PopoverController;
  const popupSpy = TestConstants.popoverSpy;
  let socketService: SocketService
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [SocketPopoverComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), RouterModule.forRoot([])],
      providers: [SocketService, PopoverController],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
    popoverController = TestBed.inject(PopoverController);
    spyOn(popoverController, 'create').and.callFake(() => {
      return popupSpy;
    });
    popupSpy.present.and.stub();
    spyOn(popoverController, 'dismiss').and.stub();
    socketService = TestBed.inject(SocketService);
    fixture = TestBed.createComponent(SocketPopoverComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call PopoverController.dismiss when close is called', () => {
    component.close();
    expect(popoverController.dismiss).toHaveBeenCalled();
  });
  describe('setSocketParams', () => {

    it('should call setSocketParams', () => {
      component.setSocketParams();
      expect(component.setSocketParams).toBeDefined();
    });

    it('should set socketStatus from socketService.status', () => {
      socketService.status = true;
      component.setSocketParams();
      expect(component.socketStatus).toBe(true);
    });

    it('should not set lastActive if socketService.timeStamp is not available', () => {
      socketService.timeStamp = undefined;
      component.setSocketParams();
      expect(component.lastActive).toBeUndefined();
    });
  });

  it('should subscribe to socketStatusUpdated event on ngOnInit', () => {
    spyOn(socketService.socketStatusUpdated, 'subscribe');
    component.ngOnInit();
    expect(socketService.socketStatusUpdated.subscribe).toHaveBeenCalled();
  });
});
