<app-header></app-header>
<ion-content>
  <div class="ion-content-container barlow-regular">
    <div class="centered-content">
      <h4 class="summary-title">{{ 'TITLES.VISIT_SUMMARY' | translate }}</h4>
    </div>
    <div class="details-container">
      <div class="details-row">
        <ion-icon name="person-outline" class="details-icon"></ion-icon>
        <div class="details-content">
          <span class="details-label">{{ 'LABELS.FIRST_NAME' | translate }}</span>
          <span class="details-value">{{ patientFirstName }}</span>
        </div>
      </div>
      <div class="details-row">
        <ion-icon name="person-outline" class="details-icon"></ion-icon>
        <div class="details-content">
          <span class="details-label">{{ 'LABELS.LAST_NAME' | translate }}</span>
          <span class="details-value">{{ patientLastName }}</span>
        </div>
      </div>
      <div class="details-row">
        <ion-icon name="mail-outline" class="details-icon"></ion-icon>
        <div class="details-content">
          <span class="details-label">{{ 'LABELS.EMAIL' | translate }}</span>
          <span class="details-value email-value">{{ patientEmail }}</span>
        </div>
      </div>
      @if (patientDob) {
        <div class="details-row">
          <ion-icon name="calendar-outline" class="details-icon"></ion-icon>
          <div class="details-content">
            <span class="details-label">{{ 'LABELS.DATE_OF_BIRTH_SUMMARY' | translate }}</span>
            <span class="details-value">{{ patientDob }}</span>
          </div>
        </div>
      }
      @if (patientPhoneNumber) {
        <div class="details-row">
          <ion-icon name="call-outline" class="details-icon"></ion-icon>
          <div class="details-content">
            <span class="details-label">{{ 'LABELS.PHONE_NUMBER' | translate }}</span>
            <span class="details-value">{{ patientPhoneNumber }}</span>
          </div>
        </div>
      }
      <div class="details-row">
        <ion-icon name="calendar-outline" class="details-icon"></ion-icon>
        <div class="details-content">
          <span class="details-label">{{ 'LABELS.DATE_VISIT' | translate }}</span>
          <span class="details-value">{{ visitDate }}</span>
        </div>
      </div>
      <div class="details-row">
        <ion-icon name="time-outline" class="details-icon"></ion-icon>
        <div class="details-content">
          <span class="details-label">{{ 'LABELS.VISIT_TIME' | translate }}</span>
          <span class="details-value">{{ visitTime }} {{ timeZoneName }}</span>
        </div>
      </div>
      <div class="ion-padding">
        <ion-item lines="none" class="checkbox-item patient-reminder">
          <ion-checkbox slot="start" [(ngModel)]="patientReminder"></ion-checkbox>
          <ion-label class="checkbox-text barlow-regular">
            <span class="barlow-regular">{{ 'LABELS.NEED_REMINDER' | translate }}</span>
          </ion-label>
        </ion-item>
      </div>
      <div class="ion-padding">
        @if (!userConsent) {
          <ion-item lines="none" class="checkbox-item air-view-consent barlow-regular">
            <ion-checkbox slot="start" [(ngModel)]="patientConsent"></ion-checkbox>
            <ion-label class="checkbox-text">
              <span class="barlow-regular">{{ 'LABELS.AIR_VIEW_CONSENT' | translate }}</span>
            </ion-label>
          </ion-item>
        }
        <p class="barlow-regular">
          {{ 'LABELS.CONFIRM_TERMS_AND_CONDITIONS' | translate }}
          <a class="terms-conditions barlow-regular" (click)="documentSummary()">{{ 'LABELS.TERMS_AND_CONDITIONS' | translate }}</a
          >.
        </p>
      </div>
      <div class="button-group">
        <ion-button class="cancel-btn barlow-regular" (click)="cancelVisitSummary()">{{ 'BUTTONS.CANCEL' | translate }}</ion-button>
        <ion-button class="confirm-btn barlow-regular" (click)="submitVisit()">{{ 'BUTTONS.CONFIRM' | translate }}</ion-button>
      </div>
    </div>
    <ion-modal [keepContentsMounted]="true" #modal>
      <ng-template>
        <ion-header class="pab-modal-header">
          <ion-toolbar>
            <ion-buttons slot="start">
              <ion-button class="btn-close barlow-regular" (click)="closeModal()">{{ 'BUTTONS.GO_BACK' | translate }}</ion-button>
            </ion-buttons>
            <ion-title class="btn-close barlow-regular">{{ 'LABELS.TERMS_AND_CONDITIONS' | translate }}</ion-title>
          </ion-toolbar>
        </ion-header>
        <ion-content class="ion-padding content-container">
          <pdf-viewer
            [src]="agreementUrl"
            [render-text]="true"
            [original-size]="false"
            style="display: block; width: 100vw; height: 100vh"
            (after-load-complete)="onPdfLoad()"
            (error)="onPdfLoad()"
          ></pdf-viewer>
        </ion-content>
      </ng-template>
    </ion-modal>
  </div>
</ion-content>
