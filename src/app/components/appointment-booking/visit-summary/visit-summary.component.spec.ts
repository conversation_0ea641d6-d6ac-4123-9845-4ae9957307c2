import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, Navigation } from '@angular/router';
import { IonicModule, IonModal } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { FormsModule } from '@angular/forms';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { CommonService } from 'src/app/services/common-service/common.service';
import { environment } from 'src/environments/environment';
import { of } from 'rxjs';
import { VisitSummaryComponent } from './visit-summary.component';
import { ApiService } from '../../../services/api-service/api.service';

describe('VisitSummaryComponent', () => {
  let component: VisitSummaryComponent;
  let fixture: ComponentFixture<VisitSummaryComponent>;
  let apiService: jasmine.SpyObj<ApiService>;
  let sharedService: jasmine.SpyObj<SharedService>;
  let commonService: jasmine.SpyObj<CommonService>;
  let router: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const apiServiceSpy = jasmine.createSpyObj('ApiService', ['createVisit']);
    const sharedServiceSpy = jasmine.createSpyObj('SharedService', ['redirectPage']);
    const commonServiceSpy = jasmine.createSpyObj('CommonService', ['showToast', 'getTranslateData']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate', 'getCurrentNavigation']);
    const mockNavigation: Partial<Navigation> = {
      extras: {
        state: {
          slot: {
            date: '2024-08-01',
            from: '10:00',
            startTime: '10:00',
            endTime: '11:00'
          }
        }
      }
    };
    routerSpy.getCurrentNavigation.and.returnValue(mockNavigation as Navigation);
    await TestBed.configureTestingModule({
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), PdfViewerModule, FormsModule, VisitSummaryComponent],
      providers: [
        { provide: ApiService, useValue: apiServiceSpy },
        { provide: SharedService, useValue: sharedServiceSpy },
        { provide: CommonService, useValue: commonServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(VisitSummaryComponent);
    component = fixture.componentInstance;
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    sharedService = TestBed.inject(SharedService) as jasmine.SpyObj<SharedService>;
    commonService = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    sessionStorage.setItem(
      'patientInfo',
      JSON.stringify({ firstName: 'John', lastName: 'Doe', email: '<EMAIL>', phoneNumber: '**********', dob: '2000-01-01' })
    );
    sessionStorage.setItem('userConsent', JSON.stringify(true));
    sessionStorage.setItem('timeZoneInfo', JSON.stringify({ city: 'New York', name: 'EST' }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should redirect to appointment booking if patientInfo is blank', () => {
    sessionStorage.removeItem('patientInfo');
    component.ngOnInit();
    expect(sharedService.redirectPage).toHaveBeenCalledWith('appointment-booking');
  });

  it('should set userConsent from session storage', () => {
    component.ngOnInit();
    expect(component.userConsent).toBeTrue();
  });

  it('should correctly parse and set patient info from session storage', () => {
    component.ngOnInit();
    expect(component.patientFirstName).toBe('John');
    expect(component.patientLastName).toBe('Doe');
    expect(component.patientEmail).toBe('<EMAIL>');
    expect(component.patientPhoneNumber).toBe('**********');
    expect(component.patientDob).toBe('Jan 1, 2000');
  });

  it('should handle missing patient info gracefully', () => {
    sessionStorage.setItem('patientInfo', null);
    component.ngOnInit();
    expect(component.patientFirstName).toBeUndefined();
    expect(component.patientLastName).toBeUndefined();
    expect(component.patientEmail).toBeUndefined();
    expect(component.patientPhoneNumber).toBeUndefined();
    expect(component.patientDob).toBe('');
  });

  it('should correctly parse and set slot information from router navigation', () => {
    component.ngOnInit();
    expect(component.visitDate).toBe('Aug 1, 2024');
    expect(component.visitTime).toBe('10:00');
    expect(component.startTimeWithDate).toBe('2024-08-01T10:00Z');
    expect(component.endTimeWithDate).toBe('2024-08-01T11:00Z');
  });

  it('should correctly parse and set time zone info from session storage', () => {
    component.ngOnInit();
    expect(component.timeZoneCity).toBe('New York');
    expect(component.timeZoneName).toBe('EST');
  });

  it('should handle missing time zone info gracefully', () => {
    sessionStorage.removeItem('timeZoneInfo');
    component.ngOnInit();
    expect(component.timeZoneCity).toBe('');
    expect(component.timeZoneName).toBe('');
  });

  it('should navigate to the correct route on cancelVisitSummary()', () => {
    component.cancelVisitSummary();
    expect(router.navigate).toHaveBeenCalledWith(['appointment-booking/visit']);
  });

  it('should set isLoading to true and call apiService.createVisit with correct payload', () => {
    const payload = {
      regId: environment.regId,
      startTimeWithDate: component.startTimeWithDate,
      endTimeWithDate: component.endTimeWithDate,
      patientVisitConsent: component.patientConsent,
      patientReminder: component.patientReminder,
      locationTimezone: component.timeZoneCity
    };
    apiService.createVisit.and.returnValue(of({ status: 'SUCCESS', data: {} }));
    component.submitVisit();
    sharedService.isLoading = true;
    expect(sharedService.isLoading).toBe(true);
    expect(apiService.createVisit).toHaveBeenCalledWith(payload);
  });

  it('should navigate to visit-success on successful response', () => {
    apiService.createVisit.and.returnValue(of({ status: 'SUCCESS', data: {} }));
    component.visitDate = '2024-08-09';
    component.visitTime = '12:00';
    component.submitVisit();
    expect(sharedService.isLoading).toBe(false);
    expect(router.navigate).toHaveBeenCalledWith(['appointment-booking/visit-success'], {
      state: { visitDate: component.visitDate, visitTime: component.visitTime }
    });
  });

  it('should show error toast on failed response with error message', () => {
    const errorResponse = { status: 'FAILED', data: { error: { message: 'Error occurred' } } };
    apiService.createVisit.and.returnValue(of(errorResponse));
    component.submitVisit();
    expect(sharedService.isLoading).toBe(false);
    expect(commonService.showToast).toHaveBeenCalledWith({
      message: 'Error occurred',
      color: 'danger',
      duration: 5000,
      position: 'middle',
      cssClass: 'barlow-regular'
    });
  });

  it('should show generic error toast on failed response without error message', () => {
    const failedResponse = { status: 'FAILED', data: {} };
    apiService.createVisit.and.returnValue(of(failedResponse));
    commonService.getTranslateData.and.returnValue('Something went wrong');
    component.submitVisit();
    expect(sharedService.isLoading).toBe(false);
    expect(commonService.showToast).toHaveBeenCalledWith({
      message: 'Something went wrong',
      color: 'danger',
      duration: 5000,
      position: 'middle',
      cssClass: 'barlow-regular'
    });
  });
  it('should set isLoading to false and call modal.present if modal is truthy and onPdfLoaded is truthy', () => {
    spyOn(component.modal, 'present');
    component.onPdfLoaded = true;
    sharedService.isLoading = true;
    component.documentSummary();
    expect(sharedService.isLoading).toBeFalse();
    expect(component.modal.present).toHaveBeenCalled();
  });

  it('should keep isLoading true if modal is truthy but onPdfLoaded is falsy', () => {
    spyOn(component.modal, 'present');
    component.onPdfLoaded = false;
    sharedService.isLoading = true;
    component.documentSummary();
    expect(sharedService.isLoading).toBeTrue();
    expect(component.modal.present).toHaveBeenCalled();
  });

  it('should not call modal.present and keep isLoading true if modal is falsy', () => {
    const spy = jasmine.createSpyObj('modal', ['present']);
    component.modal = null;
    component.documentSummary();
    expect(sharedService.isLoading).toBeTrue();
    if (component.modal) {
      expect(spy.present).not.toHaveBeenCalled();
    } else {
      expect(spy.present).not.toHaveBeenCalled();
    }
  });

  it('should set isLoading to false and onPdfLoaded to true when onPdfLoad is called', () => {
    sharedService.isLoading = true;
    component.onPdfLoaded = false;
    component.onPdfLoad();
    expect(sharedService.isLoading).toBeFalse();
    expect(component.onPdfLoaded).toBeTrue();
  });

  it('should set isLoading to false and call modal.dismiss() when closeModal is called', () => {
    const modal = { dismiss: jasmine.createSpy('dismiss') } as unknown as IonModal;
    component.modal = modal;
    component.closeModal();
    expect(sharedService.isLoading).toBeFalse();
    expect(component.modal.dismiss).toHaveBeenCalled();
  });

  it('should set isLoading to false and not call modal.dismiss() when modal is undefined', () => {
    const dismissSpy = jasmine.createSpy('dismiss');
    component.modal = undefined;
    component.closeModal();
    expect(sharedService.isLoading).toBeFalse();
    expect(dismissSpy).not.toHaveBeenCalled();
  });
});
