.centered-content {
  text-align: center;
  margin-top: 16px; /* Add space at the top of the page */
}

ion-content-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.details-container {
  width: 100%;
  max-width: 800px; /* Adjust as needed for your design */
  margin: auto;
  padding: 16px;
}

.details-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  overflow: hidden; /* Hide overflow */
}

.details-icon {
  margin-right: 8px;
  font-size: 24px;
  flex-shrink: 0; 
}

.details-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.details-label {
  font-weight: bold;
  flex-shrink: 0;
  width: 120px;
}

.details-value {
  flex: 1;
  overflow: hidden;
  white-space: normal;
  word-break: break-all; 
}

.email-value {
  white-space: normal;
  word-break: break-all; /* Allow breaking for long emails */
  overflow: hidden;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
.ion-content-container {
    justify-content: flex-start;
    align-items: flex-start;
    padding: 20px;
  }

  .details-container {
    width: 100%;
    max-width: none;
    padding: 8px;
  }

  .details-row {
    flex-direction: row; /* Ensure row direction on mobile */
    align-items: center;
    overflow: hidden;
  }

  .details-content {
    flex-direction: row; /* Keep the icon and label on the same line */
    align-items: center;
    gap: 8px;
  }
  ion-header.pab-modal-header {
    margin-top: 20px;
  }
}
@media (min-width: 320px) {
  .ion-content-container {
    padding: 20px;
  }
}
.checkbox-item {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
  --ion-item-background: transparent;
  --padding-start: 0px;
  --padding-end: 0px;
  --padding-top: 0px;
  --padding-bottom: 0px;
  text-align: left;
}

ion-label.checkbox-text {
  white-space: normal;
  line-height: 1.5;
  word-break: break-word;
  display: inline-block;
  width: auto;
  flex-grow: 1; 
}
ion-checkbox {
  --size: 20px;
  margin-right: 10px;
  --checkbox-background-checked: var(--pab-ion-color-checkbox-background-summary);
}

ion-checkbox.patient-reminder {
  margin-top: 8px;
}
ion-checkbox.air-view-consent {
  margin-top: 20px;
}

ion-checkbox::part(container) {
  border-radius: 6px;
  border: 2px solid var(--pab-ion-color-checkbox-border-summary);
}

.button-group {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  align-items: center;
}

.confirm-btn {
  --background: var(--pab-ion-color-confirm-button-summary);
  --color: var(--pab-ion-color-text);
  --background-activated: transparent;
  --background-focused: transparent;
  --background-hover: transparent;
  text-transform: capitalize;
}

.cancel-btn {
  --background: var(--pab-ion-color-cancel-button-summary);
  --color: var(--pab-ion-color-text);
  --background-activated: transparent;
  --background-focused: transparent;
  --background-hover: transparent;
  text-transform: capitalize;
}

.summary-title {
  text-align: center;
}

.air-view-consent {
  margin-top: -30px;
}

.btn-close {
  color: var(--pab-ion-color-close-modal-text) !important;
}

ion-modal {
  --width: 100vw;
  --height: 100vh;
  --max-width: 100vw;
  --max-height: 100vh;
  --border-radius: 0;
  --margin: 0;
}
.terms-conditions {
  cursor: pointer;
}
