import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { IonicModule, IonModal } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { convertDateTimeToIsoFormat, formatDate, isBlank } from 'src/app/utils/utils';
import { environment } from 'src/environments/environment';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { FormsModule } from '@angular/forms';
import { Constants } from 'src/app/constants/constants';
import { CommonService } from 'src/app/services/common-service/common.service';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';
import { ApiService } from '../../../services/api-service/api.service';

@Component({
  selector: 'app-visit-summary',
  templateUrl: './visit-summary.component.html',
  styleUrls: ['./visit-summary.component.scss'],
  standalone: true,
  imports: [IonicModule, HeaderComponent, TranslateModule, FooterComponent, PdfViewerModule, FormsModule]
})
export class VisitSummaryComponent implements OnInit {
  patientFirstName: string | undefined;
  patientLastName: string | undefined;
  patientEmail: string | undefined;
  patientPhoneNumber: string | undefined;
  patientDob = '';
  visitDate = '';
  visitTime = '';
  visitTimeEnd = '';
  startTimeWithDate = '';
  endTimeWithDate = '';
  patientReminder = false;
  patientConsent = false;
  agreementUrl = environment.appointmentBookingDocument;
  userConsent = false;
  onPdfLoaded = false;
  timeZoneCity = '';
  timeZoneName = '';
  @ViewChild('modal', { static: true }) modal: IonModal | undefined;
  constructor(
    private router: Router,
    private apiService: ApiService,
    private sharedService: SharedService,
    private commonService: CommonService
  ) {}
  ngOnInit() {
    if (isBlank(sessionStorage.getItem('patientInfo'))) {
      this.sharedService.redirectPage('appointment-booking');
    }
    this.sharedService.isLoading = false;
    const patentInfoString = sessionStorage.getItem('patientInfo');
    this.userConsent = Boolean(JSON.parse(sessionStorage.getItem('userConsent')));
    const patientInfo = patentInfoString ? JSON.parse(patentInfoString) : null;
    if (patientInfo) {
      this.patientFirstName = patientInfo.firstName;
      this.patientLastName = patientInfo.lastName;
      this.patientEmail = patientInfo.email;
      this.patientPhoneNumber = patientInfo.phoneNumber;
      this.patientDob =
        patientInfo.dob && patientInfo.dob !== Constants.defaultDateOfBirth ? formatDate(patientInfo.dob, Constants.dateFormat.MMMDYYYY) : '';
    }
    const selectedSlot = this.router.getCurrentNavigation().extras.state.slot;
    if (selectedSlot) {
      this.visitDate = formatDate(selectedSlot.date, Constants.dateFormat.MMMDYYYY);
      this.visitTime = selectedSlot.from;
      this.startTimeWithDate = convertDateTimeToIsoFormat(selectedSlot.date, selectedSlot.startTime);
      this.endTimeWithDate = convertDateTimeToIsoFormat(selectedSlot.date, selectedSlot.endTime);
    }
    const timeZoneInfo = sessionStorage.getItem('timeZoneInfo');
    const timeZone = timeZoneInfo ? JSON.parse(timeZoneInfo) : null;
    if (timeZone) {
      this.timeZoneCity = timeZone.city;
      this.timeZoneName = timeZone.name;
    }
  }
  cancelVisitSummary() {
    this.router.navigate(['appointment-booking/visit']);
  }
  submitVisit() {
    this.sharedService.isLoading = true;
    const payload = {
      regId: environment.regId,
      startTimeWithDate: this.startTimeWithDate,
      endTimeWithDate: this.endTimeWithDate,
      patientVisitConsent: this.patientConsent,
      patientReminder: this.patientReminder,
      locationTimezone: this.timeZoneCity
    };
    this.apiService.createVisit(payload).subscribe({
      next: (response) => {
        this.sharedService.isLoading = false;
        if (response.status === 'SUCCESS') {
          this.router.navigate(['appointment-booking/visit-success'], {
            state: {
              visitDate: this.visitDate,
              visitTime: this.visitTime
            }
          });
        } else if (response.status === 'FAILED' && response.data?.error) {
          const errorMessage = response.data.error.message;
          this.commonService.showToast({ message: errorMessage, color: 'danger', duration: 5000, position: 'middle', cssClass: 'barlow-regular' });
        } else {
          this.commonService.showToast({
            message: this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
            color: 'danger',
            duration: 5000,
            position: 'middle',
            cssClass: 'barlow-regular'
          });
        }
      }
    });
  }
  documentSummary() {
    this.sharedService.isLoading = true;
    if (this.modal) {
      this.modal.present();
      if (this.onPdfLoaded) {
        this.sharedService.isLoading = false;
      }
    }
  }
  onPdfLoad() {
    this.sharedService.isLoading = false;
    this.onPdfLoaded = true;
  }
  closeModal() {
    this.sharedService.isLoading = false;
    if (this.modal) {
      this.modal.dismiss();
    }
  }
}
