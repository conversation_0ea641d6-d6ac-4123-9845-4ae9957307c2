import { TestBed, ComponentFixture } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';
import { RouterTestingModule } from '@angular/router/testing';
import { NgxPermissionsModule } from 'ngx-permissions';
import { NgIdleModule } from '@ng-idle/core'; // Ensure NgIdleModule is imported
import { Keepalive } from '@ng-idle/keepalive';
import { FooterComponent } from '../appointment-booking/footer/footer.component';
import { HeaderComponent } from '../appointment-booking/header/header.component';
import { CreatePatientComponent } from './create-patient/create-patient.component';
import { AppointmentBookingPage } from './appointment-booking.page';

describe('AppointmentBookingPage', () => {
  let component: AppointmentBookingPage;
  let fixture: ComponentFixture<AppointmentBookingPage>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        FormsModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        RouterTestingModule,
        NgxPermissionsModule.forRoot(),
        NgIdleModule.forRoot(), // Import NgIdleModule with .forRoot()
        IonContent,
        IonHeader,
        IonTitle,
        IonToolbar,
        CreatePatientComponent,
        HeaderComponent,
        FooterComponent
      ],
      providers: [
        TranslateService, // Ensure TranslateService is provided
        Keepalive // Provide Keepalive service
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AppointmentBookingPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create the AppointmentBookingPage component', () => {
    expect(component).toBeTruthy();
  });
});
