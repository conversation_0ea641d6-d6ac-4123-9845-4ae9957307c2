import { Routes } from '@angular/router';
import { CreateVisitComponent } from './create-visit/create-visit.component';
import { CreatePatientComponent } from './create-patient/create-patient.component';
import { VerifyOtpComponent } from './verify-otp/verify-otp.component';
import { VisitSummaryComponent } from './visit-summary/visit-summary.component';
import { VisitSuccessComponent } from './visit-success/visit-success.component';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () => import('./appointment-booking.page').then((m) => m.AppointmentBookingPage)
  },
  {
    path: 'patient',
    component: CreatePatientComponent
  },
  {
    path: 'visit',
    component: CreateVisitComponent
  },
  {
    path: 'verify-otp',
    component: VerifyOtpComponent
  },
  {
    path: 'visit-summary',
    component: VisitSummaryComponent
  },
  {
    path: 'visit-success',
    component: VisitSuccessComponent
  },
  {
    path: '**',
    loadComponent: () => import('./appointment-booking.page').then((m) => m.AppointmentBookingPage)
  }
];
