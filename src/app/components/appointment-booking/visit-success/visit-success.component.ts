import { Component, OnInit } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { isBlank, formatDate } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';
import { Router } from '@angular/router';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';
import { SharedService } from '../../../services/shared-service/shared.service';

@Component({
  selector: 'app-visit-success',
  templateUrl: './visit-success.component.html',
  styleUrls: ['./visit-success.component.scss'],
  standalone: true,
  imports: [HeaderComponent, IonicModule, FooterComponent, TranslateModule]
})
export class VisitSuccessComponent implements OnInit {
  visitDate: string;
  visitTime: string;
  timeZoneName = '';
  constructor(
    private sharedService: SharedService,
    private router: Router
  ) {}

  ngOnInit() {
    const navigation = this.router.getCurrentNavigation();
    if (navigation && navigation.extras.state) {
      const selectedSlot = navigation.extras.state;
      this.visitDate = formatDate(selectedSlot.visitDate, Constants.dateFormat.eeeddmmmDD);
      this.visitTime = selectedSlot.visitTime;
    }
    const timeZoneInfo = sessionStorage.getItem('timeZoneInfo');
    const timeZone = timeZoneInfo ? JSON.parse(timeZoneInfo) : null;
    if (timeZone) {
      this.timeZoneName = timeZone.name;
    }
    if (isBlank(sessionStorage.getItem('patientInfo')) && isBlank(sessionStorage.getItem('sessionToken'))) {
      this.sharedService.redirectPage('appointment-booking');
    } else {
      setTimeout(() => {
        sessionStorage.removeItem('patientInfo');
        sessionStorage.removeItem('sessionToken');
      }, 10000);
    }
  }
}
