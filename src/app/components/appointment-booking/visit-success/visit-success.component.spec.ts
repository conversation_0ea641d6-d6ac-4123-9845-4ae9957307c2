import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { IonicModule } from '@ionic/angular';
import { VisitSuccessComponent } from './visit-success.component';
import { SharedService } from '../../../services/shared-service/shared.service';
import { HeaderComponent } from '../header/header.component';
import { FooterComponent } from '../footer/footer.component';

describe('VisitSuccessComponent', () => {
  let component: VisitSuccessComponent;
  let fixture: ComponentFixture<VisitSuccessComponent>;
  let sharedService: jasmine.SpyObj<SharedService>;
  let router: jasmine.SpyObj<Router>;
  beforeEach(async () => {
    sharedService = jasmine.createSpyObj('SharedService', ['redirectPage']);
    router = jasmine.createSpyObj('Router', ['getCurrentNavigation']);
    await TestBed.configureTestingModule({
      imports: [IonicModule.forRoot(), TranslateModule.forRoot(), VisitSuccessComponent, HeaderComponent, FooterComponent],
      providers: [
        { provide: SharedService, useValue: sharedService },
        { provide: Router, useValue: router }
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(VisitSuccessComponent);
    component = fixture.componentInstance;
  });
  beforeEach(() => {
    sessionStorage.clear();
  });
  it('should set visitDate and visitTime from router state', () => {
    const mockNavigation = {
      extras: {
        state: {
          visitDate: new Date(),
          visitTime: '10:00 AM'
        }
      }
    };
    router.getCurrentNavigation.and.returnValue(mockNavigation as any);
    component.ngOnInit();
    expect(component.visitDate).toBeDefined(); // Check if the date is formatted properly
    expect(component.visitTime).toBe('10:00 AM');
  });

  it('should set timeZoneName from sessionStorage', () => {
    const mockTimeZone = { name: 'GMT+5:30' };
    sessionStorage.setItem('timeZoneInfo', JSON.stringify(mockTimeZone));
    component.ngOnInit();
    expect(component.timeZoneName).toBe('GMT+5:30');
  });

  it('should call redirectPage if sessionStorage is empty', () => {
    // Set items to ensure they are removed
    sessionStorage.setItem('patientInfo', 'some info');
    sessionStorage.setItem('sessionToken', 'some token');
    // Remove items to test the condition
    sessionStorage.removeItem('patientInfo');
    sessionStorage.removeItem('sessionToken');
    component.ngOnInit();
    expect(sharedService.redirectPage).toHaveBeenCalledWith('appointment-booking');
  });

  it('should remove sessionStorage items after 10 seconds', (done) => {
    const removeItemSpy = spyOn(sessionStorage, 'removeItem');
    sessionStorage.setItem('patientInfo', 'some info');
    sessionStorage.setItem('sessionToken', 'some token');
    component.ngOnInit();
    setTimeout(() => {
      expect(removeItemSpy).toHaveBeenCalledWith('patientInfo');
      expect(removeItemSpy).toHaveBeenCalledWith('sessionToken');
      done();
    }, 10000); // Ensure this matches the delay in your component
  }, 15000); // Increase the timeout for the test
});
