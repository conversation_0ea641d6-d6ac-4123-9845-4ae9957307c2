ion-split-pane {
  --side-width: 65%;
  --side-max-width: 65%;
  --border: none;
  ion-content {
    --ion-background-color: var(--pab-ion-color-background-content);
  }
}
.image-section {
  display: flex;
  flex: 0 0 65%;
}
.first-section-image {
  background-image: var(--pab-ion-background-header-image-url);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 100%;
}
@media (max-width: 767px) {
  .image-section {
    display: none;
  }
  .form-container {
    justify-content: center;
    min-height: 90vh;
  }
  ion-header.pab-modal-header {
    margin-top: 20px;
  }
}
.patient-heading {
  color: var(--pab-ion-color-patient-heading-text);
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}
.patient-heading h3 {
  margin: 10px 0 0 0; /* Adjust margin for better spacing */
}
.form-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 90vh;
}
.form-row {
  justify-content: center;
  width: 100%;
}
.text-plus{
  color:var(--pab-ion-color-text-plus);
  font-weight: bold;
}
.form-col {
  display: flex;
  justify-content: center;
}

.form-content {
  background: var(--pab-ion-color-form-content);
  width: 100%;
  padding: 10px;
  border-radius: 10px;
}
.next-button {
  --background: var(--pab-ion-color-next-button);
  --color: var(--pab-ion-color-text);
  --background-activated: transparent;
  --background-focused: transparent;
  --background-hover: transparent;
  --border-radius: 0;
  text-transform: capitalize;
}
.reset-button {
  --background: var(--pab-ion-color-reset-button);
  --color: var(--pab-ion-color-text);
  --background-activated: transparent;
  --background-focused: transparent;
  --background-hover: transparent;
  --border-radius: 0;
  text-transform: capitalize;
}
.cancel-modal a {
  color: var(--pab-ion-color-cancel-modal-a);
  text-decoration: none;
  cursor: pointer;
}
.common-flag-sec {
 height: 54px;
}
#custom-popover {
  --height: 350px;
  --width: 300px;
}

.flag-spacing {
  margin-right: 10px;
}
ion-input, ion-button {
  --border-radius: 5px;
  margin-bottom: 15px;
  background: var(--ion-item-background, #fff);
}
ion-modal {
  --width: 100vw;
  --height: 100vh;
  --max-width: 100vw;
  --max-height: 100vh;
  --border-radius: 0;
  --margin: 0;
}
input:-internal-autofill-selected {
  appearance: menulist-button;
  background-image: none !important;
  background-color: var( --pab-ion-color-background-input) !important;
  color: var(--pab-ion-color-text-input) !important;
}
.logo{
  width: 140px; 
  height: 140px;
  background-image: var(--pab-ion-logo-url)!important;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  margin: 0 auto;
}
@media (min-width: 768px) and (max-width: 1023px) {
  .image-section {
    flex: 0 0 50%; /* Adjust the width of the image section */
  }

  .form-container {
    flex: 0 0 50%; /* Adjust the width of the form container */
  }

  ion-input {
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 50px;
    font-size: 16px;
  }
}

/* Styles for iPad Pro (1024px width) */
@media (min-width: 1024px) {
  .image-section {
    flex: 0 0 65%; /* Adjust the width of the image section */
  }

  .form-container {
    flex: 0 0 35%; /* Adjust the width of the form container */
  }

  ion-input {
    --padding-start: 20px;
    --padding-end: 20px;
    --min-height: 55px;
    font-size: 18px;
  }
  
}
