import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { IonicModule, ToastController } from '@ionic/angular';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { CommonService } from 'src/app/services/common-service/common.service';
import { of, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { Constants } from 'src/app/constants/constants';
import { ApiService } from '../../../services/api-service/api.service';
import { SharedService } from '../../../services/shared-service/shared.service';
import { CreatePatientComponent } from './create-patient.component';

interface Country {
  name: string;
  code: string;
  dialCode: string;
}

describe('CreatePatientComponent', () => {
  let component: CreatePatientComponent;
  let fixture: ComponentFixture<CreatePatientComponent>;
  let apiServiceSpy: jasmine.SpyObj<ApiService>;
  let sharedServiceSpy: jasmine.SpyObj<SharedService>;
  let commonServiceSpy: jasmine.SpyObj<CommonService>;
  let router: jasmine.SpyObj<Router>;
  let modal: jasmine.SpyObj<{ dismiss: jasmine.Spy }>;

  beforeEach(async () => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['sendOTP']);
    const sharedSpy = jasmine.createSpyObj('SharedService', ['getCountryDetails', 'handleErrorWithToast', 'redirectPage']);
    const commonSpy = jasmine.createSpyObj('CommonService', ['showToast', 'getTranslateData']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const mockCountryDetails: Country = { name: 'Country', code: 'CC', dialCode: '+1' };
    sharedSpy.getCountryDetails.and.returnValue(of(mockCountryDetails));

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, IonicModule.forRoot(), RouterTestingModule, TranslateModule.forRoot(), CreatePatientComponent],
      providers: [
        { provide: ApiService, useValue: apiSpy },
        { provide: SharedService, useValue: sharedSpy },
        { provide: CommonService, useValue: commonSpy },
        ToastController
      ]
    }).compileComponents();

    apiServiceSpy = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    sharedServiceSpy = TestBed.inject(SharedService) as jasmine.SpyObj<SharedService>;
    commonServiceSpy = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    modal = jasmine.createSpyObj('modal', ['dismiss']);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CreatePatientComponent);
    component = fixture.componentInstance;
    component.countryDetails = { code: 'US', name: 'United States', dialCode: '+1' }; // Set default country details if required

    fixture.detectChanges();
  });

  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize countryDetails in ngOnInit', () => {
    const mockCountryDetails = {
      name: 'United States',
      dialCode: '+1',
      code: 'US'
    };
    sharedServiceSpy.getCountryDetails.and.returnValue(mockCountryDetails);
    component.ngOnInit();
    expect(sharedServiceSpy.getCountryDetails).toHaveBeenCalledWith(
      environment.appointmentBookingCountryCode,
      environment.appointmentBookingCountryISO
    );
    expect(component.countryDetails).toEqual(mockCountryDetails);
  });

  it('should create the patient form with correct controls and validators', () => {
    const form: FormGroup = component.createPatientForm();
    expect(form.contains('firstName')).toBeTrue();
    expect(form.contains('lastName')).toBeTrue();
    expect(form.contains('dateOfBirth')).toBeTrue();
    expect(form.contains('email')).toBeTrue();
    expect(form.contains('phoneNumber')).toBeTrue();
    const firstNameControl = form.get('firstName');
    const lastNameControl = form.get('lastName');
    const emailControl = form.get('email');
    expect(firstNameControl?.validator).toBeTruthy();
    expect(lastNameControl?.validator).toBeTruthy();
    expect(emailControl?.validator).toBeTruthy();
    const firstNameErrors = firstNameControl?.errors;
    const lastNameErrors = lastNameControl?.errors;
    const emailErrors = emailControl?.errors;
    expect(firstNameErrors?.required).toBeTrue();
    expect(firstNameErrors?.validateName).toBeUndefined();
    expect(lastNameErrors?.required).toBeTrue();
    expect(lastNameErrors?.validateName).toBeUndefined();
    expect(emailErrors?.required).toBeTrue();
    expect(emailErrors?.validateEmail).toBeUndefined();
  });

  it('should update the dateOfBirth control with the formatted date', () => {
    const patientDobControl = component.patientForm.get('dateOfBirth');
    const mockEvent = { detail: { value: '2024-08-13T00:00:00Z' } };
    component.getSelectedDate(mockEvent);
    const selectedDate = new Date(mockEvent.detail.value);
    const formattedDate = `${`0${selectedDate.getMonth() + 1}`.slice(-2)}/${`0${selectedDate.getDate()}`.slice(-2)}/${selectedDate.getFullYear()}`;
    expect(patientDobControl?.value).toBe(formattedDate);
  });

  it('should set the selectedDate property with the formatted date', () => {
    const mockEvent = { detail: { value: '2024-08-13T00:00:00Z' } };
    component.getSelectedDate(mockEvent);
    const selectedDate = new Date(mockEvent.detail.value);
    const formattedDate = `${`0${selectedDate.getMonth() + 1}`.slice(-2)}/${`0${selectedDate.getDate()}`.slice(-2)}/${selectedDate.getFullYear()}`;
    expect(component.selectedDate).toBe(formattedDate);
  });

  it('should set the selectedFormattedDate property with the formatted date in the specified format', () => {
    const mockEvent = { detail: { value: '2024-08-13T00:00:00Z' } };
    component.getSelectedDate(mockEvent);
    const selectedDate = new Date(mockEvent.detail.value);
    const formattedDate = `${selectedDate.getFullYear()}-${`0${selectedDate.getMonth() + 1}`.slice(-2)}-${`0${selectedDate.getDate()}`.slice(-2)}`;
    expect(component.selectedFormattedDate).toBe('2024-08-13');
  });

  it('should not throw an error if the dateOfBirth control is not found', () => {
    component.patientForm.removeControl('dateOfBirth');
    const mockEvent = { detail: { value: '2024-08-13T00:00:00Z' } };
    expect(() => component.getSelectedDate(mockEvent)).not.toThrow();
  });

  it("should set maxDate to today's date in YYYY-MM-DD format", () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = `0${today.getMonth() + 1}`.slice(-2);
    const day = `0${today.getDate()}`.slice(-2);
    const expectedDate = `${year}-${month}-${day}`;
    component.setMaxDate();
    expect(component.maxDate).toBe(expectedDate);
  });

  it('should set selectedFormattedDate to the same value as maxDate', () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = `0${today.getMonth() + 1}`.slice(-2);
    const day = `0${today.getDate()}`.slice(-2);
    const expectedDate = `${year}-${month}-${day}`;
    component.setMaxDate();
    expect(component.selectedFormattedDate).toBe(expectedDate);
  });

  it('should reset the patientForm', () => {
    spyOn(component.patientForm, 'reset');
    component.resetForm();
    expect(component.patientForm.reset).toHaveBeenCalled();
  });

  it('should set showOTP to false', () => {
    component.showOTP = true;
    component.resetForm();
    expect(component.showOTP).toBeFalse();
  });

  it('should return null for a valid email', () => {
    const control = { value: '<EMAIL>' };
    const result = component.validateEmail(control);
    expect(result).toBeNull();
  });

  it('should return { invalid: true } for an invalid email', () => {
    const control = { value: 'invalid-email' };
    const result = component.validateEmail(control);
    expect(result).toEqual({ invalid: true });
  });

  it('should return { invalid: true } for an empty email', () => {
    const control = { value: '' };
    const result = component.validateEmail(control);
    expect(result).toEqual({ invalid: true });
  });

  it('should return { invalid: true } for a null value', () => {
    const control = { value: null };
    const result = component.validateEmail(control);
    expect(result).toEqual({ invalid: true });
  });

  it('should return null for a valid name', () => {
    const control = { value: 'John Doe' };
    const result = component.validateName(control);

    expect(result).toBeNull();
  });

  it('should return { invalidName: true } for an invalid name with only numbers or special characters', () => {
    const control = { value: '123456' };
    const controlSpecialChars = { value: '@#$%^' };
    const result = component.validateName(control);
    const resultSpecialChars = component.validateName(controlSpecialChars);
    expect(result).toEqual({ invalidName: true });
    expect(resultSpecialChars).toEqual({ invalidName: true });
  });

  it('should return { invalidName: true } for an empty name', () => {
    const control = { value: '' };
    const result = component.validateName(control);
    expect(result).toEqual({ invalidName: true });
  });

  it('should return { invalidName: true } for a null value', () => {
    const control = { value: null };
    const result = component.validateName(control);
    expect(result).toEqual({ invalidName: true });
  });

  it('should navigate to "appointment-booking/visit" if the form is valid', () => {
    component.patientForm = new FormBuilder().group({
      name: ['John Doe', Validators.required]
    });
    component.patientForm.controls['name'].setValue('John Doe');
    const navigateSpy = spyOn(component['router'], 'navigate');
    component.goToVisitPage();
    expect(navigateSpy).toHaveBeenCalledWith(['appointment-booking/visit']);
  });

  it('should not navigate if the form is invalid', () => {
    component.patientForm = new FormBuilder().group({
      name: ['', Validators.required]
    });
    const navigateSpy = spyOn(component['router'], 'navigate');
    component.goToVisitPage();
    expect(navigateSpy).not.toHaveBeenCalled();
  });

  it('should call sendOTP with the correct payload and handle successful response', () => {
    const response = { patientUuid: '12345' };
    apiServiceSpy.sendOTP.and.returnValue(of(response));
    component.verifyOtpComponent = {
      ngOtpInput: { setValue: jasmine.createSpy('setValue') },
      otpExpired: false,
      isCountdownDone: false,
      counter: { restart: jasmine.createSpy('restart') },
      sendOtpMessage: false
    } as any;
    component.patientForm = new FormBuilder().group({
      email: ['<EMAIL>'],
      firstName: ['John'],
      lastName: ['Doe'],
      dateOfBirth: [null],
      phoneNumber: ['**********']
    });
    component.countryDetails = { dialCode: '+1', code: 'US', name: 'United States' };
    component.modal = jasmine.createSpyObj('IonModal', [
      'present',
      'ionModalDidPresent',
      'ionModalWillPresent',
      'ionModalWillDismiss',
      'ionModalDidDismiss'
    ]);
    commonServiceSpy.getTranslateData.and.returnValue('OTP sent successfully');
    component.sendOTP();
    expect(component.verifyOtpComponent.ngOtpInput.setValue).toHaveBeenCalledWith('');
    expect((component as any).sharedService.isLoading).toBeFalse();
    expect(apiServiceSpy.sendOTP).toHaveBeenCalledWith({
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      dob: Constants.defaultDateOfBirth,
      countryCode: '+1',
      countryIsoCode: 'US',
      phoneNumber: '**********'
    });
    expect(component.patientUuid).toBe('12345');
    expect(component.modal.present).toHaveBeenCalled();
    expect(component.showOtpTimer).toBeTrue();
    expect(component.verifyOtpComponent.sendOtpMessage).toBeTrue();
    expect(commonServiceSpy.showToast).toHaveBeenCalledWith({
      message: 'OTP sent successfully',
      duration: 4000,
      position: 'middle',
      color: 'success',
      cssClass: 'barlow-regular'
    });
  });

  it('should handle API error', () => {
    const error = new Error('API error');
    apiServiceSpy.sendOTP.and.returnValue(throwError(() => error));
    component.sendOTP();
    expect(sharedServiceSpy.handleErrorWithToast).toHaveBeenCalledWith(error, jasmine.any(Function));
  });

  it('should call showToast with correct parameters when apiErrorToast is called', () => {
    const message = 'An error occurred';
    component.apiErrorToast(message);
    expect(commonServiceSpy.showToast).toHaveBeenCalledWith({
      message,
      duration: 5000,
      position: 'middle',
      color: 'danger',
      cssClass: 'barlow-regular'
    });
  });

  it('should call dismiss on modal and reset component properties when modal is not null', () => {
    const modal = jasmine.createSpyObj('modal', ['dismiss']);
    component.modal = modal;
    component.closeModal();
    expect(modal.dismiss).toHaveBeenCalled();
    expect(component.enableNextButton).toBeTrue();
    expect(component.patientForm.errors).toBeNull();
    expect(component.verifyOtpComponent.sendOtpMessage).toBeFalse();
    expect(component.showOtpTimer).toBeFalse();
  });

  it('should not do anything when modal is null', () => {
    component.modal = null;
    const initialEnableNextButton = component.enableNextButton;
    const initialPatientFormErrors = component.patientForm.errors;
    const initialSendOtpMessage = component.verifyOtpComponent.sendOtpMessage;
    const initialShowOtpTimer = component.showOtpTimer;
    component.closeModal();
    expect(modal.dismiss).not.toHaveBeenCalled();
    expect(component.enableNextButton).toBe(initialEnableNextButton);
    expect(component.patientForm.errors).toBe(initialPatientFormErrors);
    expect(component.verifyOtpComponent.sendOtpMessage).toBe(initialSendOtpMessage);
    expect(component.showOtpTimer).toBe(initialShowOtpTimer);
  });

  it('should call dismiss on modal and goToVisitPage when e is truthy and modal is not null', () => {
    const modal = jasmine.createSpyObj('modal', ['dismiss']);
    component.modal = modal;
    spyOn(component, 'goToVisitPage');
    component.handleOtpVerification(true);
    expect(modal.dismiss).toHaveBeenCalled();
    expect(component.goToVisitPage).toHaveBeenCalled();
  });

  it('should not call dismiss on modal or goToVisitPage when e is falsy', () => {
    const modal = jasmine.createSpyObj('modal', ['dismiss']);
    component.modal = modal;
    spyOn(component, 'goToVisitPage');
    component.handleOtpVerification(false);
    expect(modal.dismiss).not.toHaveBeenCalled();
    expect(component.goToVisitPage).not.toHaveBeenCalled();
  });

  it('should not call dismiss on modal or goToVisitPage when modal is null', () => {
    spyOn(component, 'goToVisitPage').and.callThrough();
    component.modal = null;
    component.handleOtpVerification(true);
    expect(component.modal).toBeNull();
    expect(component.goToVisitPage).not.toHaveBeenCalled();
  });
});
