import { Component, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormBuilder, Validators, ReactiveFormsModule } from '@angular/forms';
import { IonicModule, ToastController, IonModal } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Router, RouterModule } from '@angular/router';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Country } from 'src/app/interfaces/common-interface';
import { SharedService } from '../../../services/shared-service/shared.service';
import { ApiService } from '../../../services/api-service/api.service';
import { environment } from '../../../../environments/environment';
import { VerifyOtpComponent } from '../verify-otp/verify-otp.component';
import { formatDate } from 'src/app/utils/utils';
import { Constants } from 'src/app/constants/constants';

@Component({
  selector: 'app-create-patient',
  templateUrl: './create-patient.component.html',
  styleUrls: ['./create-patient.component.scss'],
  standalone: true,
  imports: [ReactiveFormsModule, IonicModule, CommonModule, TranslateModule, RouterModule, VerifyOtpComponent]
})
export class CreatePatientComponent implements OnInit {
  patientForm: FormGroup;
  maxDate: string | null = null;
  showOTP = false;
  enteredOTP = '';
  enableNextButton = false;
  patientUuid = '';
  showOtpTimer = false;
  countryDetails: Country;
  countryId: string;
  sendOtpMessage = false;
  selectedDate = '';
  selectedFormattedDate = '';
  @ViewChild('modal', { static: true }) modal: IonModal | undefined;
  @ViewChild(VerifyOtpComponent) verifyOtpComponent!: VerifyOtpComponent;
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private toastController: ToastController,
    private apiService: ApiService,
    private sharedService: SharedService,
    private commonService: CommonService
  ) {
    this.patientForm = this.createPatientForm();
    this.setMaxDate();
  }
  ngOnInit() {
    this.countryDetails = this.sharedService.getCountryDetails(
      `${environment.appointmentBookingCountryCode}`,
      `${environment.appointmentBookingCountryISO}`
    );
  }
  createPatientForm() {
    return this.fb.group({
      firstName: ['', [Validators.required, this.validateName.bind(this)]],
      lastName: ['', [Validators.required, this.validateName.bind(this)]],
      dateOfBirth: [''],
      email: ['', [Validators.required, this.validateEmail.bind(this)]],
      phoneNumber: ['']
    });
  }
  getSelectedDate(event: any) {
    const patientDobControl = this.patientForm.get('dateOfBirth');
    if (patientDobControl) {
      const selectedDate = new Date(event.detail.value);
      const formattedDate = `${`0${selectedDate.getMonth() + 1}`.slice(-2)}/${`0${selectedDate.getDate()}`.slice(-2)}/${selectedDate.getFullYear()}`;
      patientDobControl.setValue(formattedDate);
      this.selectedDate = formattedDate;
      this.selectedFormattedDate = formatDate(this.selectedDate, Constants.dateFormat.ymd);
    }
  }
  setMaxDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = `0${today.getMonth() + 1}`.slice(-2);
    const day = `0${today.getDate()}`.slice(-2);
    this.maxDate = `${year}-${month}-${day}`;
    this.selectedFormattedDate = this.maxDate;
  }
  resetForm() {
    this.patientForm.reset();
    this.showOTP = false;
  }
  validateEmail(control: any) {
    const name = control.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return name ? (emailRegex.test(name) ? null : { invalid: true }) : { invalid: true };
  }

  validateName(control: any) {
    const name = control.value;
    const nameRegex = /[^\W\d]|\p{L}/;
    return name ? (nameRegex.test(name) ? null : { invalidName: true }) : { invalidName: true };
  }
  goToVisitPage() {
    if (this.patientForm.valid) {
      this.router.navigate(['appointment-booking/visit']);
    }
  }

  sendOTP() {
    this.verifyOtpComponent.ngOtpInput?.setValue('');
    this.verifyOtpComponent.otpExpired = false;
    this.verifyOtpComponent.isCountdownDone = false;
    this.sharedService.isLoading = true;
    if (this.verifyOtpComponent.counter) {
      this.verifyOtpComponent.counter.restart();
    }
    const payload = {
      email: this.patientForm.get('email')?.value,
      firstName: this.patientForm.get('firstName')?.value,
      lastName: this.patientForm.get('lastName')?.value,
      dob: this.patientForm.get('dateOfBirth')?.value ? this.patientForm.get('dateOfBirth')?.value : Constants.defaultDateOfBirth,
      countryCode: this.countryDetails.dialCode,
      countryIsoCode: this.countryDetails.code,
      phoneNumber: this.patientForm.get('phoneNumber')?.value
    };
    this.apiService.sendOTP(payload).subscribe({
      next: (response) => {
        this.sharedService.isLoading = false;
        if (response) {
          sessionStorage.setItem('patientInfo', JSON.stringify(payload));
          this.patientUuid = response.patientUuid;
          if (this.modal) {
            this.modal.present();
            this.showOtpTimer = true;
            this.verifyOtpComponent.sendOtpMessage = true;

          }
        }
      },
      error: (error) => {
        this.sharedService.handleErrorWithToast(error, this.apiErrorToast.bind(this));
      }
    });
    this.commonService.showToast({
      message: this.commonService.getTranslateData('MESSAGES.OTP_SEND_MESSAGE'),
      duration: 4000,
      position: 'middle',
      color: 'success',
      cssClass: 'barlow-regular'
    });
  }

  apiErrorToast(message: string) {
    this.commonService.showToast({
      message,
      duration: 5000,
      position: 'middle',
      color: 'danger',
      cssClass: 'barlow-regular'
    });
  }
  closeModal() {
    if (this.modal) {
      this.modal.dismiss();
      this.enableNextButton = true;
      this.patientForm.setErrors(null);
      this.verifyOtpComponent.sendOtpMessage = false;
      this.showOtpTimer = false;
    }
  }
  handleOtpVerification(e: any) {
    if (e) {
      if (this.modal) {
        this.modal.dismiss();
        this.goToVisitPage();
      }
    }
  }
  presentCountryPopover(ev: any): void {
    this.countryDetails = this.sharedService.selectedCountry;
    this.sharedService.presentCountryPopover(ev, (callback: Country) => {
      this.countryDetails = callback;
      this.countryId = this.countryDetails.dialCode;
    });
  }
}
