<ion-split-pane when="md" contentId="main" class="tablet-ion-split-pane">
  <ion-menu contentId="main" class="image-section">
    <ion-content>
      <div class="first-section-image"></div>
    </ion-content>
  </ion-menu>
  <div class="ion-page" id="main">
    <ion-header></ion-header>
    <ion-content>
      <ion-grid class="form-container barlow-regular">
        <ion-row class="form-row">
          <ion-col size="12" size-lg="8" size-md="8" size-sm="10" size-xs="12">
            <div class="ion-text-center patient-heading">
              <div class="logo"></div>
              <h3>
                {{ 'LABELS.CPAP_TRIAL' | translate }} <span class="text-plus">{{ 'LABELS.PLUS' | translate }}</span>
              </h3>
            </div>
            <div class="form-content">
              <div class="ion-text-center patient-heading">
                <h3>{{ 'TITLES.ENTER_PATIENT_DETAILS' | translate }}</h3>
              </div>
              <form [formGroup]="patientForm" class="barlow-regular">
                <ion-row>
                  <ion-col size="12">
                    <ion-input
                      label="{{ 'LABELS.FIRST_NAME' | translate }} *"
                      mode="md"
                      label-placement="floating"
                      fill="outline"
                      formControlName="firstName"
                      class="barlow-regular"
                      errorText="{{ 'ERROR_MESSAGES.INVALID_FIRST_NAME' | translate }}"
                    ></ion-input>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="12">
                    <ion-input
                      label="{{ 'LABELS.LAST_NAME' | translate }} *"
                      mode="md"
                      label-placement="floating"
                      fill="outline"
                      formControlName="lastName"
                      class="barlow-regular"
                      errorText="{{ 'ERROR_MESSAGES.INVALID_LAST_NAME' | translate }}"
                    ></ion-input>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="12">
                    <ion-input
                      label="{{ 'LABELS.DATE_OF_BIRTH_DOB' | translate }}"
                      mode="md"
                      label-placement="floating"
                      fill="outline"
                      formControlName="dateOfBirth"
                      id="dateOfBirth"
                      class="barlow-regular"
                      [(ngModel)]="selectedDate"
                    ></ion-input>
                    <ion-popover trigger="dateOfBirth" id="custom-popover" mode="ios">
                      <ng-template>
                        <ion-datetime
                          #dateOfBirth
                          display-format="MM/DD/YYYY"
                          [max]="maxDate"
                          [value]="selectedFormattedDate"
                          show-default-buttons="true"
                          (ionChange)="getSelectedDate($event)"
                          presentation="date"
                          cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
                          doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
                        ></ion-datetime>
                      </ng-template>
                    </ion-popover>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="12">
                    <ion-input
                      label="{{ 'LABELS.EMAIL' | translate }} *"
                      label-placement="floating"
                      mode="md"
                      fill="outline"
                      formControlName="email"
                      class="barlow-regular"
                      errorText="{{ 'ERROR_MESSAGES.INVALID_EMAIL' | translate }}"
                    ></ion-input>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="2">
                    <div class="common-flag-sec flag-spacing">
                      <div class="country-popover" id="flag-chooser" tappable (click)="presentCountryPopover($event)" id="profile-country-popover">
                        <span class="fi fi-{{ countryDetails.code }} flag-image"></span>
                        <ion-icon class="ion-caret-down" name="caret-down"></ion-icon>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="10">
                    <ion-input
                      type="tel"
                      label="{{ 'LABELS.PHONE_NUMBER' | translate }}"
                      label-placement="floating"
                      fill="outline"
                      mode="md"
                      maxlength="12"
                      minlength="7"
                      formControlName="phoneNumber"
                      inputmode="numeric"
                      pattern="[0-9]*"
                      class="barlow-regular"
                      errorText="{{ 'ERROR_MESSAGES.INVALID_PHONE_NUMBER' | translate }}"
                    ></ion-input>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="12">
                    <ion-button
                      class="next-button barlow-regular"
                      expand="full"
                      [disabled]="!patientForm.valid || (showOTP && !enableNextButton)"
                      (click)="sendOTP()"
                    >
                      {{ 'BUTTONS.NEXT' | translate }}
                    </ion-button>
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col size="12">
                    <ion-button [disabled]="!patientForm.touched" id="cancel" (click)="resetForm()" class="reset-button barlow-regular" expand="full">
                      {{ 'BUTTONS.RESET' | translate }}
                    </ion-button>
                  </ion-col>
                </ion-row>
              </form>
            </div>
          </ion-col>
        </ion-row>
      </ion-grid>
      <ion-modal [keepContentsMounted]="true" #modal>
        <ng-template>
          <ion-header class="pab-modal-header">
            <ion-toolbar>
              <ion-buttons slot="start">
                <p class="cancel-modal barlow-regular">
                  {{ 'LABELS.WRONG_DETAILS_ENTERED' | translate }}
                  <a (click)="closeModal()"> {{ 'LABELS.GO_BACK' | translate }}</a>
                </p>
              </ion-buttons>
            </ion-toolbar>
          </ion-header>
          <ion-content class="ion-padding">
            <app-verify-otp [patientUuid]="patientUuid" [showOtpTimer]="showOtpTimer" (otpVerified)="handleOtpVerification($event)"></app-verify-otp>
          </ion-content>
        </ng-template>
      </ion-modal>
    </ion-content>
  </div>
</ion-split-pane>
