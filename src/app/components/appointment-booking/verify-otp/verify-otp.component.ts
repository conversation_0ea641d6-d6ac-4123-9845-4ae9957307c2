import { CommonService } from 'src/app/services/common-service/common.service';
import { IonicModule, ToastController } from '@ionic/angular';
import { Component, OnInit, Output, EventEmitter, ViewChild, Input } from '@angular/core';
import { NgOtpInputComponent, NgOtpInputModule } from 'ng-otp-input';
import { TranslateModule } from '@ngx-translate/core';
import { CountdownComponent } from 'ngx-countdown';
import { isBlank, getTimezoneFromOffset } from 'src/app/utils/utils';
import { Router } from '@angular/router';
import { ApiService } from '../../../services/api-service/api.service';
import { SharedService } from '../../../services/shared-service/shared.service';

@Component({
  selector: 'app-verify-otp',
  templateUrl: './verify-otp.component.html',
  styleUrls: ['./verify-otp.component.scss'],
  standalone: true,
  imports: [IonicModule, NgOtpInputModule, TranslateModule, CountdownComponent]
})
export class VerifyOtpComponent implements OnInit {
  @Input() patientUuid: string | undefined;
  @Input() showOtpTimer: boolean | undefined;
  @Output() otpVerified: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild('countdown') counter: CountdownComponent | undefined;
  @ViewChild(NgOtpInputComponent, { static: false }) ngOtpInput: NgOtpInputComponent | undefined;
  enteredOTP = '';
  authToken = '';
  sendOtpMessage = true;
  isCountdownDone = false;
  enableVerifyButton = false;
  otpExpired = false;
  loader = false;
  isOtpInvalid = false;
  isVerifyOtpCalled = false;
  timeZoneInfo: string;
  constructor(
    private apiService: ApiService,
    private sharedService: SharedService,
    private toastController: ToastController,
    private router: Router,
    private commonService: CommonService
  ) {}
  ngOnInit() {
    if (isBlank(sessionStorage.getItem('patientInfo'))) {
      this.sharedService.redirectPage('appointment-booking');
    }
  }
  onOtpChange(otp: string) {
    this.enteredOTP = otp;
    this.sendOtpMessage = false;
    if (this.enteredOTP.length === 6 && !this.isVerifyOtpCalled) {
      this.enableVerifyButton = true;
      this.verifyOTP();
    } else {
      this.enableVerifyButton = false;
      this.isOtpInvalid = false;
    }
  }
  verifyOTP() {
    this.isVerifyOtpCalled = true;
    this.loader = true;
    this.enableVerifyButton = false;
    const payload = {
      uuid: this.patientUuid,
      otp: this.enteredOTP
    };
    this.apiService.verifyOTP(payload).subscribe({
      next: (response) => {
        if (response.status === 'SUCCESS') {
          sessionStorage.setItem('sessionToken', response.session);
          sessionStorage.setItem('userConsent', response.userConsent);
          this.loader = false;
          this.enableVerifyButton = true;
          this.timeZoneInfo = getTimezoneFromOffset(response.timeZone);
          sessionStorage.setItem('timeZoneInfo', this.timeZoneInfo);
          this.otpVerified.emit(true);
        } else {
          this.isVerifyOtpCalled = false;
          this.loader = false;
          this.enableVerifyButton = true;
          this.isOtpInvalid = true;
          this.commonService.showToast({
            message: this.commonService.getTranslateData('ERROR_MESSAGES.INVALID_OTP'),
            color: 'danger',
            duration: 5000,
            position: 'middle',
            cssClass: 'barlow-regular'
          });
        }
      },
      error: async (error) => {
        this.isVerifyOtpCalled = false;
        this.loader = false;
        this.enableVerifyButton = true;
        this.sharedService.handleErrorWithToast(error, this.apiErrorToast.bind(this));
        this.sharedService.redirectPage('appointment-booking');
      }
    });
  }
  onCountdownEvent(event: any) {
    if (event.action === 'done') {
      this.isCountdownDone = true;
      this.otpExpired = true;
      this.showOtpTimer = false;
      this.sendOtpMessage = false;
    }
  }
  resendOTP() {
    this.ngOtpInput?.setValue('');
    this.isCountdownDone = false;
    this.sendOtpMessage = true;
    this.otpExpired = false;
    this.isOtpInvalid = false;
    this.sharedService.isLoading = true;
    if (this.counter) {
      this.counter.restart();
    }
    this.apiService.resendOTP(this.patientUuid).subscribe({
      next: (response) => {
        this.sharedService.isLoading = false;
        if (response.status === 'SUCCESS') {
          this.commonService.showToast({
            message: this.commonService.getTranslateData('MESSAGES.OTP_RESEND_MESSAGE'),
            color: 'success',
            duration: 5000,
            position: 'middle',
            cssClass: 'barlow-regular'
          });
          this.showOtpTimer = true;
        } else {
          this.commonService.showToast({
            message: this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
            color: 'danger',
            duration: 5000,
            position: 'middle',
            cssClass: 'barlow-regular'
          });
        }
      },
      error: async (error) => {
        this.isOtpInvalid = true;
        this.sharedService.handleErrorWithToast(error, this.apiErrorToast.bind(this));
      }
    });
  }
  apiErrorToast(message: string) {
    this.commonService.showToast({
      message,
      color: 'danger',
      duration: 5000,
      position: 'middle',
      cssClass: 'barlow-regular'
    });
  }
}
