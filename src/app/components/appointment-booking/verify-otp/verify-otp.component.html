<ion-content>
  <div class="otp-content-container no-scroll-content barlow-regular">
  <div class="send-otp-message">
    @if (sendOtpMessage) {
      <p class="send-otp-text-message">{{ 'MESSAGES.OTP_SEND_MESSAGE' | translate }}</p>
    }
  </div>
  <div class="otp-verification-box">
    <h2>{{ 'LABELS.OTP_VERIFICATION' | translate }}</h2>

    @if (isOtpInvalid) {
      <p class="invalid-otp-message">{{ 'ERROR_MESSAGES.INVALID_OTP' | translate }}</p>
    }
    <div class="otp-input-container">
        <ng-otp-input class="otp-input" (onInputChange)="onOtpChange($event)" [config]="{ length: 6, allowNumbersOnly: true }"></ng-otp-input>
    </div>
    @if (showOtpTimer && !otpExpired) {
      <p class="otp-timer">
          {{ 'LABELS.OTP_EXPIRY' | translate
          }}<countdown #countdown [config]="{ leftTime: 180, format: 'mm:ss' }" (event)="onCountdownEvent($event)" />
          {{ 'LABELS.MINUTES' | translate }}
      </p>
    } @else {
      <p class="otp-timer">{{ 'LABELS.OTP_EXPIRED' | translate }}</p>
    }
    <p class="resend-otp" [hidden]="!isCountdownDone">
      {{ 'LABELS.NOT_RECEIVE_OTP' | translate }} <a (click)="resendOTP()">{{ 'LABELS.OTP_RESEND' | translate }}</a>
    </p>
    <ion-button class="verify-button barlow-regular" expand="block" [disabled]="!enableVerifyButton" (click)="verifyOTP()"
      >{{ 'BUTTONS.VERIFY' | translate }}
      @if (loader) {
        <ion-spinner name="bubbles"></ion-spinner>
      }
    </ion-button>
  </div>
</div>
</ion-content>
