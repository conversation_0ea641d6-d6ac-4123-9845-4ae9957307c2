.otp-content-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  background-color: var(--pab-ion-color-background-otp-container);
  overflow: hidden; /* Prevent scrolling issues */
  margin-top: -100px !important;
}

/* OTP Box Styling */
.otp-verification-box {
  text-align: center;
  width: 90%;
  max-width: 400px;
  padding: 20px;
  border-radius: 10px;
  background-color: var(--pab-ion-color-background-otp-verification-box);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.ng-otp-input-wrapper {
  display: flex;
  justify-content: center;
  gap: 10px;
}

::ng-deep input.otp-input {
  background-color: var(--pab-ion-color-background-otp-input) !important;
  color: var(--pab-ion-color-otp-input) !important;
}
/* Additional styles */
.otp-timer {
  margin: 10px 0;
  font-size: 1rem;
}

.resend-otp {
  margin-bottom: 20px;
}

.resend-otp a {
  color: var(--pab-ion-color-resend-otp-text);
  text-decoration: none;
}

.verify-button {
  --background: var(--pab-ion-color-verify-otp-button);
  --color: var(--pab-ion-color-text);
    text-transform: capitalize;
  --border-radius: 10px;
  --background-activated: transparent;
  --background-focused: transparent;
  --background-hover: transparent;
}
.send-otp-message{
  height: 50px;
  display: flex;
  align-items: center; 
  justify-content: center;
}
.send-otp-text-message{
  color:var(--pab-ion-color-send-otp-text-message);
}
.invalid-otp-message{
  color:var(--pab-ion-color-invalid-otp-message);
}
@media (min-width: 768px) {
  .otp-content-container {
    justify-content: center;
    align-items: center;
  }
}