import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { CommonService } from 'src/app/services/common-service/common.service';
import { ToastController } from '@ionic/angular';
import { Router } from '@angular/router';
import { NgOtpInputModule } from 'ng-otp-input';
import { CountdownModule } from 'ngx-countdown';
import { TranslateService } from '@ngx-translate/core';
import { of, throwError } from 'rxjs';
import { EventEmitter } from '@angular/core';
import { getTimezoneFromOffset } from 'src/app/utils/utils';
import { ApiService } from '../../../services/api-service/api.service';
import { SharedService } from '../../../services/shared-service/shared.service';
import { VerifyOtpComponent } from './verify-otp.component';

class MockTranslateService {
  get(key: any) {
    return of(key);
  }
}

describe('VerifyOtpComponent', () => {
  let component: VerifyOtpComponent;
  let fixture: ComponentFixture<VerifyOtpComponent>;
  let apiService: jasmine.SpyObj<ApiService>;
  let sharedService: jasmine.SpyObj<SharedService>;
  let commonService: jasmine.SpyObj<CommonService>;
  let toastController: jasmine.SpyObj<ToastController>;
  let router: jasmine.SpyObj<Router>;
  let translateService: TranslateService;

  beforeEach(async () => {
    const apiServiceSpy = jasmine.createSpyObj('ApiService', ['verifyOTP', 'resendOTP']);
    const sharedServiceSpy = jasmine.createSpyObj('SharedService', ['handleErrorWithToast', 'redirectPage', 'isLoading']);
    const commonServiceSpy = jasmine.createSpyObj('CommonService', ['showToast', 'getTranslateData']);
    const toastControllerSpy = jasmine.createSpyObj('ToastController', ['create']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [NgOtpInputModule, CountdownModule],
      providers: [
        { provide: ApiService, useValue: apiServiceSpy },
        { provide: SharedService, useValue: sharedServiceSpy },
        { provide: CommonService, useValue: commonServiceSpy },
        { provide: ToastController, useValue: toastControllerSpy },
        { provide: Router, useValue: routerSpy },
        { provide: TranslateService, useClass: MockTranslateService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(VerifyOtpComponent);
    component = fixture.componentInstance;
    apiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    sharedService = TestBed.inject(SharedService) as jasmine.SpyObj<SharedService>;
    commonService = TestBed.inject(CommonService) as jasmine.SpyObj<CommonService>;
    toastController = TestBed.inject(ToastController) as jasmine.SpyObj<ToastController>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    translateService = TestBed.inject(TranslateService);

    apiService.verifyOTP.and.returnValue(of({ success: true }));
    (window as any).getTimezoneFromOffset = jasmine.createSpy('getTimezoneFromOffset').and.returnValue('Asia/Kolkata');
    component.otpVerified = new EventEmitter<boolean>();
    spyOn(component.otpVerified, 'emit');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call redirectPage if patientInfo is not present in sessionStorage', () => {
    spyOn(sessionStorage, 'getItem').and.returnValue(null);
    component.ngOnInit();
    expect(sharedService.redirectPage).toHaveBeenCalledWith('appointment-booking');
  });

  it('should not call redirectPage if patientInfo is present in sessionStorage', () => {
    spyOn(sessionStorage, 'getItem').and.returnValue('someValue');
    component.ngOnInit();
    expect(sharedService.redirectPage).not.toHaveBeenCalled();
  });

  it('should update enteredOTP and set sendOtpMessage to false', () => {
    component.onOtpChange('123');
    expect(component.enteredOTP).toBe('123');
    expect(component.sendOtpMessage).toBe(false);
  });

  it('should not enable verify button when OTP length is less than 6', () => {
    component.onOtpChange('12345');
    expect(component.enableVerifyButton).toBe(false);
  });

  it('should enable verify button and call verifyOTP when OTP length is 6 and isVerifyOtpCalled is false', () => {
    component.isVerifyOtpCalled = false;
    spyOn(component, 'verifyOTP');
    component.onOtpChange('123456');
    expect(component.enableVerifyButton).toBe(true);
    expect(component.verifyOTP).toHaveBeenCalled();
  });

  it('should not enable verify button or call verifyOTP when OTP length is 6 but isVerifyOtpCalled is true', () => {
    component.isVerifyOtpCalled = true;
    spyOn(component, 'verifyOTP');
    component.onOtpChange('123456');
    expect(component.enableVerifyButton).toBe(false);
    expect(component.verifyOTP).not.toHaveBeenCalled();
  });

  it('should not change enableVerifyButton and isOtpInvalid when OTP length is 6 but isVerifyOtpCalled is true', () => {
    component.isVerifyOtpCalled = true;
    component.enableVerifyButton = true;
    component.isOtpInvalid = true;
    component.onOtpChange('123456');
    expect(component.enableVerifyButton).toBe(false);
    expect(component.isOtpInvalid).toBe(false);
  });

  it('should set `isCountdownDone`, `otpExpired`, `showOtpTimer`, and `sendOtpMessage` to correct values when event.action is "done"', () => {
    const event = { action: 'done' };
    component.onCountdownEvent(event);
    expect(component.isCountdownDone).toBeTrue();
    expect(component.otpExpired).toBeTrue();
    expect(component.showOtpTimer).toBeFalse();
    expect(component.sendOtpMessage).toBeFalse();
  });

  it('should not modify properties if event.action is not "done"', () => {
    component.isCountdownDone = false;
    component.otpExpired = false;
    component.showOtpTimer = true;
    component.sendOtpMessage = true;
    const event = { action: 'notDone' };
    component.onCountdownEvent(event);
    expect(component.isCountdownDone).toBeFalse();
    expect(component.otpExpired).toBeFalse();
    expect(component.showOtpTimer).toBeTrue();
    expect(component.sendOtpMessage).toBeTrue();
  });

  it('should call `commonService.showToast` with the correct parameters when `apiErrorToast` is invoked', () => {
    const message = 'Test error message';
    component.apiErrorToast(message);
    expect(commonService.showToast).toHaveBeenCalledWith({
      message,
      color: 'danger',
      duration: 5000,
      position: 'middle',
      cssClass: 'barlow-regular'
    });
  });

  it('should reset the UI state and call the resendOTP API method', () => {
    const response = { status: 'SUCCESS' };
    apiService.resendOTP.and.returnValue(of(response));
    component.counter = jasmine.createSpyObj('counter', ['restart']);
    component.ngOtpInput = jasmine.createSpyObj('ngOtpInput', ['setValue']);
    component.resendOTP();
    expect(component.ngOtpInput?.setValue).toHaveBeenCalledWith('');
    expect(component.isCountdownDone).toBeFalse();
    expect(component.sendOtpMessage).toBeTrue();
    expect(component.otpExpired).toBeFalse();
    expect(component.isOtpInvalid).toBeFalse();
    expect(sharedService.isLoading).toBeFalse();
    expect(component.counter.restart).toHaveBeenCalled();
    expect(apiService.resendOTP).toHaveBeenCalledWith(component.patientUuid);
  });

  it('should handle a failure resendOTP response correctly', () => {
    const response = { status: 'FAILURE' };
    apiService.resendOTP.and.returnValue(of(response));
    commonService.getTranslateData.and.returnValue('Something went wrong');
    component.resendOTP();
    expect(sharedService.isLoading).toBeFalse();
    expect(commonService.showToast).toHaveBeenCalledWith({
      message: 'Something went wrong',
      color: 'danger',
      duration: 5000,
      position: 'middle',
      cssClass: 'barlow-regular'
    });
    expect(component.showOtpTimer).toBeUndefined();
  });

  it('should handle an error from the resendOTP API method', fakeAsync(() => {
    const errorResponse = new Error('Network Error');
    apiService.resendOTP.and.returnValue(throwError(() => errorResponse));
    component.resendOTP();
    tick();
    expect(sharedService.isLoading).toBeTrue();
    expect(component.isOtpInvalid).toBeTrue();
    expect(sharedService.handleErrorWithToast).toHaveBeenCalledWith(errorResponse, jasmine.any(Function));
  }));

  it('should set initial values when verifyOTP is called', () => {
    apiService.verifyOTP.and.returnValue(of({ status: 'SUCCESS' }));
    component.verifyOTP();
    expect(component.isVerifyOtpCalled).toBe(true);
    expect(component.loader).toBe(false);
    expect(component.enableVerifyButton).toBe(true);
  });

  it('should handle successful OTP verification', () => {
    const mockResponse = {
      status: 'SUCCESS',
      session: 'mockSession',
      userConsent: 'mockConsent',
      timeZone: 'UTC+0'
    };
    apiService.verifyOTP.and.returnValue(of(mockResponse));
    spyOn(sessionStorage, 'setItem');
    component.verifyOTP();
    expect(sessionStorage.setItem).toHaveBeenCalledWith('sessionToken', 'mockSession');
    expect(sessionStorage.setItem).toHaveBeenCalledWith('userConsent', 'mockConsent');
    expect(sessionStorage.setItem).toHaveBeenCalledWith('timeZoneInfo', jasmine.any(String));
    expect(component.loader).toBe(false);
    expect(component.enableVerifyButton).toBe(true);
    expect(component.otpVerified.emit).toHaveBeenCalledWith(true);
  });

  it('should handle API error', () => {
    const mockError = new Error('API Error');
    apiService.verifyOTP.and.returnValue(throwError(() => mockError));
    component.verifyOTP();
    expect(component.isVerifyOtpCalled).toBe(false);
    expect(component.loader).toBe(false);
    expect(component.enableVerifyButton).toBe(true);
    expect(sharedService.handleErrorWithToast).toHaveBeenCalledWith(mockError, jasmine.any(Function));
    expect(sharedService.redirectPage).toHaveBeenCalledWith('appointment-booking');
  });
});
