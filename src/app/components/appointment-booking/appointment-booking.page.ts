import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone';
import { CreatePatientComponent } from './create-patient/create-patient.component';
import { HeaderComponent } from '../appointment-booking/header/header.component';
import { FooterComponent } from '../appointment-booking/footer/footer.component';

@Component({
  selector: 'app-appointment-booking',
  templateUrl: './appointment-booking.page.html',
  styleUrls: ['./appointment-booking.page.scss'],
  standalone: true,
  imports: [IonContent, IonHeader, IonTitle, IonToolbar, CommonModule, FormsModule, CreatePatientComponent, HeaderComponent, FooterComponent]
})
export class AppointmentBookingPage implements OnInit {

  constructor() { }

  ngOnInit() {
  }

}
