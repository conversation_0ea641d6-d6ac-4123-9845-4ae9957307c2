ion-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.centered-content {
  text-align: center;
  width: 100%;
  max-width: 800px; /* Limit the maximum width */
  margin: 0 auto; /* Center the content on larger screens */
}

ion-input {
  margin-top: 20px;
  width: 100%;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

ion-datetime {
  margin-top: 20px;
  width: 100%;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.card-row {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin: 20px 0; /* Add some space around the card row */
}

ion-card {
  margin: 10px;
  flex: 1 1 calc(33.33% - 20px); /* Three cards per row with space around */
  box-sizing: border-box;
}

#custom-popover {
  --width: 300px;
}

.selected-slot {
  border: 2px solid var(--pab-ion-color-selected-slot);
}

.display-day {
  font-size: 20px;
}

.display-slot-available {
  font-size: 14px;
  font-weight: bold
}

.slot-available {
  color: var(--pab-ion-color-slot-available-text);
  font-weight: bold
}
.check-slot {
  color: var(--pab-ion-color-no-slots-text);
  font-weight: bold
}
.no-slots{
  color:var(--pab-ion-color-no-slots-text);
  font-weight: bold;
}

.button-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start; /* Align buttons to the left */
  gap: 10px; /* Space between buttons */
  margin: 20px 0; /* Add some space around the button row */
}

.button-container {
  flex: 0 1 calc(25% - 10px);
  box-sizing: border-box;
}

.slot-available-button,
.no-slot-button {
  width: 100%;
}

.slot-available-button {
  --background: var(--pab-ion-color-slot-available-button)!important;
}

.no-slot-button {
  --background: var(--pab-ion-color-no-slots-button)!important;
}

.timezone-message {
  text-align: center;
  width: 100%;
}
.timezone-content{
  font-size:16px;
}

.select-card ion-card {
  cursor: pointer;
}
/* Media query for smaller screens */
@media only screen and (max-width: 300px) {
  ion-input,
  ion-datetime {
    width: 90%;
    max-width: none;
  }

  ion-card {
    flex: 1 1 calc(33.33% - 10px); 
  }
}

@media (max-width: 480px) {
  .button-container {
    flex: 0 1 calc(33.33% - 10px);
  }
}
