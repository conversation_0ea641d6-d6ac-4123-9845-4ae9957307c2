<app-header></app-header>
<ion-content>
  <div class="centered-content barlow-regular">
    <h4>{{ 'TITLES.CREATE_VISIT' | translate }}</h4>
    <form [formGroup]="visitForm">
      <div>
        <ion-input
          label="{{ 'LABELS.VISIT_DATE' | translate }}"
          mode="md"
          label-placement="floating"
          fill="outline"
          formControlName="visitDate"
          class="barlow-regular"
          id="visitDate"
        ></ion-input>
        <ion-popover trigger="visitDate" id="custom-popover" mode="ios">
          <ng-template>
            <ion-datetime
              #visitDate
              display-format="MMM D,YYYY"
              (ionChange)="getSelectedDate($event)"
              presentation="date"
              [min]="minDate"
              [max]="maxYear"
              [value]="selectedDate"
              cancelText="{{ 'BUTTONS.CANCEL_UPPER' | translate }}"
              doneText="{{ 'BUTTONS.DONE_UPPER' | translate }}"
              show-default-buttons="true"
            ></ion-datetime>
          </ng-template>
        </ion-popover>
      </div>
    </form>
    <div class="card-row select-card">
      <ion-card *ngFor="let date of displayedDates; let i = index" [ngClass]="{ 'selected-slot': date.active }" (click)="selectDate(date, i)">
        <ion-card-header>
          <ion-card-title class="display-day barlow-regular">{{ date.day }}</ion-card-title>
          <ion-card-subtitle class="barlow-regular">{{ date.date | date: 'MMM dd' }}</ion-card-subtitle>
        </ion-card-header>
        <!-- TODO: Show availability in later stage. -->
        <!-- <ion-card-content class="display-slot-available">
          <span *ngIf="date.slots && date.active" class="slot-available">
            {{ 'LABELS.SLOT_AVAILABLE' | translate }}
          </span>
          <span *ngIf="!date.slots && date.active" class="no-slots">
            {{ 'LABELS.NO_SLOTS' | translate }}
          </span>
          <span *ngIf="!date.active" class="check-slot">
            {{ 'LABELS.CHECK_SLOT' | translate }}
          </span>
        </ion-card-content> -->
      </ion-card>
    </div>
    @if (availability && availability.length > 0) {
      <div class="button-row">
        <ng-container *ngFor="let slot of availability; let i = index">
          <div class="button-container barlow-regular">
            <ion-button
        class="barlow-regular"
        [ngClass]="slot.isAvailable ? 'slot-available-button' : 'no-slot-button'"
        (click)="slot.isAvailable ? checkAvailabiltyTime(slot, i) : bookedSlotMessageToast()"
      >{{ slot.from }}
      </ion-button>
      </div>
      </ng-container>
      </div>
    }
    @if (noSlots) {
     <div class="timezone-message barlow-regular">
       <h6 class="no-slots">{{ 'LABELS.SLOT_UNAVAILABLE' | translate }}</h6>
     </div>
    }
    <div class="timezone-message barlow-regular">
      <h6 class="timezone-content">{{ 'LABELS.DISPLAY_TIMEZONE' | translate: { timeZoneName: timeZoneName } }}</h6>
    </div>
  </div>
</ion-content>
<app-footer></app-footer>
