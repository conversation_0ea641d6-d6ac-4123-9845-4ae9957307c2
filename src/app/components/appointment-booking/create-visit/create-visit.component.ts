import { TranslateModule } from '@ngx-translate/core';
import { IonicModule, IonPopover, IonModal, ToastController } from '@ionic/angular';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule, Location } from '@angular/common';
import { convertDateTimeToIsoFormat, formatDate, formatTime, isBlank } from 'src/app/utils/utils';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/services/common-service/common.service';
import { Constants } from 'src/app/constants/constants';
import { environment } from 'src/environments/environment';
import { ApiService } from '../../../services/api-service/api.service';
import { SharedService } from '../../../services/shared-service/shared.service';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';

@Component({
  selector: 'app-pab-create-visit',
  templateUrl: './create-visit.component.html',
  styleUrls: ['./create-visit.component.scss'],
  standalone: true,
  imports: [HeaderComponent, IonicModule, FooterComponent, CommonModule, FormsModule, ReactiveFormsModule, TranslateModule]
})
export class CreateVisitComponent implements OnInit {
  availability: any;
  visitForm: FormGroup;
  minDate: string = '';
  todayDate: Date | undefined;
  selectedDate: any;
  displayedDates: any;
  startDate: string = '';
  startTime: string = '';
  endTime: string = '';
  selectedCard = 0;
  startDay: any;
  maxYear: string;
  noSlots = false;
  timeZoneName = '';
  timeZoneCity = '';

  @ViewChild('datePopover', { static: false }) datePopover: IonPopover | undefined;
  @ViewChild('modal', { static: true }) modal: IonModal | undefined;

  constructor(
    private location: Location,
    private formBuilder: FormBuilder,
    private sharedService: SharedService,
    private router: Router,
    private toastController: ToastController,
    private apiService: ApiService,
    private commonService: CommonService
  ) {
    this.visitForm = this.createVisitForm();
  }

  ngOnInit() {
    if (isBlank(sessionStorage.getItem('patientInfo'))) {
      this.sharedService.redirectPage('appointment-booking');
    }
    const today = new Date();
    today.setDate(today.getDate() + 1);
    this.startDay = today;
    this.minDate = today.toISOString().split('T')[0];
    const formattedToday = formatDate(today, Constants.dateFormat.MMMDYYYY);
    this.visitForm.get('visitDate')?.setValue(formattedToday);
    this.displayedDates = this.calculateNextDates(today);
    this.getSlots(today);
    this.maxYear = this.sharedService.setCalendarPickerMaxYear();
    const timeZoneInfo = sessionStorage.getItem('timeZoneInfo');
    const timeZone = timeZoneInfo ? JSON.parse(timeZoneInfo) : null;
    if (timeZone) {
      this.timeZoneCity = timeZone.city;
      this.timeZoneName = timeZone.name;
    }
  }

  createVisitForm() {
    return this.formBuilder.group({
      visitDate: ['']
    });
  }
  getSelectedDate(event: any = '') {
    const visitDateControl = this.visitForm.get('visitDate');
    if (visitDateControl) {
      const selectedDate = event !== '' ? new Date(event.detail.value) : new Date(this.startDay);
      const formattedDate = formatDate(selectedDate, Constants.dateFormat.MMMDYYYY);
      visitDateControl.setValue(formattedDate);
      this.displayedDates = this.calculateNextDates(selectedDate);
      this.getSlots(selectedDate);
    }
  }
  calculateNextDates(startDate: Date): { day: string; date: Date; active: boolean }[] {
    const dates = [];
    for (let i = 0; i < 3; i++) {
      const nextDate = new Date(startDate);
      nextDate.setDate(startDate.getDate() + i);
      dates.push({
        day: nextDate.toLocaleDateString('en-US', { weekday: 'short' }).toUpperCase(),
        date: nextDate,
        active: i === 0,
        slots: null
      });
    }
    return dates;
  }
  selectDate(date: any, i) {
    this.selectedCard = i;
    this.displayedDates.forEach((d) => ((d.active = false), (d.slots = null)));
    this.displayedDates[this.selectedCard].active = true;
    const visitDateControl = this.visitForm.get('visitDate');
    if (visitDateControl) {
      const selectedDate = new Date(date.date);
      const formattedDate = formatDate(selectedDate, Constants.dateFormat.MMMDYYYY);
      visitDateControl.setValue(formattedDate);
      this.getSlots(selectedDate);
    }
  }

  getSlots(dateInput) {
    this.sharedService.isLoading = true;
    const date = formatDate(dateInput, Constants.dateFormat.YYYYMMDD);
    this.selectedDate = date;
    this.apiService.getSlotsApi(date).subscribe({
      next: (response) => {
        this.sharedService.isLoading = false;
        if (response.status === 'SUCCESS' && response.data.length > 0) {
          this.displaySlots(response.data);
        } else {
          this.commonService.showToast({
            message: this.commonService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
            color: 'danger',
            duration: 5000,
            position: 'middle',
            cssClass: 'barlow-regular'
          });
        }
      }
    });
  }

  summary() {
    if (this.modal) {
      this.modal.present();
    }
  }
  bookedSlotMessageToast() {
    this.commonService.showToast({
      message: this.commonService.getTranslateData('MESSAGES.SLOT_UNAVAILABLE'),
      color: 'danger',
      position: 'middle',
      duration: 4000,
      cssClass: 'barlow-regular'
    });
  }
  displaySlots(data: any) {
    const now = new Date();
    const hours = now.getMinutes() > 0 ? now.getHours() : now.getHours();
    this.displayedDates[this.selectedCard].slots = false;

    const filteredAvailability = data.map((data: any) => {
      const formattedStartTime = formatTime(data.startTime.slice(0, 5), Constants.dateFormat.hhmm0, Constants.dateFormat.h2ma);
      const formattedEndTime = formatTime(data.endTime.slice(0, 5), Constants.dateFormat.hhmm0, Constants.dateFormat.h2ma);
      const slotTime = parseInt(data.startTime.slice(0, 2), 10);

      const slot = {
        from: formattedStartTime,
        to: formattedEndTime,
        availability: data.availability,
        startTime: data.startTime,
        endTime: data.endTime,
        isAvailable: data.availability && (this.selectedDate !== formatDate(this.startDay, Constants.dateFormat.YYYYMMDD) || slotTime > hours)
      };

      if (slot.availability) {
        this.displayedDates[this.selectedCard].slots = true;
      }
      return slot;
    });
    this.availability = filteredAvailability.sort((a: any, b: any) => {
      const [aTime, aPeriod] = a.from.split(' ');
      const [bTime, bPeriod] = b.from.split(' ');

      if (aPeriod !== bPeriod) {
        return aPeriod === 'AM' ? -1 : 1;
      }

      const [aHour, aMinute] = aTime.split(':').map(Number);
      const [bHour, bMinute] = bTime.split(':').map(Number);

      const aHour24 = (aHour === 12 ? 0 : aHour) + (aPeriod === 'PM' ? 12 : 0);
      const bHour24 = (bHour === 12 ? 0 : bHour) + (bPeriod === 'PM' ? 12 : 0);

      if (aHour24 !== bHour24) {
        return aHour24 - bHour24;
      }
      return aMinute - bMinute;
    });

    this.sharedService.isLoading = false;
    this.noSlots = isBlank(this.availability);
  }
  checkAvailabiltyTime(slot, i) {
    this.sharedService.isLoading = true;
    slot['date'] = this.selectedDate;
    const startDateTime = convertDateTimeToIsoFormat(slot.date, slot.startTime);
    const endDateTime = convertDateTimeToIsoFormat(slot.date, slot.endTime);
    const params = {
      startIntervalTime: startDateTime,
      endIntervalTime: endDateTime,
      regId: environment.regId,
      timeZone: this.timeZoneCity
    };
    this.apiService.getAvailability(params).subscribe({
      next: (response) => {
        this.sharedService.isLoading = false;
        if (response.status === 'SUCCESS') {
          this.router.navigate(['appointment-booking/visit-summary'], {
            state: {
              slot
            }
          });
        } else {
          this.availability[i].isAvailable = false;
          this.commonService.showToast({
            message: this.commonService.getTranslateData('MESSAGES.SLOT_BOOKED'),
            color: 'danger',
            duration: 5000,
            position: 'middle',
            cssClass: 'barlow-regular'
          });
        }
      }
    });
  }
}
