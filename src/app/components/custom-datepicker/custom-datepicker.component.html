<ion-modal [trigger]="triggerID" show-backdrop="true" mode="ios" id="custom-datepicker-modal" #dateModal>
    <ng-template>
        <ion-datetime id="custom-datepicker" color="blumine" mode="ios" #selectDate presentation="date"
            (ionChange)="getDate(selectDate.value)" [min]="minDate" [max]="maxDate" *ngIf="pickerType === 'date'"
            [value]="selectedDate">
            <!-- Set custom buttons to handle clear,cancel,done click event -->
            <ion-buttons slot="buttons">
                <ion-button class="set-date-picker-button-text-color ion-text-uppercase"
                    (click)="selectDate.cancel(); dateModal.dismiss();">{{'BUTTONS.CANCEL' |
                    translate}}</ion-button>
                <ion-button class="set-date-picker-button-text-color ion-text-uppercase"
                    (click)="getDate('');">{{'BUTTONS.CLEAR' |
                    translate}}</ion-button>
                <ion-button class="set-date-picker-button-text-color ion-text-uppercase"
                    (click)="selectDate.confirm();dateModal.dismiss();">{{'BUTTONS.DONE' |
                    translate}}</ion-button>
            </ion-buttons>
        </ion-datetime>
        <ion-datetime id="custom-timepicker" color="blumine" mode="ios" #selectDate presentation="time"
            (ionChange)="getDate(selectDate.value)" *ngIf="pickerType === 'time'" [hourCycle]="pickerFormat"
            [minuteValues]="minuteVal" [value]="selectedDate">
            <!-- Set custom buttons to handle clear,cancel,done click event -->
            <ion-buttons slot="buttons">
                <ion-button class="set-date-picker-button-text-color ion-text-uppercase"
                    (click)="selectDate.cancel();dateModal.dismiss();">{{'BUTTONS.CANCEL' |
                    translate}}</ion-button>
                <ion-button class="set-date-picker-button-text-color ion-text-uppercase"
                    (click)="getDate('');">{{'BUTTONS.CLEAR' |
                    translate}}</ion-button>
                <ion-button class="set-date-picker-button-text-color ion-text-uppercase"
                    (click)="selectDate.confirm();dateModal.dismiss();">{{'BUTTONS.DONE' |
                    translate}}</ion-button>
            </ion-buttons>
        </ion-datetime>
    </ng-template>
</ion-modal>