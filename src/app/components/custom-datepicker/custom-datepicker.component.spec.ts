import { PopoverController, AngularDelegate, ModalController } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CustomDatepickerComponent } from './custom-datepicker.component';

describe('CustomDatepickerComponent', () => {
  let component: CustomDatepickerComponent;
  let fixture: ComponentFixture<CustomDatepickerComponent>;
  let modalController: ModalController;
  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [CustomDatepickerComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [ModalController, AngularDelegate]
    }).compileComponents();
    modalController = TestBed.inject(ModalController);
    spyOn(modalController, 'dismiss').and.stub();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CustomDatepickerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call getDate', () => {
    component.getDate('2022-11-17T13:40:00+05:30');
    expect(component.getDate).toBeTruthy();
  });
});
