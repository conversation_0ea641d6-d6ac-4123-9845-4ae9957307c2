import { Component, Input, Output, EventEmitter } from '@angular/core';
import { DateSelectKeyValue } from 'src/app/interfaces/common-interface';

@Component({
  selector: 'app-custom-datepicker',
  templateUrl: './custom-datepicker.component.html',
  styleUrls: ['./custom-datepicker.component.scss']
})
export class CustomDatepickerComponent {
  @Input() triggerID: string;
  @Input() controlName: string;
  @Input() pickerType: string;
  @Input() minDate: any;
  @Input() maxDate: any;
  @Input() pickerFormat: string;
  @Input() minuteVal: string;
  @Output() selectDate: EventEmitter<any> = new EventEmitter();
  @Input() selectedDate: any;
  constructor() { }

  getDate(selectedVal) {
    const passData: DateSelectKeyValue = {
      pickerFormat: this.pickerFormat,
      formControlName: this.controlName,
      value: selectedVal
    };
    this.selectDate.emit(passData);
  }
}
