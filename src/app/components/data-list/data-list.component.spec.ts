import { Keepalive } from '@ng-idle/keepalive';
import { Idle, IdleExpiry } from '@ng-idle/core';
import { NgxPermissionsService, NgxPermissionsStore, USE_PERMISSIONS_STORE } from 'ngx-permissions';
import { TranslateTestingModule } from 'src/app/services/translate-testing.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { DataListComponent } from './data-list.component';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';
import { SQLite } from '@ionic-native/sqlite/ngx';
import { NativeStorage } from '@ionic-native/native-storage/ngx';
import { OfflineFormStatus } from 'src/app/constants/constants';
import { APIs } from 'src/app/constants/apis';
import { HttpService } from 'src/app/services/http-service/http.service';
import { of, throwError } from 'rxjs';

describe('DataListComponent', () => {
  let component: DataListComponent;
  let fixture: ComponentFixture<DataListComponent>;
  let httpService: HttpService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DataListComponent],
      imports: [IonicModule.forRoot(), RouterModule.forRoot([]), HttpClientModule, HttpClientTestingModule, TranslateTestingModule],
      providers: [
        NgxPermissionsService,
        NgxPermissionsStore,
        Idle,
        IdleExpiry,
        Keepalive,
        { provide: USE_PERMISSIONS_STORE, useValue: undefined },
        PersistentService,
        SQLite,
        NativeStorage
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(DataListComponent);
    component = fixture.componentInstance;
    httpService = TestBed.inject(HttpService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit itemBtnClick event with correct parameters and close swipeList', () => {
    const swipeList = {
      close: jasmine.createSpy('close')
    };
    const item = { id: 1, name: 'Test Item' };
    const index = 0;
    const expectedAction = 'click';
    spyOn(component.itemBtnClick, 'emit');
    component.itemClicked(swipeList, expectedAction, item, index);
    expect(swipeList.close).toHaveBeenCalled();
    expect(component.itemBtnClick.emit).toHaveBeenCalledWith({
      action: expectedAction,
      item: item,
      index: index
    });
  });

  it('should not close swipeList if swipeList is not provided', () => {
    const item = { id: 1, name: 'Test Item' };
    const index = 0;
    const expectedAction = 'click';
    spyOn(component.itemBtnClick, 'emit');
    component.itemClicked(null, expectedAction, item, index);
    expect(component.itemBtnClick.emit).toHaveBeenCalledWith({
      action: expectedAction,
      item: item,
      index: index
    });
  });

  it('should call isUserOnline', () => {
    expect(component.isUserOnline).toBeDefined();
  });

  it('should call listData', () => {
    component.listData = [];
    expect(component.listData).toBeTruthy();
  });

  describe('updateDraftStatus', () => {
    it('should call updateDraftStatus', () => {
      component.updateDraftStatus([]);
      expect(component.updateDraftStatus).toBeDefined();
    });
    it('should update draft status', () => {
      const listData = [
        { draftuniqueID: '1', status: OfflineFormStatus.ACTIVE, offlineFormId: 'form1' },
        { draftuniqueID: '2', status: OfflineFormStatus.SUBMITTED, offlineFormId: 'form2' }
      ];
      const response = {
        data: [
          { draftId: '1', status: OfflineFormStatus.SUBMITTED },
          { draftId: '2', status: OfflineFormStatus.COMPLETED }
        ],
        status: 'success'
      };
      spyOn(httpService, 'doGet').and.returnValue(of(response));
      component.updateDraftStatus(listData);
      expect(component.statusLoading).toBeFalsy();
      expect(listData[0].status).toEqual(OfflineFormStatus.ACTIVE);
      expect(listData[1].status).toEqual(OfflineFormStatus.SUBMITTED);
    });
    it('should handle error', () => {
      const listData = [{ draftuniqueID: '1', status: OfflineFormStatus.ACTIVE, offlineFormId: 'form1' }];
      spyOn(httpService, 'doGet').and.returnValue(throwError(''));
      component.updateDraftStatus(listData);
      expect(component.statusLoading).toBeTrue();
    });
  });
});
