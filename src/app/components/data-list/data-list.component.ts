import { Component, Input, Output, EventEmitter } from '@angular/core';
import { SharedService } from 'src/app/services/shared-service/shared.service';
import { Constants, OfflineFormStatus } from 'src/app/constants/constants';
import { Signature } from 'src/app/constants/signature';
import { Config } from 'src/app/constants/config';
import { isBlank } from 'src/app/utils/utils';
import { APIs } from 'src/app/constants/apis';
import { HttpService } from 'src/app/services/http-service/http.service';
import { PersistentService } from 'src/app/services/persistent-service/persistent.service';

@Component({
  selector: 'app-data-list',
  templateUrl: './data-list.component.html',
  styleUrls: ['./data-list.component.scss']
})
export class DataListComponent {

  @Input() extraData: any = {};
  @Input() listType: string = null;
  @Input() buttons: any = {};
  @Output() readonly itemBtnClick: EventEmitter<any> = new EventEmitter();
  _listData: any;
  isEnableMultiAdmissions;
  @Input() set listData(data: any) {
    this._listData = data;
    if (this.isUserOnline && data) {
      this.updateDraftStatus(data);
    }
  }
  get listData() {
    return this._listData;
  }
  userData: any;
  constants: any = {};
  signatureConstants: any = {};
  statusLoading = false;
  isBlank = isBlank;
  OfflineFormStatus = OfflineFormStatus;
  isAlternateContactWorkFlow :boolean;
  constructor(public shared: SharedService, public httpService: HttpService, public persistentService: PersistentService) {
    this.userData = this.shared.userData;
    this.constants = Constants;
    this.signatureConstants = Signature;
    this.isAlternateContactWorkFlow = this.shared.getConfigValue(Config.defaultPatientsWorkflow) === Constants.workflowAlternateContact;
    this.isEnableMultiAdmissions = this.shared.isEnableConfig(Config.enableMultiAdmissions);
  }

  get isUserOnline(): boolean {
    return navigator.onLine;
  }

  itemClicked(swipeList: any, action: string, item: any, index: number): void {
    if (swipeList) {
      swipeList.close();
    }
    this.itemBtnClick.emit({ action, item, index });
  }

  updateDraftStatus(listData) {
    const draftuniqueIDs = listData.filter(item => !isBlank(item.draftuniqueID) && item.status !== OfflineFormStatus.SUBMITTED).map(item => item.draftuniqueID).join(',');
    if (!isBlank(draftuniqueIDs)) {
      this.statusLoading = true;
      this.httpService.doGet({ endpoint: APIs.getOfflineDraftStatus+`?draftIds=${draftuniqueIDs}`, loader: true}).subscribe(
        (response) => {
          this.statusLoading = false;
          listData.forEach(item => {
            const status = response.data.find(draft => draft.draftId == item.draftuniqueID)?.status;
            if (status) {
              this.persistentService.updateOfflineFormStatus(item.offlineFormId, status);
              item.status = status;
            }
          });
        },
        (error) => {
          this.shared.errorHandler(error);
        }
      );
    }
  }
}
