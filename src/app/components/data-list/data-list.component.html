<ion-list *ngIf="extraData.type === constants?.documents">
  <ng-container *ngFor="let document of listData; trackByIdentifier: extraData?.trackBy; let d = index; let l = last">
    <ion-item-sliding #swipeList [ngClass]="l === true ? 'lastIonItem' : ''">
      <ion-item lines="none" class="item" (click)="itemClicked(null, 'click', document, d)"
        [ngClass]="{ unread: !document.isRead, 'item-archiving': document.archiveing, read: document.isRead }"
        id="document-{{ d }}">
        <div class="list-item list-name-div new-document-view">
          <label class="list-item-label list-item-docname">{{ document.displayText.text }}</label>
          <br />
          <label
            *ngIf="userData?.group !== constants?.patientGroupId?.toString() && userData?.config?.enable_multisite === '1'">
            <strong>{{ document?.siteName }}</strong>
          </label>
          <br />
          <span *ngIf="document.type.allowRecipientRoles">
            <label class="list-item-label list-item-tagname"
              *ngIf="userData.userId.toString() !== document.signatureByUsers.userId.toString() && document.signatureByUsers.userId === 0">
              {{ document.type.name }}
            </label>
            <label class="list-item-label list-item-tagname"
              *ngIf="userData.userId.toString() !== document.signatureByUsers.userId.toString() && document.signatureByUsers.userId !== 0">
              {{ document.type.name }}&nbsp;{{ 'LABELS.SEND_TO' | translate }}&nbsp;{{
              document.signatureByUsers.displayName }}
            </label>
            <label class="list-item-label list-item-tagname"
              *ngIf="userData.userId.toString() === document.signatureByUsers.userId.toString()">
              {{ document.type.name }} {{ 'LABELS.FROM' | translate }} {{ document.owner }}
            </label>
          </span>
          <label *ngIf="!document.type.allowRecipientRoles" class="list-item-label list-item-tagname">
            {{ document.type.name }}
          </label>
          <label
            class="list-item-label list-item-tagname"
            *ngIf="document?.document?.associatePatientDetails || (userData?.group?.toString() === constants.patientGroupId?.toString() &&
              +document?.caregiverAssociatePatient > 0 &&
              userData?.userId?.toString() !== document?.caregiverAssociatePatient?.toString() &&
              document?.document?.associatePatient)
            "
          >
            <br />
            <span>{{ 'GENERAL.PATIENT' | translate }}: </span> {{ document?.document?.associatePatient }}
          </label>
          <label class="list-item-label list-item-tagname" *ngIf="isEnableMultiAdmissions && document?.admissionName">
            <br />
            <strong>{{ document?.admissionName }}</strong>
          </label>
          <br />
          <span
            *ngIf="document.signatureStatus === signatureConstants.signatureStatus.signaturePendingStatus || document.signatureStatus===signatureConstants.signatureStatus.signatureApprovalStatus">
            <ion-icon name="time-outline"></ion-icon>{{ document.createdOn * 1000 | shortDateTime }}
          </span>
          <span *ngIf="document.signatureStatus === signatureConstants.signatureStatus.signatureSignedStatus">
            <ion-icon name="time-outline"></ion-icon>{{ document.signedOn * 1000 | shortDateTime }}
          </span>
        </div>
        <ion-icon slot="end" name="chevron-forward" size="large"></ion-icon>
      </ion-item>
      <ion-item-options side="end" class="common-swipe-buttons swipe-item">
        <ng-template *ngFor="let button of document.swipeButtons" [ngxPermissionsOnly]="button.permission">
          <ion-item-option *ngIf="button.show" class="{{ button.class }}"
            id="{{ button.id ? button.id : button.label }}"
            (click)="itemClicked(swipeList, button.action, document, d)">
            <ion-icon name="{{ button.icon }}" slot="{{ button.slot }}"></ion-icon>
            <ion-label translate>{{ button.label }}</ion-label>
          </ion-item-option>
        </ng-template>
      </ion-item-options>
    </ion-item-sliding>
  </ng-container>
</ion-list>

<ion-list *ngIf="extraData.type === constants?.forms">
  <ng-container *ngFor="let form of listData; trackByIdentifier: extraData?.trackBy; let d = index; let l = last">
    <ion-item-sliding #swipeList [ngClass]="l === true ? 'lastIonItem' : ''">
      <ion-item
        id="form-{{ d }}"
        [ngClass]="{
          'disabled': (form.status === OfflineFormStatus.SUBMITTED && extraData?.formType === constants?.formTypes?.offline) || form.sync,
          'unread set-white-background': form.unreadCount && form.unreadCount !== constants?.formStatus.read,
          'read set-grey-background': !(form.unreadCount && form.unreadCount !== constants?.formStatus.read)
        }" lines="none" class="item" (click)="itemClicked(null, 'click', form, d)">
        <div id="view-{{ form.formId }}"
          [ngClass]="form?.unreadCount && form?.unreadCount !== constants?.formStatus.read ? 'unread' : 'read'">
          <div class="col inbox-message-from inbox-form">
            <div class="data-list-title">
              <div class="data-list-title"
                *ngIf="extraData?.formType === constants?.formTypes?.draft || extraData?.formType === constants?.formTypes?.offline">
                <span
                  [innerHTML]="
                    userData?.crossTenantId?.toString() && userData?.tenantId?.toString() !== form.tenantId?.toString() && form?.tenantName
                      ? form.formname + '(' + form.tenantName + ')'
                      : form.formname
                  "
                ></span>
              </div>
              <div
                [ngClass]="form?.unreadCount && form?.unreadCount !== constants?.formStatus.read ? 'set-heading-color' : ''"
                class="data-list-title"
                *ngIf="extraData?.formType !== constants?.formTypes?.draft && extraData?.formType !== constants?.formTypes?.offline">
                <span *ngIf="
                    form?.fgrp?.toString() !== constants?.roleId.partner ||
                    (form?.fgrp?.toString() === constants?.roleId?.partner && form?.staff_facing === constants?.checkWorkFlow?.practitionerFacing) ||
                    (form?.recipient_id && form?.recipient_id?.toString() === userData?.userId?.toString())
                  ">
                  {{
                  userData?.userId?.toString() === form.fromId?.toString() && form?.staff_facing !==
                  constants?.checkWorkFlow?.practitionerFacing
                  ? form?.patientDisplayName
                  : (form?.recipient_id && userData?.userId?.toString() === form.recipient_id?.toString()
                  ? form.fromName
                  : (form.staff_facing === constants?.checkWorkFlow?.staffFacing
                  ? form.fromName
                  : (form.staff_facing === constants?.checkWorkFlow?.practitionerFacing
                  ? form.patientFirstname
                  : form.patientName)))
                  }}
                  {{
                  userData.userId?.toString() !== form.recipient_id?.toString() &&
                  form.staff_facing === constants?.checkWorkFlow?.practitionerFacing
                  ? form.patientLastname
                  : ''
                  }}
                </span>
                <br />
                <span *ngIf="
                    userData?.group !== constants?.patientGroupId?.toString() && form?.facing_new === constants?.checkWorkFlow?.practitionerFacing
                  ">
                  {{ 'LABELS.PATIENT' | translate }} : {{ form?.patientDisplayName }}
                </span>
                <span *ngIf="
                    form?.fgrp?.toString() === constants?.roleId?.partner &&
                    form?.staff_facing !== constants?.checkWorkFlow?.practitionerFacing &&
                    form?.recipient_id?.toString() !== userData.userId?.toString()
                  ">
                  {{ form?.caregiver_displayname }}
                  ({{
                  userData?.userId?.toString() === form?.fromId?.toString()
                  ? form?.patientName
                  : (userData?.userId?.toString() === form?.recipient_id?.toString()
                  ? form?.fromName
                  : form?.patientName)
                  }})
                </span>
              </div>
              <div class="data-list-title" *ngIf="isEnableMultiAdmissions && form.admissionName">
                {{ 'LABELS.ADMISSION' | translate }}: {{ form.admissionName }}
              </div>
              <div class="data-list-title"
                [ngClass]="form?.unreadCount && form?.unreadCount !== constants?.formStatus.read ? 'set-heading-color' : ''"
                *ngIf="userData?.group !== constants?.patientGroupId?.toString() && userData?.config?.enable_multisite === '1'">
                <span>{{ form?.siteName }}</span>
              </div>
            </div>
            <div class="data-list-title"
              [ngClass]="form?.unreadCount && form?.unreadCount !== constants?.formStatus.read ? 'set-heading-color' : ''"
              *ngIf="extraData?.formType !== constants?.formTypes?.draft && extraData?.formType !== constants?.formTypes?.offline">
              <span
                *ngIf="(userData?.userId?.toString() === form.fromId?.toString() || userData?.userId?.toString() !== form?.recipient_id?.toString() && form?.staffFacing !== constants?.checkWorkFlow?.staffFacing)&& userData?.roleId?.toString() != constants?.roleId?.patient && form?.patientDob && form?.fgrp == constants?.roleId?.patient">
                <!-- Replace with Id instead of Name -->
                {{ 'LABELS.DOB' | translate }}: {{ form?.patientDOB | date: constants?.dateFormat?.mmddyyyy }}
              </span>
              <span
                *ngIf="((form?.fromId && userData?.userId?.toString()===form?.fromId?.toString()) || (userData?.userId?.toString() !==form?.recipient_id?.toString()) && form?.staff_facing !== constants?.checkWorkFlow?.staffFacing) && (userData?.roleId && userData?.roleId !== constants?.roleId?.patient) && form?.IdentityValue && form.IdentityValue !== 'null'">
                [{{ 'LABELS.MRN' | translate }}: {{ form?.IdentityValue }}]
              </span>
              <span *ngIf="
                  (userData?.userId?.toString() === form.fromId?.toString() ||
                    (userData?.userId?.toString() !== form.recipient_id?.toString() &&
                      form?.staffFacing !== constants?.checkWorkFlow?.staffFacing)) &&
                  userData?.roleId?.toString() !== constants?.roleId?.patient &&
                  form?.caregiver_dob &&
                  form?.fgrp?.toString() === constants?.roleId?.patient &&
                  !isAlternateContactWorkFlow
                ">
                <!-- Replace with Id instead of Name -->
                {{ 'LABELS.DOB' | translate }}: {{ form?.caregiver_dob | date: constants?.dateFormat?.mmddyyyy }}
              </span>
              <span *ngIf="
                  (userData?.userId?.toString() === form?.fromId?.toString() ||
                    (userData?.userId?.toString() !== form?.recipient_id?.toString() &&
                      form?.staff_facing !== constants?.checkWorkFlow?.staffFacing)) &&
                  userData?.roleId?.toString() !== constants?.roleId?.patient &&
                  !form.IdentityValue &&
                  form.caregiverIdentityValue &&
                  form.caregiverIdentityValue !== '' &&
                  form.caregiverIdentityValue !== 'null' &&
                  form?.fgrp?.toString() === constants?.roleId.patient &&
                  !isAlternateContactWorkFlow
                ">
                [{{ 'LABELS.MRN' | translate }}: {{ form?.caregiverIdentityValue }}]
              </span>
            </div>
            <div class="data-list-title"
              [ngClass]="form?.unreadCount && form?.unreadCount !== constants?.formStatus.read ? 'set-heading-color' : ''">
              {{
              userData?.userId?.toString() === form.fromId?.toString()
              ? (form?.staff_facing === constants?.checkWorkFlow?.practitionerFacing
              ? form?.patienUser_role_name
              : form?.recipient_id?.toString() !== form.fromId?.toString()
              ? form?.patienUser_role_name
              : form?.recipient_role_name)
              : (userData?.userId?.toString() === form.recipient_id?.toString()
              ? form?.from_role_name
              : (form?.staff_facing === constants?.checkWorkFlow?.staffFacing
              ? form?.from_role_name
              : form?.recipient_role_name))
              }}
            </div>
            <div class="data-list-title"
              *ngIf="(userData && userData.group && userData.group.toString() === constants.patientGroupId?.toString()) && form?.caregiver_userid">
              <div class="form-patient-role"
                [ngClass]="form?.unreadCount && form?.unreadCount !== constants?.formStatus.read ? 'set-heading-color' : ''">
                <span>{{ 'GENERAL.PATIENT' | translate }}: </span>{{ form?.caregiver_displayname }}
              </div>
            </div>

            <div class="data-list-title">
              <div class="data-list-title"
                [ngClass]="form?.unreadCount && form?.unreadCount !== constants?.formStatus.read ? 'set-heading-color' : ''"
                *ngIf="extraData?.formType !== constants?.formTypes?.draft && extraData?.formType !== constants?.formTypes?.offline">
                <span [innerHTML]="
                    userData?.crossTenantId && userData?.crossTenantId?.toString() !== form.tenantId?.toString()
                      ? form.formName + '(' + form.tenantName + ')'
                      : form.formName
                  "></span>
              </div>
            </div>
            <div
              class="data-list-title"
              *ngIf="extraData?.formType === constants?.formTypes?.draft || extraData?.formType === constants?.formTypes?.offline"
            >
              <div class="data-list-title" *ngIf="form.patientName && form.patientName.trim() !== ''">
                <span>{{ 'GENERAL.PATIENT' | translate }}: </span>{{ form.patientName }}
              </div>
              <div class="data-list-title">
                <span *ngIf="userData?.roleId !== constants?.roleId?.patient && form?.patientDOB">
                  {{ 'LABELS.DOB' | translate }}: {{ form?.patientDOB | date: constants?.dateFormat?.mmddyyyy }}
                </span>
                <span *ngIf="userData?.roleId !== constants?.roleId?.patient && form.IdentityValue">
                  &nbsp;[{{ 'LABELS.MRN' | translate }}:&nbsp;{{ form.IdentityValue }}]
                </span>
              </div>
            </div>
            <div class="data-list-title"
              *ngIf="extraData.formType === constants?.formTypes?.draft && userData.config.enable_collaborate_edit === '1'">
              <div class="data-list-title">
                <span>{{ 'LABELS.CREATED' | translate }} : </span>{{ form.createdUser }}
              </div>
            </div>
            <div class="data-list-title"
              *ngIf="extraData?.formType === constants?.formTypes?.draft && userData.config.enable_collaborate_edit === '1'">
              <div class="data-list-title">
                <span>{{ 'LABELS.LAST_MODIFIED' | translate }} : </span>{{ form.modified_firstname }}&nbsp;
                {{ form.modified_lastname }}
              </div>
            </div>
            <div *ngIf="form?.message !== '' && form?.recipient_id?.toString() === userData?.userId?.toString()">
              <span>{{ form?.message }}</span>
            </div>
            <div class="font-weight-lite">
              <ion-icon name="time-outline" class="set-align-center"></ion-icon>
              {{ form.time * 1000 | shortDateTime }}
            </div>
          </div>
        </div>
        <ion-button slot="end" color="{{ form.sync ? 'success' : 'danger' }}"
          *ngIf="extraData?.formType === constants?.formTypes?.offline && form.isFormDataExist && isUserOnline && !statusLoading"
          shape="round" (click)="$event.stopPropagation(); itemClicked(null, 'sync', form, d)">{{ 'BUTTONS.SYNC' |
          translate }}
        </ion-button>
        <ion-icon
          id="edit-{{ form.formId }}"
          slot="end"
          name="create"
          size="large"
          color="blumine"
          *ngIf="
            (extraData?.formType === constants?.formTypes?.offline && form.isFormDataExist && !statusLoading && !form.sync) ||
            (form.allowEdit === 1 &&
            ((form.recipient_id?.toString() === userData.userId?.toString() && form.staff_facing !== constants?.checkWorkFlow?.staffFacing) ||
              (form.staff_facing === constants?.checkWorkFlow?.staffFacing && userData.userId.toString() === form.fromId.toString())))
          ">
        </ion-icon>
        <ion-icon
          *ngIf="!(extraData?.formType === constants?.formTypes?.offline && form.isFormDataExist && isUserOnline && !statusLoading)"
          slot="end" name="chevron-forward" size="large"></ion-icon>
      </ion-item>
      <ion-item-options side="end" class="common-swipe-buttons swipe-item">
        <ng-template *ngFor="let button of form.swipeButtons" [ngxPermissionsOnly]="button.permission">
          <ion-item-option *ngIf="button.show" class="{{ button.class }}"
            id="{{ button.id ? button.id : button.label }}" (click)="itemClicked(swipeList, button.action, form, d)">
            <ion-icon name="{{ button.icon }}" slot="{{ button.slot }}"></ion-icon>
            <ion-label translate>{{ button.label }}</ion-label>
          </ion-item-option>
        </ng-template>
      </ion-item-options>
    </ion-item-sliding>
  </ng-container>
</ion-list>
<ion-list *ngIf="extraData.type === constants?.materials" class="materials-list">
  <div class="list-item-style" *ngFor="let material of listData; trackByIdentifier: extraData?.trackBy; let i = index">
    <ion-item lines="none" class="item unread" (click)="itemClicked(null, 'click', material, i)" id="item-{{ i }}">
      <div class="row">
        <div class="col">
          <div class="data-list-title">
            <div class="data-list-title">{{ material.materialName }}</div>
          </div>
          <div class="data-list-title">
            <div>{{ material.description }}</div>
          </div>
          <div class="data-list-title">
            <div class="data-list-title">{{ 'LABELS.BY' | translate }}&nbsp;{{ material.fromName }}</div>
          </div>
          <div>
            {{ 'LABELS.ON' | translate }}&nbsp;{{ material.updatedOn ? (material.updatedOn | shortDateTime) :
            (material.createdOn | shortDateTime) }}
          </div>
        </div>
      </div>
    </ion-item>
  </div>
</ion-list>
<ion-list *ngIf="extraData.type === constants?.delivery" class="materials-list">
  <div class="list-item-style" *ngFor="let delivery of listData; trackByIdentifier: extraData?.trackBy; let d = index; let l = last">
    <ion-item lines="none" class="item set-grey-background" (click)="itemClicked(null, 'click', delivery, d)" id="delivery-{{ d }}">
      <div class="row">
        <div class="col">
          <div class="data-list-title">
            <div class="data-list-title">{{ delivery.courierTicketNumber }}</div>
          </div>
          <div class="data-list-title">
            <div class="inbox-per-description">{{ delivery.courierStatus }}</div>
          </div>
          <div class="data-list-title" *ngIf="userData.group?.toString() !== constants.patientGroupId?.toString()">
            <div class="data-list-title">{{ delivery.firstname }}</div>
          </div>
          <div class="data-list-title" *ngIf="userData.group?.toString() !== constants.patientGroupId?.toString()">
            <div class="data-list-title">{{ delivery.mrn }}</div>
          </div>
          <div class="data-list-title" *ngIf="userData.group?.toString() !== constants.patientGroupId?.toString()">
            <div class="data-list-title">{{ delivery.branch }}</div>
          </div>
          <div class="data-list-title font-weight-lite">
            <ion-icon name="time-outline" class="set-align-center"></ion-icon>
            {{ delivery.courierDateTime * 1000 | shortDateTime }}
          </div>
        </div>
      </div>
      <ion-icon slot="end" name="chevron-forward" size="large"></ion-icon>
    </ion-item>
  </div>
</ion-list>
<app-skeleton-loader *ngIf="listData?.length === 0 && !extraData?.showNoDataMessage" [count]="5"></app-skeleton-loader>
<app-show-message *ngIf="extraData.showNoDataMessage" [messageData]="extraData"></app-show-message>
