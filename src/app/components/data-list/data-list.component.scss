ion-item-sliding {
    border-radius: 10px;
    margin-bottom: 5px;
    border: solid 2px #f4f4f4;
}

ion-list {
    padding-right: 10px;
    padding-left: 10px;
}

ion-list .item.read,
.set-grey-background {
    background-color: #f2f2f2;
    --background: #f2f2f2;
}

.set-white-background {
    --background: var(--ion-color-white);
}

ion-list .item-content {
    --ion-item-background: transparent !important;
}

ion-list .item.unread {
    border-left: 8px solid #64c28d;
}

.new-document-view label.list-item-docname {
    color: #000;
    font-size: 15px !important;
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: bold;
    white-space: nowrap;
}

.data-list-title {
    font-weight: 600;
    color: #404040;
    font-size: 14px;
    font-family: 'Open Sans', sans-serif;
    line-height: 20px;
}
.font-weight-lite {
    font-weight: 400;
}
.new-document-view .list-item-tagname {
    font-size: 13px !important;
}

.lastIonItem {
    margin-bottom: 60px;
}

.set-heading-color {
    color: #369ab1;
}

.set-align-center {
    vertical-align: middle;
}

.materials-list .list-item-style {
    border-radius: 10px;
    margin-bottom: 5px;
    border: 2px solid #f4f4f4;

    .item {
        border-radius: 10px;
    }
}

ion-list .item.unread .data-list-title,
ion-list .item.unread .list-item-docname {
    color: #369ab1;
}
.disabled {
    pointer-events: none;
    cursor: default;
    opacity: 0.6;
    color: #828282 !important;
}