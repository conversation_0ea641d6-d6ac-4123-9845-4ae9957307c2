import { TranslateModule } from '@ngx-translate/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { SelectListGridComponent } from './select-list-grid.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('SelectListGridComponent', () => {
  let component: SelectListGridComponent;
  let fixture: ComponentFixture<SelectListGridComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SelectListGridComponent],
      imports: [IonicModule.forRoot(), TranslateModule.forRoot()],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(SelectListGridComponent);
    component = fixture.componentInstance;
    component.subHead = '';
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('isListType', () => {
    it('should set isList to true and emit true', () => {
      spyOn(component.viewType, 'emit');
      component.isListType(true);
      expect(component.isList).toBe(true);
      expect(component.viewType.emit).toHaveBeenCalledWith(true);
    });

    it('should set isList to false and emit false', () => {
      spyOn(component.viewType, 'emit');
      component.isListType(false);
      expect(component.isList).toBe(false);
      expect(component.viewType.emit).toHaveBeenCalledWith(false);
    });
  });
});
