<section class="common-document-header">
    <div class="common-document-head-title">
        {{ subHead | translate }}
    </div>
    <div class="common-document-head-menu" *ngIf="showIcons">
        <ul>
            <li (click)="isListType(true)" id="select-list-view" tappable>
                <span>
                    <ion-icon class="view-list" [ngClass]="isList? 'common-active': ''"
                        src="assets/icon/material-svg/view-list.svg"></ion-icon>
                </span>
            </li>
            <li (click)="isListType(false)" id="select-grid-view" tappable>
                <span>
                    <ion-icon class="view-grid" [ngClass]="!isList? 'common-active': ''"
                        src="assets/icon/material-svg/grid.svg"></ion-icon>
                </span>
            </li>
        </ul>
    </div>
</section>