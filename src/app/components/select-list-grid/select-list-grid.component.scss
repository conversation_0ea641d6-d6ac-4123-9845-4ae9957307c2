.item {
    border-radius: 10px !important;
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 5px;
    border: 2px solid #f4f4f4 !important;
}

ion-list .item.read {
    --ion-item-background: #F2F2F2 url(/assets/icon/documents/listview-arrow-right.png) no-repeat 95% center !important;
    background-size: 15px auto !important;
    padding-right: 30px !important;
    background-color: #F2F2F2;
}

ion-list .item-content {
    --ion-item-background: transparent !important;
}

ion-list .item.item-not-read {
    --ion-item-background: #ffffff url(/assets/icon/documents/listview-arrow-right.png) no-repeat 95% center !important;
    background-size: 15px auto !important;
    padding-right: 30px !important;
    border-left: 8px solid #64c28d;
}

ion-list .item.unread {
    --ion-item-background: #F2F2F2 url(/assets/icon/documents/listview-arrow-right.png) no-repeat 95% center !important;
    background-size: 15px auto !important;
    border-left: 8px solid #64c28d;
}

.new-document-view label.list-item-docname {
    color: #000 !important;
    font-size: 15px !important;
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: bold;
    white-space: nowrap;
}

.new-document-view .list-item-tagname {
    font-size: 13px !important;
}