import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-select-list-grid',
  templateUrl: './select-list-grid.component.html',
  styleUrls: ['./select-list-grid.component.scss']
})
export class SelectListGridComponent {
  @Input() subHead: any;
  isList = true;
  @Output() readonly viewType: EventEmitter<any> = new EventEmitter();
  @Input() showIcons = true;

  constructor() {}

  isListType(type: boolean): void {
    this.isList = type;
    this.viewType.emit(this.isList);
  }
}
