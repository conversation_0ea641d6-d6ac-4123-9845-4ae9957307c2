export class VisitScheduleConstants {
  static isDraft = '2';
  static isSchedule = '1';
  static visitScheduleDownloadFileFormats = ['odt'];
  static createScheduleParams = {
    name: '',
    userId: '',
    type: 'staffs',
    source: '',
    eventDetails: {
      id: '',
      seriesId: '',
      start: '',
      end: '',
      startTime: '',
      endTime: ''
    }
  };
  static otherStaffAvailability = 'otherStaffAvailability';
  static myAvailability = 'myAvailability';
  static createVisitPage = 'CreateVisit';
  static scheduleExist = 'Schedule exist';
  static starttimeCheck1 = '11:30 PM';
  static starttimeCheck2 = '12:00 AM';
  static patientSearchParam = {
    type: 'patients',
    pageCount: 0,
    searchKeyword: '',
    offset: 0,
    limit: 20,
    siteId: ''
  };
  static createVisit = 'createVisit';
  static isCompleted = '1';
  static complete = 'complete';
  static reviewCompleted = '4';
  static homeVisitType = '1';
  static aicVisitType = '2';
  static week = 'week';
  static month = 'month';
  static day = 'day';
  static timeGridWeek = 'timeGridWeek';
  static dayGridMonth = 'dayGridMonth';
  static timeGridDay = 'timeGridDay';
  static viewVisit = 'view_visit';
  static actualTimeIn = 'actualTimeIn';
  static actualTimeOut = 'actualTimeOut';
  static commonSearchParam = {
    currentPage: 0,
    rowsPerPage: 25,
    searchString: '',
    siteIds: [],
    sortBy: '',
    sortDirection: 'ASC',
    status: -1
  }
}

export enum VisitType {
  Home = '1',
  AIC = '2',
  Facility = '3',
  aicText = 'AIC'
}
