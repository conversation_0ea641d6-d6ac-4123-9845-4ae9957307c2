import { ConfigValues } from 'src/assets/config/config';
import { BrandConfig } from '../interfaces/common-interface';
import { LoginResponse } from '../interfaces/login';

export class TestConstants {
  static userPermissions = '';
  static appConfig = {
    configurations: {
      allowedFileFormat: [
        'pdf',
        'doc',
        'docx',
        'word',
        'xl',
        'xls',
        'xlsx',
        'odt',
        'jpg',
        'JPG',
        'jpeg',
        'JPEG',
        'png',
        'PNG'
      ]
    },
    appUpgradeMessages: {
      cancelText: 'cancel',
      confirmText: 'confirm',
      contentMessage: 'test'
    }
  };
  static userData: LoginResponse = {
    userName: 'name',
    username: 'name',
    authenticationToken: 'ghfiusehfiehnfuesi',
    displayName: 'name',
    userId: '3',
    roleId: '1',
    crossTenantId: '1',
    tenantId: '1',
    group: '2',
    mySites: [],
    buttonText: 'ok',
    profileImageUrl: '',
    accessSecurityEnabled: true,
    associated_user: [
      {
        displayname: 'Test Name',
        firstname: 'Test',
        lastname: 'Name',
        dob: '08/09/1992',
        userid: '233'
      }
    ],
    config: {
      showElevio: '1',
      signature_serverside: '1',
      enable_multisite: '1',
      enable_appless_video_chat: '1',
      web_notification_hide_behaviour: '1',
      patient_name_display: '1',
      enable_appless_model: '1',
      fax_queue_show_warning: '1',
      enable_nursing_agencies_visibility_restrictions: 1,
      enable_sftp_integration_machform_value: '1',
      branch_start_time: '9:0',
      branch_end_time: '9:0',
      home_infusion_start_time: '9:0',
      home_infusion_end_time: '9:0',
      tenant_timezoneName: 'Asia/Kolkata',
      enable_message_center: '1',
      enable_support_widget_branding: '1',
      recipient_email_for_support_widget: '<EMAIL>',
      magiclink_verification_token_expiration_time: '10',
      magiclink_verification_expiry_time: '',
      magiclink_token_expiration_time: ''
    },
    siteConfigs: {
      branch_start_time: '9:0',
      branch_end_time: '9:0',
      working_hour: '',
      branch_working_days: '',
      timezoneName: '',
      magiclink_verification_token_expiration_time: '',
      magiclink_verification_expiry_time: '10',
      magiclink_token_expiration_time: ''
    },
    privileges: '',
    schedulerData: {},
    masterSchedulerData: {}
  } as any;
  static localConfig = ConfigValues.config;
  static roomID = 1;
  static brandConfig: BrandConfig = {
    tenantId: 1,
    title: 'dsfsd',
    alias: 'citushealth',
    appName: 'fgfd',
    theme: ''
  };
  static sharedServiceTestProperties = {
    userData: this.formatTestProperty(this.userData),
    localConfig: this.formatTestProperty(this.localConfig),
    roomID: this.formatTestProperty(this.roomID),
    userPermissions: this.formatTestProperty(this.userPermissions),
    appConfig: this.formatTestProperty(this.appConfig),
    brandConfig: this.formatTestProperty(this.brandConfig)
  };
  static controllerEvents = ['onDidDismiss', 'onWillDismiss', 'present', 'dismiss'];
  static actionSheetSpy = jasmine.createSpyObj('Actionsheet', this.controllerEvents);
  static popoverSpy = jasmine.createSpyObj('Popover', this.controllerEvents);
  static modalSpy = jasmine.createSpyObj('Modal', this.controllerEvents);
  static alertSpy = jasmine.createSpyObj('Alert', this.controllerEvents);
  static formatTestProperty(data) {
    return {
      value: data,
      writable: true
    };
  }
  static readonly talentlmsUrl = 'https://example.com/talentlms';
}
