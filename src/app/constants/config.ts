export class Config {
  static enableMsgCenter = 'enable_message_center';
  static enableMaskedMsg = 'enable_masked_discussion_group';
  static enableUserCenter = 'enable_user_center';
  static enableDocCenter = 'show_document_tagging';
  static enableDeliveryCenter = 'enable_delivery_center';
  static enableScheduleCenter = 'enable_visit_schedule';
  static enableFormCenter = 'enable_forms';
  static enableEduCenter = 'show_education_training';
  static enableApplessModel = 'enable_appless_model';
  static readonly enableApplessHome = 'enable_appless_home';
  static enableApplessVideoChat = 'enable_appless_video_chat';
  static enableNursingAgencies = 'enable_nursing_agencies_visibility_restrictions';
  static enableIDM = 'enable_idm_authentication';
  static showSupportAndFeedback = 'show_support_and_feedback_form';
  static enableSftp = 'enable_sftp_integration';
  static showFaxQueueWarning = 'fax_queue_show_warning';
  static sessionTimeout = 'session_timeout';
  static readonly enableDocumentManagement = 'enable_document_management';
  static sessionTimeoutWarning = 'session_timeout_warning';
  static enablePartialInviteInAutoEnrollment = 'enable_partial_invite_in_auto_enrollment';
  static token = 'token';
  static wpEnrollUrl = 'wp_enroll_url';
  /** @deprecated TODO: esi_code_for_patient_identity decommissioned, remove usage if it is not causing any issue */
  static esiCodeForPatientIdentity = 'esi_code_for_patient_identity';
  static urlExpirationDaysInAutoenrollment = 'url_expiration_days_in_autoenrollment';
  static allowPrefillingDuringPartialEnrollment = 'allow_prefilling_during_partial_enrollment';
  static caregiverSeesEnrollmentInvitationData = 'caregiver_sees_enrollment_invitation_data';
  static esiCodeForStaffIdentity = 'esi_code_for_staff_identity';
  static esiCodeForStaffName = 'esi_code_for_staff_name';
  static enableProgressNoteIntegration = 'enable_progress_note_integration';
  static makeMrnFieldMandatoryInPatientInvite = 'make_mrn_field_mandatory_in_patient_invite';
  static makeStaffidFieldMandatoryInStaffInvitePage = 'make_staffid_field_mandatory_in_staff_invite_page';
  static messageForwardingBehaviour = 'message_forwarding_behaviour';
  static enableMessageFlagging = 'enable_message_flagging';
  static enableApplessMessaging = 'enable_appless_messaging';
  static enableMessageTagging = 'show_form_features';
  static defaultClinicianRoles = 'default_clinician_roles_available';
  static noClinicianMessage = 'no_clinician_message';
  static branchWorkingDays = 'branch_working_days';
  static workingHour = 'working_hour';
  static branchStartTime = 'branch_start_time';
  static allowVirtualPatient = 'allow_virtual_patient';
  static branchEndTime = 'branch_end_time';
  static homeInfusionStartTime = 'home_infusion_start_time';
  static homeInfusionEndTime = 'home_infusion_end_time';
  static messageReplyTimeout = 'message_reply_timeout';
  static restrictToBranchHour = 'restrict_to_branch_hour';
  static showInfusionSupport = 'show_infusion_support';
  static enableWhereIsMyNurseComing = 'enable_when_is_my_nurse_coming';
  static enableWhereIsMYDelivery = 'enable_where_is_my_delivery';
  static newPatientWelcomeMessage = 'new_patient_chat_welcome_message';
  static messageEscalationBehavior = 'message_escalation_behavior';
  static escalationTime = 'escalation_time';
  static patientMessageNotification = 'patient_message_sms_notifcation_beyond_branch_24hr';
  static patientReminderTime = 'patient_reminder_time';
  static patientReminderTypes = 'patient_reminder_types';
  static flexSitePatientsCanChat = 'flex_site_patients_can_chat_with_internal_staffs';
  static enableDoubleVerification = 'enable_double_verification';
  static patientReminderCheckingType = 'patient_reminder_checking_type';
  static savaAsDraftPatient = 'enable_form_save_draft_patient';
  static savaAsDraftStaff = 'enable_form_save_draft_staff';
  static saveDraftMessageInterval = 'save_as_draft_message_interval';
  static enableCollaborateEdit = 'enable_collaborate_edit';
  static enableIntegrationStatusWorklist = 'enable_integration_status_worklist';
  static formMobileOrientaion = 'form_mobile_orientaion';
  static enableSftpIntegrationMachformValue = 'enable_sftp_integration_machform_value';
  static enableVerificationOfCellAndMobile = 'enable_verification_of_cell_and_mobile';
  static signatureServerside = 'signature_serverside';
  static toggleChatTranslation = 'toggle_chat_translation';
  static chatAutoTranslate = 'chat_auto_translate';
  static enableFormAutoSave = 'enable_form_auto_save';
  static showElevio = 'showElevio';
  static elevioPatient = 'elevioPatient';
  static elevioStaff = 'elevioStaff';
  static elevioPartner = 'elevioPartner';
  static defaultPartnerRole = 'default_partner_role';
  static webNotificationHideBehaviour = 'web_notification_hide_behaviour';
  static enableSaveSignatureToProfile = 'enable_save_signature_to_profile';
  static staffMsgSmsNotification = 'staff_message_sms_notifcation';
  static enableEmailPatient = 'enable_email_for_patient';
  static enableEmailStaff = 'enable_email_for_staff';
  static defaultCategoryOfMessageDisplay = 'default_category_of_message_display';
  static documentExchangeMode = 'documet_exchange_mode';
  static enableMultiSite = 'enable_multisite';
  static tenantTimezoneName = 'tenant_timezoneName';
  static defaultPatientsWorkflow = 'default_patients_workflow';
  static addUserToGroupOnInviteToChatSession = 'add_user_to_group_on_invite_to_chat_session';
  static patientNameDisplay = 'patient_name_display';
  static enableMobilePAH = 'enable_mobile_pah';
  static formSendMode = 'form_send_mode';
  static magiclinkVerificationExpiryTime = 'magiclink_verification_expiry_time';
  static magiclinkVerificationTokenExpirationTime = 'magiclink_verification_token_expiration_time';
  static recipientEmailForSupportWidget = 'recipient_email_for_support_widget';
  static enableSupportWidgetBranding = 'enable_support_widget_branding';
  static timezoneName = 'timezoneName';
  static showTalentlmsTutorial = 'enable_talentlms';
  static talentlmsUrl = 'talentlms_url';
  static talentlmsIdpId = 'talentlms_idp_id';
  static timezoneNameValue = 'timezoneNameValue';
  static enableDemographicProfile = 'enable_demographic_profile';
  static enableDocumentCenter = 'enable_document_center';
  static tenantTimezoneNameValue = 'tenant_timezoneNameValue';
  static showChatHistoryToNewParticipant = 'show_chat_history_to_new_participant';
  static appName = 'app_name';
  static enablePatientDrievenFlowForms = 'enable_patient_drieven_flow_forms';
  static supportWidgetColorCode = 'support_widget_email_color_code';
  static supportWidgetFromEmail = 'support_widget_from_email';
  static supportWidgetEmailLogo = 'support_widget_email_logo';
  static SMTPDomain = 'SMTP_domain';
  static brandedApplessFormsUrl = 'branded_appless_forms_url';
  static defaultCategoryOfChatWindowPopup = 'default_category_of_chat_window_popup';
  static enablePatientInfoFormThirdPartyApp = 'enable_patient_info_from_third_party_app';
  static enableOfflineForms = 'enable_offline_forms';
  static enableVideoChat = 'enable_video_chat';
  static showPushNotificationForChatRoomInvite = 'show_push_notification_for_chat_room_invite';
  static chatEndTime = 'chat_end_time';
  static chatStartTime = 'chat_start_time';
  static enableMultiAdmissions = 'enable_multi_admissions';
  static enableMultiPartMRNEnabled = 'enable_multipart_mrn';
  static patientIdentityValidationFields = 'patient_identity_validation_fields';
  static enablePatientIdentityValidation = 'enable_patient_identity_validation';
  static otpSingleFieldEntry = 'otp_single_field_entry';
  static visitColorCodingCategory = 'visit_color_coding_category';
}
