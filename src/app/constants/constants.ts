import { Channel } from '@capacitor/push-notifications';
import * as moment from 'moment';

export class Constants {
  static authFailed = 'Authentication failed';
  static statusAll = 'All';
  static NOTIFICATION_FREQUENCY = '0';
  static formPendingStatus = 'pending';
  static formCompletedStatus = 'completed';
  static formDraftStatus = 'draft';
  static formOfflineStatus = 'offline';
  static formArchivedStatus = 'archived';
  static notRejected = 'notRejected';
  static removeStatus = 'remove';
  static undefined = 'undefined';
  static nullValue = 'null';
  static alternateContact = 'Alternate Contact';
  static defaultLimit = 10;
  static defaultOffset = 0;
  static duplicate = 'duplicate';
  static documentExchangeFC = 'fc';
  static online = 'online';
  static sessionTimeout = 'session-timeout';
  static defaultDateOfBirth = '01/01/1900';
  static messageInputType = {
    attachment: 'attachment',
    text: 'text'
  };
  static loaderMessages = {
    submittingMessage: 'Submitting the Form. Please wait...'
  };
  static defaultPageCount = 0;
  static defaultChatRoomId = 0;
  static defaultFlagValue = 0;
  static defaultPageLast = 0;
  static defaultBaseId = 0;
  static defaultInsertionStatus = 1;
  static defaultDoubleVerifyStatus = 0;
  static defaultDocumentFormat = 'jpg';
  static defaultDocumentMimeType = 'image/jpeg';
  static defaultCountryId = '+1';
  static maxFileUploadSize = 20971520;
  static maxFileLength = 10;
  static maxFileSize = 20;
  static sortOrderAsc = 'asc';
  static sortOrderDesc = 'desc';
  static orderData = {
    sentOn: 'senton',
    modifiedOn: 'modified_on',
    deletedOn: 'deletedOn'
  };
  static profilePic = 'profile-pic';
  static fileUniqueName = 'multipleupload';
  static fileSourceType = 'attachment';
  static objectType = 'file';
  static parentFolderId = 'nil';
  static messageUserType = 'owner';
  static messageChatType = 'broadcast_message';
  static normalMessageType = 0;
  static patientImage = 'patient';
  static otherUserImage = 'clinician-nurse-default';
  static profilePhotoType = '.png';
  static profileThumbUrl = 'thumbs';
  static normalChat = 'normal';
  static groupChat = 'groupchat';
  static attachmentStartTag = "<comment class='comment-attachment'> <span>";
  static attachmentEndTag = '</span></comment>';
  static pushNotificationGroupChatTarget = 'group-chat';
  static appless = 'appless';
  static nonAppless = 'nonAppless';
  static mobileapp = 'mobileapp';
  static emailCharecterHide = /^(.{2})[^@]+/;
  static emailHideFormat = '$1****';
  static mobileNumberSlice = /.(?=.{4})/g;
  static magiclinkTokenExpiryTime = 10;
  static documentSignedMedium = 'APP_LESS';
  static applessVerificationSuccessName = 'CODE_VERIFICATION_SUCCESS';
  static otpDigits = '**********';
  static applessCheckingTimeOut = 1500;
  static magicLinkExpiryDefaultTime = '0';
  static defaultTokenRememberDefaultTime = 60;
  static magiclinkVerificationTokenExpirationTime = '-1';
  static applessFlowMode = {
    form: 'forms',
    message: 'message',
    document: 'document',
    visitView: 'visit-view',
    video: 'video',
    userConsent: 'user-consent',
    download: 'download',
    general: 'general'
  };
  static applessNotificationMode = { sms: 1, email: 2 };
  static sourceId = {
    message: 10001,
    form: 10002,
    document: 10003,
    other: 100010
  };
  static sourceCategoryId = {
    messageSendNotification: '10001_001',
    videoCallNotification: '10001_005',
    staffReviewingPatientMessage: '10001_006',
    formSentNotification: '10002_001',
    formReminderNotification: '10002_002',
    formReSendNotification: '10002_005',
    userForwardNotificationForRecipient: '100010_001',
    userForwardNotificationForInitiator: '100010_002',
    userIniviteNotification: '100010_003'
  };
  static token = 'token';
  static documentActivityParams = {
    throughApp: 'CODE_SENT_BY_CITUS',
    throughUser: 'RECIPIENT_REQUESTED_CODE'
  };
  static accessCodeSentMedium = {
    mobile: 'MOBILE',
    email: 'EMAIL'
  };
  static alertTypes = {
    info: 1,
    warning: 2,
    error: 3
  };
  static documentTypes = {
    image: 'image',
    document: 'document',
    pdf: 'pdf',
    video: 'video',
    audio: 'audio',
    excel: 'excel'
  };
  static invalidCode = 400;
  static loginResponseStatus = {
    invalid: 0,
    Unauthorized: 401,
    pendingActivation: 2,
    disable: 1,
    disableAccount: 8,
    dischargeUser: 5
  };
  static get = 'GET';
  static post = 'POST';
  static patientForms = 'patientForms';
  static timezone = 5.5;
  static timezoneDefault = 'UTC';
  static dateFormatDefault = 'YYYY-MM-DD HH:mm:ss';
  static userTypes = {
    patients: 'patients',
    partner: 'partner',
    staff: 'staff',
    pdg: 'pdg',
    groups: 'groups',
    associateContacts: 'associateContacts'
  };
  static flagTypes = {
    low: 1,
    medium: 2,
    high: 3,
    noFlag: 0
  };
  static flagClass = {
    low: 'low',
    medium: 'medium',
    high: 'high',
    noFlag: null
  };
  static flagColor = {
    green: 'green',
    blue: 'blue',
    orange: 'orange',
    red: 'red'
  };

  static messageLoadLimit = 20;
  static messageListLoadLimit = 25;
  static allowedImageFileTypes = ['png', 'jpg', 'jpeg', 'JPG', 'JPEG', 'PNG'];
  static allowedBulkMessageFileTypes = [
    'gif',
    'png',
    'jpg',
    'jpeg',
    'jpe',
    'mpeg',
    'bmp',
    'mpg',
    'mpe',
    'avi',
    'mp4',
    'movie',
    'qt',
    'mov',
    'pdf',
    'mp3',
    'mp2',
    'doc',
    'docx',
    'word',
    'xl',
    'xls',
    'xlsx',
    'aac',
    'amr'
  ];
  static documentAllowedType = ['xl', 'xls', 'xlsx'];
  static pngMimes = ['image/x-png'];
  static jpegMimes = ['image/jpg', 'image/jpe', 'image/jpeg', 'image/pjpeg'];
  static imgMimes = ['image/gif', 'image/jpeg', 'image/png'];
  static mimeDoc = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/zip',
    'application/msword',
    'application/octet-stream',
    'application/excel',
    'application/vnd.ms-excel',
    'application/msexcel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];
  static mimePdf = ['application/pdf', 'application/x-download'];
  static excelMimes = [
    'application/excel',
    'application/msexcel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
  ];
  static assetRelativePath = {
    pdf: 'pdf/1',
    doc: 'doc/2'
  };
  static messageTypes = {
    active: 'active',
    archived: 'archived'
  };
  static messageListTypes = {
    general: '0',
    broadcast: '1',
    masked: '2',
    pdg: '3',
    messageGroup: '4'
  };
  static tagUserTypes = {
    patientFacing: 'patient-facing',
    staffFacing: 'staff-facing',
    staffPatientFacing: 'staff-patient-facing'
  };
  static messageEscalationBehaviour = {
    escalateFirst: 'escalate_first',
    escalateAll: 'escalate_all'
  };
  static messageForwardingBehaviourOptions = {
    removeForwarder: 'remove_forwarder',
    userPreference: 'user_preference',
    keepForwarder: 'keep_forwarder'
  };
  static platform = {
    mobile: 'mobile',
    desktop: 'desktop',
    web: 'web'
  };
  static app = 'App';
  static userProfileUpdateStatus = 40;
  /** @deprecated instead use enum UserGroup */
  static patientGroupId = 3;
  static falseAsString = 'false';
  static trueAsString = 'true';
  static patientFaceId = 2;
  static patientEnroll = 1;
  static partnerGroupId = 20;
  static webNotificationHideBehaviour = 1;
  static patientnameDisplay = 1;
  static operationSearch = 'enrollSearch';
  static associatedPatientSearch = 'associatedPatientSearch';
  static mrnChecking = 'mrnChecking';
  static getUserDetails = 'getUserDetails';
  static virtualUser = 'virtual';
  static realUser = 'real';
  static avatarImage = 'profile-pic-clinician-nurse-default.png';
  static regType = {
    0: 'Manual',
    1: 'Enroll',
    2: 'Enroll',
    3: 'CHIE',
    4: 'Import',
    6: 'User Center'
  };
  static userStatus = {
    0: 'Inactive',
    1: 'Active',
    2: 'Pending',
    7: 'Active'
  };
  static citusRoleId = '20';
  static privilegeKey = {
    allowEnrollmentInitiation: 'allowEnrollmentInitiation',
    allowDelegation: 'allowDelegation'
  };
  static inviteUserValidationFields = {
    role: 'role',
    siteId: 'siteId',
    firstName: 'firstName',
    lastName: 'lastName',
    staffId: 'staffId',
    contact: 'contact',
    dob: 'dob',
    mrnForPatients: 'mrn',
    delegatedRole: 'delegatedRole',
    password: 'password',
    confirmPassword: 'confirmPassword',
    relation: 'relation',
    userId: 'userId'
  };
  static validationPattern = {
    // tslint:disable-next-line: max-line-length
    emailPattern:
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    dateOfBirthPattern: /(0[1-9]|1[012])[/](0[1-9]|[12][0-9]|3[01])[/](19|20)\d\d/,
    phoneNumberPattern: /^\d{7,12}$/,
    activityDesPattern: /{([^\{]+)\}/,
    isHTMLTag: '<(.|\n)*?>',
    usernameDelimiters: /[.,\-_#@]/,
    oktaPasswordPattern:
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>~+\-=_'`/\\[\];’])[A-Za-z\d!@#$%^&*(),.?":{}|<>~+\-=_'`/\\[\];’\s]{9,}$/
  };
  static usernameDelimitersPattern = /[^a-zA-Z0-9]/;
  static docNamePattern = /\.[^/.]+$/;
  static minDate = '1900-01-01';
  static maxDate = '2099-12-31';
  static userPatient = 'Patient';
  static userCaregiver = 'caregiver';
  static userAlternateContact = 'relative';
  static workflowAlternateContact = 'alternate_contacts';
  static regAlternate = 'regAlternate';
  static editAlternate = 'editAlternate';
  static enabled = 'Enabled';
  static disabled = 'Disabled';
  static success = 'Success';
  static error = 'error';
  static invalidUser = 'Invalid user';
  static operationCheck = 'checking';
  static configTrue = '1';
  static configFalse = '0';
  static operationPatientCheck = 'patientChecking';
  static docSourceCam = 'Camera';
  static docSourceScan = 'Scan';
  static docSourceGallery = 'Gallery';
  static docSourceiCloud = 'iCloud';
  static docFromFile = 'filingcenter';
  static docSourceType = {
    Camera: 'Camera',
    iCloud: 'iCloud',
    filingcenter: 'filingCenter',
    photoGallery: 'Photo Gallery',
    Gallery: 'Gallery'
  };
  static stageChooseDoc = 'chooseDocument';
  static stageDocSelected = 'documentSelected';
  static stageDocView = 'viewDocument';
  static statusCode = {
    0: 'SUCCESS',
    10: 'EMAIL_EXIST',
    20: 'VALIDATION_ERROR',
    25: 'TECHNICAL_ERROR',
    30: 'ERROR',
    40: 'SERVER_ERROR'
  };
  static dateFormat = {
    mdy: 'MM/DD/YYYY',
    mmddyy: 'MM-DD-YYYY',
    dmy: 'DD-MM-YYYY',
    ymdhms: 'YYMMDDHmmssSSS',
    MMMDYYYY: 'MMM D, YYYY',
    mmddyyyy: 'MM/dd/yyyy',
    mmmmdy: 'MMMM d, y h:mm a',
    yymmdd: 'YYYYMMDD',
    hhmm: 'HH:mm',
    ymd: 'YYYY-MM-DD',
    hh: 'HH:00:00',
    hhmm0: 'HH:mm:00',
    ymd0: 'YYYY-MM-DD 00:00:00',
    hma: 'HH:mm A',
    h1ma: 'h:mm A',
    h2ma: 'hh:mm A',
    dd: 'dd',
    mmddyyhma: 'MM/DD/YYYY hh:mm A',
    ddmmhma: 'DD MMM H:mm A',
    mmddh1ma: 'MMM DD h:mm A',
    mmddh2ma: 'MMM DD hh:mm A',
    mmddhmaComma: 'MMM dd, h:mm a', // Jul 18, 12:45 PM
    hhmma: 'hh:mm A',
    yyyyMMDD: 'yyyy-MM-dd',
    mmDDYYY: 'MM-dd-yyyy',
    YYYYMMDD: 'YYYY-MM-DD',
    hhMM: 'hh:mm',
    ddMMYYYY: 'dd-MM-yyyy',
    mmmddhma: 'MMM dd, h:mm a',
    yyyMMDDT: 'YYYY-MM-DDTHH:mm:ss',
    mDY: 'YYYY-DD-MM',
    ddmmyyyyhma: 'DD-MM-YYYY hh:mm A',
    eeeddmmmDD: 'ddd MMM DD'
  };
  static submissionTypes = {
    finalSubmission: 'Full Submission - Mobile',
    partialSubmission: 'Partial Submission - Mobile'
  };
  static finalsubmissionValue = {
    finalSubmissionTrue: 'True',
    finalSubmissionFalse: 'False'
  };
  static defaultPatientSearchOptions = {
    type: 'new',
    messageText: 'MESSAGES.INVITE_PATIENT',
    buttonText: 'BUTTONS.INVITE_PATIENT_ADD_DETAILS',
    page: 'invite'
  };
  static interactionChannel = [
    { key: 'magiclink', value: 'AppLess(MagicLink)' },
    { key: 'mobileapp', value: 'In-App' }
  ];
  static defaultInteractionMethod = 'mobileapp';
  static documentPatientSearchOptions = {
    type: 'new',
    messageText: 'MESSAGES.ADD_PATIENT',
    buttonText: 'BUTTONS.ADD_PATIENT_MANUALLY',
    page: 'document'
  };
  static formPatientSearchOptions = {
    type: 'new',
    messageText: 'MESSAGES.ADD_PATIENT',
    buttonText: 'BUTTONS.ADD_PATIENT_MANUALLY',
    page: 'form'
  };
  static addUserOptions = {
    title: 'MENU.ADD_USERS',
    tabText: {
      patient: 'GENERAL.VIRTUAL_PATIENT',
      staff: 'GENERAL.VIRTUAL_STAFF',
      partner: 'GENERAL.VIRTUAL_PARTNER'
    },
    page: 'add',
    type: 'new',
    messageText: 'MESSAGES.ADD_VIRTUAL_PATIENT',
    buttonText: 'BUTTONS.ADD_PATIENT_DETAILS'
  };
  static inviteUserOptions = {
    title: 'MENU.INVITE_USERS',
    tabText: {
      patient: 'GENERAL.PATIENT',
      staff: 'GENERAL.STAFF',
      partner: 'GENERAL.PARTNER'
    }
  };
  static deletedUserRemovedStatus = 8;
  static defaultProfilePic = 'profile-pic-patient.png';
  static associateUserCount = '0';
  static associateUser = 0;
  static allowPrefilling = '1';
  static duplicateUserRegType = {
    manual: '0',
    enroll: '1',
    enrollType: '2',
    chie: '3',
    import: '4',
    userCenter: '6'
  };
  static checkWorkFlow = {
    patientFacing: 0,
    staffFacing: 1,
    practitionerFacing: 2
  };
  static inviteTab = {
    patient: 'patient',
    staff: 'staff',
    partner: 'partner'
  };
  static staffExistsStatus = {
    staffExist: 10,
    userExists: 11,
    differentTenantUser: 12
  };
  static delegatorUserType = 'delegator';
  static delegateeUserType = 'delegatee';
  static statusSuccess = 1;
  static statusFailure = 0;
  static messageHasGroup = 1;
  static messageNoGroup = 0;
  static statusUnknown = 3;
  static applessDevices = 'both';
  static signatureStateMachine = 'simple';
  static notificationSoundList = [
    { name: 'Aurora', status: true, url: 'aurora.mp3' },
    { name: 'Bell', status: true, url: 'bell.mp3' },
    { name: 'Chime', status: true, url: 'chime.mp3' },
    { name: 'Ring', status: true, url: 'ring.mp3' },
    { name: 'Glass', status: true, url: 'glass.mp3' },
    { name: 'Tritone', status: true, url: 'tritone.mp3' }
  ];
  static defaultNotificationSound = 'Glass';
  static caregiverStatus = 'active';
  static citusInteractionChannnel = 'mobileapp';
  static enrollSource = 'chwebsite';
  static enrollType = 'Enrollment';
  static patientAssociatedId = 0;
  static okValue = 'ok';
  static yesValue = 'yes';
  static noValue = 'no';
  static filterTypeMyWorkListDefault = 'me';
  static mrnMandatoryValue = '1';
  static addUserSource = 'AddUser';
  static inviteUserSource = 'inviteUser';
  static enrollFullSource = 'EnrollFull';
  static enrollPartialSource = 'EnrollPartial';
  static virtualPatient = 'virtualpatient';
  static pwa = 'PWA';
  static referralCodeStatus = 'success';
  static pixel = 'px';
  static otherValue = 'other';
  static patientValue = 'patient';
  static recipient = 'recipient';
  static associate = 'associate';
  static formSendModes = {
    sqs: 'sqs',
    api: 'api'
  };
  static formElementTypes = {
    section: 'section',
    media: 'media',
    signature: 'signature',
    text: 'text',
    textarea: 'textarea',
    file: 'file'
  };
  static formStatusDraft = 'Draft';
  static apiStatusCode = 200;
  static scheduleInterval = 30;
  static apiVersion = 'v4';
  static staffGroupId = 2;
  static clearFormTrueValue = 1;
  static clearFormFalseValue = 0;
  static receipientCount = 1;
  static filterSettingsDefault = {
    contrast: 100,
    brightness: 100,
    saturate: 100,
    greyscale: 0
  };
  static filterSettings = [
    {
      name: 'contrast',
      iconName: 'contrast-outline',
      value: 100,
      min: 0,
      max: 200
    },
    {
      name: 'brightness',
      iconName: 'sunny-outline',
      value: 100,
      min: 0,
      max: 200
    },
    {
      name: 'saturate',
      iconName: 'thermometer-outline',
      value: 100,
      min: 0,
      max: 200
    },
    {
      name: 'greyscale',
      iconName: 'invert-mode-outline',
      value: 0,
      min: 0,
      max: 200
    }
  ];
  static defaultFilter = this.filterSettings[0];
  static deepLinkingStates = {
    groupChat: 'eventmenu.group-chat',
    formsCenter: 'eventmenu.forms',
    documentCenter: 'eventmenu.obtain-sign',
    deliveryCenter: 'eventmenu.delivery-details',
    scheduleCenter: 'schedule-center'
  };
  static pushDeepLinkCategories = {
    chat: '10',
    inventory: '11',
    userApproval: '12',
    forms: '13'
  };
  static fromCallBell = 1;
  static profileImageMaxSize = 10485760;
  static elevioKey = '5e850b04459a6';
  static userTypeKey = 'user-type';
  static platformUser = 'platform-user';
  static appType = 'app-type';
  static magicLink = 'magiclink';
  static staffIdMRNMissing = 'StaffIdMRNMissing';
  static patientMRNMissing = 'PatientMRNMissing';
  static staffIdMissing = 'StaffIdMissing';
  static enrolledUser = 'Enrolled';
  static showVirtualUser = 'Virtual';
  static practitioner = 'practitioner';
  static practitionerFacingValue = 2;
  static allowEditValue = 1;
  static incoming = 'INCOMING';
  static outgoing = 'OUTGOING';
  static staffValue = 'staff';
  static timeZones = [
    { name: 'TIMEZONES.T1', value: 'Pacific/Honolulu' },
    { name: 'TIMEZONES.T2', value: 'America/Anchorage' },
    { name: 'TIMEZONES.T3', value: 'America/Los_Angeles' },
    { name: 'TIMEZONES.T4', value: 'America/Phoenix' },
    { name: 'TIMEZONES.T5', value: 'America/Denver' },
    { name: 'TIMEZONES.T6', value: 'America/Regina' },
    { name: 'TIMEZONES.T7', value: 'America/Chicago' },
    { name: 'TIMEZONES.T8', value: 'America/Jamaica' },
    { name: 'TIMEZONES.T9', value: 'America/New_York' },
    { name: 'TIMEZONES.T10', value: 'America/Halifax' },
    { name: 'TIMEZONES.T11', value: 'America/Blanc-Sablon' },
    { name: 'TIMEZONES.T12', value: 'America/St_Johns' },
    { name: 'TIMEZONES.T13', value: 'UTC' },
    { name: 'TIMEZONES.T14', value: 'Asia/Kolkata' }
  ];
  static repeatDays = [
    { value: 'SU', name: 'GENERAL.SUNDAY' },
    { value: 'MO', name: 'GENERAL.MONDAY' },
    { value: 'TU', name: 'GENERAL.TUESDAY' },
    { value: 'WE', name: 'GENERAL.WEDNESDAY' },
    { value: 'TH', name: 'GENERAL.THURSDAY' },
    { value: 'FR', name: 'GENERAL.FRIDAY' },
    { value: 'SA', name: 'GENERAL.SATURDAY' }
  ];
  static staffVisit = [
    { value: '1', name: 'GENERAL.NOT_CONFIRMED' },
    { value: '2', name: 'GENERAL.CONFIRMED' },
    { value: '3', name: 'GENERAL.DECLINED' },
    { value: '4', name: 'GENERAL.COMPLETED' },
    { value: '5', name: 'GENERAL.CANCEL_VISIT' }
  ];
  static patientVisit = [
    { value: '1', name: 'GENERAL.NOT_CONFIRMED' },
    { value: '2', name: 'GENERAL.CONFIRMED' },
    { value: '3', name: 'GENERAL.DECLINED' },
    { value: '4', name: 'GENERAL.COMPLETED' },
    { value: '5', name: 'GENERAL.CANCEL_VISIT' }
  ];
  static repeatSchedules = [
    { value: 'daily', name: 'GENERAL.DAILY', disabled: false },
    { value: 'weekly', name: 'GENERAL.WEEKLY', disabled: false },
    { value: 'monthly', name: 'GENERAL.MONTHLY', disabled: false }
  ];
  static schedulesCadence = [
    { value: 'none', name: 'GENERAL.DOES_NOT_REPEAT', disabled: false },
    { value: 'daily', name: 'GENERAL.DAILY', disabled: false },
    { value: 'weekly', name: 'GENERAL.WEEKLY', disabled: false },
    { value: 'monthly', name: 'GENERAL.MONTHLY', disabled: false }
  ];
  static availabilityTypes = [
    { value: '1', name: 'GENERAL.AVAILABLE' },
    { value: '2', name: 'GENERAL.UNAVAILABLE' },
    { value: '3', name: 'GENERAL.ON_CALL' }
  ];
  static manageScheduleFilterParams = {
    startIntervalTime: '',
    endIntervalTime: '',
    siteId: '',
    statusFilter: '',
    selectedUserId: '',
    tenantTimeZoneName: ''
  };
  static scheduleFilterParams = {
    type: 'staffs',
    pageCount: 0,
    searchKeyword: '',
    offset: 0,
    limit: 20,
    siteId: ''
  };
  static staffs = 'staffs';
  static staffCapitalized = 'Staff';
  static partners = 'partners';
  static partner = 'Partner';
  static staffPartner = 'staff-partner';
  static manage = 'manage';
  static eventClick = 'event-click';
  static datesSet = 'dates-set';
  static create = 'create';
  static add = 'add';
  static update = 'update';
  static edit = 'edit';
  static editForm = 'Edit Form';
  static submit = 'Submit';
  static delete = 'delete';
  static cancel = 'cancel';
  static myVisitStaff = 'myVisitStaff';
  static pageCount = 1;
  static offset = 20;
  static daily = 'daily';
  static weekly = 'weekly';
  static monthly = 'monthly';
  static dayOfMonth = 'dayOfMonth';
  static dayOfWeek = 'dayOfWeek';
  static none = 'none';
  static fetch = 'fetch';
  static whiteColor = '#FFFFFF';
  static flex = 'flex';
  static inlineFlex = 'inline-flex';
  static wrapReverse = 'wrap-reverse';
  static wrap = 'wrap';
  static hidden = 'hidden';
  static calendarEventPadding = '6px 4px';
  static messageSentChat = 'message-sent-chat';
  static messageReceivedChat = 'message-received-chat';
  static visitSchedule = 'visitScheduler';
  static patients = 'patients';
  static checkToken = /\#(?:id_token)\=([\S\s]*?)\&/;
  static checkCode = /\?(?:code)\=([\S\s]*?)\&/;

  static staffList = [
    {
      value: 'patients',
      title: 'Patients'
    },
    {
      value: 'staffs',
      title: 'Staffs'
    },
    {
      value: 'partners',
      title: 'Partners'
    },
    {
      value: 'resources',
      title: 'Resources'
    },
    {
      value: 'staff-partner',
      title: 'All'
    }
  ];
  static loadStaff = {
    type: '',
    pageCount: 0,
    searchKeyword: '',
    offset: 0,
    limit: 20
  };
  static visitStatusData = [
    {
      value: 'unassigned',
      name: 'Staff/Partner Unassigned'
    },
    {
      value: 'staffnotconfirmed',
      name: 'Staff Not Confirmed'
    },
    {
      value: 'patientnotconfirmed',
      name: 'Patient Not Confirmed'
    },
    {
      value: 'savedVisitsAsDraft',
      name: 'Saved Visits as Draft'
    },
    {
      value: 'staffdeclined',
      name: 'Staff Declined'
    },
    {
      value: 'patientdeclined',
      name: 'Patient Declined'
    },
    {
      value: 'visitsCompleted',
      name: 'Review Completed'
    }
  ];
  static roleId = {
    patient: '3',
    partner: '20',
    staff: ''
  };
  static roleName = {
    caregiver: 'Caregiver',
    patient: 'Patient',
    alternateContact: 'Alternate Contact'
  };
  static formStatus = {
    read: '0',
    unread: '1'
  };
  static facility = 'Facility';
  static therapyType = 'therapyType';
  static listVisitLocation = 'listVisitLocation';
  static payor = 'payor';
  static modeOfAdministration = 'modeOfAdministration';
  static staffVisitStatus = [
    { value: 0, text: 'Pending Staff/Partner Assignment', disabled: true },
    { value: 1, text: 'Not Confirmed', disabled: true },
    { value: 2, text: 'Confirmed', disabled: true },
    { value: 3, text: 'Declined', disabled: true },
    { value: 4, text: 'Completed', disabled: true },
    { value: 5, text: 'Cancel Visit', disabled: true }
  ];
  static staffVisitCompleted = 4;
  static patientVisitStatus = [
    { value: 1, text: 'Not Confirmed', disabled: true },
    { value: 2, text: 'Confirmed', disabled: true },
    { value: 3, text: 'Declined', disabled: true },
    { value: 5, text: 'Cancel Visit', disabled: true },
    { value: 4, text: 'Completed', disabled: true }
  ];
  static formTypes = {
    pending: 'pending-forms',
    completed: 'completed-forms',
    archived: 'archived-forms',
    draft: 'draft-forms',
    offline: 'offline-forms'
  };
  static deliveryTypes = {
    pending: 'pending-delivery',
    completed: 'completed-delivery'
  };
  static InProgress = 'InProgress';
  static completed = 'completed';
  static courierDateTime = 'courierDateTime';
  static forms = 'forms';
  static materials = 'materials';
  static documents = 'documents';
  static delivery = 'delivery';
  static enableMultiSite = '1';
  static whValue = 'wh';
  static siteDetailsConfigValues = [
    { configKey: 'chat_end_time' },
    { configKey: 'chat_end_period' },
    { configKey: 'working_hour' },
    { configKey: 'default_escalation_users' },
    { configKey: 'default_clinician_roles_available' },
    { configKey: 'staff_escalation_members' },
    { configKey: 'member_roles_for_pdg_which_create_automatically' },
    { configKey: 'no_clinician_message_on_working_hours' },
    { configKey: 'new_patient_chat_welcome_message' },
    { configKey: 'chat_start_time' },
    { configKey: 'chat_start_period' },
    { configKey: 'default_outgoing_filing_center_faxq' },
    { configKey: 'default_outgoing_filing_center_notification' },
    { configKey: 'default_outgoing_filing_center_directlinking' },
    { configKey: 'default_outgoing_filing_center_progressnote' },
    { configKey: 'default_outgoing_filing_center_phi' },
    { configKey: 'default_outgoing_filing_center_formdiscretedata' },
    { configKey: 'site_external_system_integration' },
    { configKey: 'default_inbound_fc' },
    { configKey: 'app_short_link' },
    { configKey: 'enable_support_widget_branding' },
    { configKey: 'support_widget_from_email' },
    { configKey: 'app_name' },
    { configKey: 'SMTP_domain' },
    { configKey: 'support_widget_email_color_code' },
    { configKey: 'recipient_email_for_support_widget' },
    { configKey: 'realm_key' }
  ];
  static repeatIntervals = [
    { value: '1' },
    { value: '2' },
    { value: '3' },
    { value: '4' },
    { value: '5' },
    { value: '6' },
    { value: '7' },
    { value: '8' },
    { value: '9' },
    { value: '10' },
    { value: '11' },
    { value: '12' },
    { value: '13' },
    { value: '14' },
    { value: '15' },
    { value: '16' },
    { value: '17' },
    { value: '18' },
    { value: '19' },
    { value: '20' }
  ];
  static day = 'day';
  static weeks = 'weeks';
  static months = 'months';
  static weeklySchedule = [
    { value: 'SU', name: 'Sunday', selected: false, disabled: false },
    { value: 'MO', name: 'Monday', selected: false, disabled: false },
    { value: 'TU', name: 'Tuesday', selected: false, disabled: false },
    { value: 'WE', name: 'Wednesday', selected: false, disabled: false },
    { value: 'TH', name: 'Thursday', selected: false, disabled: false },
    { value: 'FR', name: 'Friday', selected: false, disabled: false },
    { value: 'SA', name: 'Saturday', selected: false, disabled: false }
  ];
  static monthlySchedule = [
    { value: 'dayOfMonth', name: 'Day of Month' },
    { value: 'dayOfWeek', name: 'Day of Week' }
  ];
  static aic = 'AIC';
  static home = 'Home';
  static viewAvilabilitystaffList = [
    {
      value: 'staffs',
      title: 'Staffs'
    },
    {
      value: 'partners',
      title: 'Partners'
    }
  ];
  static clientConnected = 'MESSAGES.CLIENT_CONNECTED_FORM';
  static visitAssignedTo = 'visitAssignedTo';
  static visitDeclinedStatus = '3';
  static successStatus = '1';
  static passwordStatus = { true: '1', false: '' };
  static messageFilterOptions = [
    { id: 'all', name: 'OPTIONS.ALL', value: 'all' },
    { id: 'user', name: 'OPTIONS.USER', value: '1' },
    { id: 'system', name: 'OPTIONS.SYSTEM', value: '0' }
  ];
  static removeHtmlTags = [
    { value: /<img[^>]*alt="([^"]+)"[^>]*>/gi, replaceWith: '$1' },
    { value: /\n|\r/g, replaceWith: '' },
    { value: /<br[^>]*>/gi, replaceWith: '\n' },
    { value: /(?:<(?:div|p|ol|ul|li|pre|code|object)[^>]*>)+/gi, replaceWith: '<div>' },
    { value: /(?:<\/(?:div|p|ol|ul|li|pre|code|object)>)+/gi, replaceWith: '</div>' },
    { value: /\n<div><\/div>/g, replaceWith: '\n' },
    { value: /<div><\/div>\n/gi, replaceWith: '\n' },
    { value: /(?:<div>)+<\/div>/gi, replaceWith: '\n' },
    { value: /([^\n])<\/div><div>/gi, replaceWith: '$1\n' },
    { value: /(?:<\/div>)+/gi, replaceWith: '</div>' },
    { value: /([^\n])<\/div>([^\n])/gi, replaceWith: '$1\n$2' },
    { value: /<\/div>/gi, replaceWith: '' },
    { value: /([^\n])<div>/gi, replaceWith: '$1\n' },
    { value: /\n<div>/gi, replaceWith: '\n' },
    { value: /<div>\n/gi, replaceWith: '\n\n' },
    { value: /<(?:[^>]+)?>/g, replaceWith: '' },
    { value: new RegExp('&#8203;', 'g'), replaceWith: '' },
    { value: /&nbsp;/g, replaceWith: ' ' },
    { value: /&lt;/g, replaceWith: '<' },
    { value: /&gt;/g, replaceWith: '>' },
    { value: /&quot;/g, replaceWith: '"' },
    { value: /&#x27;/g, replaceWith: "'" },
    { value: /&#x60;/g, replaceWith: '`' },
    { value: /&amp;/g, replaceWith: '&' }
  ];
  static addressType = {
    home: Constants.home,
    main: 'Main',
    others: 'Others',
    shipping: 'Shipping'
  };
  static myVisitPatient = 'myVisitPatient';
  static new = 'new';
  static extToMimes = {
    gif: 'image/gif',
    png: 'image/png',
    jpg: 'image/jpg',
    jpeg: 'image/jpeg',
    mpeg: 'video/jpeg',
    bmp: 'image/bmp',
    mpe: 'video/mpe',
    avi: 'video/x-msvideo',
    mp4: 'video/mp4',
    mov: 'video/quicktime',
    pdf: 'application/pdf',
    doc: 'application/msword',
    dot: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    txt: 'text/plain',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    csv: 'text/csv'
  };
  static base64Prefix = 'data:image/jpeg;base64,';
  static no = 'No';
  static yes = 'Yes';
  static enablePatientConfirmationVisitTypeValue = '0';
  static sendPatientNotificationValue = '1';
  static initialStaffConfirmation = '0';
  static break = '<br>';
  static staffFacingValue = 1;
  static iframeHeight = 'mf_iframe_height';
  static scrollTopformsubmit = 'scrollTopformsubmit';
  static loaderOnSubmitForm = 'loaderOnSubmitForm';
  static hideFormSubmitLoader = 'hideFormSubmitLoader';
  static iframeLanguage = 'mf_iframe_language_code';
  static iframePageTitle = 'mf_iframe_page_title';
  static enrolledPatient = 'Enrolled Patient';
  static virtual = 'Virtual Patient';
  static statusUpdate = 'status update';
  static invite = 'invite';
  static activate = 'activate';
  static inActivate = 'inactivate';
  static schedules = [
    { id: 'Patient Not Confirmed', name: 'Patient Not Confirmed' },
    { id: 'Patient Confirmed', name: 'Patient Confirmed' },
    { id: 'Patient Canceled', name: 'Patient Canceled' },
    { id: 'Patient Declined', name: 'Patient Declined' },
    { id: 'Staff Not Confirmed', name: 'Staff Not Confirmed' },
    { id: 'Staff Confirmed', name: 'Staff Confirmed' },
    { id: 'Staff Declined', name: 'Staff Declined' },
    { id: 'Saved As Drafts', name: 'Saved As Drafts' },
    { id: 'Review Completed', name: 'Review Completed' }
  ];
  static staffStatus = [
    { id: '1', name: 'Visitation Title' },
    { id: '2', name: 'Not Confirmed' },
    { id: '3', name: 'Confirmed' },
    { id: '4', name: 'Canceled' },
    { id: '5', name: 'Declined' },
    { id: '6', name: 'Completed' },
    { id: '7', name: 'Saved As Drafts' }
  ];
  static fileSize = '25 MB';
  static maxFileSizeByte = '26214400';
  static alternateContactStatus = {
    inActivate: '1',
    activate: '2',
    delete: '3'
  };
  static availability = [
    { id: 'available', name: 'Available', isHide: false },
    { id: 'unavailable', name: 'Unavailable', isHide: false },
    { id: 'oncall', name: 'On Call', isHide: false }
  ];

  static myAvailability = 'myAvailability';
  static otherStaff = 'otherStaff';
  static myVisit = 'myVisit';
  static allVisit = 'allVisit';
  static userPayload = {
    type: '',
    pageCount: 0,
    searchKeyword: '',
    offset: -1,
    limit: -1
  };
  static enterKeyCode = 13;
  static emojiFilter = [
    {
      name: 'Smileys & Emotion',
      image: 'assets/images/emojis-filter/smileys.png'
    },
    {
      name: 'Animals & Nature',
      image: 'assets/images/emojis-filter/animals.png'
    },
    {
      name: 'Food & Drink',
      image: 'assets/images/emojis-filter/food.png'
    },
    {
      name: 'Travel & Places',
      image: 'assets/images/emojis-filter/travel.png'
    },
    {
      name: 'Activities',
      image: 'assets/images/emojis-filter/activities.png'
    },
    {
      name: 'Objects',
      image: 'assets/images/emojis-filter/objects.png'
    },
    {
      name: 'Symbols',
      image: 'assets/images/emojis-filter/symbols.png'
    },
    {
      name: 'Flags',
      image: 'assets/images/emojis-filter/flags.png'
    }
  ];
  static userWarning = `<div class="warn-img"><img src="../../../../../assets/images/warning-sign.png"></div> <br/>
    <div class="warn-text">{{userWarning}}</div>`;
  static userWarningReplaceWith = '{{userWarning}}';
  static emailMobileVerified = {
    verified: 1,
    notVerified: 2
  };
  static alternateStatus = '1';
  static newMessageAckOnPollingStatus = {
    success: 'success',
    retry: 'retry',
    failedAfterRetry: 'failed after retry'
  };
  static formRecipients = '1';
  static checkFileExtensionType = ['pngimage', 'jpgimage'];
  static documentTagTypeValue = 1;
  static administratorId = 0;
  static mockVisit = {
    VisitSTatus: 'Deleted',
    color: '#CE1AEB',
    end: '2022-07-13 12:30:00',
    end_date: '2022-07-13 01:30:00',
    eventVisitTitle: 'Testvisit',
    id: '0001',
    locationTimezone: 'Asia/Kolkata',
    myVisitFor: 'myVisitPatient',
    patientName: 'Patient5304 User',
    patientVisitStatus: 'Declined',
    resourceName: '',
    staffName: 'Demo Staff 4',
    staffVisitStatus: 'Confirmed',
    start: '2022-07-13 10:30:00',
    start_date: '2022-07-13 10:30:00',
    tasks: '',
    therapyTypes: 'test ttherapy',
    title: 'Hm: User,Patient5304',
    visitId: '1011',
    visitLocName: 'USA',
    visitTitle: 'Sample Visit (Deleted)',
    visitTypeName: 'Home'
  };
  static deletedVisit = 'Deleted';
  static apiVersions = { apiV4: 'v4', apiV5: 'v5' };
  static noParticipents = 0;
  static active = 'active';
  static Forms = 'Forms';
  static staffFacing = 'staffFacing';
  static userFirsrtLoginStatus = 6;
  static enableSupportWidgetBrandingValue = '1';
  static emptySubmissionId = 0;
  static checkScheduleExist = 'checkScheduleExist';
  static otherStaffAvailability = 2;
  static selfStaffAvailability = 1;
  static viewAvailabilityParam = {
    siteId: '',
    userId: '',
    statusFilter: '',
    startIntervalTime: '',
    endIntervalTime: '',
    tenantTimeZoneName: '',
    startTime: '',
    endTime: ''
  };
  static editType = { single: 1, series: 2 };
  static open = 'open';
  static type = { string: 'string', object: 'object' };
  static visitScheduleAllowedFileFormats = ['pdf', 'doc', 'docx', 'word', 'xl', 'xls', 'xlsx', 'odt', 'jpg', 'JPG', 'jpeg', 'JPEG', 'png', 'PNG'];
  static deleteAll = 'all';
  static deleteSingle = 'single';
  static pollingAckReload = 'reload';
  static pollingAckStatus = {
    retry: 'retry',
    failedAfterRetry: 'failed after retry',
    unknown: 'unknown',
    failedWithoutRetry: 'failed without retry'
  };
  static startOfDay = '00:00:00';
  static endOfDay = '23:59:00';
  static confirm = 'confirm';
  static decline = 'decline';
  static defaultSiteId = '0';
  static isBillable = '1';
  static isSubContracted = '1';
  static startNewChatButton = {
    iconCustom: false,
    type: 'new-chat',
    buttonType: 'ion-button',
    buttonIcon: 'chatbox-ellipses',
    colorTheme: 'de-york',
    customClass: 'setBottomFabButton'
  };
  static uploadedVisitFileNameDefaultSubstringLength = 48;
  static controlNames = {
    startDate: 'startDate',
    startTime: 'startTime',
    endDate: 'endDate',
    endTime: 'endTime',
    reset: ''
  };
  static specialChar = /[&\/\\#,+()$~%'":*?<>{} ]/g;
  static checkSpace = '%20';
  static fetchMessageAction = 'loadmessage';
  static pushDefaultForeground = 'PushDefaultForeground';
  static brandedApps = {
    /* update host urls when they are ready */
    'mobile.soleoconnect.com': {
      tenantId: 531,
      title: 'Soleo Connect Portal',
      alias: 'soleoconnect',
      theme: 'soleoconnect',
      appName: 'Soleo Connect',
      serverUrlCustom: 'https://portal.soleoconnect.com'
      // machFormUrl: 'https://forms.soleoconnect.com/'
    },
    'mobile.vitalcare.com': {
      tenantId: 711,
      title: 'Vital Care Connect Portal',
      alias: 'vitalcare',
      theme: 'vitalcare',
      appName: 'Vital Care Connect',
      serverUrlCustom: 'https://portal.vitalcare.com'
      // machFormUrl: 'https://forms.vitalcare.com/'
    },
    'mobile.performspecialty.com': {
      tenantId: 149,
      title: 'PerformSpecialty Portal',
      alias: 'performspecialty',
      appName: 'PerformSpecialty',
      serverUrlCustom: 'https://portal.performspecialty.com'
      // machFormUrl: 'https://forms.performspecialty.com/'
    },
    'mobile.kennebecpharmacy.com': {
      tenantId: 776,
      title: 'Kennebec Pharmacy Connect',
      alias: 'kennebecpharmacy',
      theme: 'kennebecpharmacy',
      appName: 'Kennebec Pharmacy Connect',
      serverUrlCustom: 'https://portal.kennebecpharmacy.com'
      // machFormUrl: 'https://forms.kennebecpharmacy.com/'
    },
    'mobile.realospecialtycare.com': {
      tenantId: 802,
      title: 'RealoCares',
      alias: 'realocares',
      theme: 'realocares',
      appName: 'RealoCares',
      serverUrlCustom: 'https://portal.realospecialtycare.com'
      // machFormUrl: 'https://forms.realospecialtycare.com/'
    },
    'mobile.anovorx.com': {
      tenantId: 1010,
      title: 'MyAnovo',
      alias: 'myanovo',
      theme: 'myanovo',
      appName: 'MyAnovo',
      serverUrlCustom: 'https://portal.anovorx.com'
      // machFormUrl: 'https://forms.anovorx.com/'
    },
    'mobile.matrixcare.com': {
      tenantId: 413,
      title: 'Link',
      alias: 'matrixcare',
      theme: 'matrixcare',
      appName: 'Link'
    },
    'mobile.kabafusion.com': {
      title: 'myKabaFusion Portal',
      tenantId: 372,
      alias: 'kabafusion',
      theme: 'kabafusion',
      appName: 'myKabaFusion'
    }
  };
  static countTypes = {
    all: 'messages,forms',
    messages: 'messages',
    forms: 'forms'
  };
  static production = 'Production';
  static invalidDate = 'Invalid date';
  static relation_type = '1';
  static checkboxLabelValue = 'checkboxLabelValue';
  static textValue = 'textValue';
  static videoCallSessionExpiredStatus = '2';
  static defaultTimerValue = 1;
  static connecting = 'connecting';
  static siteConfigs = {
    defaultBranchStartTime: '9:0',
    defaultBranchEndTime: '6:0'
  };
  static displayNamePatientCaregiverFormat = 'Patient(Caregiver)';
  static loginSuccessSocket = 'loginSuccess';
  static signatureRequestActionType = {
    associated: 'Associated',
    onBehalf: 'OnBehalf',
    recipients: 'Recipients'
  };
  static pageType = {
    recipients: 'recipients'
  };
  static fontSize = {
    minimumFont: 5,
    maxFont: 14,
    mobileMaxFontSize: 6,
    fontSizeRation: 0.0172,
    androidMobileLetterSpacing: '-0.6px'
  };
  static paletteHeight = {
    max: 17,
    min: 13
  };
  static lineHeight = {
    minLneHeight: '0.8vw',
    mobileLineHeightiOS: '1.25',
    mobileLineHeightAndroid: '1',
    webLineHeight: 'normal'
  };
  static saveAsDraftMaxRetry = 3; // No of attempts
  static saveAsDraftMaxRetryOffset = 3; // no of seconds to wait before next attempt.
  static appUsageEvent = {
    appId: 'callbell',
    eventName: 'first-login',
    eventDescription: 'user first login',
    eventDateTime: ''
  };
  static eventSourceComponent = {
    applessVideo: 'Appless Video',
    chatPage: 'Message Center Chat',
    scheduleCenterChat: 'Schedule Center Chat',
    resendFailed: 'Failed Message Resend'
  };
  static acceptCall = 'acceptCall';
  static inAppAddOnFromPath = {
    session: 'session',
    message: 'message',
    draft: 'draft',
    toast: 'toast',
    videoCall: 'videoCall'
  };
  static storageKeys = {
    isVirtual: 'isVirtual',
    authToken: 'authToken',
    vidyoToken: 'vidyoToken',
    organization: 'organization',
    showBanner: 'showBanner',
    touchIdKey: 'touchIdKey',
    touchIdOktaAuth: 'touchIdOktaAuth',
    idpAppAuthLink: 'idp_app_auth_link',
    idpEmailDomainName: 'idp_email_domain_name',
    idpEmail: 'idp_email',
    recentEmojis: 'recentEmojis',
    deviceInfo: 'deviceInfo',
    deviceID: 'deviceID',
    failedMessages: 'failedMessages',
    brandConfig: 'brandConfig',
    userPushDeviceRegistration: 'userPushDeviceRegistration',
    appConfig: 'appConfig',
    localConfig: 'localConfig',
    routeHistory: 'routeHistory',
    applessRememberDays: 'applessRememberDays',
    applessDocTokenVerified: 'applessDocTokenVerified',
    dateRangeFilterForms: 'dateRangeFilterForms',
    dateRangeFilterFormsDateOptions: 'dateRangeFilterFormsDateOptions',
    selectedSites: 'selectedSites',
    deviceLocationMessage: 'disabledDeviceLocationMesssage',
    searchPatientActivityHub: 'searchPatientActivityHub',
    persistentData: 'persistentData',
    searchMyFormWorkCompleted: 'searchMyFormWorkListCompleted',
    searchMyFormWorkPending: 'searchMyFormWorkListPending',
    searchMyFormWorkArchived: 'searchMyFormWorkListArchived',
    searchMyFormWorkDraft: 'searchMyFormWorkListdraft',
    searchMyDocCompleted: 'searchMyDocCompleted',
    searchMyDocPending: 'searchMyDocPending',
    searchMyDocArchived: 'searchMyDocArchived',
    dateRangeFilterMyDocuments: 'dateRangeFilterMyDocuments',
    dateRangeSelectedDateOptionsMyDocuments: 'dateRangeSelectedDateOptionsMyDocuments',
    dateRangeFilterMyForms: 'dateRangeFilterMyForms',
    dateRangeSelectedDateOptionsMyForms: 'dateRangeSelectedDateOptionsMyFormss',
    oktaLogin: 'oktaLogin',
    oktaTokenExpiry: 'oktaTokenExpiry',
    oktaTokenStorage: 'okta-token-storage',
    searchTextActiveMessages: 'searchTextActiveMessages',
    searchTextArchivedMessages: 'searchTextArchivedMessages',
    dateRangeFilterActiveMessages: 'dateRangeFilterActiveMessages',
    dateRangeFilterArchivedMessages: 'dateRangeFilterActivedMessages',
    dateRangeSelectedDateOptionsActiveMessages: 'dateRangeSelectedDateOptionsActiveMessages',
    dateRangeSelectedDateOptionsArchivedMessages: 'dateRangeSelectedDateOptionsArchivedMessages',
    searchTextEducationMaterials: 'searchTextEducationMaterials',
    activeMessageFilterKeys: 'activeMessageFilterKeys',
    myVisitFilterKeys: 'myVisitFilterKeys',
    allVisitFilterKeys: 'allVisitFilterKeys',
    myAvailabilityFilterKeys: 'myAvailabilityFilterKeys',
    rememberUsername: 'rememberUsername',
    otherStaffAvailabilityFilterKeys: 'otherStaffAvailabilityFilterKeys',
    appLessHomeLoggedIn: 'appLessHomeLoggedIn'
  };
  static oneDayInMilliSeconds = 86400000;
  static unknown = 'Unknown';
  static numericPattern = /^\d+$/;
  static BackspaceKeyCode = 8;
  static ArrowRightKeyCode = 39;
  static ArrowLeftKeyCode = 37;
  static KeyA = 'KeyA';
  static KeyC = 'KeyC';
  static KeyV = 'KeyV';
  static KeyX = 'KeyX';
  static numericPatternText = '[0-9]*';
  static isGranted = {
    yes: 1,
    no: 0
  };
  static deviceOrientation = {
    portrait: 'portrait',
    landscapePrimary: 'landscape-primary' // 90 degree rotation
  };
  static saveAsDocumentDisplay = 'save-as-document-display';
  static copyDocumentPageName = {
    documentCenter: 'document-center',
    formCenter: 'form-center'
  };
  static chatRoomActionType = {
    forward: 'Forward',
    invite: 'Invite'
  };
  static viewAvailabilitySegmentData = {
    calendar: 'calendar',
    list: 'list'
  };
  static userAvailability = {
    available: 'available',
    unavailable: 'unavailable',
    oncall: 'oncall'
  };
  static filterOptions = {
    all: 'DATE_FILTER.LABELS.ALL',
    lastMonth: 'DATE_FILTER.LABELS.LAST_MONTH',
    lastThreeMonth: 'DATE_FILTER.LABELS.LAST_THREE_MONTH',
    lastSixMonth: 'DATE_FILTER.LABELS.LAST_SIX_MONTH',
    custom: 'DATE_FILTER.LABELS.CUSTOM'
  };
  static filterSelectedOptions = {
    all: 0,
    lastMonth: 1,
    custom: 2,
    lastThreeMonth: 3,
    lastSixMonth: 4
  };
  static monthRange = {
    lastMonth: 30,
    lastThreeMonth: 90,
    lastSixMonth: 6
  };
  static monthRangeType = {
    lastMonth: <moment.unitOfTime.DurationConstructor>'days',
    lastThreeMonth: <moment.unitOfTime.DurationConstructor>'days',
    lastSixMonth: <moment.unitOfTime.DurationConstructor>'months'
  };
  static monthTypes = {
    default: ['all', 'lastMonth', 'custom'],
    messages: ['lastSixMonth', 'lastMonth', 'custom'],
    admissions: ['all', 'lastThreeMonth', 'custom']
  };
  static resetSelectedDateRange = { from: '', to: '' };
  static canVasHeightAndWidth = {
    canvasWidth: 530,
    canvasHeight: 180,
    maxCanvasWidth: 550
  };
  static userLabel = '{displayName} {dob} {identityValue} {userType} {siteName}';
}
export enum SkeletonLoaderTypes {
  CHAT = 1,
  LIST = 2
}
export enum AssetSource {
  CMIS = 'cmis',
  SCHEDULE_CENTER = 'schedule-center'
}

export enum VisitScheduleStatus {
  REVIEW_COMPLETED = 'Review Completed'
}

export enum MessagePriority {
  HIGH = 1,
  NORMAL = 2,
  LOW = 3
}

export enum UserGroup {
  PATIENT = 3,
  PARTNER = 20
}
export enum OooStatusColor {
  outOfOffice = '#f9081a',
  messageOnly = '#818080'
}

export enum DateFormat {
  YYMMDD_FORMAT_HYPHEN = 'YYYY-MM-DD',
  YYMMDD_FORMAT_SLASH = 'YYYY/MM/DD',
  DDMMYY_FORMAT_HYPHEN = 'DD-MM-YYYY',
  DDMMYY_FORMAT_SLASH = 'DD/MM/YYYY',
  MMDDYY_FORMAT_HYPHEN = 'MM-DD-YYYY',
  MMDDYY_FORMAT_SLASH = 'MM/DD/YYYY',
  TIME_24_HR = 'HH:mm:ss',
  MMDDYY_FORMAT_SLASH_TIME_24_HR = 'MM/DD/YYYY HH:mm:ss'
}

export enum CHECKED {
  TRUE = '1',
  FALSE = '0'
}

export enum DeepLinkTypes {
  VISIT = 'visit'
}
export enum CHAR_CODES {
  SPACE = 32,
  NBSP = 160,
  NO_BREAK = 8288
}
export declare type UseBaseAs = 'default' | 'visit-schedule' | 'visit-schedule-root' | 'forms';

export enum UseBaseTypes {
  DEFAULT = 'default',
  VISIT_SCHEDULE = 'visit-schedule',
  VISIT_SCHEDULE_ROOT = 'visit-schedule-root',
  FORMS = 'forms'
}
export enum ChatWithTypes {
  PATIENT = 'patient',
  STAFF = 'staff',
  PARTNER = 'partner',
  MESSAGE_GROUP = 'messageGroup',
  PATIENT_GROUP = 'patientGroup',
  ROLE = 'role'
}
export enum RecipientIdType {
  ROLE = 'roleId',
  MESSAGE_GROUP = 'groupId',
  DEFAULT = 'userId'
}
export enum TagType {
  THREAD = 'thread',
  MESSAGE = 'message'
}
export enum MessageDeliveryStatus {
  DELIVERED = 0,
  READ = 1,
  ALL = -1
}
export enum MessageCategory {
  GENERAL = 'general',
  BROADCAST = 'broadcast',
  MASKED = 'masked',
  PDG = 'pdg',
  MESSAGE_GROUP = 'message-group',
  PATIENT_INITIATED = 'patient-staff',
  STAFF_TO_PATIENT = 'staff-patient',
  STAFF_TO_STAFF = 'staff-staff',
}
export enum AvatarPaths {
  PATIENT = 'profile-pic-patient.png',
  CLINICIAN = 'profile-pic-clinician.png',
  BROADCAST = 'assets/icon/chat/broadcast.png',
  MASKED = 'assets/icon/chat/masked-message-icon.png',
  PDG = 'assets/icon/chat/pdg-group-icon.png',
  MESSAGE_GROUP = 'assets/icon/chat/msg-group-icon.png'
}
export const fallbackImage = 'assets/images/file-404.png';
export enum ParticipantType {
  ROLE = 'role',
  USER = 'user'
}
export enum OfflineFormStatus {
  DRAFT = 'Draft',
  COMPLETED = 'Completed',
  ACTIVE = 'ACTIVE',
  SUBMITTED = 'Submitted',
  SYNCED = 'Synced',
  NOT_SYNCED = 'Not Synced'
}
export enum AdmissionRecordStatus {
  DELETED = 0,
  ACTIVE = 1,
  INACTIVE = 2
}

export enum IntegrationType {
  FORM_ARCHIVE = 'formArchive',
  ADD_MESSAGE_TAG = 'addMessageTag',
  SEND_TO_EHR = 'sendToEHR',
  DOCUMENT_SUBMIT = 'documentSubmit',
  FORM_SUBMIT = 'formSubmit'
}

export const chatThreadTypes = [
  {
    label: 'PDG',
    id: 3
  },
  {
    label: 'Message Group',
    id: 4
  },
  {
    label: 'Masked',
    id: 2
  },
  {
    label: 'Broadcast',
    id: 1
  },
  {
    label: 'Patient Initiated',
    id: 5
  },
  {
    label: 'Staff To Patient',
    id: 6
  },
  {
    label: 'Staff To Staff',
    id: 7
  }
];
export const userType = {
  staff: 1,
  partner: 2,
  patient: 3,
  alternateContact: 4,
  caregiver: 5
};
export const messageTypeAndCategory = {
  general: 0,
  broadcast: 1,
  masked: 2,
  pdg: 3,
  'message-group': 4,
  'patient-staff': 5,
  'staff-patient': 6,
  staff: 7
};
export enum FolderType {
  fillingCenter = 'fillingCenter',
  documentFolder = 'documentFolder'
}
export const msgHistoryDateTimeFormat = 'MM/DD/YYYY hh:mm A';
