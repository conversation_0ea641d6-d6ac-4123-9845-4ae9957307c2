export class Socket {
  static joinToChatroom = 'joinToChatroom';
  static messagePollingAck = 'messagePollingAck';
  static messagePollingNewThreadAck = 'messagePollingNewThreadAck';
  static lastUserActivity = 'lastUserActivity';
  static userMessage = 'userMessage';
  static leaveChatRoom = 'leaveChatRoom';
  static userLeave = 'userLeave';
  static messagePolling = 'messagePolling';
  static joinToApp = 'joinToApp';
  static forseDisconnect = 'forseDisconnect';
  static bannerAlertWithPolling = 'BannerAlertWithPolling';
  static userMessagetoServer = 'userMessagetoServer';
  static signatureFromUser = 'signatureFromUser';
  static removesignatureFromUser = 'removesignatureFromUser';
  static removeSignature = 'removeSignature';
  static userTyping = 'userTyping';
  static typing = 'typing';
  static updateUserMessage = 'updateUserMessage';
  static updateUserMessageReadStatus = 'updateUserMessageReadStatus';
  static confirmReviewPushNotification = 'confirmReviewPushNotification';
  static signatureToUser = 'signatureToUser';
  static videoChatInitiate = 'videoChatInitiate';
  static videoChatToUser = 'videoChatToUser';
  static videoChatReject = 'videoChatReject';
  static exitVideoChat = 'exitVideoChat';
  static enrollmentUserLogin = 'enrollmentUserLogin';
  static videoChatRejectFromUser = 'videoChatRejectFromUser';
  static allUsersNotAccept = 'allUsersNotAccept';
  static userBusy = 'userBusy';
  static onGoingVideoCall = 'onGoingVideoCall';
  static userAccpetYourCall = 'userAccpetYourCall';
  static userJoinedToRoom = 'userJoinedToRoom';
  static userJoinedToVideoChat = 'userJoinedToVideoChat';
  static userLeftRoom = 'userLeftRoom';
  static busyUser = 'busyUser';
  static videoChatJoin = 'videoChatJoin';
  static updateConfigPolling = 'updateConfigPolling';
  static pushNotifyToUser = 'pushNotifyToUser';
  static saveAsDraft = 'saveAsDraft';
  static activityTrack = 'activityTrack';
  static obtainSignPollingToServer = 'obtainSignPollingToServer';
  static obtainSignResendPollingToServer = 'obtainSignResendPollingToServer';
  static reminderForDocument = 'ReminderForDocument';
  static submissionId = 'submissionId';
  static sendFormsToRecipients = 'sendFormsToRecipients';
  static formPolling = 'formPolling';
  static obtainSignPolling = 'obtainSignPolling';
  static formAllowEditPolling = 'formAllowEditPolling';
  static disconnect = 'disconnect';
  static isConnected = 'isConnected';
  static polling = 'polling';
  static websocket = 'websocket';
  static hello = 'hellow';
  static verifyEmailOrMobile = 'verifyEmailOrMobile';
  static verifyEmailOrMobileSend = 'verifyEmailOrMobileSend';
  static sendrecpResponse = 'Sendrecpresponse';
  static submittedData = 'submittedData';
  static userPushDeviceRegistration = 'userPushDeviceRegistration';
  static applessLinkAlreadyInUse = 'applessLinkAlreadyInUse';
  static updateChatMessageDeleteStatus = 'updateChatMessageDeleteStatus';
  static updateMessageTags = 'updateMessageTags';
  static updateChatroomParticipants = 'updateChatroomParticipants';
}
