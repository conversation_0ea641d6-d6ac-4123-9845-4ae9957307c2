export class Urls {
  static writableFilePath = '/cometchat/writable/filetransfer/uploads/';
  static documentSignaturePath = '/document/signatureRequest/images/';
  static signUp = 'wp-json/api/wp/v2/signup';
  static profilePicPath = '/cometchat/citus-health/avatars/';
  static avatarUrlThumbs = '/cometchat/citus-health/avatars/thumbs/';
  static vidyoMainSource =
    'https://static.vidyo.io/latest/javascript/VidyoClient/VidyoClient.js?onload=onVidyoClientLoaded&webrtc=true&plugin=false&webrtcLogLevel=infol';
  static generateVidyoTocken = 'generateVidyoTocken';
  static noImage = 'assets/images/no-avatar.png';
  static videocall = 'assets/audio/videocall.mp3';
  static calltone = 'assets/audio/calltone.mp3';
  static audioSrc = 'assets/audio/';
  static createAWSPlatformUserEndpoint = 'createAWSPlatformUserEndpoint';
  static cmisAPI = '/api/cmis/';
  static https = 'https://';
  static documentSignedpdfPath = '/document/signatureRequest/signedpdf/';
  static imagePlaceholder = 'assets/images/placeholder-image.jpg';
}
