export class VideoCall {
  static vidyoMainSource = 'vidyoMainSource';
  static empty = 'empty';
  static occupied = 'occupied';
  static videoClass = {
    success: 'vid-btn vid-btn-success',
    danger: 'vid-btn vid-btn-danger',
    conformCallNotify: 'common-video video_call_notify',
    commonSweetAlert: 'common-sweet sweet'
  };
  static audios = {
    busy: 'busy.mp3',
    noUser: 'nouser.mp3',
    callTone: 'calltone.mp3',
    videoCall: 'videocall.mp3'
  };
  static vidyo = 'vidyo';
  static topbar = 'topbar';
  static viewStyleDefault = 'VIDYO_CONNECTORVIEWSTYLE_Default';
  static logFileFilter =
    'warning all@VidyoConnector info@VidyoClient debug@VidyoClient debug@VidyoSDP debug@VidyoResourceManager all@VidyoSignaling';
  static logFileFilterIos = 'debug@VidyoClient debug@VidyoConnector info warning';
  static remote = 'remote';
  static onLeft = 'onLeft';
  static failedRemoteSlot = 'FAILED_REMOTE_SLOT';
  static loaded = 'Loaded';
  static incomingCall = 'incoming call';
  static local = 'local';
  static muted = 'Muted';
  static unMuted = 'Unmuted';
  static default = 'default';
  static forCall = 'forcall';
  static forcallPlay = 'forcallPlay';
  static forcallPause = 'forcallPause';
  static vcLat = 36.778259;
  static vcLong = -119.417931;
  static vidyoClient = {
    ready: 'vidyoClient:ready',
    fail: 'vidyoClient:fail',
    videoChanged: 'vidyoClient:videoChanged',
    rendered: 'vidyoClient:rendered',
    error: 'vidyoClientError',
    videoUpdateds: 'videoUpdateds',
    vidyoEventCallback: 'VidyoEventCallback'
  };
  static notAccept = 'notAccept';
  static initiator = 'initiator';
  static user = 'user';
  static endCall = 'endcall';
  static participant = 'participant';
  static joinViaNotification = 'has joined via notification';
  static initiated = 'initiated';
  static joinTo = 'joined to';
  static warning = 'warning';
  static conformNotify = 'confrom-notify';
  static alertMsgId = 'waiting-msg-alert';
  static onOnlineUsers = 'onOnlineUsers';
  static rendererViewId = 'renderer';
  static fullScreenViewId = 'fullscreen';
  static emiDataAction = 'initiator';
  static openRemoteSlots = 'OPEN_REMOTE_SLOT';
  static vidyoHost = 'https://citushealth.platform.vidyo.io';
  static maxParticipants = 5;
  static allUsersNotAccept = 'allUsersNotAccept';
  static sdkLoadConfirmed = {
    ready: 'READY',
    retrying: 'RETRYING',
    failed: 'FAILED',
    failedversion: 'FAILEDVERSION',
    notAvailable: 'NOTAVAILABLE',
    description: 'Native XMPP + WebRTC'
  };
  static javaScript = 'text/javascript';
  static script = 'script';
  static loadedDynamically = 'loaded';
  static completeDynamically = 'complete';
  static alreadyLoaded = 'Already Loaded';
  static head = 'head';
  static rendererSlots = ['1'];
  static ringWaitTime = 60000;
  static generateVidyoSource = 'login';
  static displayStyle = {
    block: 'block',
    none: 'none',
    display: 'display'
  };
  static vidyoFailure = {
    invalidToken: 'VIDYO_CONNECTORFAILREASON_InvalidToken',
    miscLocalError: 'VIDYO_CONNECTORFAILREASON_MiscLocalError',
    miscErrorDisconnect: 'VIDYO_CONNECTORDISCONNECTREASON_MiscError',
    miscRemoteError: 'VIDYO_CONNECTORFAILREASON_MiscRemoteError',
    miscError: 'VIDYO_CONNECTORFAILREASON_MiscError',
    connectionLost: 'VIDYO_CONNECTORDISCONNECTREASON_ConnectionLost',
    connectionTimeout: 'VIDYO_CONNECTORDISCONNECTREASON_ConnectionTimeout',
    noResponse: 'VIDYO_CONNECTORDISCONNECTREASON_NoResponse',
    terminated: 'VIDYO_CONNECTORDISCONNECTREASON_Terminated',
    connectionFailed: 'VIDYO_CONNECTORFAILREASON_ConnectionFailed',
    invalidResourceId: 'VIDYO_CONNECTORFAILREASON_InvalidResourceId',
    disConnectReason4: 'VCConnectorDisconnectReason(rawValue: 4)',
    disConnectReason1: 'VCConnectorDisconnectReason(rawValue: 1)'
  };

  static retry = 'retry';
  static disconnect = 'disconnect';
  static success = 'success';
  static toastCss = {
    postionTop: 'top',
    danger: 'danger'
  };
  static maxRetryCount = 5;
  static videoRetryCount = 0;
  static sendAppLessAction = {
    initiatorLeft: 'initiatorLeft',
    userLeft: 'userLeft',
    participantJoin: 'participantJoin',
    participantLeft: 'participantLeft'
  };
  static leftStatus = '2';
  static noRemoteParticipant = 'no-remote-participant';
  static videoIosSDKLoad = {
    init: 'init',
    connected: 'connected',
    disconnected: 'disconnected',
    failed: 'failed',
    participant: 'participant'
  };
  static iosVideoCallPrivacy = {
    camera: 'camera',
    microphone: 'microphone'
  };
  static audioUniqueKey = {
    videoCall: 'videoCall',
    noUser: 'noUser',
    busyUser: 'busyUser',
    forCallPlay: 'forCallPlay'
  };
}
