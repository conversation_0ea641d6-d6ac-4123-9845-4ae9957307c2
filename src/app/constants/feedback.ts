export class Feedback {
  static subject = 'Mobile App';
  static subjectPrefix = 'CH';
  static accountLogo = 'https://assets.citushealth.com/a/89/img/account-logo-on-white-bg.png';
  static citusHealthImage = 'https://portal.citushealth.com/cometchat/images/citus-health-mail.png';
  static teamNameTenantIdPresent = 'WellSky';
  static teamNameTenantIdAbsent = 'CitusHealth';
  static emailTitle = 'Feedback Confirmation';
  static supportMail = '<EMAIL>';
  static supportMailTest = '<EMAIL>';
  static status = 'Unresolved';
  static support = 'Support';
  static salutation = 'Hi Dear';
  static salutationFormal = 'Hi';
  static releaseStage = 'Demo';
  static tenantId = [89, 121, 124, 141, 142];
  static confirmationStatus = 1;
  static contactPhone = 'Phone';
  static feedbackType = [
    'Report A Problem',
    'Request A Feature',
    'Ask A Question',
    'Give Praise',
    'Share An Idea',
    'Other Comments'
  ];
  static contactMode = [
    { key: 'Email', value: 'By Email' },
    { key: 'Phone', value: 'By Phone' }
  ];

  static defaultFeedbackType = 'Ask A Question';
  static defaultFeedbackMethod = 'Email';
  static fileFolder = 'feedback-widget';
  static headerColor = '#399f9c';
  static textColor = '#16665a';
  static colorLabel = 'color:colorCode;';
  static borderLabel = 'border-bottom-color: colorCode;';
}
