export class Buttons {
  static restore = {
    label: 'BUTTONS.RESTORE',
    icon: 'refresh',
    action: 'restore',
    slot: 'top',
    class: 'restore',
    permission: '',
    id: 'refresh',
    show: true
  };
  static resend = {
    label: 'BUTTONS.RESEND',
    icon: 'refresh',
    action: 'resend',
    slot: 'top',
    class: 'resend',
    permission: '',
    id: 'resend',
    show: true
  };
  static reminder = {
    label: 'BUTTONS.REMINDER',
    icon: 'time',
    action: 'reminder',
    slot: 'top',
    class: 'resend',
    permission: '',
    id: 'reminder',
    show: true
  };
  static cancel = {
    label: 'BUTTONS.CANCEL',
    icon: 'close-circle',
    action: 'cancel',
    slot: 'top',
    class: 'cancel',
    permission: '',
    id: 'cancel',
    show: true
  };
  static archive = {
    label: 'BUTTONS.ARCHIVE',
    icon: 'trash',
    action: 'archive',
    slot: 'top',
    class: 'archive',
    permission: '',
    id: 'archive',
    show: true
  };
  static cancelDraft = {
    label: 'BUTTONS.CANCEL',
    icon: 'close-circle',
    action: 'cancelDraft',
    slot: 'top',
    class: 'cancel',
    permission: '',
    id: 'cancel-draft',
    show: true
  };
  static delete = {
    label: 'BUTTONS.DELETE',
    icon: 'trash',
    action: 'delete',
    slot: 'top',
    class: 'archive',
    permission: '',
    id: 'delete',
    show: true
  };
  static inactivate = {
    label: 'BUTTONS.INACTIVATE',
    icon: 'ban',
    action: 'inactivate',
    slot: 'top',
    class: 'archive',
    permission: '',
    id: 'inactivate',
    show: true
  };
  static invite = {
    label: 'BUTTONS.INVITE',
    icon: 'add',
    action: 'invite',
    slot: 'top',
    class: 'archive',
    permission: '',
    id: 'invite',
    show: true
  };
  static activate = {
    label: 'BUTTONS.ACTIVATE',
    icon: 'checkmark',
    action: 'activate',
    slot: 'top',
    class: 'archive',
    permission: '',
    id: 'activate',
    show: true
  };
}
