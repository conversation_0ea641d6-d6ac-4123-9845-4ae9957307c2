export const emojiJson = {
    'Smile<PERSON> & Emotion': [
        {
            emoji: '😀',
            skin_tone_support: false,
            name: 'grinning_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😃',
            skin_tone_support: false,
            name: 'grinning_face_with_big_eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😄',
            skin_tone_support: false,
            name: 'grinning_face_with_smiling_eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😁',
            skin_tone_support: false,
            name: 'beaming_face_with_smiling_eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😆',
            skin_tone_support: false,
            name: 'grinning_squinting_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😅',
            skin_tone_support: false,
            name: 'grinning_face_with_sweat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤣',
            skin_tone_support: false,
            name: 'rolling_on_the_floor_laughing',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '😂',
            skin_tone_support: false,
            name: 'face_with_tears_of_joy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙂',
            skin_tone_support: false,
            name: 'slightly_smiling_face',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙃',
            skin_tone_support: false,
            name: 'upside_down_face',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😉',
            skin_tone_support: false,
            name: 'winking_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😊',
            skin_tone_support: false,
            name: 'smiling_face_with_smiling_eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😇',
            skin_tone_support: false,
            name: 'smiling_face_with_halo',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥰',
            skin_tone_support: false,
            name: 'smiling_face_with_hearts',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '😍',
            skin_tone_support: false,
            name: 'smiling_face_with_heart_eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤩',
            skin_tone_support: false,
            name: 'star_struck',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '😘',
            skin_tone_support: false,
            name: 'face_blowing_a_kiss',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😗',
            skin_tone_support: false,
            name: 'kissing_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☺️',
            skin_tone_support: false,
            name: 'smiling_face',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😚',
            skin_tone_support: false,
            name: 'kissing_face_with_closed_eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😙',
            skin_tone_support: false,
            name: 'kissing_face_with_smiling_eyes',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😋',
            skin_tone_support: false,
            name: 'face_savoring_food',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😛',
            skin_tone_support: false,
            name: 'face_with_tongue',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😜',
            skin_tone_support: false,
            name: 'winking_face_with_tongue',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤪',
            skin_tone_support: false,
            name: 'zany_face',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '😝',
            skin_tone_support: false,
            name: 'squinting_face_with_tongue',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤑',
            skin_tone_support: false,
            name: 'money_mouth_face',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤗',
            skin_tone_support: false,
            name: 'hugging_face',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤭',
            skin_tone_support: false,
            name: 'face_with_hand_over_mouth',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🤫',
            skin_tone_support: false,
            name: 'shushing_face',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🤔',
            skin_tone_support: false,
            name: 'thinking_face',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤐',
            skin_tone_support: false,
            name: 'zipper_mouth_face',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤨',
            skin_tone_support: false,
            name: 'face_with_raised_eyebrow',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '😐',
            skin_tone_support: false,
            name: 'neutral_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😑',
            skin_tone_support: false,
            name: 'expressionless_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😶',
            skin_tone_support: false,
            name: 'face_without_mouth',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😏',
            skin_tone_support: false,
            name: 'smirking_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😒',
            skin_tone_support: false,
            name: 'unamused_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙄',
            skin_tone_support: false,
            name: 'face_with_rolling_eyes',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😬',
            skin_tone_support: false,
            name: 'grimacing_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🤥',
            skin_tone_support: false,
            name: 'lying_face',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '😌',
            skin_tone_support: false,
            name: 'relieved_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😔',
            skin_tone_support: false,
            name: 'pensive_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😪',
            skin_tone_support: false,
            name: 'sleepy_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤤',
            skin_tone_support: false,
            name: 'drooling_face',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '😴',
            skin_tone_support: false,
            name: 'sleeping_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😷',
            skin_tone_support: false,
            name: 'face_with_medical_mask',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤒',
            skin_tone_support: false,
            name: 'face_with_thermometer',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤕',
            skin_tone_support: false,
            name: 'face_with_head_bandage',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤢',
            skin_tone_support: false,
            name: 'nauseated_face',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤮',
            skin_tone_support: false,
            name: 'face_vomiting',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🤧',
            skin_tone_support: false,
            name: 'sneezing_face',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥵',
            skin_tone_support: false,
            name: 'hot_face',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🥶',
            skin_tone_support: false,
            name: 'cold_face',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🥴',
            skin_tone_support: false,
            name: 'woozy_face',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '😵',
            skin_tone_support: false,
            name: 'dizzy_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤯',
            skin_tone_support: false,
            name: 'exploding_head',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🤠',
            skin_tone_support: false,
            name: 'cowboy_hat_face',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥳',
            skin_tone_support: false,
            name: 'partying_face',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '😎',
            skin_tone_support: false,
            name: 'smiling_face_with_sunglasses',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤓',
            skin_tone_support: false,
            name: 'nerd_face',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧐',
            skin_tone_support: false,
            name: 'face_with_monocle',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '😕',
            skin_tone_support: false,
            name: 'confused_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😟',
            skin_tone_support: false,
            name: 'worried_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🙁',
            skin_tone_support: false,
            name: 'slightly_frowning_face',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☹️',
            skin_tone_support: false,
            name: 'frowning_face',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😮',
            skin_tone_support: false,
            name: 'face_with_open_mouth',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😯',
            skin_tone_support: false,
            name: 'hushed_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😲',
            skin_tone_support: false,
            name: 'astonished_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😳',
            skin_tone_support: false,
            name: 'flushed_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥺',
            skin_tone_support: false,
            name: 'pleading_face',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '😦',
            skin_tone_support: false,
            name: 'frowning_face_with_open_mouth',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😧',
            skin_tone_support: false,
            name: 'anguished_face',
            unicode_version: '6.1',
            emoji_version: '2.0'
        },
        {
            emoji: '😨',
            skin_tone_support: false,
            name: 'fearful_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😰',
            skin_tone_support: false,
            name: 'anxious_face_with_sweat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😥',
            skin_tone_support: false,
            name: 'sad_but_relieved_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😢',
            skin_tone_support: false,
            name: 'crying_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😭',
            skin_tone_support: false,
            name: 'loudly_crying_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😱',
            skin_tone_support: false,
            name: 'face_screaming_in_fear',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😖',
            skin_tone_support: false,
            name: 'confounded_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😣',
            skin_tone_support: false,
            name: 'persevering_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😞',
            skin_tone_support: false,
            name: 'disappointed_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😓',
            skin_tone_support: false,
            name: 'downcast_face_with_sweat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😩',
            skin_tone_support: false,
            name: 'weary_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😫',
            skin_tone_support: false,
            name: 'tired_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥱',
            skin_tone_support: false,
            name: 'yawning_face',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '😤',
            skin_tone_support: false,
            name: 'face_with_steam_from_nose',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😡',
            skin_tone_support: false,
            name: 'pouting_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😠',
            skin_tone_support: false,
            name: 'angry_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤬',
            skin_tone_support: false,
            name: 'face_with_symbols_on_mouth',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '😈',
            skin_tone_support: false,
            name: 'smiling_face_with_horns',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👿',
            skin_tone_support: false,
            name: 'angry_face_with_horns',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💀',
            skin_tone_support: false,
            name: 'skull',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☠️',
            skin_tone_support: false,
            name: 'skull_and_crossbones',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '💩',
            skin_tone_support: false,
            name: 'pile_of_poo',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤡',
            skin_tone_support: false,
            name: 'clown_face',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👹',
            skin_tone_support: false,
            name: 'ogre',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👺',
            skin_tone_support: false,
            name: 'goblin',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👻',
            skin_tone_support: false,
            name: 'ghost',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👽',
            skin_tone_support: false,
            name: 'alien',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👾',
            skin_tone_support: false,
            name: 'alien_monster',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤖',
            skin_tone_support: false,
            name: 'robot',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😺',
            skin_tone_support: false,
            name: 'grinning_cat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😸',
            skin_tone_support: false,
            name: 'grinning_cat_with_smiling_eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😹',
            skin_tone_support: false,
            name: 'cat_with_tears_of_joy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😻',
            skin_tone_support: false,
            name: 'smiling_cat_with_heart_eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😼',
            skin_tone_support: false,
            name: 'cat_with_wry_smile',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😽',
            skin_tone_support: false,
            name: 'kissing_cat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙀',
            skin_tone_support: false,
            name: 'weary_cat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😿',
            skin_tone_support: false,
            name: 'crying_cat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '😾',
            skin_tone_support: false,
            name: 'pouting_cat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙈',
            skin_tone_support: false,
            name: 'see_no_evil_monkey',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙉',
            skin_tone_support: false,
            name: 'hear_no_evil_monkey',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙊',
            skin_tone_support: false,
            name: 'speak_no_evil_monkey',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💋',
            skin_tone_support: false,
            name: 'kiss_mark',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💌',
            skin_tone_support: false,
            name: 'love_letter',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💘',
            skin_tone_support: false,
            name: 'heart_with_arrow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💝',
            skin_tone_support: false,
            name: 'heart_with_ribbon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💖',
            skin_tone_support: false,
            name: 'sparkling_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💗',
            skin_tone_support: false,
            name: 'growing_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💓',
            skin_tone_support: false,
            name: 'beating_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💞',
            skin_tone_support: false,
            name: 'revolving_hearts',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💕',
            skin_tone_support: false,
            name: 'two_hearts',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💟',
            skin_tone_support: false,
            name: 'heart_decoration',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '❣️',
            skin_tone_support: false,
            name: 'heart_exclamation',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '💔',
            skin_tone_support: false,
            name: 'broken_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '❤️',
            skin_tone_support: false,
            name: 'red_heart',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🧡',
            skin_tone_support: false,
            name: 'orange_heart',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '💛',
            skin_tone_support: false,
            name: 'yellow_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💚',
            skin_tone_support: false,
            name: 'green_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💙',
            skin_tone_support: false,
            name: 'blue_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💜',
            skin_tone_support: false,
            name: 'purple_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤎',
            skin_tone_support: false,
            name: 'brown_heart',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🖤',
            skin_tone_support: false,
            name: 'black_heart',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤍',
            skin_tone_support: false,
            name: 'white_heart',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '💯',
            skin_tone_support: false,
            name: 'hundred_points',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💢',
            skin_tone_support: false,
            name: 'anger_symbol',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💥',
            skin_tone_support: false,
            name: 'collision',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💫',
            skin_tone_support: false,
            name: 'dizzy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💦',
            skin_tone_support: false,
            name: 'sweat_droplets',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💨',
            skin_tone_support: false,
            name: 'dashing_away',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕳️',
            skin_tone_support: false,
            name: 'hole',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💣',
            skin_tone_support: false,
            name: 'bomb',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💬',
            skin_tone_support: false,
            name: 'speech_balloon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👁️‍🗨️',
            skin_tone_support: false,
            name: 'eye_in_speech_bubble',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗨️',
            skin_tone_support: false,
            name: 'left_speech_bubble',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗯️',
            skin_tone_support: false,
            name: 'right_anger_bubble',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💭',
            skin_tone_support: false,
            name: 'thought_balloon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💤',
            skin_tone_support: false,
            name: 'zzz',
            unicode_version: '6.0',
            emoji_version: '2.0'
        }
    ],
    'People & Body': [
        {
            emoji: '👋',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'waving_hand',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤚',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'raised_back_of_hand',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🖐️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'hand_with_fingers_splayed',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '✋',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'raised_hand',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖖',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'vulcan_salute',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👌',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'ok_hand',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤏',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'pinching_hand',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '✌️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'victory_hand',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🤞',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'crossed_fingers',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤟',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'love_you_gesture',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🤘',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'sign_of_the_horns',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤙',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'call_me_hand',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👈',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'backhand_index_pointing_left',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👉',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'backhand_index_pointing_right',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👆',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'backhand_index_pointing_up',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖕',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'middle_finger',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👇',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'backhand_index_pointing_down',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☝️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'index_pointing_up',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '👍',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'thumbs_up',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👎',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'thumbs_down',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '✊',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'raised_fist',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👊',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'oncoming_fist',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤛',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'left_facing_fist',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤜',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'right_facing_fist',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👏',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'clapping_hands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙌',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'raising_hands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👐',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'open_hands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤲',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'palms_up_together',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🤝',
            skin_tone_support: false,
            name: 'handshake',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙏',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'folded_hands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '✍️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'writing_hand',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '💅',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'nail_polish',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤳',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'selfie',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💪',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'flexed_biceps',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦾',
            skin_tone_support: false,
            name: 'mechanical_arm',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🦿',
            skin_tone_support: false,
            name: 'mechanical_leg',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🦵',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'leg',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦶',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'foot',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '👂',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'ear',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦻',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'ear_with_hearing_aid',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👃',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'nose',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧠',
            skin_tone_support: false,
            name: 'brain',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🦷',
            skin_tone_support: false,
            name: 'tooth',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦴',
            skin_tone_support: false,
            name: 'bone',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '👀',
            skin_tone_support: false,
            name: 'eyes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👁️',
            skin_tone_support: false,
            name: 'eye',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👅',
            skin_tone_support: false,
            name: 'tongue',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👄',
            skin_tone_support: false,
            name: 'mouth',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👶',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'baby',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧒',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'child',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '👦',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👧',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'girl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧑',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'person',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '👱',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_blond_hair',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧔',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'man_beard',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '👨‍🦰',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'man_red_hair',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '👨‍🦱',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'man_curly_hair',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '👨‍🦳',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'man_white_hair',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '👨‍🦲',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'man_bald',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '👩',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍🦰',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'woman_red_hair',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧑‍🦰',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'person_red_hair',
            unicode_version: '11.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👩‍🦱',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'woman_curly_hair',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧑‍🦱',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'person_curly_hair',
            unicode_version: '11.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👩‍🦳',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'woman_white_hair',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧑‍🦳',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'person_white_hair',
            unicode_version: '11.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👩‍🦲',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'woman_bald',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧑‍🦲',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'person_bald',
            unicode_version: '11.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👱‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_blond_hair',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👱‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_blond_hair',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧓',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'older_person',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '👴',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'old_man',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👵',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'old_woman',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙍',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_frowning',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙍‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_frowning',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙍‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_frowning',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙎',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_pouting',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙎‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_pouting',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙎‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_pouting',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙅',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_gesturing_no',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙅‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_gesturing_no',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙅‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_gesturing_no',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙆',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_gesturing_ok',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙆‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_gesturing_ok',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙆‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_gesturing_ok',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💁',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_tipping_hand',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💁‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_tipping_hand',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💁‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_tipping_hand',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙋',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_raising_hand',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙋‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_raising_hand',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙋‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_raising_hand',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧏',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'deaf_person',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧏‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'deaf_man',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧏‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'deaf_woman',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🙇',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_bowing',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🙇‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_bowing',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🙇‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_bowing',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤦',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'person_facepalming',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤦‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'man_facepalming',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤦‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'woman_facepalming',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤷',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'person_shrugging',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤷‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'man_shrugging',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤷‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'woman_shrugging',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍⚕️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'health_worker',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍⚕️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_health_worker',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍⚕️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_health_worker',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🎓',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'student',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🎓',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_student',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🎓',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_student',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🏫',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'teacher',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🏫',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_teacher',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🏫',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_teacher',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍⚖️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'judge',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍⚖️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_judge',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍⚖️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_judge',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🌾',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'farmer',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🌾',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_farmer',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🌾',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_farmer',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🍳',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'cook',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🍳',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_cook',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🍳',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_cook',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🔧',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'mechanic',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🔧',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_mechanic',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🔧',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_mechanic',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🏭',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'factory_worker',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🏭',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_factory_worker',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🏭',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_factory_worker',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍💼',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'office_worker',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍💼',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_office_worker',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍💼',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_office_worker',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🔬',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'scientist',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🔬',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_scientist',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🔬',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_scientist',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍💻',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'technologist',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍💻',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_technologist',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍💻',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_technologist',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🎤',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'singer',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🎤',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_singer',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🎤',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_singer',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🎨',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'artist',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🎨',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_artist',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🎨',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_artist',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍✈️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'pilot',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍✈️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_pilot',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍✈️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_pilot',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🚀',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'astronaut',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🚀',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_astronaut',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🚀',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_astronaut',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧑‍🚒',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'firefighter',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🚒',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_firefighter',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍🚒',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_firefighter',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👮',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'police_officer',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👮‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_police_officer',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👮‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_police_officer',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🕵️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'detective',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕵️‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_detective',
            unicode_version: '7.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🕵️‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_detective',
            unicode_version: '7.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💂',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'guard',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💂‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_guard',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💂‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_guard',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👷',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'construction_worker',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👷‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_construction_worker',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👷‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_construction_worker',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤴',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'prince',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👸',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'princess',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👳',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_wearing_turban',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👳‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_wearing_turban',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👳‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_wearing_turban',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👲',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_with_skullcap',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧕',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'woman_with_headscarf',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🤵',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'man_in_tuxedo',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👰',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'bride_with_veil',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤰',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'pregnant_woman',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤱',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'breast_feeding',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '👼',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'baby_angel',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎅',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'santa_claus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤶',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'mrs_claus',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦸',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'superhero',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦸‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'man_superhero',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦸‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'woman_superhero',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦹',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'supervillain',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦹‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'man_supervillain',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦹‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '11.0',
            name: 'woman_supervillain',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧙',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'mage',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧙‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'man_mage',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧙‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'woman_mage',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧚',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'fairy',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧚‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'man_fairy',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧚‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'woman_fairy',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧛',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'vampire',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧛‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'man_vampire',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧛‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'woman_vampire',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧜',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'merperson',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧜‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'merman',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧜‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'mermaid',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧝',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'elf',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧝‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'man_elf',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧝‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'woman_elf',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧞',
            skin_tone_support: false,
            name: 'genie',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧞‍♂️',
            skin_tone_support: false,
            name: 'man_genie',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧞‍♀️',
            skin_tone_support: false,
            name: 'woman_genie',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧟',
            skin_tone_support: false,
            name: 'zombie',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧟‍♂️',
            skin_tone_support: false,
            name: 'man_zombie',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧟‍♀️',
            skin_tone_support: false,
            name: 'woman_zombie',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '💆',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_getting_massage',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💆‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_getting_massage',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💆‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_getting_massage',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💇',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_getting_haircut',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💇‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_getting_haircut',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💇‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_getting_haircut',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚶',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_walking',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚶‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_walking',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚶‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_walking',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧍',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'person_standing',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧍‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'man_standing',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧍‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'woman_standing',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧎',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'person_kneeling',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧎‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'man_kneeling',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧎‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'woman_kneeling',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧑‍🦯',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'person_with_probing_cane',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🦯',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'man_with_probing_cane',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👩‍🦯',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'woman_with_probing_cane',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧑‍🦼',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'person_in_motorized_wheelchair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🦼',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'man_in_motorized_wheelchair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👩‍🦼',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'woman_in_motorized_wheelchair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧑‍🦽',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'person_in_manual_wheelchair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👨‍🦽',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'man_in_manual_wheelchair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👩‍🦽',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '12.0',
            name: 'woman_in_manual_wheelchair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🏃',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_running',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏃‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_running',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏃‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_running',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '💃',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_dancing',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕺',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'man_dancing',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🕴️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_in_suit_levitating',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👯',
            skin_tone_support: false,
            name: 'people_with_bunny_ears',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👯‍♂️',
            skin_tone_support: false,
            name: 'men_with_bunny_ears',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👯‍♀️',
            skin_tone_support: false,
            name: 'women_with_bunny_ears',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧖',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'person_in_steamy_room',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧖‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'man_in_steamy_room',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧖‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'woman_in_steamy_room',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧗',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'person_climbing',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧗‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'man_climbing',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧗‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'woman_climbing',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🤺',
            skin_tone_support: false,
            name: 'person_fencing',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏇',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'horse_racing',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛷️',
            skin_tone_support: false,
            name: 'skier',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🏂',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'snowboarder',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏌️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_golfing',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏌️‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_golfing',
            unicode_version: '7.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏌️‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_golfing',
            unicode_version: '7.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏄',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_surfing',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏄‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_surfing',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏄‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_surfing',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚣',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_rowing_boat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚣‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_rowing_boat',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚣‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_rowing_boat',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏊',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_swimming',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏊‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_swimming',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏊‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_swimming',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '⛹️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_bouncing_ball',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⛹️‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_bouncing_ball',
            unicode_version: '5.2',
            emoji_version: '4.0'
        },
        {
            emoji: '⛹️‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_bouncing_ball',
            unicode_version: '5.2',
            emoji_version: '4.0'
        },
        {
            emoji: '🏋️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_lifting_weights',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏋️‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_lifting_weights',
            unicode_version: '7.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏋️‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_lifting_weights',
            unicode_version: '7.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚴',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_biking',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚴‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_biking',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚴‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_biking',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚵',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_mountain_biking',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚵‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'man_mountain_biking',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚵‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_mountain_biking',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤸',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'person_cartwheeling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤸‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'man_cartwheeling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤸‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'woman_cartwheeling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤼',
            skin_tone_support: false,
            name: 'people_wrestling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤼‍♂️',
            skin_tone_support: false,
            name: 'men_wrestling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤼‍♀️',
            skin_tone_support: false,
            name: 'women_wrestling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤽',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'person_playing_water_polo',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤽‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'man_playing_water_polo',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤽‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'woman_playing_water_polo',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤾',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'person_playing_handball',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤾‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'man_playing_handball',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤾‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'woman_playing_handball',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤹',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'person_juggling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤹‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'man_juggling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🤹‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '9.0',
            name: 'woman_juggling',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧘',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'person_in_lotus_position',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧘‍♂️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'man_in_lotus_position',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧘‍♀️',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'woman_in_lotus_position',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🛀',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_taking_bath',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛌',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'person_in_bed',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧑‍🤝‍🧑',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '10.0',
            name: 'people_holding_hands',
            unicode_version: '10.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👭',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'women_holding_hands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👫',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'woman_and_man_holding_hands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👬',
            skin_tone_support: true,
            skin_tone_support_unicode_version: '8.0',
            name: 'men_holding_hands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💏',
            skin_tone_support: false,
            name: 'kiss',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍❤️‍💋‍👨',
            skin_tone_support: false,
            name: 'kiss_woman_man',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍❤️‍💋‍👨',
            skin_tone_support: false,
            name: 'kiss_man_man',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍❤️‍💋‍👩',
            skin_tone_support: false,
            name: 'kiss_woman_woman',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💑',
            skin_tone_support: false,
            name: 'couple_with_heart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍❤️‍👨',
            skin_tone_support: false,
            name: 'couple_with_heart_woman_man',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍❤️‍👨',
            skin_tone_support: false,
            name: 'couple_with_heart_man_man',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍❤️‍👩',
            skin_tone_support: false,
            name: 'couple_with_heart_woman_woman',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👪',
            skin_tone_support: false,
            name: 'family',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👩‍👦',
            skin_tone_support: false,
            name: 'family_man_woman_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👩‍👧',
            skin_tone_support: false,
            name: 'family_man_woman_girl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👩‍👧‍👦',
            skin_tone_support: false,
            name: 'family_man_woman_girl_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👩‍👦‍👦',
            skin_tone_support: false,
            name: 'family_man_woman_boy_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👩‍👧‍👧',
            skin_tone_support: false,
            name: 'family_man_woman_girl_girl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👨‍👦',
            skin_tone_support: false,
            name: 'family_man_man_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👨‍👧',
            skin_tone_support: false,
            name: 'family_man_man_girl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👨‍👧‍👦',
            skin_tone_support: false,
            name: 'family_man_man_girl_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👨‍👦‍👦',
            skin_tone_support: false,
            name: 'family_man_man_boy_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👨‍👧‍👧',
            skin_tone_support: false,
            name: 'family_man_man_girl_girl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍👩‍👦',
            skin_tone_support: false,
            name: 'family_woman_woman_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍👩‍👧',
            skin_tone_support: false,
            name: 'family_woman_woman_girl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍👩‍👧‍👦',
            skin_tone_support: false,
            name: 'family_woman_woman_girl_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍👩‍👦‍👦',
            skin_tone_support: false,
            name: 'family_woman_woman_boy_boy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👩‍👩‍👧‍👧',
            skin_tone_support: false,
            name: 'family_woman_woman_girl_girl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👨‍👦',
            skin_tone_support: false,
            name: 'family_man_boy',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👨‍👦‍👦',
            skin_tone_support: false,
            name: 'family_man_boy_boy',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👨‍👧',
            skin_tone_support: false,
            name: 'family_man_girl',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👨‍👧‍👦',
            skin_tone_support: false,
            name: 'family_man_girl_boy',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👨‍👧‍👧',
            skin_tone_support: false,
            name: 'family_man_girl_girl',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍👦',
            skin_tone_support: false,
            name: 'family_woman_boy',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍👦‍👦',
            skin_tone_support: false,
            name: 'family_woman_boy_boy',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍👧',
            skin_tone_support: false,
            name: 'family_woman_girl',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍👧‍👦',
            skin_tone_support: false,
            name: 'family_woman_girl_boy',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '👩‍👧‍👧',
            skin_tone_support: false,
            name: 'family_woman_girl_girl',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🗣️',
            skin_tone_support: false,
            name: 'speaking_head',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👤',
            skin_tone_support: false,
            name: 'bust_in_silhouette',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👥',
            skin_tone_support: false,
            name: 'busts_in_silhouette',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👣',
            skin_tone_support: false,
            name: 'footprints',
            unicode_version: '6.0',
            emoji_version: '2.0'
        }
    ],
    'Animals & Nature': [
        {
            emoji: '🐵',
            skin_tone_support: false,
            name: 'monkey_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐒',
            skin_tone_support: false,
            name: 'monkey',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦍',
            skin_tone_support: false,
            name: 'gorilla',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦧',
            skin_tone_support: false,
            name: 'orangutan',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🐶',
            skin_tone_support: false,
            name: 'dog_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐕',
            skin_tone_support: false,
            name: 'dog',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦮',
            skin_tone_support: false,
            name: 'guide_dog',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🐕‍🦺',
            skin_tone_support: false,
            name: 'service_dog',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🐩',
            skin_tone_support: false,
            name: 'poodle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐺',
            skin_tone_support: false,
            name: 'wolf',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦊',
            skin_tone_support: false,
            name: 'fox',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦝',
            skin_tone_support: false,
            name: 'raccoon',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🐱',
            skin_tone_support: false,
            name: 'cat_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐈',
            skin_tone_support: false,
            name: 'cat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦁',
            skin_tone_support: false,
            name: 'lion',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐯',
            skin_tone_support: false,
            name: 'tiger_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐅',
            skin_tone_support: false,
            name: 'tiger',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐆',
            skin_tone_support: false,
            name: 'leopard',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐴',
            skin_tone_support: false,
            name: 'horse_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐎',
            skin_tone_support: false,
            name: 'horse',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦄',
            skin_tone_support: false,
            name: 'unicorn',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦓',
            skin_tone_support: false,
            name: 'zebra',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🦌',
            skin_tone_support: false,
            name: 'deer',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🐮',
            skin_tone_support: false,
            name: 'cow_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐂',
            skin_tone_support: false,
            name: 'ox',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐃',
            skin_tone_support: false,
            name: 'water_buffalo',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐄',
            skin_tone_support: false,
            name: 'cow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐷',
            skin_tone_support: false,
            name: 'pig_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐖',
            skin_tone_support: false,
            name: 'pig',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐗',
            skin_tone_support: false,
            name: 'boar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐽',
            skin_tone_support: false,
            name: 'pig_nose',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐏',
            skin_tone_support: false,
            name: 'ram',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐑',
            skin_tone_support: false,
            name: 'ewe',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐐',
            skin_tone_support: false,
            name: 'goat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐪',
            skin_tone_support: false,
            name: 'camel',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐫',
            skin_tone_support: false,
            name: 'two_hump_camel',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦙',
            skin_tone_support: false,
            name: 'llama',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦒',
            skin_tone_support: false,
            name: 'giraffe',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🐘',
            skin_tone_support: false,
            name: 'elephant',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦏',
            skin_tone_support: false,
            name: 'rhinoceros',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦛',
            skin_tone_support: false,
            name: 'hippopotamus',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🐭',
            skin_tone_support: false,
            name: 'mouse_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐁',
            skin_tone_support: false,
            name: 'mouse',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐀',
            skin_tone_support: false,
            name: 'rat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐹',
            skin_tone_support: false,
            name: 'hamster',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐰',
            skin_tone_support: false,
            name: 'rabbit_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐇',
            skin_tone_support: false,
            name: 'rabbit',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐿️',
            skin_tone_support: false,
            name: 'chipmunk',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦔',
            skin_tone_support: false,
            name: 'hedgehog',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🦇',
            skin_tone_support: false,
            name: 'bat',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🐻',
            skin_tone_support: false,
            name: 'bear',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐨',
            skin_tone_support: false,
            name: 'koala',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐼',
            skin_tone_support: false,
            name: 'panda',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦥',
            skin_tone_support: false,
            name: 'sloth',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🦦',
            skin_tone_support: false,
            name: 'otter',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🦨',
            skin_tone_support: false,
            name: 'skunk',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🦘',
            skin_tone_support: false,
            name: 'kangaroo',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦡',
            skin_tone_support: false,
            name: 'badger',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🐾',
            skin_tone_support: false,
            name: 'paw_prints',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦃',
            skin_tone_support: false,
            name: 'turkey',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐔',
            skin_tone_support: false,
            name: 'chicken',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐓',
            skin_tone_support: false,
            name: 'rooster',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐣',
            skin_tone_support: false,
            name: 'hatching_chick',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐤',
            skin_tone_support: false,
            name: 'baby_chick',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐥',
            skin_tone_support: false,
            name: 'front_facing_baby_chick',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐦',
            skin_tone_support: false,
            name: 'bird',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐧',
            skin_tone_support: false,
            name: 'penguin',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕊️',
            skin_tone_support: false,
            name: 'dove',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦅',
            skin_tone_support: false,
            name: 'eagle',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦆',
            skin_tone_support: false,
            name: 'duck',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦢',
            skin_tone_support: false,
            name: 'swan',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦉',
            skin_tone_support: false,
            name: 'owl',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦩',
            skin_tone_support: false,
            name: 'flamingo',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🦚',
            skin_tone_support: false,
            name: 'peacock',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦜',
            skin_tone_support: false,
            name: 'parrot',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🐸',
            skin_tone_support: false,
            name: 'frog',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐊',
            skin_tone_support: false,
            name: 'crocodile',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐢',
            skin_tone_support: false,
            name: 'turtle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦎',
            skin_tone_support: false,
            name: 'lizard',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🐍',
            skin_tone_support: false,
            name: 'snake',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐲',
            skin_tone_support: false,
            name: 'dragon_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐉',
            skin_tone_support: false,
            name: 'dragon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦕',
            skin_tone_support: false,
            name: 'sauropod',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🦖',
            skin_tone_support: false,
            name: 't_rex',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🐳',
            skin_tone_support: false,
            name: 'spouting_whale',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐋',
            skin_tone_support: false,
            name: 'whale',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐬',
            skin_tone_support: false,
            name: 'dolphin',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐟',
            skin_tone_support: false,
            name: 'fish',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐠',
            skin_tone_support: false,
            name: 'tropical_fish',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐡',
            skin_tone_support: false,
            name: 'blowfish',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦈',
            skin_tone_support: false,
            name: 'shark',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🐙',
            skin_tone_support: false,
            name: 'octopus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐚',
            skin_tone_support: false,
            name: 'spiral_shell',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐌',
            skin_tone_support: false,
            name: 'snail',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦋',
            skin_tone_support: false,
            name: 'butterfly',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🐛',
            skin_tone_support: false,
            name: 'bug',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐜',
            skin_tone_support: false,
            name: 'ant',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐝',
            skin_tone_support: false,
            name: 'honeybee',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🐞',
            skin_tone_support: false,
            name: 'lady_beetle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦗',
            skin_tone_support: false,
            name: 'cricket',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🕷️',
            skin_tone_support: false,
            name: 'spider',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕸️',
            skin_tone_support: false,
            name: 'spider_web',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦂',
            skin_tone_support: false,
            name: 'scorpion',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦟',
            skin_tone_support: false,
            name: 'mosquito',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦠',
            skin_tone_support: false,
            name: 'microbe',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '💐',
            skin_tone_support: false,
            name: 'bouquet',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌸',
            skin_tone_support: false,
            name: 'cherry_blossom',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💮',
            skin_tone_support: false,
            name: 'white_flower',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏵️',
            skin_tone_support: false,
            name: 'rosette',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌹',
            skin_tone_support: false,
            name: 'rose',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥀',
            skin_tone_support: false,
            name: 'wilted_flower',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🌺',
            skin_tone_support: false,
            name: 'hibiscus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌻',
            skin_tone_support: false,
            name: 'sunflower',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌼',
            skin_tone_support: false,
            name: 'blossom',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌷',
            skin_tone_support: false,
            name: 'tulip',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌱',
            skin_tone_support: false,
            name: 'seedling',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌲',
            skin_tone_support: false,
            name: 'evergreen_tree',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌳',
            skin_tone_support: false,
            name: 'deciduous_tree',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌴',
            skin_tone_support: false,
            name: 'palm_tree',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌵',
            skin_tone_support: false,
            name: 'cactus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌾',
            skin_tone_support: false,
            name: 'sheaf_of_rice',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌿',
            skin_tone_support: false,
            name: 'herb',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☘️',
            skin_tone_support: false,
            name: 'shamrock',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🍀',
            skin_tone_support: false,
            name: 'four_leaf_clover',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍁',
            skin_tone_support: false,
            name: 'maple_leaf',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍂',
            skin_tone_support: false,
            name: 'fallen_leaf',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍃',
            skin_tone_support: false,
            name: 'leaf_fluttering_in_wind',
            unicode_version: '6.0',
            emoji_version: '2.0'
        }
    ],
    'Food & Drink': [
        {
            emoji: '🍇',
            skin_tone_support: false,
            name: 'grapes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍈',
            skin_tone_support: false,
            name: 'melon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍉',
            skin_tone_support: false,
            name: 'watermelon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍊',
            skin_tone_support: false,
            name: 'tangerine',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍋',
            skin_tone_support: false,
            name: 'lemon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍌',
            skin_tone_support: false,
            name: 'banana',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍍',
            skin_tone_support: false,
            name: 'pineapple',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥭',
            skin_tone_support: false,
            name: 'mango',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🍎',
            skin_tone_support: false,
            name: 'red_apple',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍏',
            skin_tone_support: false,
            name: 'green_apple',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍐',
            skin_tone_support: false,
            name: 'pear',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍑',
            skin_tone_support: false,
            name: 'peach',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍒',
            skin_tone_support: false,
            name: 'cherries',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍓',
            skin_tone_support: false,
            name: 'strawberry',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥝',
            skin_tone_support: false,
            name: 'kiwi_fruit',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🍅',
            skin_tone_support: false,
            name: 'tomato',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥥',
            skin_tone_support: false,
            name: 'coconut',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🥑',
            skin_tone_support: false,
            name: 'avocado',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🍆',
            skin_tone_support: false,
            name: 'eggplant',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥔',
            skin_tone_support: false,
            name: 'potato',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥕',
            skin_tone_support: false,
            name: 'carrot',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🌽',
            skin_tone_support: false,
            name: 'ear_of_corn',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌶️',
            skin_tone_support: false,
            name: 'hot_pepper',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥒',
            skin_tone_support: false,
            name: 'cucumber',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥬',
            skin_tone_support: false,
            name: 'leafy_green',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🥦',
            skin_tone_support: false,
            name: 'broccoli',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧄',
            skin_tone_support: false,
            name: 'garlic',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧅',
            skin_tone_support: false,
            name: 'onion',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🍄',
            skin_tone_support: false,
            name: 'mushroom',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥜',
            skin_tone_support: false,
            name: 'peanuts',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🌰',
            skin_tone_support: false,
            name: 'chestnut',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍞',
            skin_tone_support: false,
            name: 'bread',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥐',
            skin_tone_support: false,
            name: 'croissant',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥖',
            skin_tone_support: false,
            name: 'baguette_bread',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥨',
            skin_tone_support: false,
            name: 'pretzel',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🥯',
            skin_tone_support: false,
            name: 'bagel',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🥞',
            skin_tone_support: false,
            name: 'pancakes',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧇',
            skin_tone_support: false,
            name: 'waffle',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧀',
            skin_tone_support: false,
            name: 'cheese_wedge',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍖',
            skin_tone_support: false,
            name: 'meat_on_bone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍗',
            skin_tone_support: false,
            name: 'poultry_leg',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥩',
            skin_tone_support: false,
            name: 'cut_of_meat',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🥓',
            skin_tone_support: false,
            name: 'bacon',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🍔',
            skin_tone_support: false,
            name: 'hamburger',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍟',
            skin_tone_support: false,
            name: 'french_fries',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍕',
            skin_tone_support: false,
            name: 'pizza',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌭',
            skin_tone_support: false,
            name: 'hot_dog',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥪',
            skin_tone_support: false,
            name: 'sandwich',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🌮',
            skin_tone_support: false,
            name: 'taco',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌯',
            skin_tone_support: false,
            name: 'burrito',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥙',
            skin_tone_support: false,
            name: 'stuffed_flatbread',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🧆',
            skin_tone_support: false,
            name: 'falafel',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🥚',
            skin_tone_support: false,
            name: 'egg',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🍳',
            skin_tone_support: false,
            name: 'cooking',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥘',
            skin_tone_support: false,
            name: 'shallow_pan_of_food',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🍲',
            skin_tone_support: false,
            name: 'pot_of_food',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥣',
            skin_tone_support: false,
            name: 'bowl_with_spoon',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🥗',
            skin_tone_support: false,
            name: 'green_salad',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🍿',
            skin_tone_support: false,
            name: 'popcorn',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧈',
            skin_tone_support: false,
            name: 'butter',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧂',
            skin_tone_support: false,
            name: 'salt',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🥫',
            skin_tone_support: false,
            name: 'canned_food',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🍱',
            skin_tone_support: false,
            name: 'bento_box',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍘',
            skin_tone_support: false,
            name: 'rice_cracker',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍙',
            skin_tone_support: false,
            name: 'rice_ball',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍚',
            skin_tone_support: false,
            name: 'cooked_rice',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍛',
            skin_tone_support: false,
            name: 'curry_rice',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍜',
            skin_tone_support: false,
            name: 'steaming_bowl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍝',
            skin_tone_support: false,
            name: 'spaghetti',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍠',
            skin_tone_support: false,
            name: 'roasted_sweet_potato',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍢',
            skin_tone_support: false,
            name: 'oden',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍣',
            skin_tone_support: false,
            name: 'sushi',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍤',
            skin_tone_support: false,
            name: 'fried_shrimp',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍥',
            skin_tone_support: false,
            name: 'fish_cake_with_swirl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥮',
            skin_tone_support: false,
            name: 'moon_cake',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🍡',
            skin_tone_support: false,
            name: 'dango',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥟',
            skin_tone_support: false,
            name: 'dumpling',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🥠',
            skin_tone_support: false,
            name: 'fortune_cookie',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🥡',
            skin_tone_support: false,
            name: 'takeout_box',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🦀',
            skin_tone_support: false,
            name: 'crab',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🦞',
            skin_tone_support: false,
            name: 'lobster',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦐',
            skin_tone_support: false,
            name: 'shrimp',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦑',
            skin_tone_support: false,
            name: 'squid',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦪',
            skin_tone_support: false,
            name: 'oyster',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🍦',
            skin_tone_support: false,
            name: 'soft_ice_cream',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍧',
            skin_tone_support: false,
            name: 'shaved_ice',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍨',
            skin_tone_support: false,
            name: 'ice_cream',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍩',
            skin_tone_support: false,
            name: 'doughnut',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍪',
            skin_tone_support: false,
            name: 'cookie',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎂',
            skin_tone_support: false,
            name: 'birthday_cake',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍰',
            skin_tone_support: false,
            name: 'shortcake',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧁',
            skin_tone_support: false,
            name: 'cupcake',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🥧',
            skin_tone_support: false,
            name: 'pie',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🍫',
            skin_tone_support: false,
            name: 'chocolate_bar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍬',
            skin_tone_support: false,
            name: 'candy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍭',
            skin_tone_support: false,
            name: 'lollipop',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍮',
            skin_tone_support: false,
            name: 'custard',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍯',
            skin_tone_support: false,
            name: 'honey_pot',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍼',
            skin_tone_support: false,
            name: 'baby_bottle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥛',
            skin_tone_support: false,
            name: 'glass_of_milk',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '☕',
            skin_tone_support: false,
            name: 'hot_beverage',
            unicode_version: '4.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍵',
            skin_tone_support: false,
            name: 'teacup_without_handle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍶',
            skin_tone_support: false,
            name: 'sake',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍾',
            skin_tone_support: false,
            name: 'bottle_with_popping_cork',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍷',
            skin_tone_support: false,
            name: 'wine_glass',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍸',
            skin_tone_support: false,
            name: 'cocktail_glass',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍹',
            skin_tone_support: false,
            name: 'tropical_drink',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍺',
            skin_tone_support: false,
            name: 'beer_mug',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍻',
            skin_tone_support: false,
            name: 'clinking_beer_mugs',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥂',
            skin_tone_support: false,
            name: 'clinking_glasses',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥃',
            skin_tone_support: false,
            name: 'tumbler_glass',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥤',
            skin_tone_support: false,
            name: 'cup_with_straw',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧃',
            skin_tone_support: false,
            name: 'beverage_box',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧉',
            skin_tone_support: false,
            name: 'mate',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧊',
            skin_tone_support: false,
            name: 'ice',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🥢',
            skin_tone_support: false,
            name: 'chopsticks',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🍽️',
            skin_tone_support: false,
            name: 'fork_and_knife_with_plate',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🍴',
            skin_tone_support: false,
            name: 'fork_and_knife',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥄',
            skin_tone_support: false,
            name: 'spoon',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🔪',
            skin_tone_support: false,
            name: 'kitchen_knife',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏺',
            skin_tone_support: false,
            name: 'amphora',
            unicode_version: '8.0',
            emoji_version: '2.0'
        }
    ],
    'Travel & Places': [
        {
            emoji: '🌍',
            skin_tone_support: false,
            name: 'globe_showing_europe_africa',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌎',
            skin_tone_support: false,
            name: 'globe_showing_americas',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌏',
            skin_tone_support: false,
            name: 'globe_showing_asia_australia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌐',
            skin_tone_support: false,
            name: 'globe_with_meridians',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗺️',
            skin_tone_support: false,
            name: 'world_map',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗾',
            skin_tone_support: false,
            name: 'map_of_japan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧭',
            skin_tone_support: false,
            name: 'compass',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🏔️',
            skin_tone_support: false,
            name: 'snow_capped_mountain',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛰️',
            skin_tone_support: false,
            name: 'mountain',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🌋',
            skin_tone_support: false,
            name: 'volcano',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗻',
            skin_tone_support: false,
            name: 'mount_fuji',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏕️',
            skin_tone_support: false,
            name: 'camping',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏖️',
            skin_tone_support: false,
            name: 'beach_with_umbrella',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏜️',
            skin_tone_support: false,
            name: 'desert',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏝️',
            skin_tone_support: false,
            name: 'desert_island',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏞️',
            skin_tone_support: false,
            name: 'national_park',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏟️',
            skin_tone_support: false,
            name: 'stadium',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏛️',
            skin_tone_support: false,
            name: 'classical_building',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏗️',
            skin_tone_support: false,
            name: 'building_construction',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧱',
            skin_tone_support: false,
            name: 'brick',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🏘️',
            skin_tone_support: false,
            name: 'houses',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏚️',
            skin_tone_support: false,
            name: 'derelict_house',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏠',
            skin_tone_support: false,
            name: 'house',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏡',
            skin_tone_support: false,
            name: 'house_with_garden',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏢',
            skin_tone_support: false,
            name: 'office_building',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏣',
            skin_tone_support: false,
            name: 'japanese_post_office',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏤',
            skin_tone_support: false,
            name: 'post_office',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏥',
            skin_tone_support: false,
            name: 'hospital',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏦',
            skin_tone_support: false,
            name: 'bank',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏨',
            skin_tone_support: false,
            name: 'hotel',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏩',
            skin_tone_support: false,
            name: 'love_hotel',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏪',
            skin_tone_support: false,
            name: 'convenience_store',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏫',
            skin_tone_support: false,
            name: 'school',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏬',
            skin_tone_support: false,
            name: 'department_store',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏭',
            skin_tone_support: false,
            name: 'factory',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏯',
            skin_tone_support: false,
            name: 'japanese_castle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏰',
            skin_tone_support: false,
            name: 'castle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💒',
            skin_tone_support: false,
            name: 'wedding',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗼',
            skin_tone_support: false,
            name: 'tokyo_tower',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗽',
            skin_tone_support: false,
            name: 'statue_of_liberty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛪',
            skin_tone_support: false,
            name: 'church',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🕌',
            skin_tone_support: false,
            name: 'mosque',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛕',
            skin_tone_support: false,
            name: 'hindu_temple',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🕍',
            skin_tone_support: false,
            name: 'synagogue',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛩️',
            skin_tone_support: false,
            name: 'shinto_shrine',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🕋',
            skin_tone_support: false,
            name: 'kaaba',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛲',
            skin_tone_support: false,
            name: 'fountain',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⛺',
            skin_tone_support: false,
            name: 'tent',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🌁',
            skin_tone_support: false,
            name: 'foggy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌃',
            skin_tone_support: false,
            name: 'night_with_stars',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏙️',
            skin_tone_support: false,
            name: 'cityscape',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌄',
            skin_tone_support: false,
            name: 'sunrise_over_mountains',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌅',
            skin_tone_support: false,
            name: 'sunrise',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌆',
            skin_tone_support: false,
            name: 'cityscape_at_dusk',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌇',
            skin_tone_support: false,
            name: 'sunset',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌉',
            skin_tone_support: false,
            name: 'bridge_at_night',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '♨️',
            skin_tone_support: false,
            name: 'hot_springs',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🎠',
            skin_tone_support: false,
            name: 'carousel_horse',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎡',
            skin_tone_support: false,
            name: 'ferris_wheel',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎢',
            skin_tone_support: false,
            name: 'roller_coaster',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💈',
            skin_tone_support: false,
            name: 'barber_pole',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎪',
            skin_tone_support: false,
            name: 'circus_tent',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚂',
            skin_tone_support: false,
            name: 'locomotive',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚃',
            skin_tone_support: false,
            name: 'railway_car',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚄',
            skin_tone_support: false,
            name: 'high_speed_train',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚅',
            skin_tone_support: false,
            name: 'bullet_train',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚆',
            skin_tone_support: false,
            name: 'train',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚇',
            skin_tone_support: false,
            name: 'metro',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚈',
            skin_tone_support: false,
            name: 'light_rail',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚉',
            skin_tone_support: false,
            name: 'station',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚊',
            skin_tone_support: false,
            name: 'tram',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚝',
            skin_tone_support: false,
            name: 'monorail',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚞',
            skin_tone_support: false,
            name: 'mountain_railway',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚋',
            skin_tone_support: false,
            name: 'tram_car',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚌',
            skin_tone_support: false,
            name: 'bus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚍',
            skin_tone_support: false,
            name: 'oncoming_bus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚎',
            skin_tone_support: false,
            name: 'trolleybus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚐',
            skin_tone_support: false,
            name: 'minibus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚑',
            skin_tone_support: false,
            name: 'ambulance',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚒',
            skin_tone_support: false,
            name: 'fire_engine',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚓',
            skin_tone_support: false,
            name: 'police_car',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚔',
            skin_tone_support: false,
            name: 'oncoming_police_car',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚕',
            skin_tone_support: false,
            name: 'taxi',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚖',
            skin_tone_support: false,
            name: 'oncoming_taxi',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚗',
            skin_tone_support: false,
            name: 'automobile',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚘',
            skin_tone_support: false,
            name: 'oncoming_automobile',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚙',
            skin_tone_support: false,
            name: 'sport_utility_vehicle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚚',
            skin_tone_support: false,
            name: 'delivery_truck',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚛',
            skin_tone_support: false,
            name: 'articulated_lorry',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚜',
            skin_tone_support: false,
            name: 'tractor',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏎️',
            skin_tone_support: false,
            name: 'racing_car',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏍️',
            skin_tone_support: false,
            name: 'motorcycle',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛵',
            skin_tone_support: false,
            name: 'motor_scooter',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🦽',
            skin_tone_support: false,
            name: 'manual_wheelchair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🦼',
            skin_tone_support: false,
            name: 'motorized_wheelchair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🛺',
            skin_tone_support: false,
            name: 'auto_rickshaw',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🚲',
            skin_tone_support: false,
            name: 'bicycle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛴',
            skin_tone_support: false,
            name: 'kick_scooter',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🛹',
            skin_tone_support: false,
            name: 'skateboard',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🚏',
            skin_tone_support: false,
            name: 'bus_stop',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛣️',
            skin_tone_support: false,
            name: 'motorway',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛤️',
            skin_tone_support: false,
            name: 'railway_track',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛢️',
            skin_tone_support: false,
            name: 'oil_drum',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛽',
            skin_tone_support: false,
            name: 'fuel_pump',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🚨',
            skin_tone_support: false,
            name: 'police_car_light',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚥',
            skin_tone_support: false,
            name: 'horizontal_traffic_light',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚦',
            skin_tone_support: false,
            name: 'vertical_traffic_light',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛑',
            skin_tone_support: false,
            name: 'stop_sign',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚧',
            skin_tone_support: false,
            name: 'construction',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⚓',
            skin_tone_support: false,
            name: 'anchor',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⛵',
            skin_tone_support: false,
            name: 'sailboat',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🛶',
            skin_tone_support: false,
            name: 'canoe',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚤',
            skin_tone_support: false,
            name: 'speedboat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛳️',
            skin_tone_support: false,
            name: 'passenger_ship',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛴️',
            skin_tone_support: false,
            name: 'ferry',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🛥️',
            skin_tone_support: false,
            name: 'motor_boat',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚢',
            skin_tone_support: false,
            name: 'ship',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '✈️',
            skin_tone_support: false,
            name: 'airplane',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🛩️',
            skin_tone_support: false,
            name: 'small_airplane',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛫',
            skin_tone_support: false,
            name: 'airplane_departure',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛬',
            skin_tone_support: false,
            name: 'airplane_arrival',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🪂',
            skin_tone_support: false,
            name: 'parachute',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '💺',
            skin_tone_support: false,
            name: 'seat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚁',
            skin_tone_support: false,
            name: 'helicopter',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚟',
            skin_tone_support: false,
            name: 'suspension_railway',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚠',
            skin_tone_support: false,
            name: 'mountain_cableway',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚡',
            skin_tone_support: false,
            name: 'aerial_tramway',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛰️',
            skin_tone_support: false,
            name: 'satellite',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚀',
            skin_tone_support: false,
            name: 'rocket',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛸',
            skin_tone_support: false,
            name: 'flying_saucer',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🛎️',
            skin_tone_support: false,
            name: 'bellhop_bell',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧳',
            skin_tone_support: false,
            name: 'luggage',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '⌛',
            skin_tone_support: false,
            name: 'hourglass_done',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⏳',
            skin_tone_support: false,
            name: 'hourglass_not_done',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⌚',
            skin_tone_support: false,
            name: 'watch',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⏰',
            skin_tone_support: false,
            name: 'alarm_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏱️',
            skin_tone_support: false,
            name: 'stopwatch',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏲️',
            skin_tone_support: false,
            name: 'timer_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕰️',
            skin_tone_support: false,
            name: 'mantelpiece_clock',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕛',
            skin_tone_support: false,
            name: 'twelve_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕧',
            skin_tone_support: false,
            name: 'twelve_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕐',
            skin_tone_support: false,
            name: 'one_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕜',
            skin_tone_support: false,
            name: 'one_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕑',
            skin_tone_support: false,
            name: 'two_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕝',
            skin_tone_support: false,
            name: 'two_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕒',
            skin_tone_support: false,
            name: 'three_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕞',
            skin_tone_support: false,
            name: 'three_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕓',
            skin_tone_support: false,
            name: 'four_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕟',
            skin_tone_support: false,
            name: 'four_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕔',
            skin_tone_support: false,
            name: 'five_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕠',
            skin_tone_support: false,
            name: 'five_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕕',
            skin_tone_support: false,
            name: 'six_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕡',
            skin_tone_support: false,
            name: 'six_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕖',
            skin_tone_support: false,
            name: 'seven_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕢',
            skin_tone_support: false,
            name: 'seven_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕗',
            skin_tone_support: false,
            name: 'eight_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕣',
            skin_tone_support: false,
            name: 'eight_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕘',
            skin_tone_support: false,
            name: 'nine_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕤',
            skin_tone_support: false,
            name: 'nine_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕙',
            skin_tone_support: false,
            name: 'ten_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕥',
            skin_tone_support: false,
            name: 'ten_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕚',
            skin_tone_support: false,
            name: 'eleven_o_clock',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕦',
            skin_tone_support: false,
            name: 'eleven_thirty',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌑',
            skin_tone_support: false,
            name: 'new_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌒',
            skin_tone_support: false,
            name: 'waxing_crescent_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌓',
            skin_tone_support: false,
            name: 'first_quarter_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌔',
            skin_tone_support: false,
            name: 'waxing_gibbous_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌕',
            skin_tone_support: false,
            name: 'full_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌖',
            skin_tone_support: false,
            name: 'waning_gibbous_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌗',
            skin_tone_support: false,
            name: 'last_quarter_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌘',
            skin_tone_support: false,
            name: 'waning_crescent_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌙',
            skin_tone_support: false,
            name: 'crescent_moon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌚',
            skin_tone_support: false,
            name: 'new_moon_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌛',
            skin_tone_support: false,
            name: 'first_quarter_moon_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌜',
            skin_tone_support: false,
            name: 'last_quarter_moon_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌡️',
            skin_tone_support: false,
            name: 'thermometer',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☀️',
            skin_tone_support: false,
            name: 'sun',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🌝',
            skin_tone_support: false,
            name: 'full_moon_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌞',
            skin_tone_support: false,
            name: 'sun_with_face',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🪐',
            skin_tone_support: false,
            name: 'ringed_planet',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '⭐',
            skin_tone_support: false,
            name: 'star',
            unicode_version: '5.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🌟',
            skin_tone_support: false,
            name: 'glowing_star',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌠',
            skin_tone_support: false,
            name: 'shooting_star',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌌',
            skin_tone_support: false,
            name: 'milky_way',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☁️',
            skin_tone_support: false,
            name: 'cloud',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⛅',
            skin_tone_support: false,
            name: 'sun_behind_cloud',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⛈️',
            skin_tone_support: false,
            name: 'cloud_with_lightning_and_rain',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🌤️',
            skin_tone_support: false,
            name: 'sun_behind_small_cloud',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌥️',
            skin_tone_support: false,
            name: 'sun_behind_large_cloud',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌦️',
            skin_tone_support: false,
            name: 'sun_behind_rain_cloud',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌧️',
            skin_tone_support: false,
            name: 'cloud_with_rain',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌨️',
            skin_tone_support: false,
            name: 'cloud_with_snow',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌩️',
            skin_tone_support: false,
            name: 'cloud_with_lightning',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌪️',
            skin_tone_support: false,
            name: 'tornado',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌫️',
            skin_tone_support: false,
            name: 'fog',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌬️',
            skin_tone_support: false,
            name: 'wind_face',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌀',
            skin_tone_support: false,
            name: 'cyclone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌈',
            skin_tone_support: false,
            name: 'rainbow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌂',
            skin_tone_support: false,
            name: 'closed_umbrella',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☂️',
            skin_tone_support: false,
            name: 'umbrella',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☔',
            skin_tone_support: false,
            name: 'umbrella_with_rain_drops',
            unicode_version: '4.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛱️',
            skin_tone_support: false,
            name: 'umbrella_on_ground',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⚡',
            skin_tone_support: false,
            name: 'high_voltage',
            unicode_version: '4.0',
            emoji_version: '2.0'
        },
        {
            emoji: '❄️',
            skin_tone_support: false,
            name: 'snowflake',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☃️',
            skin_tone_support: false,
            name: 'snowman',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⛄',
            skin_tone_support: false,
            name: 'snowman_without_snow',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '☄️',
            skin_tone_support: false,
            name: 'comet',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🔥',
            skin_tone_support: false,
            name: 'fire',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💧',
            skin_tone_support: false,
            name: 'droplet',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🌊',
            skin_tone_support: false,
            name: 'water_wave',
            unicode_version: '6.0',
            emoji_version: '2.0'
        }
    ],
    Activities: [
        {
            emoji: '🎃',
            skin_tone_support: false,
            name: 'jack_o_lantern',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎄',
            skin_tone_support: false,
            name: 'christmas_tree',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎆',
            skin_tone_support: false,
            name: 'fireworks',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎇',
            skin_tone_support: false,
            name: 'sparkler',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧨',
            skin_tone_support: false,
            name: 'firecracker',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '✨',
            skin_tone_support: false,
            name: 'sparkles',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎈',
            skin_tone_support: false,
            name: 'balloon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎉',
            skin_tone_support: false,
            name: 'party_popper',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎊',
            skin_tone_support: false,
            name: 'confetti_ball',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎋',
            skin_tone_support: false,
            name: 'tanabata_tree',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎍',
            skin_tone_support: false,
            name: 'pine_decoration',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎎',
            skin_tone_support: false,
            name: 'japanese_dolls',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎏',
            skin_tone_support: false,
            name: 'carp_streamer',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎐',
            skin_tone_support: false,
            name: 'wind_chime',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎑',
            skin_tone_support: false,
            name: 'moon_viewing_ceremony',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧧',
            skin_tone_support: false,
            name: 'red_envelope',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🎀',
            skin_tone_support: false,
            name: 'ribbon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎁',
            skin_tone_support: false,
            name: 'wrapped_gift',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎗️',
            skin_tone_support: false,
            name: 'reminder_ribbon',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎟️',
            skin_tone_support: false,
            name: 'admission_tickets',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎫',
            skin_tone_support: false,
            name: 'ticket',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎖️',
            skin_tone_support: false,
            name: 'military_medal',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏆',
            skin_tone_support: false,
            name: 'trophy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏅',
            skin_tone_support: false,
            name: 'sports_medal',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥇',
            skin_tone_support: false,
            name: '1st_place_medal',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥈',
            skin_tone_support: false,
            name: '2nd_place_medal',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥉',
            skin_tone_support: false,
            name: '3rd_place_medal',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '⚽',
            skin_tone_support: false,
            name: 'soccer_ball',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⚾',
            skin_tone_support: false,
            name: 'baseball',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🥎',
            skin_tone_support: false,
            name: 'softball',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🏀',
            skin_tone_support: false,
            name: 'basketball',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏐',
            skin_tone_support: false,
            name: 'volleyball',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏈',
            skin_tone_support: false,
            name: 'american_football',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏉',
            skin_tone_support: false,
            name: 'rugby_football',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎾',
            skin_tone_support: false,
            name: 'tennis',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥏',
            skin_tone_support: false,
            name: 'flying_disc',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🎳',
            skin_tone_support: false,
            name: 'bowling',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏏',
            skin_tone_support: false,
            name: 'cricket_game',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏑',
            skin_tone_support: false,
            name: 'field_hockey',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏒',
            skin_tone_support: false,
            name: 'ice_hockey',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥍',
            skin_tone_support: false,
            name: 'lacrosse',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🏓',
            skin_tone_support: false,
            name: 'ping_pong',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏸',
            skin_tone_support: false,
            name: 'badminton',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥊',
            skin_tone_support: false,
            name: 'boxing_glove',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥋',
            skin_tone_support: false,
            name: 'martial_arts_uniform',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🥅',
            skin_tone_support: false,
            name: 'goal_net',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '⛳',
            skin_tone_support: false,
            name: 'flag_in_hole',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⛸️',
            skin_tone_support: false,
            name: 'ice_skate',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🎣',
            skin_tone_support: false,
            name: 'fishing_pole',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🤿',
            skin_tone_support: false,
            name: 'diving_mask',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🎽',
            skin_tone_support: false,
            name: 'running_shirt',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎿',
            skin_tone_support: false,
            name: 'skis',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛷',
            skin_tone_support: false,
            name: 'sled',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🥌',
            skin_tone_support: false,
            name: 'curling_stone',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🎯',
            skin_tone_support: false,
            name: 'direct_hit',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🪀',
            skin_tone_support: false,
            name: 'yo_yo',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🪁',
            skin_tone_support: false,
            name: 'kite',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🎱',
            skin_tone_support: false,
            name: 'pool_8_ball',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔮',
            skin_tone_support: false,
            name: 'crystal_ball',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧿',
            skin_tone_support: false,
            name: 'nazar_amulet',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🎮',
            skin_tone_support: false,
            name: 'video_game',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕹️',
            skin_tone_support: false,
            name: 'joystick',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎰',
            skin_tone_support: false,
            name: 'slot_machine',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎲',
            skin_tone_support: false,
            name: 'game_die',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧩',
            skin_tone_support: false,
            name: 'puzzle_piece',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧸',
            skin_tone_support: false,
            name: 'teddy_bear',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '♠️',
            skin_tone_support: false,
            name: 'spade_suit',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♥️',
            skin_tone_support: false,
            name: 'heart_suit',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♦️',
            skin_tone_support: false,
            name: 'diamond_suit',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♣️',
            skin_tone_support: false,
            name: 'club_suit',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♟️',
            skin_tone_support: false,
            name: 'chess_pawn',
            unicode_version: '1.1',
            emoji_version: '11.0'
        },
        {
            emoji: '🃏',
            skin_tone_support: false,
            name: 'joker',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🀄',
            skin_tone_support: false,
            name: 'mahjong_red_dragon',
            unicode_version: '5.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🎴',
            skin_tone_support: false,
            name: 'flower_playing_cards',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎭',
            skin_tone_support: false,
            name: 'performing_arts',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖼️',
            skin_tone_support: false,
            name: 'framed_picture',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎨',
            skin_tone_support: false,
            name: 'artist_palette',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧵',
            skin_tone_support: false,
            name: 'thread',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧶',
            skin_tone_support: false,
            name: 'yarn',
            unicode_version: '11.0',
            emoji_version: '11.0'
        }
    ],
    Objects: [
        {
            emoji: '👓',
            skin_tone_support: false,
            name: 'glasses',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕶️',
            skin_tone_support: false,
            name: 'sunglasses',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥽',
            skin_tone_support: false,
            name: 'goggles',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🥼',
            skin_tone_support: false,
            name: 'lab_coat',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🦺',
            skin_tone_support: false,
            name: 'safety_vest',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👔',
            skin_tone_support: false,
            name: 'necktie',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👕',
            skin_tone_support: false,
            name: 't_shirt',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👖',
            skin_tone_support: false,
            name: 'jeans',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧣',
            skin_tone_support: false,
            name: 'scarf',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧤',
            skin_tone_support: false,
            name: 'gloves',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧥',
            skin_tone_support: false,
            name: 'coat',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🧦',
            skin_tone_support: false,
            name: 'socks',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '👗',
            skin_tone_support: false,
            name: 'dress',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👘',
            skin_tone_support: false,
            name: 'kimono',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥻',
            skin_tone_support: false,
            name: 'sari',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🩱',
            skin_tone_support: false,
            name: 'one_piece_swimsuit',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🩲',
            skin_tone_support: false,
            name: 'briefs',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🩳',
            skin_tone_support: false,
            name: 'shorts',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👙',
            skin_tone_support: false,
            name: 'bikini',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👚',
            skin_tone_support: false,
            name: 'woman_s_clothes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👛',
            skin_tone_support: false,
            name: 'purse',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👜',
            skin_tone_support: false,
            name: 'handbag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👝',
            skin_tone_support: false,
            name: 'clutch_bag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛍️',
            skin_tone_support: false,
            name: 'shopping_bags',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎒',
            skin_tone_support: false,
            name: 'backpack',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👞',
            skin_tone_support: false,
            name: 'man_s_shoe',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👟',
            skin_tone_support: false,
            name: 'running_shoe',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🥾',
            skin_tone_support: false,
            name: 'hiking_boot',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🥿',
            skin_tone_support: false,
            name: 'flat_shoe',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '👠',
            skin_tone_support: false,
            name: 'high_heeled_shoe',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👡',
            skin_tone_support: false,
            name: 'woman_s_sandal',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🩰',
            skin_tone_support: false,
            name: 'ballet_shoes',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '👢',
            skin_tone_support: false,
            name: 'woman_s_boot',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👑',
            skin_tone_support: false,
            name: 'crown',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '👒',
            skin_tone_support: false,
            name: 'woman_s_hat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎩',
            skin_tone_support: false,
            name: 'top_hat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎓',
            skin_tone_support: false,
            name: 'graduation_cap',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧢',
            skin_tone_support: false,
            name: 'billed_cap',
            unicode_version: '10.0',
            emoji_version: '5.0'
        },
        {
            emoji: '⛑️',
            skin_tone_support: false,
            name: 'rescue_worker_s_helmet',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '📿',
            skin_tone_support: false,
            name: 'prayer_beads',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💄',
            skin_tone_support: false,
            name: 'lipstick',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💍',
            skin_tone_support: false,
            name: 'ring',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💎',
            skin_tone_support: false,
            name: 'gem_stone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔇',
            skin_tone_support: false,
            name: 'muted_speaker',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔈',
            skin_tone_support: false,
            name: 'speaker_low_volume',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔉',
            skin_tone_support: false,
            name: 'speaker_medium_volume',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔊',
            skin_tone_support: false,
            name: 'speaker_high_volume',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📢',
            skin_tone_support: false,
            name: 'loudspeaker',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📣',
            skin_tone_support: false,
            name: 'megaphone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📯',
            skin_tone_support: false,
            name: 'postal_horn',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔔',
            skin_tone_support: false,
            name: 'bell',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔕',
            skin_tone_support: false,
            name: 'bell_with_slash',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎼',
            skin_tone_support: false,
            name: 'musical_score',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎵',
            skin_tone_support: false,
            name: 'musical_note',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎶',
            skin_tone_support: false,
            name: 'musical_notes',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎙️',
            skin_tone_support: false,
            name: 'studio_microphone',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎚️',
            skin_tone_support: false,
            name: 'level_slider',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎛️',
            skin_tone_support: false,
            name: 'control_knobs',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎤',
            skin_tone_support: false,
            name: 'microphone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎧',
            skin_tone_support: false,
            name: 'headphone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📻',
            skin_tone_support: false,
            name: 'radio',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎷',
            skin_tone_support: false,
            name: 'saxophone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎸',
            skin_tone_support: false,
            name: 'guitar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎹',
            skin_tone_support: false,
            name: 'musical_keyboard',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎺',
            skin_tone_support: false,
            name: 'trumpet',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎻',
            skin_tone_support: false,
            name: 'violin',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🪕',
            skin_tone_support: false,
            name: 'banjo',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🥁',
            skin_tone_support: false,
            name: 'drum',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '📱',
            skin_tone_support: false,
            name: 'mobile_phone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📲',
            skin_tone_support: false,
            name: 'mobile_phone_with_arrow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☎️',
            skin_tone_support: false,
            name: 'telephone',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '📞',
            skin_tone_support: false,
            name: 'telephone_receiver',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📟',
            skin_tone_support: false,
            name: 'pager',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📠',
            skin_tone_support: false,
            name: 'fax_machine',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔋',
            skin_tone_support: false,
            name: 'battery',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔌',
            skin_tone_support: false,
            name: 'electric_plug',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💻',
            skin_tone_support: false,
            name: 'laptop',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖥️',
            skin_tone_support: false,
            name: 'desktop_computer',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖨️',
            skin_tone_support: false,
            name: 'printer',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⌨️',
            skin_tone_support: false,
            name: 'keyboard',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🖱️',
            skin_tone_support: false,
            name: 'computer_mouse',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖲️',
            skin_tone_support: false,
            name: 'trackball',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💽',
            skin_tone_support: false,
            name: 'computer_disk',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💾',
            skin_tone_support: false,
            name: 'floppy_disk',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💿',
            skin_tone_support: false,
            name: 'optical_disk',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📀',
            skin_tone_support: false,
            name: 'dvd',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧮',
            skin_tone_support: false,
            name: 'abacus',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🎥',
            skin_tone_support: false,
            name: 'movie_camera',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎞️',
            skin_tone_support: false,
            name: 'film_frames',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📽️',
            skin_tone_support: false,
            name: 'film_projector',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎬',
            skin_tone_support: false,
            name: 'clapper_board',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📺',
            skin_tone_support: false,
            name: 'television',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📷',
            skin_tone_support: false,
            name: 'camera',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📸',
            skin_tone_support: false,
            name: 'camera_with_flash',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📹',
            skin_tone_support: false,
            name: 'video_camera',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📼',
            skin_tone_support: false,
            name: 'videocassette',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔍',
            skin_tone_support: false,
            name: 'magnifying_glass_tilted_left',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔎',
            skin_tone_support: false,
            name: 'magnifying_glass_tilted_right',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🕯️',
            skin_tone_support: false,
            name: 'candle',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💡',
            skin_tone_support: false,
            name: 'light_bulb',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔦',
            skin_tone_support: false,
            name: 'flashlight',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏮',
            skin_tone_support: false,
            name: 'red_paper_lantern',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🪔',
            skin_tone_support: false,
            name: 'diya_lamp',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '📔',
            skin_tone_support: false,
            name: 'notebook_with_decorative_cover',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📕',
            skin_tone_support: false,
            name: 'closed_book',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📖',
            skin_tone_support: false,
            name: 'open_book',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📗',
            skin_tone_support: false,
            name: 'green_book',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📘',
            skin_tone_support: false,
            name: 'blue_book',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📙',
            skin_tone_support: false,
            name: 'orange_book',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📚',
            skin_tone_support: false,
            name: 'books',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📓',
            skin_tone_support: false,
            name: 'notebook',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📒',
            skin_tone_support: false,
            name: 'ledger',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📃',
            skin_tone_support: false,
            name: 'page_with_curl',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📜',
            skin_tone_support: false,
            name: 'scroll',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📄',
            skin_tone_support: false,
            name: 'page_facing_up',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📰',
            skin_tone_support: false,
            name: 'newspaper',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗞️',
            skin_tone_support: false,
            name: 'rolled_up_newspaper',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📑',
            skin_tone_support: false,
            name: 'bookmark_tabs',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔖',
            skin_tone_support: false,
            name: 'bookmark',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏷️',
            skin_tone_support: false,
            name: 'label',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💰',
            skin_tone_support: false,
            name: 'money_bag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💴',
            skin_tone_support: false,
            name: 'yen_banknote',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💵',
            skin_tone_support: false,
            name: 'dollar_banknote',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💶',
            skin_tone_support: false,
            name: 'euro_banknote',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💷',
            skin_tone_support: false,
            name: 'pound_banknote',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💸',
            skin_tone_support: false,
            name: 'money_with_wings',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💳',
            skin_tone_support: false,
            name: 'credit_card',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🧾',
            skin_tone_support: false,
            name: 'receipt',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '💹',
            skin_tone_support: false,
            name: 'chart_increasing_with_yen',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💱',
            skin_tone_support: false,
            name: 'currency_exchange',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💲',
            skin_tone_support: false,
            name: 'heavy_dollar_sign',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '✉️',
            skin_tone_support: false,
            name: 'envelope',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '📧',
            skin_tone_support: false,
            name: 'e_mail',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📨',
            skin_tone_support: false,
            name: 'incoming_envelope',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📩',
            skin_tone_support: false,
            name: 'envelope_with_arrow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📤',
            skin_tone_support: false,
            name: 'outbox_tray',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📥',
            skin_tone_support: false,
            name: 'inbox_tray',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📦',
            skin_tone_support: false,
            name: 'package',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📫',
            skin_tone_support: false,
            name: 'closed_mailbox_with_raised_flag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📪',
            skin_tone_support: false,
            name: 'closed_mailbox_with_lowered_flag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📬',
            skin_tone_support: false,
            name: 'open_mailbox_with_raised_flag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📭',
            skin_tone_support: false,
            name: 'open_mailbox_with_lowered_flag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📮',
            skin_tone_support: false,
            name: 'postbox',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗳️',
            skin_tone_support: false,
            name: 'ballot_box_with_ballot',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '✏️',
            skin_tone_support: false,
            name: 'pencil',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '✒️',
            skin_tone_support: false,
            name: 'black_nib',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🖋️',
            skin_tone_support: false,
            name: 'fountain_pen',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖊️',
            skin_tone_support: false,
            name: 'pen',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖌️',
            skin_tone_support: false,
            name: 'paintbrush',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖍️',
            skin_tone_support: false,
            name: 'crayon',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📝',
            skin_tone_support: false,
            name: 'memo',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💼',
            skin_tone_support: false,
            name: 'briefcase',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📁',
            skin_tone_support: false,
            name: 'file_folder',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📂',
            skin_tone_support: false,
            name: 'open_file_folder',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗂️',
            skin_tone_support: false,
            name: 'card_index_dividers',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📅',
            skin_tone_support: false,
            name: 'calendar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📆',
            skin_tone_support: false,
            name: 'tear_off_calendar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗒️',
            skin_tone_support: false,
            name: 'spiral_notepad',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗓️',
            skin_tone_support: false,
            name: 'spiral_calendar',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📇',
            skin_tone_support: false,
            name: 'card_index',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📈',
            skin_tone_support: false,
            name: 'chart_increasing',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📉',
            skin_tone_support: false,
            name: 'chart_decreasing',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📊',
            skin_tone_support: false,
            name: 'bar_chart',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📋',
            skin_tone_support: false,
            name: 'clipboard',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📌',
            skin_tone_support: false,
            name: 'pushpin',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📍',
            skin_tone_support: false,
            name: 'round_pushpin',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📎',
            skin_tone_support: false,
            name: 'paperclip',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🖇️',
            skin_tone_support: false,
            name: 'linked_paperclips',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📏',
            skin_tone_support: false,
            name: 'straight_ruler',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📐',
            skin_tone_support: false,
            name: 'triangular_ruler',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '✂️',
            skin_tone_support: false,
            name: 'scissors',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🗃️',
            skin_tone_support: false,
            name: 'card_file_box',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗄️',
            skin_tone_support: false,
            name: 'file_cabinet',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗑️',
            skin_tone_support: false,
            name: 'wastebasket',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔒',
            skin_tone_support: false,
            name: 'locked',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔓',
            skin_tone_support: false,
            name: 'unlocked',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔏',
            skin_tone_support: false,
            name: 'locked_with_pen',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔐',
            skin_tone_support: false,
            name: 'locked_with_key',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔑',
            skin_tone_support: false,
            name: 'key',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗝️',
            skin_tone_support: false,
            name: 'old_key',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔨',
            skin_tone_support: false,
            name: 'hammer',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🪓',
            skin_tone_support: false,
            name: 'axe',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '⛏️',
            skin_tone_support: false,
            name: 'pick',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⚒️',
            skin_tone_support: false,
            name: 'hammer_and_pick',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🛠️',
            skin_tone_support: false,
            name: 'hammer_and_wrench',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🗡️',
            skin_tone_support: false,
            name: 'dagger',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⚔️',
            skin_tone_support: false,
            name: 'crossed_swords',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🔫',
            skin_tone_support: false,
            name: 'pistol',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏹',
            skin_tone_support: false,
            name: 'bow_and_arrow',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛡️',
            skin_tone_support: false,
            name: 'shield',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔧',
            skin_tone_support: false,
            name: 'wrench',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔩',
            skin_tone_support: false,
            name: 'nut_and_bolt',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⚙️',
            skin_tone_support: false,
            name: 'gear',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🗜️',
            skin_tone_support: false,
            name: 'clamp',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⚖️',
            skin_tone_support: false,
            name: 'balance_scale',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🦯',
            skin_tone_support: false,
            name: 'probing_cane',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🔗',
            skin_tone_support: false,
            name: 'link',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛓️',
            skin_tone_support: false,
            name: 'chains',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🧰',
            skin_tone_support: false,
            name: 'toolbox',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧲',
            skin_tone_support: false,
            name: 'magnet',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '⚗️',
            skin_tone_support: false,
            name: 'alembic',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🧪',
            skin_tone_support: false,
            name: 'test_tube',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧫',
            skin_tone_support: false,
            name: 'petri_dish',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧬',
            skin_tone_support: false,
            name: 'dna',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🔬',
            skin_tone_support: false,
            name: 'microscope',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔭',
            skin_tone_support: false,
            name: 'telescope',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📡',
            skin_tone_support: false,
            name: 'satellite_antenna',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💉',
            skin_tone_support: false,
            name: 'syringe',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🩸',
            skin_tone_support: false,
            name: 'drop_of_blood',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '💊',
            skin_tone_support: false,
            name: 'pill',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🩹',
            skin_tone_support: false,
            name: 'adhesive_bandage',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🩺',
            skin_tone_support: false,
            name: 'stethoscope',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🚪',
            skin_tone_support: false,
            name: 'door',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛏️',
            skin_tone_support: false,
            name: 'bed',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛋️',
            skin_tone_support: false,
            name: 'couch_and_lamp',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🪑',
            skin_tone_support: false,
            name: 'chair',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🚽',
            skin_tone_support: false,
            name: 'toilet',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚿',
            skin_tone_support: false,
            name: 'shower',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛁',
            skin_tone_support: false,
            name: 'bathtub',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🪒',
            skin_tone_support: false,
            name: 'razor',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🧴',
            skin_tone_support: false,
            name: 'lotion_bottle',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧷',
            skin_tone_support: false,
            name: 'safety_pin',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧹',
            skin_tone_support: false,
            name: 'broom',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧺',
            skin_tone_support: false,
            name: 'basket',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧻',
            skin_tone_support: false,
            name: 'roll_of_paper',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧼',
            skin_tone_support: false,
            name: 'soap',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧽',
            skin_tone_support: false,
            name: 'sponge',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🧯',
            skin_tone_support: false,
            name: 'fire_extinguisher',
            unicode_version: '11.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🛒',
            skin_tone_support: false,
            name: 'shopping_cart',
            unicode_version: '9.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🚬',
            skin_tone_support: false,
            name: 'cigarette',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⚰️',
            skin_tone_support: false,
            name: 'coffin',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⚱️',
            skin_tone_support: false,
            name: 'funeral_urn',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🗿',
            skin_tone_support: false,
            name: 'moai',
            unicode_version: '6.0',
            emoji_version: '2.0'
        }
    ],
    Symbols: [
        {
            emoji: '🏧',
            skin_tone_support: false,
            name: 'atm_sign',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚮',
            skin_tone_support: false,
            name: 'litter_in_bin_sign',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚰',
            skin_tone_support: false,
            name: 'potable_water',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '♿',
            skin_tone_support: false,
            name: 'wheelchair_symbol',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🚹',
            skin_tone_support: false,
            name: 'men_s_room',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚺',
            skin_tone_support: false,
            name: 'women_s_room',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚻',
            skin_tone_support: false,
            name: 'restroom',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚼',
            skin_tone_support: false,
            name: 'baby_symbol',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚾',
            skin_tone_support: false,
            name: 'water_closet',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛂',
            skin_tone_support: false,
            name: 'passport_control',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛃',
            skin_tone_support: false,
            name: 'customs',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛄',
            skin_tone_support: false,
            name: 'baggage_claim',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛅',
            skin_tone_support: false,
            name: 'left_luggage',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⚠️',
            skin_tone_support: false,
            name: 'warning',
            unicode_version: '4.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚸',
            skin_tone_support: false,
            name: 'children_crossing',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⛔',
            skin_tone_support: false,
            name: 'no_entry',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🚫',
            skin_tone_support: false,
            name: 'prohibited',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚳',
            skin_tone_support: false,
            name: 'no_bicycles',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚭',
            skin_tone_support: false,
            name: 'no_smoking',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚯',
            skin_tone_support: false,
            name: 'no_littering',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚱',
            skin_tone_support: false,
            name: 'non_potable_water',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚷',
            skin_tone_support: false,
            name: 'no_pedestrians',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📵',
            skin_tone_support: false,
            name: 'no_mobile_phones',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔞',
            skin_tone_support: false,
            name: 'no_one_under_eighteen',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☢️',
            skin_tone_support: false,
            name: 'radioactive',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☣️',
            skin_tone_support: false,
            name: 'biohazard',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⬆️',
            skin_tone_support: false,
            name: 'up_arrow',
            unicode_version: '4.0',
            emoji_version: '2.0'
        },
        {
            emoji: '↗️',
            skin_tone_support: false,
            name: 'up_right_arrow',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '➡️',
            skin_tone_support: false,
            name: 'right_arrow',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '↘️',
            skin_tone_support: false,
            name: 'down_right_arrow',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⬇️',
            skin_tone_support: false,
            name: 'down_arrow',
            unicode_version: '4.0',
            emoji_version: '2.0'
        },
        {
            emoji: '↙️',
            skin_tone_support: false,
            name: 'down_left_arrow',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⬅️',
            skin_tone_support: false,
            name: 'left_arrow',
            unicode_version: '4.0',
            emoji_version: '2.0'
        },
        {
            emoji: '↖️',
            skin_tone_support: false,
            name: 'up_left_arrow',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '↕️',
            skin_tone_support: false,
            name: 'up_down_arrow',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '↔️',
            skin_tone_support: false,
            name: 'left_right_arrow',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '↩️',
            skin_tone_support: false,
            name: 'right_arrow_curving_left',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '↪️',
            skin_tone_support: false,
            name: 'left_arrow_curving_right',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⤴️',
            skin_tone_support: false,
            name: 'right_arrow_curving_up',
            unicode_version: '3.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⤵️',
            skin_tone_support: false,
            name: 'right_arrow_curving_down',
            unicode_version: '3.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🔃',
            skin_tone_support: false,
            name: 'clockwise_vertical_arrows',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔄',
            skin_tone_support: false,
            name: 'counterclockwise_arrows_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔙',
            skin_tone_support: false,
            name: 'back_arrow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔚',
            skin_tone_support: false,
            name: 'end_arrow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔛',
            skin_tone_support: false,
            name: 'on_arrow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔜',
            skin_tone_support: false,
            name: 'soon_arrow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔝',
            skin_tone_support: false,
            name: 'top_arrow',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🛐',
            skin_tone_support: false,
            name: 'place_of_worship',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⚛️',
            skin_tone_support: false,
            name: 'atom_symbol',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🕉️',
            skin_tone_support: false,
            name: 'om',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '✡️',
            skin_tone_support: false,
            name: 'star_of_david',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☸️',
            skin_tone_support: false,
            name: 'wheel_of_dharma',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☯️',
            skin_tone_support: false,
            name: 'yin_yang',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '✝️',
            skin_tone_support: false,
            name: 'latin_cross',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☦️',
            skin_tone_support: false,
            name: 'orthodox_cross',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☪️',
            skin_tone_support: false,
            name: 'star_and_crescent',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '☮️',
            skin_tone_support: false,
            name: 'peace_symbol',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🕎',
            skin_tone_support: false,
            name: 'menorah',
            unicode_version: '8.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔯',
            skin_tone_support: false,
            name: 'dotted_six_pointed_star',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '♈',
            skin_tone_support: false,
            name: 'aries',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♉',
            skin_tone_support: false,
            name: 'taurus',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♊',
            skin_tone_support: false,
            name: 'gemini',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♋',
            skin_tone_support: false,
            name: 'cancer',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♌',
            skin_tone_support: false,
            name: 'leo',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♍',
            skin_tone_support: false,
            name: 'virgo',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♎',
            skin_tone_support: false,
            name: 'libra',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♏',
            skin_tone_support: false,
            name: 'scorpio',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♐',
            skin_tone_support: false,
            name: 'sagittarius',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♑',
            skin_tone_support: false,
            name: 'capricorn',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♒',
            skin_tone_support: false,
            name: 'aquarius',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '♓',
            skin_tone_support: false,
            name: 'pisces',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⛎',
            skin_tone_support: false,
            name: 'ophiuchus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔀',
            skin_tone_support: false,
            name: 'shuffle_tracks_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔁',
            skin_tone_support: false,
            name: 'repeat_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔂',
            skin_tone_support: false,
            name: 'repeat_single_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '▶️',
            skin_tone_support: false,
            name: 'play_button',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⏩',
            skin_tone_support: false,
            name: 'fast_forward_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏭️',
            skin_tone_support: false,
            name: 'next_track_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏯️',
            skin_tone_support: false,
            name: 'play_or_pause_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '◀️',
            skin_tone_support: false,
            name: 'reverse_button',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⏪',
            skin_tone_support: false,
            name: 'fast_reverse_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏮️',
            skin_tone_support: false,
            name: 'last_track_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔼',
            skin_tone_support: false,
            name: 'upwards_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏫',
            skin_tone_support: false,
            name: 'fast_up_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔽',
            skin_tone_support: false,
            name: 'downwards_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏬',
            skin_tone_support: false,
            name: 'fast_down_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏸️',
            skin_tone_support: false,
            name: 'pause_button',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏹️',
            skin_tone_support: false,
            name: 'stop_button',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏺️',
            skin_tone_support: false,
            name: 'record_button',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⏏️',
            skin_tone_support: false,
            name: 'eject_button',
            unicode_version: '4.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎦',
            skin_tone_support: false,
            name: 'cinema',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔅',
            skin_tone_support: false,
            name: 'dim_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔆',
            skin_tone_support: false,
            name: 'bright_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📶',
            skin_tone_support: false,
            name: 'antenna_bars',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📳',
            skin_tone_support: false,
            name: 'vibration_mode',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📴',
            skin_tone_support: false,
            name: 'mobile_phone_off',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '♀️',
            skin_tone_support: false,
            name: 'female_sign',
            unicode_version: '1.1',
            emoji_version: '4.0'
        },
        {
            emoji: '♂️',
            skin_tone_support: false,
            name: 'male_sign',
            unicode_version: '1.1',
            emoji_version: '4.0'
        },
        {
            emoji: '⚕️',
            skin_tone_support: false,
            name: 'medical_symbol',
            unicode_version: '4.1',
            emoji_version: '4.0'
        },
        {
            emoji: '♾️',
            skin_tone_support: false,
            name: 'infinity',
            unicode_version: '4.1',
            emoji_version: '11.0'
        },
        {
            emoji: '♻️',
            skin_tone_support: false,
            name: 'recycling_symbol',
            unicode_version: '3.2',
            emoji_version: '2.0'
        },
        {
            emoji: '⚜️',
            skin_tone_support: false,
            name: 'fleur_de_lis',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🔱',
            skin_tone_support: false,
            name: 'trident_emblem',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '📛',
            skin_tone_support: false,
            name: 'name_badge',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔰',
            skin_tone_support: false,
            name: 'japanese_symbol_for_beginner',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '⭕',
            skin_tone_support: false,
            name: 'hollow_red_circle',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '✅',
            skin_tone_support: false,
            name: 'check_mark_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '☑️',
            skin_tone_support: false,
            name: 'check_box_with_check',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '✔️',
            skin_tone_support: false,
            name: 'check_mark',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '✖️',
            skin_tone_support: false,
            name: 'multiplication_sign',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '❌',
            skin_tone_support: false,
            name: 'cross_mark',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '❎',
            skin_tone_support: false,
            name: 'cross_mark_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '➕',
            skin_tone_support: false,
            name: 'plus_sign',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '➖',
            skin_tone_support: false,
            name: 'minus_sign',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '➗',
            skin_tone_support: false,
            name: 'division_sign',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '➰',
            skin_tone_support: false,
            name: 'curly_loop',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '➿',
            skin_tone_support: false,
            name: 'double_curly_loop',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '〽️',
            skin_tone_support: false,
            name: 'part_alternation_mark',
            unicode_version: '3.2',
            emoji_version: '2.0'
        },
        {
            emoji: '✳️',
            skin_tone_support: false,
            name: 'eight_spoked_asterisk',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '✴️',
            skin_tone_support: false,
            name: 'eight_pointed_star',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '❇️',
            skin_tone_support: false,
            name: 'sparkle',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '‼️',
            skin_tone_support: false,
            name: 'double_exclamation_mark',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⁉️',
            skin_tone_support: false,
            name: 'exclamation_question_mark',
            unicode_version: '3.0',
            emoji_version: '2.0'
        },
        {
            emoji: '❓',
            skin_tone_support: false,
            name: 'question_mark',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '❔',
            skin_tone_support: false,
            name: 'white_question_mark',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '❕',
            skin_tone_support: false,
            name: 'white_exclamation_mark',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '❗',
            skin_tone_support: false,
            name: 'exclamation_mark',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '〰️',
            skin_tone_support: false,
            name: 'wavy_dash',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '©️',
            skin_tone_support: false,
            name: 'copyright',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '®️',
            skin_tone_support: false,
            name: 'registered',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '™️',
            skin_tone_support: false,
            name: 'trade_mark',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '#️⃣',
            skin_tone_support: false,
            name: 'keycap_',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '*️⃣',
            skin_tone_support: false,
            name: 'keycap_',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '0️⃣',
            skin_tone_support: false,
            name: 'keycap_0',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '1️⃣',
            skin_tone_support: false,
            name: 'keycap_1',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '2️⃣',
            skin_tone_support: false,
            name: 'keycap_2',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '3️⃣',
            skin_tone_support: false,
            name: 'keycap_3',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '4️⃣',
            skin_tone_support: false,
            name: 'keycap_4',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '5️⃣',
            skin_tone_support: false,
            name: 'keycap_5',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '6️⃣',
            skin_tone_support: false,
            name: 'keycap_6',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '7️⃣',
            skin_tone_support: false,
            name: 'keycap_7',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '8️⃣',
            skin_tone_support: false,
            name: 'keycap_8',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '9️⃣',
            skin_tone_support: false,
            name: 'keycap_9',
            unicode_version: '3.2',
            emoji_version: '0.0'
        },
        {
            emoji: '🔟',
            skin_tone_support: false,
            name: 'keycap_10',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔠',
            skin_tone_support: false,
            name: 'input_latin_uppercase',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔡',
            skin_tone_support: false,
            name: 'input_latin_lowercase',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔢',
            skin_tone_support: false,
            name: 'input_numbers',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔣',
            skin_tone_support: false,
            name: 'input_symbols',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔤',
            skin_tone_support: false,
            name: 'input_latin_letters',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🅰️',
            skin_tone_support: false,
            name: 'a_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆎',
            skin_tone_support: false,
            name: 'ab_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🅱️',
            skin_tone_support: false,
            name: 'b_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆑',
            skin_tone_support: false,
            name: 'cl_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆒',
            skin_tone_support: false,
            name: 'cool_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆓',
            skin_tone_support: false,
            name: 'free_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: 'ℹ️',
            skin_tone_support: false,
            name: 'information',
            unicode_version: '3.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆔',
            skin_tone_support: false,
            name: 'id_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: 'Ⓜ️',
            skin_tone_support: false,
            name: 'circled_m',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🆕',
            skin_tone_support: false,
            name: 'new_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆖',
            skin_tone_support: false,
            name: 'ng_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🅾️',
            skin_tone_support: false,
            name: 'o_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆗',
            skin_tone_support: false,
            name: 'ok_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🅿️',
            skin_tone_support: false,
            name: 'p_button',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🆘',
            skin_tone_support: false,
            name: 'sos_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆙',
            skin_tone_support: false,
            name: 'up_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🆚',
            skin_tone_support: false,
            name: 'vs_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈁',
            skin_tone_support: false,
            name: 'japanese_here_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈂️',
            skin_tone_support: false,
            name: 'japanese_service_charge_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈷️',
            skin_tone_support: false,
            name: 'japanese_monthly_amount_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈶',
            skin_tone_support: false,
            name: 'japanese_not_free_of_charge_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈯',
            skin_tone_support: false,
            name: 'japanese_reserved_button',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🉐',
            skin_tone_support: false,
            name: 'japanese_bargain_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈹',
            skin_tone_support: false,
            name: 'japanese_discount_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈚',
            skin_tone_support: false,
            name: 'japanese_free_of_charge_button',
            unicode_version: '5.2',
            emoji_version: '2.0'
        },
        {
            emoji: '🈲',
            skin_tone_support: false,
            name: 'japanese_prohibited_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🉑',
            skin_tone_support: false,
            name: 'japanese_acceptable_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈸',
            skin_tone_support: false,
            name: 'japanese_application_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈴',
            skin_tone_support: false,
            name: 'japanese_passing_grade_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈳',
            skin_tone_support: false,
            name: 'japanese_vacancy_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '㊗️',
            skin_tone_support: false,
            name: 'japanese_congratulations_button',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '㊙️',
            skin_tone_support: false,
            name: 'japanese_secret_button',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🈺',
            skin_tone_support: false,
            name: 'japanese_open_for_business_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🈵',
            skin_tone_support: false,
            name: 'japanese_no_vacancy_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔴',
            skin_tone_support: false,
            name: 'red_circle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🟠',
            skin_tone_support: false,
            name: 'orange_circle',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟡',
            skin_tone_support: false,
            name: 'yellow_circle',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟢',
            skin_tone_support: false,
            name: 'green_circle',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🔵',
            skin_tone_support: false,
            name: 'blue_circle',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🟣',
            skin_tone_support: false,
            name: 'purple_circle',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟤',
            skin_tone_support: false,
            name: 'brown_circle',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '⚫',
            skin_tone_support: false,
            name: 'black_circle',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⚪',
            skin_tone_support: false,
            name: 'white_circle',
            unicode_version: '4.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🟥',
            skin_tone_support: false,
            name: 'red_square',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟧',
            skin_tone_support: false,
            name: 'orange_square',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟨',
            skin_tone_support: false,
            name: 'yellow_square',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟩',
            skin_tone_support: false,
            name: 'green_square',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟦',
            skin_tone_support: false,
            name: 'blue_square',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟪',
            skin_tone_support: false,
            name: 'purple_square',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '🟫',
            skin_tone_support: false,
            name: 'brown_square',
            unicode_version: '12.0',
            emoji_version: '12.1'
        },
        {
            emoji: '⬛',
            skin_tone_support: false,
            name: 'black_large_square',
            unicode_version: '5.1',
            emoji_version: '2.0'
        },
        {
            emoji: '⬜',
            skin_tone_support: false,
            name: 'white_large_square',
            unicode_version: '5.1',
            emoji_version: '2.0'
        },
        {
            emoji: '◼️',
            skin_tone_support: false,
            name: 'black_medium_square',
            unicode_version: '3.2',
            emoji_version: '2.0'
        },
        {
            emoji: '◻️',
            skin_tone_support: false,
            name: 'white_medium_square',
            unicode_version: '3.2',
            emoji_version: '2.0'
        },
        {
            emoji: '◾',
            skin_tone_support: false,
            name: 'black_medium_small_square',
            unicode_version: '3.2',
            emoji_version: '2.0'
        },
        {
            emoji: '◽',
            skin_tone_support: false,
            name: 'white_medium_small_square',
            unicode_version: '3.2',
            emoji_version: '2.0'
        },
        {
            emoji: '▪️',
            skin_tone_support: false,
            name: 'black_small_square',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '▫️',
            skin_tone_support: false,
            name: 'white_small_square',
            unicode_version: '1.1',
            emoji_version: '2.0'
        },
        {
            emoji: '🔶',
            skin_tone_support: false,
            name: 'large_orange_diamond',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔷',
            skin_tone_support: false,
            name: 'large_blue_diamond',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔸',
            skin_tone_support: false,
            name: 'small_orange_diamond',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔹',
            skin_tone_support: false,
            name: 'small_blue_diamond',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔺',
            skin_tone_support: false,
            name: 'red_triangle_pointed_up',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔻',
            skin_tone_support: false,
            name: 'red_triangle_pointed_down',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '💠',
            skin_tone_support: false,
            name: 'diamond_with_a_dot',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔘',
            skin_tone_support: false,
            name: 'radio_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔳',
            skin_tone_support: false,
            name: 'white_square_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🔲',
            skin_tone_support: false,
            name: 'black_square_button',
            unicode_version: '6.0',
            emoji_version: '2.0'
        }
    ],
    Flags: [
        {
            emoji: '🏁',
            skin_tone_support: false,
            name: 'chequered_flag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🚩',
            skin_tone_support: false,
            name: 'triangular_flag',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🎌',
            skin_tone_support: false,
            name: 'crossed_flags',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏴',
            skin_tone_support: false,
            name: 'black_flag',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏳️',
            skin_tone_support: false,
            name: 'white_flag',
            unicode_version: '7.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏳️‍🌈',
            skin_tone_support: false,
            name: 'rainbow_flag',
            unicode_version: '7.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🏴‍☠️',
            skin_tone_support: false,
            name: 'pirate_flag',
            unicode_version: '7.0',
            emoji_version: '11.0'
        },
        {
            emoji: '🇦🇨',
            skin_tone_support: false,
            name: 'flag_ascension_island',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇩',
            skin_tone_support: false,
            name: 'flag_andorra',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇪',
            skin_tone_support: false,
            name: 'flag_united_arab_emirates',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇫',
            skin_tone_support: false,
            name: 'flag_afghanistan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇬',
            skin_tone_support: false,
            name: 'flag_antigua_barbuda',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇮',
            skin_tone_support: false,
            name: 'flag_anguilla',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇱',
            skin_tone_support: false,
            name: 'flag_albania',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇲',
            skin_tone_support: false,
            name: 'flag_armenia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇴',
            skin_tone_support: false,
            name: 'flag_angola',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇶',
            skin_tone_support: false,
            name: 'flag_antarctica',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇷',
            skin_tone_support: false,
            name: 'flag_argentina',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇸',
            skin_tone_support: false,
            name: 'flag_american_samoa',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇹',
            skin_tone_support: false,
            name: 'flag_austria',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇺',
            skin_tone_support: false,
            name: 'flag_australia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇼',
            skin_tone_support: false,
            name: 'flag_aruba',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇽',
            skin_tone_support: false,
            name: 'flag_land_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇦🇿',
            skin_tone_support: false,
            name: 'flag_azerbaijan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇦',
            skin_tone_support: false,
            name: 'flag_bosnia_herzegovina',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇧',
            skin_tone_support: false,
            name: 'flag_barbados',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇩',
            skin_tone_support: false,
            name: 'flag_bangladesh',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇪',
            skin_tone_support: false,
            name: 'flag_belgium',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇫',
            skin_tone_support: false,
            name: 'flag_burkina_faso',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇬',
            skin_tone_support: false,
            name: 'flag_bulgaria',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇭',
            skin_tone_support: false,
            name: 'flag_bahrain',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇮',
            skin_tone_support: false,
            name: 'flag_burundi',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇯',
            skin_tone_support: false,
            name: 'flag_benin',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇱',
            skin_tone_support: false,
            name: 'flag_st_barth_lemy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇲',
            skin_tone_support: false,
            name: 'flag_bermuda',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇳',
            skin_tone_support: false,
            name: 'flag_brunei',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇴',
            skin_tone_support: false,
            name: 'flag_bolivia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇶',
            skin_tone_support: false,
            name: 'flag_caribbean_netherlands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇷',
            skin_tone_support: false,
            name: 'flag_brazil',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇸',
            skin_tone_support: false,
            name: 'flag_bahamas',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇹',
            skin_tone_support: false,
            name: 'flag_bhutan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇻',
            skin_tone_support: false,
            name: 'flag_bouvet_island',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇼',
            skin_tone_support: false,
            name: 'flag_botswana',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇾',
            skin_tone_support: false,
            name: 'flag_belarus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇧🇿',
            skin_tone_support: false,
            name: 'flag_belize',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇦',
            skin_tone_support: false,
            name: 'flag_canada',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇨',
            skin_tone_support: false,
            name: 'flag_cocos_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇩',
            skin_tone_support: false,
            name: 'flag_congo_kinshasa',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇫',
            skin_tone_support: false,
            name: 'flag_central_african_republic',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇬',
            skin_tone_support: false,
            name: 'flag_congo_brazzaville',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇭',
            skin_tone_support: false,
            name: 'flag_switzerland',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇮',
            skin_tone_support: false,
            name: 'flag_c_te_d_ivoire',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇰',
            skin_tone_support: false,
            name: 'flag_cook_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇱',
            skin_tone_support: false,
            name: 'flag_chile',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇲',
            skin_tone_support: false,
            name: 'flag_cameroon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇳',
            skin_tone_support: false,
            name: 'flag_china',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇴',
            skin_tone_support: false,
            name: 'flag_colombia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇵',
            skin_tone_support: false,
            name: 'flag_clipperton_island',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇷',
            skin_tone_support: false,
            name: 'flag_costa_rica',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇺',
            skin_tone_support: false,
            name: 'flag_cuba',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇻',
            skin_tone_support: false,
            name: 'flag_cape_verde',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇼',
            skin_tone_support: false,
            name: 'flag_cura_ao',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇽',
            skin_tone_support: false,
            name: 'flag_christmas_island',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇾',
            skin_tone_support: false,
            name: 'flag_cyprus',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇨🇿',
            skin_tone_support: false,
            name: 'flag_czechia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇩🇪',
            skin_tone_support: false,
            name: 'flag_germany',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇩🇬',
            skin_tone_support: false,
            name: 'flag_diego_garcia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇩🇯',
            skin_tone_support: false,
            name: 'flag_djibouti',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇩🇰',
            skin_tone_support: false,
            name: 'flag_denmark',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇩🇲',
            skin_tone_support: false,
            name: 'flag_dominica',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇩🇴',
            skin_tone_support: false,
            name: 'flag_dominican_republic',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇩🇿',
            skin_tone_support: false,
            name: 'flag_algeria',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇦',
            skin_tone_support: false,
            name: 'flag_ceuta_melilla',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇨',
            skin_tone_support: false,
            name: 'flag_ecuador',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇪',
            skin_tone_support: false,
            name: 'flag_estonia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇬',
            skin_tone_support: false,
            name: 'flag_egypt',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇭',
            skin_tone_support: false,
            name: 'flag_western_sahara',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇷',
            skin_tone_support: false,
            name: 'flag_eritrea',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇸',
            skin_tone_support: false,
            name: 'flag_spain',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇹',
            skin_tone_support: false,
            name: 'flag_ethiopia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇪🇺',
            skin_tone_support: false,
            name: 'flag_european_union',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇫🇮',
            skin_tone_support: false,
            name: 'flag_finland',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇫🇯',
            skin_tone_support: false,
            name: 'flag_fiji',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇫🇰',
            skin_tone_support: false,
            name: 'flag_falkland_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇫🇲',
            skin_tone_support: false,
            name: 'flag_micronesia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇫🇴',
            skin_tone_support: false,
            name: 'flag_faroe_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇫🇷',
            skin_tone_support: false,
            name: 'flag_france',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇦',
            skin_tone_support: false,
            name: 'flag_gabon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇧',
            skin_tone_support: false,
            name: 'flag_united_kingdom',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇩',
            skin_tone_support: false,
            name: 'flag_grenada',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇪',
            skin_tone_support: false,
            name: 'flag_georgia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇫',
            skin_tone_support: false,
            name: 'flag_french_guiana',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇬',
            skin_tone_support: false,
            name: 'flag_guernsey',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇭',
            skin_tone_support: false,
            name: 'flag_ghana',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇮',
            skin_tone_support: false,
            name: 'flag_gibraltar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇱',
            skin_tone_support: false,
            name: 'flag_greenland',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇲',
            skin_tone_support: false,
            name: 'flag_gambia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇳',
            skin_tone_support: false,
            name: 'flag_guinea',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇵',
            skin_tone_support: false,
            name: 'flag_guadeloupe',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇶',
            skin_tone_support: false,
            name: 'flag_equatorial_guinea',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇷',
            skin_tone_support: false,
            name: 'flag_greece',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇸',
            skin_tone_support: false,
            name: 'flag_south_georgia_south_sandwich_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇹',
            skin_tone_support: false,
            name: 'flag_guatemala',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇺',
            skin_tone_support: false,
            name: 'flag_guam',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇼',
            skin_tone_support: false,
            name: 'flag_guinea_bissau',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇬🇾',
            skin_tone_support: false,
            name: 'flag_guyana',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇭🇰',
            skin_tone_support: false,
            name: 'flag_hong_kong_sar_china',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇭🇲',
            skin_tone_support: false,
            name: 'flag_heard_mcdonald_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇭🇳',
            skin_tone_support: false,
            name: 'flag_honduras',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇭🇷',
            skin_tone_support: false,
            name: 'flag_croatia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇭🇹',
            skin_tone_support: false,
            name: 'flag_haiti',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇭🇺',
            skin_tone_support: false,
            name: 'flag_hungary',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇨',
            skin_tone_support: false,
            name: 'flag_canary_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇩',
            skin_tone_support: false,
            name: 'flag_indonesia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇪',
            skin_tone_support: false,
            name: 'flag_ireland',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇱',
            skin_tone_support: false,
            name: 'flag_israel',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇲',
            skin_tone_support: false,
            name: 'flag_isle_of_man',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇳',
            skin_tone_support: false,
            name: 'flag_india',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇴',
            skin_tone_support: false,
            name: 'flag_british_indian_ocean_territory',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇶',
            skin_tone_support: false,
            name: 'flag_iraq',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇷',
            skin_tone_support: false,
            name: 'flag_iran',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇸',
            skin_tone_support: false,
            name: 'flag_iceland',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇮🇹',
            skin_tone_support: false,
            name: 'flag_italy',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇯🇪',
            skin_tone_support: false,
            name: 'flag_jersey',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇯🇲',
            skin_tone_support: false,
            name: 'flag_jamaica',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇯🇴',
            skin_tone_support: false,
            name: 'flag_jordan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇯🇵',
            skin_tone_support: false,
            name: 'flag_japan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇪',
            skin_tone_support: false,
            name: 'flag_kenya',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇬',
            skin_tone_support: false,
            name: 'flag_kyrgyzstan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇭',
            skin_tone_support: false,
            name: 'flag_cambodia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇮',
            skin_tone_support: false,
            name: 'flag_kiribati',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇲',
            skin_tone_support: false,
            name: 'flag_comoros',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇳',
            skin_tone_support: false,
            name: 'flag_st_kitts_nevis',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇵',
            skin_tone_support: false,
            name: 'flag_north_korea',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇷',
            skin_tone_support: false,
            name: 'flag_south_korea',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇼',
            skin_tone_support: false,
            name: 'flag_kuwait',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇾',
            skin_tone_support: false,
            name: 'flag_cayman_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇰🇿',
            skin_tone_support: false,
            name: 'flag_kazakhstan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇦',
            skin_tone_support: false,
            name: 'flag_laos',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇧',
            skin_tone_support: false,
            name: 'flag_lebanon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇨',
            skin_tone_support: false,
            name: 'flag_st_lucia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇮',
            skin_tone_support: false,
            name: 'flag_liechtenstein',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇰',
            skin_tone_support: false,
            name: 'flag_sri_lanka',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇷',
            skin_tone_support: false,
            name: 'flag_liberia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇸',
            skin_tone_support: false,
            name: 'flag_lesotho',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇹',
            skin_tone_support: false,
            name: 'flag_lithuania',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇺',
            skin_tone_support: false,
            name: 'flag_luxembourg',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇻',
            skin_tone_support: false,
            name: 'flag_latvia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇱🇾',
            skin_tone_support: false,
            name: 'flag_libya',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇦',
            skin_tone_support: false,
            name: 'flag_morocco',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇨',
            skin_tone_support: false,
            name: 'flag_monaco',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇩',
            skin_tone_support: false,
            name: 'flag_moldova',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇪',
            skin_tone_support: false,
            name: 'flag_montenegro',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇫',
            skin_tone_support: false,
            name: 'flag_st_martin',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇬',
            skin_tone_support: false,
            name: 'flag_madagascar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇭',
            skin_tone_support: false,
            name: 'flag_marshall_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇰',
            skin_tone_support: false,
            name: 'flag_north_macedonia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇱',
            skin_tone_support: false,
            name: 'flag_mali',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇲',
            skin_tone_support: false,
            name: 'flag_myanmar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇳',
            skin_tone_support: false,
            name: 'flag_mongolia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇴',
            skin_tone_support: false,
            name: 'flag_macao_sar_china',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇵',
            skin_tone_support: false,
            name: 'flag_northern_mariana_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇶',
            skin_tone_support: false,
            name: 'flag_martinique',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇷',
            skin_tone_support: false,
            name: 'flag_mauritania',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇸',
            skin_tone_support: false,
            name: 'flag_montserrat',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇹',
            skin_tone_support: false,
            name: 'flag_malta',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇺',
            skin_tone_support: false,
            name: 'flag_mauritius',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇻',
            skin_tone_support: false,
            name: 'flag_maldives',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇼',
            skin_tone_support: false,
            name: 'flag_malawi',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇽',
            skin_tone_support: false,
            name: 'flag_mexico',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇾',
            skin_tone_support: false,
            name: 'flag_malaysia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇲🇿',
            skin_tone_support: false,
            name: 'flag_mozambique',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇦',
            skin_tone_support: false,
            name: 'flag_namibia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇨',
            skin_tone_support: false,
            name: 'flag_new_caledonia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇪',
            skin_tone_support: false,
            name: 'flag_niger',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇫',
            skin_tone_support: false,
            name: 'flag_norfolk_island',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇬',
            skin_tone_support: false,
            name: 'flag_nigeria',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇮',
            skin_tone_support: false,
            name: 'flag_nicaragua',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇱',
            skin_tone_support: false,
            name: 'flag_netherlands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇴',
            skin_tone_support: false,
            name: 'flag_norway',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇵',
            skin_tone_support: false,
            name: 'flag_nepal',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇷',
            skin_tone_support: false,
            name: 'flag_nauru',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇺',
            skin_tone_support: false,
            name: 'flag_niue',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇳🇿',
            skin_tone_support: false,
            name: 'flag_new_zealand',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇴🇲',
            skin_tone_support: false,
            name: 'flag_oman',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇦',
            skin_tone_support: false,
            name: 'flag_panama',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇪',
            skin_tone_support: false,
            name: 'flag_peru',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇫',
            skin_tone_support: false,
            name: 'flag_french_polynesia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇬',
            skin_tone_support: false,
            name: 'flag_papua_new_guinea',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇭',
            skin_tone_support: false,
            name: 'flag_philippines',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇰',
            skin_tone_support: false,
            name: 'flag_pakistan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇱',
            skin_tone_support: false,
            name: 'flag_poland',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇲',
            skin_tone_support: false,
            name: 'flag_st_pierre_miquelon',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇳',
            skin_tone_support: false,
            name: 'flag_pitcairn_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇷',
            skin_tone_support: false,
            name: 'flag_puerto_rico',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇸',
            skin_tone_support: false,
            name: 'flag_palestinian_territories',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇹',
            skin_tone_support: false,
            name: 'flag_portugal',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇼',
            skin_tone_support: false,
            name: 'flag_palau',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇵🇾',
            skin_tone_support: false,
            name: 'flag_paraguay',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇶🇦',
            skin_tone_support: false,
            name: 'flag_qatar',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇷🇪',
            skin_tone_support: false,
            name: 'flag_r_union',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇷🇴',
            skin_tone_support: false,
            name: 'flag_romania',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇷🇸',
            skin_tone_support: false,
            name: 'flag_serbia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇷🇺',
            skin_tone_support: false,
            name: 'flag_russia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇷🇼',
            skin_tone_support: false,
            name: 'flag_rwanda',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇦',
            skin_tone_support: false,
            name: 'flag_saudi_arabia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇧',
            skin_tone_support: false,
            name: 'flag_solomon_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇨',
            skin_tone_support: false,
            name: 'flag_seychelles',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇩',
            skin_tone_support: false,
            name: 'flag_sudan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇪',
            skin_tone_support: false,
            name: 'flag_sweden',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇬',
            skin_tone_support: false,
            name: 'flag_singapore',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇭',
            skin_tone_support: false,
            name: 'flag_st_helena',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇮',
            skin_tone_support: false,
            name: 'flag_slovenia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇯',
            skin_tone_support: false,
            name: 'flag_svalbard_jan_mayen',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇰',
            skin_tone_support: false,
            name: 'flag_slovakia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇱',
            skin_tone_support: false,
            name: 'flag_sierra_leone',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇲',
            skin_tone_support: false,
            name: 'flag_san_marino',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇳',
            skin_tone_support: false,
            name: 'flag_senegal',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇴',
            skin_tone_support: false,
            name: 'flag_somalia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇷',
            skin_tone_support: false,
            name: 'flag_suriname',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇸',
            skin_tone_support: false,
            name: 'flag_south_sudan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇹',
            skin_tone_support: false,
            name: 'flag_s_o_tom_pr_ncipe',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇻',
            skin_tone_support: false,
            name: 'flag_el_salvador',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇽',
            skin_tone_support: false,
            name: 'flag_sint_maarten',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇾',
            skin_tone_support: false,
            name: 'flag_syria',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇸🇿',
            skin_tone_support: false,
            name: 'flag_eswatini',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇦',
            skin_tone_support: false,
            name: 'flag_tristan_da_cunha',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇨',
            skin_tone_support: false,
            name: 'flag_turks_caicos_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇩',
            skin_tone_support: false,
            name: 'flag_chad',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇫',
            skin_tone_support: false,
            name: 'flag_french_southern_territories',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇬',
            skin_tone_support: false,
            name: 'flag_togo',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇭',
            skin_tone_support: false,
            name: 'flag_thailand',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇯',
            skin_tone_support: false,
            name: 'flag_tajikistan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇰',
            skin_tone_support: false,
            name: 'flag_tokelau',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇱',
            skin_tone_support: false,
            name: 'flag_timor_leste',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇲',
            skin_tone_support: false,
            name: 'flag_turkmenistan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇳',
            skin_tone_support: false,
            name: 'flag_tunisia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇴',
            skin_tone_support: false,
            name: 'flag_tonga',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇷',
            skin_tone_support: false,
            name: 'flag_turkey',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇹',
            skin_tone_support: false,
            name: 'flag_trinidad_tobago',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇻',
            skin_tone_support: false,
            name: 'flag_tuvalu',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇼',
            skin_tone_support: false,
            name: 'flag_taiwan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇹🇿',
            skin_tone_support: false,
            name: 'flag_tanzania',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇺🇦',
            skin_tone_support: false,
            name: 'flag_ukraine',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇺🇬',
            skin_tone_support: false,
            name: 'flag_uganda',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇺🇲',
            skin_tone_support: false,
            name: 'flag_u_s_outlying_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇺🇳',
            skin_tone_support: false,
            name: 'flag_united_nations',
            unicode_version: '6.0',
            emoji_version: '4.0'
        },
        {
            emoji: '🇺🇸',
            skin_tone_support: false,
            name: 'flag_united_states',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇺🇾',
            skin_tone_support: false,
            name: 'flag_uruguay',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇺🇿',
            skin_tone_support: false,
            name: 'flag_uzbekistan',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇻🇦',
            skin_tone_support: false,
            name: 'flag_vatican_city',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇻🇨',
            skin_tone_support: false,
            name: 'flag_st_vincent_grenadines',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇻🇪',
            skin_tone_support: false,
            name: 'flag_venezuela',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇻🇬',
            skin_tone_support: false,
            name: 'flag_british_virgin_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇻🇮',
            skin_tone_support: false,
            name: 'flag_u_s_virgin_islands',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇻🇳',
            skin_tone_support: false,
            name: 'flag_vietnam',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇻🇺',
            skin_tone_support: false,
            name: 'flag_vanuatu',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇼🇫',
            skin_tone_support: false,
            name: 'flag_wallis_futuna',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇼🇸',
            skin_tone_support: false,
            name: 'flag_samoa',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇽🇰',
            skin_tone_support: false,
            name: 'flag_kosovo',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇾🇪',
            skin_tone_support: false,
            name: 'flag_yemen',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇾🇹',
            skin_tone_support: false,
            name: 'flag_mayotte',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇿🇦',
            skin_tone_support: false,
            name: 'flag_south_africa',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇿🇲',
            skin_tone_support: false,
            name: 'flag_zambia',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🇿🇼',
            skin_tone_support: false,
            name: 'flag_zimbabwe',
            unicode_version: '6.0',
            emoji_version: '2.0'
        },
        {
            emoji: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
            skin_tone_support: false,
            name: 'flag_england',
            unicode_version: '7.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🏴󠁧󠁢󠁳󠁣󠁴󠁿',
            skin_tone_support: false,
            name: 'flag_scotland',
            unicode_version: '7.0',
            emoji_version: '5.0'
        },
        {
            emoji: '🏴󠁧󠁢󠁷󠁬󠁳󠁿',
            skin_tone_support: false,
            name: 'flag_wales',
            unicode_version: '7.0',
            emoji_version: '5.0'
        }
    ]
};
