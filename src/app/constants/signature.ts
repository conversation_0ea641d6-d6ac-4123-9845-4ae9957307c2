export class Signature {
  static paletteType = {
    checkboxField: 'checkbox',
    textField: 'text',
    signatureField: 'signature',
    signValue: 'Sign',
    textValue: 'Text',
    checkboxValue: 'Checkbox'
  };
  static checkboxGroupValidationBehavior = {
    exactlyOneRequiredValue: 1,
    atleastOneRequiredValue: 2,
    allRequiredValue: 3,
    optional: 4
  };
  static paletteSize = {
    minWidth: '30',
    maxWidth: '600',
    minHeight: '18',
    maxHeight: '350',
    checkboxLabelWidth: 35,
    checkboxLabelHeight: 18,
    checkBoxLabelWidthRedeuse: 31,
    checkBoxLabelHeightRedeuse: 5,
    responsiveCheckBox: 11
  };
  static scrollDefaultValue = 100;
  static adjustHeight = 60;
  static adjustHeightiOS = 80;
  static mandatory = 'Mandatory';
  static notMandatory = 'Not mandatory';
  static approverValue = 'approver';
  static selfValue = 'self';
  static mandatoryValue = 1;
  static optionalValue = 3;
  static senderChoice = 2;
  static docTypeTags = {
    preSignedSnapSend: 'Pre signed, Snap & Send',
    estimateCreateSend: 'Estimate Create & Send',
    estimateCreateSaveSign: 'Estimate Create, Save & Sign',
    estimatePresignedSnapSend: 'Estimate Pre-signed Snap & Send',
    presignSnapSend: 'Pre-sign Snap & Send',
    presignedSnapSend: 'Presigned Snap & Send',
    createSend: 'Create & Send',
    createSaveSign: 'Create, Save & Sign',
    pre_signed_SnapSend: 'Pre-signed Snap & Send',
    pre_SignedSnapSend: 'Pre-Signed, Snap & Send',
    pre_signed: 'Pre-signed',
    pre_Signed: 'Pre-Signed',
    presigned: 'Presigned',
    snapSend: 'Snap & Send'
  };
  static signaturePattern = /(?:\r\n|\r|\n)/g;
  static checkboxPaletteRegex = /<br\s*[\/]?>/gi;
  static signReplaceValue = 'data:image/png;base64,';
  static backgroundColor = {
    meBorderColor: 'rgb(0, 0, 0)',
    otherBorderColor: 'rgb(53, 171, 221)',
    associateBorderColor: 'rgb(19, 191, 76)',
    toBeSignedBorder: '1px dotted #f00',
    boxShadow: 'rgba(221, 0, 0, 0.52) 0px 0px 2px 1px',
    signedBorder: '1px dotted rgb(53, 171, 221)',
    signedBoxShadow: 'rgba(0, 171, 221, 0.7) 0px 0px 2px 0.5px',
    meBorderDottedColor: '1px dotted rgb(0, 0, 0)',
    otherBorderDottedColor: '1px dotted rgb(53, 171, 221)',
    associateBorderDottedColor: '1px dotted rgb(19, 191, 76)',
    optionalToBeSignedBorder: '1px dotted #35abdd',
    optionalBoxShadow: '0 0 2px 1px rgba(0, 171, 221,0.7)',
    navigateBorder: '1px solid rgb(220 166 23)',
    navigateBoxShadow: 'rgb(220 166 23 / 80%) 0px 0px 2px 1px'
  };
  static setCursor = {
    pointer: 'pointer',
    notAllowed: 'not-allowed'
  };
  static signatureStatus = {
    signaturePendingStatus: 'PENDING',
    signatureSignedStatus: 'SIGNED',
    signatureArchiveStatus: 'ARCHIVE',
    signatureApprovalStatus: 'SIGN_APPROVAL',
    snapSignApproval: 'SANP_SIGN_APPROVAL',
    signatureDraftStatus: 'DRAFT'
  };
  static variableTypes = {
    stringType: 'string',
    booleanType: 'boolean',
    objectType: 'object'
  };
  static paletteLock = 2;
  static paletteLockZero = 0;
  static documentCategories = 'documentCategories';
  static paletteStatuses = {
    signed: 'SIGNED',
    signing: 'SIGNING',
    confirmed: 'CONFIRMED',
    notConfirmed: 'NOT_CONFIRMED',
    notSigned: 'NOT_SIGNED'
  };
  static canvasAttributes = {
    offsetX: 0,
    offsetY: 0,
    height: 205,
    width: 490,
    fillStyle: 'rgba(0,0,0,0)',
    nameCanvasFont: 'bold 16px arial',
    dateCanvasFont: 'bold 20px arial',
    nameCanvasHeight: 260,
    nameCanvasOnlyWidth: 150,
    nameCanvasOnlyHeight: 27,
    combinedCanvasHeight: 300,
    dateCanvasHeight: 200,
    fillTextX: 10,
    fillTextY: 20,
    dx: 0,
    dy: 0,
    dw: 490,
    dh1: 160,
    dh2Date: 40,
    dh2Name: 100,
    dy2DnT: 155,
    dy3DnT: 245,
    dh2DnT: 90,
    dh3DnT: 40
  };
  static prepopulationFields = {
    mrn: 'mrn',
    guid: 'guid',
    firstName: 'firstName',
    lastName: 'lastName',
    flname: 'flname',
    lfname: 'lfname',
    dob: 'dob',
    mobile: 'mobile',
    email: 'email',
    gender: 'gender'
  };
}
