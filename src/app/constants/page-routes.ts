export class PageRoutes {
  static accessDenied = '/access-denied';
  static invite = 'invite';
  static add = 'add';
  static home = '/home';
  static login = '/login';
  static download = '/download';
  static userConsent = '/user-consent';
  static forgotPassword = '/forgot-password';
  static root = '/';
  static activeMessages = '/message-center/messages/active';
  static chatRoom = `${this.activeMessages}/chat`;
  static pendingForms = '/form-center/pending-forms';
  static draftForms = '/form-center/draft-forms';
  static completedForms = '/form-center/completed-forms';
  static formFlow = '/form-center/form-flow';
  static sendForms = '/form-center/send-forms';
  static viewOfflineForm = '/form-center/view-offline-form';
  static viewForms = '/form-center/view-forms';
  static offlineForms = '/form-center/offline-forms';
  static broadCastMessage = '/message-center/send-bulk-messages/broadcast';
  static maskedMessage = '/message-center/send-bulk-messages/masked';
  static archivedMessage = '/message-center/messages/archived';
  static addUser = '/user/add';
  static inviteUser = '/user/invite';
  static profileUser = '/user/profile';
  static documentCenter = '/document-center';
  static formCenter = '/form-center';
  static educationCenterMaterial = '/education-center/materials';
  static scheduleCenterVisits = '/schedule-center/visits';
  static scheduleCenterManage = '/schedule-center/manage';
  static scheduleCenterVisitsView = `${this.scheduleCenterVisits}/view`;
  static patientActivity = '/patient-activity';
  static support = '/support';
  static pendingDeliveries = '/delivery-center/pending-delivery';
  static completedDeliveries = '/delivery-center/completed-delivery';
  static deliveryCenter = '/delivery-center';
  static deliveryDetails = '/delivery-center/delivery-details';
  static videoCallPage = '/video-call';
  static createVisit = `${this.scheduleCenterVisits}/create-visit`;
  static pahActivityProfile = 'patient-activity/profile/';
  static pahActivityDemographicProfile = 'patient-activity/demographic-profile/';
  static pahActivityAdmissionDetails = 'patient-activity/admission-details';
  static applessURLPathVideo = '/videoAppless';
  static applessURLPathMessage = '/appLessMessage';
  static applessURLPathForm = '/form-appless-signview';
  static applessURLPathDoc = '/managedocument-appless-signview';
  static applessURLPathDownload = '/appless-download';
  static applessURLPathVisitView = '/appless-visit-view';
  static readonly appLessPage = '/appLessPage';
  static readonly appLessUserConsent = '/appLessUserConsent';
  static readonly appLess = '/appless';
}
