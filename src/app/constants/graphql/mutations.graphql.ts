import gql from 'graphql-tag';

export const Mutations = {
  resendSignatureDocument: gql`
    mutation resendSignatureDocument($sessionId: String!, $id: Int) {
      resendSignatureDocument(sessionId: $sessionId, params: { id: $id }) {
        id
        createdOn
      }
    }
  `,
  cancelSignatureDocumentTenant: gql`
    mutation cancelSignatureDocument($sessionId: String!, $id: Int, $action: String, $cancellationReason: String) {
      cancelSignatureDocument(sessionId: $sessionId, params: { id: $id, level: 1, action: $action, cancellationReason: $cancellationReason }) {
        id
        cancelledUsers
        cancelledOn
      }
    }
  `,
  archiveSignatureDocumentTenant: gql`
    mutation archiveSignatureDocument($sessionId: String!, $id: Int, $action: String, $crossTenantId: Int!) {
      archiveSignatureDocument(
        sessionId: $sessionId
        crossTenantId: $crossTenantId
        params: { id: $id, level: 1, action: $action }
      ) {
        id
        archivedUsers
        accountLevelArchived
        archivedOn
      }
    }
  `,
  uploadFileToCmis: gql`
    mutation uploadFileToCmis($sessionId: String!, $fileUniqueName: String!, $metaData: String!) {
      uploadFileToCmis(sessionId: $sessionId, params: { fileUniqueName: $fileUniqueName, metaData: $metaData }) {
        cmisFileData
      }
    }
  `,
  completeDocumentSignatureRequest: gql`
    mutation completeDocumentSignatureRequest(
      $sessionId: String!
      $documentId: Int!
      $signaturePalette: [SignaturePaletteInput]
    ) {
      completeDocumentSignatureRequest(
        sessionId: $sessionId
        params: { document: { documentId: $documentId, signaturePalette: $signaturePalette } }
      ) {
        id
        createdOn
        owner
        ownerId
        isRead
        archivedUsers
        accountLevelArchived
        associateSignatureProcess
        notifyOnSubmitSignatureRoles
        notifyOnSubmitSignatureUsers
        document {
          displayText
          pageCount
          associatePatient
          associateUser
        }
        ownerId
        owner
        signatureByUsers {
          userId
          displayName
        }
        associateSignatureByUsers {
          userId
          displayName
        }
        displayText {
          text
          document
        }
        type {
          id
          name
          obtainSignature
          allowRecipientRoles
          allowPendingApproveSignature
        }
        signatureStatus
      }
    }
  `,
  copyToFilingCenterAfterSignature: gql`
    mutation copyToFilingCenterAfterSignature(
      $sessionId: String!
      $fileUrlDownload: String!
      $fileTargetNameUpdated: String!
      $ownerIdCopyFilingCenter: Int!
      $exchangeData: String!
      $type: String!
      $folderNameCopyFilingCenter: String!
    ) {
      copyToFilingCenterAfterSignature(
        sessionId: $sessionId
        params: {
          fileName: $fileUrlDownload
          targetFileName: $fileTargetNameUpdated
          ownerId: $ownerIdCopyFilingCenter
          exchangeData: $exchangeData
          folder: { type: $type, folderName: $folderNameCopyFilingCenter }
        }
      ) {
        folder
        name
      }
    }
  `,
  copyToFilingCenterAfterSignatureMultisite: gql`
    mutation copyToFilingCenterAfterSignature(
      $sessionId: String!
      $fileUrlDownload: String!
      $fileTargetNameUpdated: String!
      $ownerIdCopyFilingCenter: Int!
      $exchangeData: String!
      $type: String!
      $folderNameCopyFilingCenter: String!
      $enableMultisite: String!
      $siteId: Int!
    ) {
      copyToFilingCenterAfterSignature(
        sessionId: $sessionId
        params: {
          fileName: $fileUrlDownload
          targetFileName: $fileTargetNameUpdated
          ownerId: $ownerIdCopyFilingCenter
          exchangeData: $exchangeData
          folder: { type: $type, folderName: $folderNameCopyFilingCenter }
          enableMultisite: $enableMultisite
          siteId: $siteId
        }
      ) {
        folder
        name
      }
    }
  `,
  signDocument: gql`
    mutation signDocument(
      $sessionId: String!
      $snapAndSaveApprove: Boolean
      $contentID: Int
      $pageCount: Int
      $associateSignatureProcess: Boolean
      $checkboxImageBehavior: Int
      $completeSignatureProcess: Boolean
      $approveSignatureProcess: Boolean
      $documentOwner: Int
      $documentName: String!
      $signatureData: [signatureDataInput]
      $documentpageSize: String!
      $signatureCrossTenant: Int
    ) {
      signDocument(
        sessionId: $sessionId
        params: {
          snapAndSaveApprove: $snapAndSaveApprove
          contentID: $contentID
          pageCount: $pageCount
          associateSignatureProcess: $associateSignatureProcess
          checkboxImageBehavior: $checkboxImageBehavior
          completeSignatureProcess: $completeSignatureProcess
          approveSignatureProcess: $approveSignatureProcess
          documentOwner: $documentOwner
          documentName: $documentName
          signatureData: $signatureData
          documentpageSize: $documentpageSize
          signatureCrossTenant: $signatureCrossTenant
        }
      ) {
        id
        associatedP
        associatedU
        createdOn
        signedOn
        owner
        ownerId
        archivedUsers
        accountLevelArchived
        associateSignatureProcess
        signatureByUsers {
          userId
          displayName
          firstName
          lastName
        }
        associateSignatureByUsers {
          userId
          displayName
        }
        isRead
        notifyOnSubmitSignatureRoles
        notifyOnSubmitSignatureUsers
        document {
          displayText
          pageCount
          associatePatient
          associatePatientFirstName
          associatePatientLastName
        }
        displayText {
          text
          document
        }
        type {
          id
          name
          obtainSignature
          allowRecipientRoles
          fileSavingFormatText
          allowPendingApproveSignature
          enableApplessWorkflow
          applessDevices
        }
        signatureStatus
        downloadUrl
        exchangeData
      }
    }
  `,
  createVideoRoom: gql`
    mutation CreateVidyoRoom($sessionToken: String!, $chatroomId: Int, $params: videoRoomDetails) {
      createVidyoRoom(sessionToken: $sessionToken, chatroomId: $chatroomId, params: $params) {
        host
        roomKey
        roomName
        roomPin
        roomEntity
      }
    }
  `,
  sendAppLessVideoRoom: gql`
    mutation SendApplessData($sessionToken: String!, $chatroomId: Int, $params: ApplessVideoInput) {
      sendApplessData(sessionToken: $sessionToken, chatroomId: $chatroomId, params: $params) {
        userid
        username
        chatroomId
        videoId
      }
    }
  `,
  deleteVideoRoom: gql`
    mutation DeleteVidyoRoom($sessionToken: String!, $chatroomId: Int) {
      deleteVidyoRoom(sessionToken: $sessionToken, chatroomId: $chatroomId) {
        status
        message
      }
    }
  `,
  updateUser: (isPatient: boolean) => gql`
    mutation updateUser(
      $sessionToken: String!
      $id: Int!
      ${!isPatient ? '$displayName: String' : ''}
      ${!isPatient ? '$firstName: String' : ''}
      ${!isPatient ? '$lastName: String' : ''}
      $mobile: String
      $countryCode: String
      $countryIsoCode: String
      $languages: String
      $enableAutoHideWebNotifications: Int
      $enableSmsNotifications: Int
      $enableEmailNotifications: Int
      $profileUpdate: Boolean
      $name: String
      $notificationSoundName: String
      $isCaregiver: Boolean
      $defaultSitesFilter: String
      $encodedUserSignature: String
      $useSavedSign: Boolean
       ${!isPatient ? '$outOfOfficeInfo: outOfOfficeInfo' : ''}
    ) {
      updateUser(
        sessionToken: $sessionToken
        id: $id
        params: {
          name: $name
          ${!isPatient ? 'displayName: $displayName' : ''}
          ${!isPatient ? 'firstName: $firstName' : ''}
          ${!isPatient ? 'lastName: $lastName' : ''}
          mobile: $mobile
          countryCode: $countryCode
          countryIsoCode: $countryIsoCode
          languages: $languages
          enableAutoHideWebNotifications: $enableAutoHideWebNotifications
          enableSmsNotifications: $enableSmsNotifications
          enableEmailNotifications: $enableEmailNotifications
          profileUpdate: $profileUpdate
          isCaregiver: $isCaregiver
          notificationSoundName: $notificationSoundName
          defaultSitesFilter: $defaultSitesFilter
          encodedUserSignature: $encodedUserSignature
          useSavedSign: $useSavedSign
          ${!isPatient ? 'outOfOfficeInfo: $outOfOfficeInfo' : ''}
        }
      ) {
        id
        updateStatus
        savedUserSignature
        __typename
      }
    }
  `,
  createDocumentActivity: gql`
    mutation createDocumentActivity(
      $sessionId: String!
      $activityName: DocumentActivityName!
      $documentId: ID!
      $sender: ID!
      $recipient: ID!
      $senderName: String!
      $recipientName: String!
      $recipientEmail: String
      $recipientPhoneNumber: String
      $recipientCountryCode: String
      $sentDocumentVia: SentDocumentMethod
      $accessCodeSentMedium: AccessCodeSentMedium
    ) {
      createDocumentActivity(
        sessionId: $sessionId
        params: {
          activityName: $activityName
          documentId: $documentId
          sender: $sender
          recipient: $recipient
          senderName: $senderName
          recipientName: $recipientName
          recipientEmail: $recipientEmail
          recipientPhoneNumber: $recipientPhoneNumber
          recipientCountryCode: $recipientCountryCode
          sentDocumentVia: $sentDocumentVia
          accessCodeSentMedium: $accessCodeSentMedium
        }
      ) {
        id
      }
    }
  `
};
