import gql from 'graphql-tag';

export const Queries = {
  getCategoriesById: gql`
    query getCategoriesById($id: Int) {
      category(where: { id: { _eq: $id } }) {
        id
        title
        subtitle
        feature_image_location
        image_file_location
      }
    }
  `,
  getSignatureRequestUnreadCount: gql`
    query signatureRequestUnreadCount($sessionId: String!) {
      signatureRequestUnreadCount(sessionId: $sessionId) {
        totalCount
      }
    }
  `,
  mySignatureRequestCount: gql`
    query mySignatureRequestCount($sessionId: String!, $startDate: String!, $endDate: String!) {
      mySignatureRequestCount(sessionId: $sessionId, startDate: $startDate, endDate: $endDate) {
        totalPendingCount
        totalSignedCount
        totalArchiveCount
      }
    }
  `,
  getExternalSystems: gql`
    query getSessionTenant($sessionToken: String!) {
      getSessionTenant(sessionToken: $sessionToken) {
        externalIntegrationSystems {
          accountId
          externalSystemId
          uniqueIdentifier
          name
          code
          createdOn
          createdBy
          filenameExpression
          showEditor
          showInInvite
          requiredInInvite
          labelShowInEditPage
        }
      }
    }
  `,
  getRoleBasedStaffs: gql`
    query getSessionTenant(
      $sessionToken: String!
      $status: Int!
      $type: Int
      $searchText: String
      $selectedBranchId: Int
      $roleId: Int!
      $siteIds: String
    ) {
      getSessionTenant(sessionToken: $sessionToken) {
        roleBasedStaffs(
          status: $status
          type: $type
          searchText: $searchText
          selectedBranchId: $selectedBranchId
          roleId: $roleId
          siteIds: $siteIds
        ) {
          id
          displayName
          role {
            roleId
            roleName
            __typename
          }
          assignRoles {
            tenantUsersRoleId
            isPrimary
            roleName
            __typename
          }
          rolefilter {
            roleId
            displayName
            __typename
          }
          naTags
          naTagNames
          __typename
        }
        __typename
      }
    }
  `,
  mySignatureRequest: (isMultiAdmissionsEnabled: boolean) => gql`
    query mySignatureRequest(
      $sessionId: String!
      $tenantId: Int
      $signatureRequestFilterInput: SignatureRequestFilterInput
      $paginationInput: PaginationInput
      $site_id: String
      $startDate: String
      $endDate: String
    ) {
      mySignatureRequest(
        sessionId: $sessionId
        tenantId: $tenantId
        filterParams: $signatureRequestFilterInput
        pageParams: $paginationInput
        site_id: $site_id
        startDate: $startDate
        endDate: $endDate
      ) {
        totalCount
        signatureRequest {
          id
          ${isMultiAdmissionsEnabled ? 'admissionId admissionName' : ''}
          ownerId
          downloadUrl
          senderTenant
          owner
          archivedUsers
          caregiverAssociatePatient
          integrationStatus
          accountLevelArchived
          associateSignatureProcess
          associateSignatureByUsers {
            userId
            displayName
          }
          confirmSaveSignToProfile
          signatureByUsers {
            userId
            displayName
          }
          isRead
          document {
            displayText
            pageCount
            associatePatient
            associatePatientDetails
            associateUser
          }
          tenantDetails {
            name
            senderTenant
            crossTenant
          }
          displayText {
            text
          }
          createdOn
          signedOn
          archivedOn
          type {
            id
            name
            obtainSignature
            allowRecipientRoles
            notifyOnSubmitSignatureRoles
            notifyOnSubmitSignatureUsers
            enableApplessWorkflow
            applessDevices
            patientDirectLink
            signedDocumentIntegeration
          }
          signatureStatus
          enableApplessWorkflow
          siteName
          siteId
        }
      }
    }
  `,
  mySignatureRequestById: (isMultiAdmissionsEnabled: boolean) => gql`
  query mySignatureRequest(
    $sessionId: String!
    $tenantId: Int
    $signatureRequestFilterInput: SignatureRequestFilterInput
    $site_id: String
    $documentId: Int
  ) {
    mySignatureRequest(
      sessionId: $sessionId
      tenantId: $tenantId
      filterParams: $signatureRequestFilterInput
      site_id: $site_id
      documentId: $documentId
    ) {
      totalCount
      signatureRequest {
        id
        ${isMultiAdmissionsEnabled ? 'admissionId admissionName' : ''}
        ownerId
        downloadUrl
        senderTenant
        owner
        archivedUsers
        caregiverAssociatePatient
        integrationStatus
        accountLevelArchived
        associateSignatureProcess
        associateSignatureByUsers {
          userId
          displayName
        }
        confirmSaveSignToProfile
        signatureByUsers {
          userId
          displayName
        }
        isRead
        document {
          displayText
          pageCount
          associatePatient
          associatePatientDetails
          associateUser
        }
        tenantDetails {
          name
          senderTenant
          crossTenant
        }
        displayText {
          text
        }
        createdOn
        signedOn
        archivedOn
        type {
          id
          name
          obtainSignature
          allowRecipientRoles
          notifyOnSubmitSignatureRoles
          notifyOnSubmitSignatureUsers
          enableApplessWorkflow
          applessDevices
          patientDirectLink
          signedDocumentIntegeration
        }
        signatureStatus
        enableApplessWorkflow
        siteName
        siteId
      }
    }
  }
`,
  signatureRequest: (isMultiAdmissionsEnabled: boolean) => gql`
    query signatureRequest($sessionId: String!, $id: Int!, $crossTenantId: Int) {
      signatureRequest(sessionId: $sessionId, id: $id, crossTenantId: $crossTenantId) {
        id
        senderTenant
        patientName
        ${isMultiAdmissionsEnabled ? 'admissionId' : ''}
        siteId
        confirmSaveSignToProfile
        signatureByUsers {
          userId
          displayName
          firstName
          lastName
        }
        displayText {
          text
          document
        }
        signatureStatus
        prepopulationData
        createdOn
        owner
        ownerId
        downloadUrl
        associateSignatureByUsers {
          userId
          displayName
        }
        associateSignatureProcess
        caregiverAssociatePatient
        type {
          id
          name
          toFilingCenter
          fromFilingCenter
          maxSignatoriesAllowed
          allowPendingApproveSignature
          allowPersonalSignature
          allowFileNameEditing
          fileNameFormatText
          allowRecipientRoles
          obtainSignature
          signatureByRoles
          roleRecipientNoficationMessageTmpl
          userRecipientNoficationMessageTmpl
          enableApplessWorkflow
          applessDevices
          sendDocumentWithoutSignature
          allowAssociateRoles
          showDelegationMessage
          editablePrePopulatedFields
          dateWithSign {
            sender
            recipient
            associate
            patient
          }
          checkBoxGroupAllowed
          checkBoxGroupAreMandatory
          checkboxImageBehavior
          documentpageSize
          patientInformationExchange
        }
        document {
          displayText
          pageCount
          associatePatient
          associatePatientFirstName
          associatePatientLastName
          signaturePalette {
            id
            paletteType
            checkboxLabel
            textFieldActAs
            textPlaceholder
            fieldorder
            pendingApproveUser
            fieldForUser
            signatureDocumentPageNumber
            signaturePaletteHeight
            signaturePaletteWidth
            horizontalSpaceLeft
            verticalSpaceTop
            signaturePaletteLock
            signature {
              signatureId
              signatureImage
            }
            associateUser
            checkBoxPaletteLabel
            mandatory
            group
            groupColor
            groupName
          }
        }
      }
    }
  `,
  signatureRequestTypesDisplay: gql`
    query signatureRequestTypesDisplay($sessionId: String!, $device: String) {
      signatureRequestTypesDisplay(sessionId: $sessionId, device: $device) {
        id
        name
        toFilingCenter
        fromFilingCenter
        maxSignatoriesAllowed
        allowPersonalSignature
        allowArchiveSignature
        allowFileNameEditing
        fileNameFormatText
        allowRecipientRoles
        nursingAgency
        obtainSignature
        patientAssociateRole
        signatureByRoles
        roleRecipientNoficationMessageTmpl
        userRecipientNoficationMessageTmpl
        notifyOnSubmitSignatureRoles
        notifyOnSubmitSignatureUsers
        allowAssociatePatient
        patientAssociateRole
        sendDocumentWithoutSignature
        allowAssociateRoles
        associateUserSignatureByRoles
        allowPendingApproveSignature
        checkBoxpaletteAllowed
        multipleCheckBoxAllowed
        checkBoxAreMandatory
        checkBoxGroupAllowed
        checkBoxGroupAreMandatory
        checkboxImageBehavior
        signaturepaletteAllowed
        sendDocumentWithoutSignature
        signatureAreMandatory
        textBoxpaletteAllowed
        textBoxAreMandatory
        textFieldMoreProperty
        dateWithSign {
          sender
          recipient
        }
        signatureImageInfo {
          orginalWIdth
          orginalHeight
          __typename
        }
        allowAssociatePatientAutomatically
        documentpageSize
        filingCenterFilenameFormt
        enableApplessWorkflow
        applessDevices
        patientDirectLink
        tagTypeId
        signaturepaletteAllowed
        signedDocumentIntegration
        fromCrud
        sendCompletedDocument
        signaturePalette {
          checkboxLabel
          mandatory
          pendingApproveUser
          samegroupOder
          checkBoxPaletteLabel
          associate
          fieldForUser
          fieldorder
          textFieldActAs
          textPlaceholder
          signature
          signatureDocumentPageNumber
          signaturePaletteWidth
          signaturePaletteHeight
          horizontalSpaceLeft
          verticalSpaceTop
          paletteType
          group
          groupName
          groupColor
          __typename
        }
        tagGuid
        allowDesignView
        __typename
      }
    }
  `,
  folderFilingCenterContent: gql`
    query getTenantFilingCenterContent($sessionId: String!, $type: SyncFolderType!, $folder: String!, $searchKey: String) {
      getTenantFilingCenterContent(sessionId: $sessionId, type: $type, folder: $folder, searchKey: $searchKey) {
        folder
        name
        type
        size
        preview
        addedOn
      }
    }
  `,
  documents: gql`
    query documents($limit: Int, $page: Int, $searchText: String, $sessionId: String, $siteIds: String, $tagId: String) {
      documents(sessionId: $sessionId, siteIds: $siteIds, tagId: $tagId, params: { limit: $limit, page: $page, searchText: $searchText }) {
        totalCount
        data {
          id
          folderName
          fileType
          fileSize
          createdOn
          documentPagesCount
          documentName
          documentOriginalName
          enableFile
          documentUniqueName
          folderId
          sites {
            id
            name
            __typename
          }
          __typename
        }
        __typename
      }
    }
  `,
  getDelegatedStaffRole: gql`
    query getSessionTenant($sessionToken: String!, $roleId: Int) {
      getSessionTenant(sessionToken: $sessionToken) {
        delegatedRoles(roleId: $roleId) {
          roleId
          delegatedRole {
            displayName
            citusRoleId
            id
          }
        }
      }
    }
  `,
  signatureRequestTextsDisplayRecipient: gql`
    query signatureRequestTextsDisplayRecipient(
      $sessionId: String!
      $roles: String!
      $virtual: Boolean
      $pageCountRecipient: Int
      $searchValue: String
      $siteId: String
    ) {
      signatureRequestTextsDisplayRecipient(
        sessionId: $sessionId
        roles: $roles
        virtual: $virtual
        pageCountRecipient: $pageCountRecipient
        searchValue: $searchValue
        site_id: $siteId
      ) {
        id
        userId
        displayName
        isVirtual
        alternateContacts {
          id
          roleId
          status
          displayName
          firstName
          lastName
          relation
          username
          ESIValue
          patientMrn
          patientId
          mobile
          countryCode
          password
          created_at
          modified_at
          cmisid
          patientId
          patientFirstName
          patientLastName
          patientStatus
          patientDob
          patientPassword
          patientDisplayName
          tenantId
          tenantName
          tenantRoleId
          __typename
        }
        userEMverification {
          mobileVerified
          emailVerified
        }
        firstName
        lastName
        dateOfBirth
        patientIdentity {
          IdentityValue
        }
        cmisId
        userName
        mobile
        countryCode
        role
        careGiver {
          id
          displayName
          userName
          firstName
          lastName
          mobile
          dob
          esi_code_patient
        }
      }
    }
  `,
  // type Recipients, OnBehalf, Associated
  getSignatureRequestTextDisplaySearch: (showVirtualOnly: boolean, list: boolean) => gql`
    query signatureRequestTextsDisplaySearch(
      $sessionId: String!
      $roles: String!
      $search: String!
      $site_id: String
      $admissionId: String
      $filterByPatientId: Int
    ) {
      signatureRequestTextsDisplaySearch(
        sessionId: $sessionId
        roles: $roles
        ${showVirtualOnly ? 'virtual: true' : ''}
        search: $search
        site_id: $site_id
        admissionId: $admissionId
        filterByPatientId: $filterByPatientId
      ) {
        id
        userId
        displayName
        firstName
        lastName
        dateOfBirth
        patientIdentity {
          IdentityValue
        }
        avatar
        cmisId
        role
        ${
          list
            ? `
        userName
        mobile
        countryCode
        careGiver {
          id
          displayName
          userName
          firstName
          lastName
          mobile
          dob
        }
          `
            : ''
        }
        isVirtual
        userEMverification {
          mobileVerified
          emailVerified
        }
        oooInfo {
          isOutOfOffice
          message
          startDateTime
          endDateTime
        }
        alternateContacts {
          id
          roleId
          status
          displayName
          firstName
          lastName
          relation
          username
          ESIValue
          patientMrn
          patientId
          mobile
          countryCode
          password
          created_at
          modified_at
          cmisid
          patientId
          patientFirstName
          patientLastName
          patientStatus
          patientDob
          patientPassword
          patientDisplayName
          tenantId
          tenantName
          tenantRoleId
        }
      }
    }
  `,
  signatureRequestTextsDisplay: gql`
    query SignatureRequestTextsDisplay($sessionId: String, $roles: String, $siteId: String) {
      signatureRequestTextsDisplay(sessionId: $sessionId, roles: $roles, site_id: $siteId) {
        id
        userId
        displayName
        cmisId
        userName
        mobile
        countryCode
        role
      }
    }
  `,
  getExternalSystemsByPatient: (isMultiAdmissionsEnabled = false) => gql`
    query getSessionTenant($sessionToken: String!, $id: Int, $admissionId: String) {
      getSessionTenant(sessionToken: $sessionToken) {
        externalSystems(id: $id, admissionId: $admissionId) {
          externalSystemId
          ${isMultiAdmissionsEnabled ? 'externalAdmissionId' : ''}
          uniqueIdentifier
          filenameExpression
          guId
        }
      }
    }
  `,
  getPatientExternalInfo: gql`
    query PatientExternalInfo(
      $sessionId: String
      $crossTenantId: Int
      $startRow: Int
      $endRow: Int
      $sorting: [SortModel]
      $rowGroups: [RowGroup]
      $filter: [FilterModel]
      $groupKeys: [String]
      $widgetField: [String]
      $accessSecurityType: String
      $accessSecurityIdentifierType: String
      $accessSecurityEsiValue: String
    ) {
      patientExternalInfo(
        sessionId: $sessionId
        crossTenantId: $crossTenantId
        startRow: $startRow
        endRow: $endRow
        sorting: $sorting
        rowGroups: $rowGroups
        filter: $filter
        groupKeys: $groupKeys
        widgetField: $widgetField
        accessSecurityType: $accessSecurityType
        accessSecurityIdentifierType: $accessSecurityIdentifierType
        accessSecurityEsiValue: $accessSecurityEsiValue
      ) {
        data {
          id
          patientFirstName
          patientLastName
          patientMRN
          patientEmail
          patientCellPhone
          patientPatStat
          patientGender
          patientDob
        }
        meta {
          firstItem
          lastItem
          hasMorePages
          count
          total
          perPage
        }
      }
    }
  `,
  getPatientDetails: gql`
    query getSessionTenant($sessionToken: String!, $patientUsersId: Int) {
      getSessionTenant(sessionToken: $sessionToken) {
        patientUsers(id: $patientUsersId) {
          dateOfBirth
          gender
          avatar
          displayName
          lastName
          firstName
          status
          id
          tenantId
          siteId
          enableSmsNotifications
          enableEmailNotifications
          userTags {
            id
            tagName
          }
          activeSince
          lastLogin
          password
          emails {
            value
            type
            primary
          }
          comments {
            comment
          }
          role {
            roleName
            id
            displayName
          }
          zip
          associatePatient {
            id
            firstName
            lastName
            displayName
            dateOfBirth
            zip
            status
          }
          associatePatients {
            id
            firstName
            lastName
            displayName
            dateOfBirth
            identityValue
            pName
            code
          }
          associateCaregivers {
            id
            firstName
            lastName
            displayName
            dateOfBirth
            identityValue
            pName
            code
            userName
            countryCode
            mobile
            registrationType
            source
            tenantId
            activeSince
            lastLogin
            status
            createdAt
            tenantName
          }
          mobile
          countryCode
          country
          state
          city
          address
          allAddresses {
            addrId
            addressType
            city
            state
            country
            address
            zipCode
            userstate {
              stateName
              postalCode
              stateId
            }
            usercountry {
              countryId
              countryName
              iso3
              iso3
            }
          }
          patientIdentity {
            externalSystemId
            patientId
            IdentityValue
            guId
          }
          naTags
          naTagNames
          userEMverification {
            mobileVerified
            emailVerified
          }
          alternateContacts {
            id
            firstName
            lastName
            status
            username
            alternateName
            relation
            ESIValue
            mobile
            countryCode
            isVirtual
            createdAt
            modifiedAt
            activeSince
          }
          externalShippingAddresses {
            patientId
            shippingAddressType
            addressName
            addressAttention
            address
            addressCity
            addressState
            addressZipCode
            addressPhone
            addressCreatedOn
            addressModifiedOn
            status
          }
          externalInfo {
            patientDob
            patientEmail
            patientAddress
            patientCity
            patientState
            patientZip
            patientAddress2
            patientCity2
            patientState2
            patientZip2
            patientCounty
            patientMRN
            patientWorkPhone
            patientCellPhone
            patientSite
            patientRefStartDate
            patientHomePhone
            patientStatus
          }
          externalDocuments {
            fileName
            docDescription
            docFileUrl
            createdAt
            modifiedAt
            docNotes
          }
          prescriptionOrders {
            fileName
            poDescription
            poFileUrl
            createdAt
            modifiedAt
            poNotes
          }
          patientAddress {
            id
            patientId
            category
            line1
            line2
            city
            district
            state
            country
            zipCode
            type
          }
          patientCustomFields {
            id
            patientId
            fieldName
            fieldValue
            fieldType
            isRequired
          }
        }
      }
    }
  `,
  getPatientDocuments: gql`
    query getSessionTenant(
      $sessionToken: String!
      $patientId: Int
      $docType: String
      $startRow: Int
      $endRow: Int
      $sorting: [SortModel]
      $filter: [FilterModel]
    ) {
      getSessionTenant(sessionToken: $sessionToken) {
        patientDocuments(patientId: $patientId, docType: $docType, startRow: $startRow, endRow: $endRow, sorting: $sorting, filter: $filter) {
          data {
            description
            createdAt
            modifiedAt
            docNotes
            docCategory
            fileName
            docFileUrl
            docSource
          }
          meta {
            firstItem
            lastItem
            hasMorePages
            count
            total
            perPage
          }
        }
      }
    }
  `,
  getAddressSearchData: gql`
    query Query(
      $sessionToken: String!
      $patientId: Int
      $tenantId: Int
      $startRow: Int
      $endRow: Int
      $sorting: [SortModel]
      $filter: [FilterModel]
      $admissionId: String!
    ) {
      getSessionTenant(sessionToken: $sessionToken) {
        patientExternalAddress(
          patientId: $patientId
          tenantId: $tenantId
          startRow: $startRow
          endRow: $endRow
          sorting: $sorting
          filter: $filter
          admissionId: $admissionId
        ) {
          data {
            id
            patientId
            category
            line1
            line2
            city
            district
            state
            country
            zipCode
            type
          }
          meta {
            firstItem
            lastItem
            hasMorePages
            count
            total
            perPage
          }
        }
      }
    }
  `,
  getSiteDetails: (includeAdmissionName: boolean) => gql`
    query Query($siteId: Int${includeAdmissionName ? ', $admissionId: String!' : ''}, $configs: [siteConfigs]) {
      getSiteDetails(siteId: $siteId${includeAdmissionName ? ', admissionId: $admissionId' : ''}, configs: $configs) {
        tenantId
        name
        status
        siteTimeZone
        logo
        startTime
        endTime
        Address
        workingDays
        modifiedBy
        createdAt
        createdBy
        modifiedAt
        data {
          userId
          displayname
        }
        configs {
          key
        }
        registrationId
        officeEmail
        contactEmail
        officePhone
        helplinePhone
        officePhoneCountryCode
        helplineCountryCode
      }
    }
  `,
  getSiteFCMappings: gql`
    query SiteFcMappings($siteRegistrationId: String, $docRefType: Int, $docRefTypeId: Int) {
      siteFcMappings(siteRegistrationId: $siteRegistrationId, docRefType: $docRefType, docRefTypeId: $docRefTypeId) {
        id
        tenantId
        siteRegistrationId
        refId
        refType
        folderPath
        folder
        filingCenterSiteConfig {
          fromFilingCenter
        }
      }
    }
  `,
  getSiteFCMappingsPatient: gql`
    query SiteFcMappings($siteRegistrationId: String, $docRefType: Int, $docRefTypeId: Int, $patient: Int) {
      siteFcMappings(siteRegistrationId: $siteRegistrationId, docRefType: $docRefType, docRefTypeId: $docRefTypeId, patient: $patient) {
        id
        tenantId
        siteRegistrationId
        refId
        refType
        folderPath
        folder
        filingCenterSiteConfig {
          fromFilingCenter
        }
      }
    }
  `,
  getSiteTenantFilingCenterContent: gql`
    query Query($sessionId: String!, $type: SyncFolderType!, $folder: String!, $guid: String, $searchKey: String, $fromCrud: Boolean) {
      getSiteTenantFilingCenterContent(sessionId: $sessionId, type: $type, folder: $folder, guid: $guid, searchKey: $searchKey, fromCrud: $fromCrud) {
        name
        folder
        size
        type
        addedOn
        fileId
        preview
      }
    }
  `,
  expireToken: gql`
    query expireToken($token: String!) {
      expireToken(token: $token) {
        status
      }
    }
  `,
  siteListByUserId: gql`
    query siteListByUserId($userId: Int, $crossTenantId: Int) {
      siteListByUserId(userId: $userId, crossTenantId: $crossTenantId) {
        data {
          id
          name
          __typename
        }
        __typename
      }
    }
  `,
  getDeleteUndoHistoryByMessageId: gql`
    query getDeleteUndoHistoryByMessageId($messageId: Int) {
      getDeleteUndoHistoryByMessageId(messageId: $messageId) {
        deleteUndoHistory {
          actionType
          actionTime
          actionMessage
        }
      }
    }
  `
};
