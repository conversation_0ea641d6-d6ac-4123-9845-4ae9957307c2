export const timezones = [
  { offset: '-600', city: 'Pacific/Honolulu', name: 'Hawaii' },
  { offset: '-540.1', city: 'America/Anchorage', name: 'Alaska' },
  { offset: '-480', city: 'America/Los_Angeles', name: 'Pacific Time' },
  { offset: '-420', city: 'America/Phoenix', name: 'Mountain Time/Phoenix (US)/Fort St John (Canada)' },
  { offset: '-420.1', city: 'America/Denver', name: 'Mountain Time' },
  { offset: '-360', city: 'America/Regina', name: 'Central Time/Regina (Canada)' },
  { offset: '-360.1', city: 'America/Chicago', name: 'Central Time' },
  { offset: '-300', city: 'America/Jamaica', name: 'Eastern Time/Coral Harbour (Canada)/.panama' },
  { offset: '-300.1', city: 'America/New_York', name: 'Eastern Time' },
  { offset: '-240', city: 'America/Halifax', name: 'Atlantic Time' },
  { offset: '-240.1', city: 'America/Blanc-Sablon', name: 'Atlantic Time/Blanc-Sablon (Canada)' },
  { offset: '-210', city: 'America/St_Johns', name: 'Newfoundland' },
  { offset: '0', city: 'UTC', name: 'Coordinated Universal Time' },
  { offset: '330', city: 'Asia/Kolkata', name: 'Indian Standard Time' },
  { offset: '600', city: 'Australia/Sydney', name: 'Australian Eastern Standard Time (AEST)' },
  { offset: '700', city: 'Australia/Melbourne', name: 'Australian Eastern Daylight Time (AEDT)' }
];
