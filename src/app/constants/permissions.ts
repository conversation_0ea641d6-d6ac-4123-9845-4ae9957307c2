import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
// Permissions mapping added here
export class Permissions {
  static activateVideoChat = 'activateVideoChat';
  static addVirtualEnrolledStaffUsers = 'addVirtualEnrolledStaffUsers';
  static addVirtualPartnerUsers = 'addVirtualPartnerUsers';
  static addVirtualPatientUsers = 'addVirtualPatientUsers';
  static allowAddUser = 'allowAddUser';
  static allowArchiveDocuments = 'allowArchiveDocuments';
  static readonly cancelSignatureRequest = 'cancelSignatureRequest';
  static readonly cancelForms = 'cancelForms';
  static allowArchiveForms = 'allowArchiveForms';
  static allowDelegation = 'allowDelegation';
  static allowEnrollmentInitiation = 'allowEnrollmentInitiation';
  static allowMultipleOrganizationsPatientCommunication = 'allowMultipleOrganizationsPatientCommunication';
  static allowMultipleOrganizationsStaffCommunication = 'allowMultipleOrganizationsStaffCommunication';
  static allowOrganizationSwitching = 'allowOrganizationSwitching';
  static allowPartnerEnrollment = 'allowPartnerEnrollment';
  static allowPartnerPatientReferralInitiation = 'allowPartnerPatientReferralInitiation';
  static allowPatientEnrollment = 'allowPatientEnrollment';
  static allowRoleTenantSwitching = 'allowRoleTenantSwitching';
  static allowScheduleForms = 'allowScheduleForms';
  static allowStaffEnrollment = 'allowStaffEnrollment';
  static archiveChatMessagesForAllUser = 'ArchiveChatMessagesForAllUser';
  static autoMessageEscalation = 'autoMessageEscalation';
  static chatLogAddComment = 'chatLogAddComment';
  static chatLogAddCommentSignedMessage = 'chatLogAddCommentSignedMessage';
  static chatLogAddSignedTag = 'chatLogAddSignedTag';
  static chatLogAddTag = 'chatLogAddTag';
  static chatLogDeleteSignedTag = 'chatLogDeleteSignedTag';
  static chatLogDeleteTag = 'chatLogDeleteTag';
  static chatWithClinician = 'chatWithClinician';
  static chatWithMessageGroups = 'chatWithMessageGroups';
  static chatWithPatients = 'chatWithPatients';
  static clinicianApprover = 'clinicianApprover';
  static crossTenantCommunication = 'crossTenantCommunication';
  static enableEducationTraining = 'enableEducationTraining';
  static enableMoveForm = 'enableMoveForm';
  static enablePatientActivityHub = 'enablePatientActivityHub';
  static fillStructuredForms = 'FillStructuredForms';
  static formsLibrarymanager = 'formsLibrarymanager';
  static invitePatientsToChatroom = 'invitePatientsToChatroom';
  static inviteToChat = 'inviteToChat';
  static manageAllMessageGroup = 'manageAllMessageGroup';
  static manageChatLogs = 'manageChatLogs';
  static manageChatRoomParticipants = 'manageChatRoomParticipants';
  static manageDocumentTemplates = 'ManageDocumentTemplates';
  static manageExternalIntegration = 'manageExternalIntegration';
  static manageMessageGroup = 'manageMessageGroup';
  static manageTenants = 'manageTenants';
  static manageUserEnrollment = 'manageUserEnrollment';
  static manageWorklistSettings = 'manageWorklistSettings';
  static messageForwarding = 'messageForwarding';
  static patientApprover = 'patientApprover';
  static patientSelectioninMessageTagging = 'PatientSelectioninMessageTagging';
  static patientSurveys = 'patientSurveys';
  static requestSignature = 'requestSignature';
  static reviewSubmittedForm = 'reviewSubmittedForm';
  static sendBroadcaseMessage = 'sendBroadcaseMessage';
  static sendMaskedMessage = 'sendMaskedMessage';
  static showArchive = 'showArchive';
  static showDashboard = 'showDashboard';
  static showManageAlert = 'showManageAlert';
  static signMessage = 'SignMessage';
  static tagMessage = 'TagMessage';
  static viewAllSignedDocs = 'viewAllSignedDocs';
  static viewFormEntries = 'viewFormEntries';
  static viewInventory = 'viewInventory';
  static messageEscalation = 'messageEscalation';
  static messageReminder = 'messageReminder';
  static allowStaffToScheduleForThemselves = 'allowStaffToScheduleForThemselves';
  static manageVisitSchedule = 'manageVisitSchedule';
  static manageStaffAvailability = 'manageStaffAvailability';
  static allVisitSchedule = 'allVisitSchedule';
  static cancelCompletedArchivedForms = 'cancelCompletedArchivedForms';
  static viewAllVisitSchedule = 'viewAllVisitSchedule';
  static allowRolesToSchedule = 'allowRolesToSchedule';
  static allowEditCompletedVisits = 'allowEditCompletedVisits';
}
// Permission check with route params type.
export const permissionWithParamsType = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot): any => {
  if (route.params.type === 'broadcast') {
    return [Permissions.sendBroadcaseMessage];
  } else if (route.params.type === 'masked') {
    return Permissions.sendMaskedMessage;
  } else if (route.params.type === 'archived') {
    return Permissions.showArchive;
  }
};
