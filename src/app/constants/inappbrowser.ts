import { environment } from 'src/environments/environment';
import { theme } from 'src/theme/theme';
import { getComputedStyleProperty } from '../utils/utils';
import { Constants } from './constants';

export class InAppBrowserData {
  refPath = theme.alias === 'citushealth' ? '' : `themes/${theme.alias}/`;
  headerBottomBg = `${environment.apiServer}/webapp/www/${this.refPath}img/header-bottom-bg.png`;
  headerColor = getComputedStyleProperty('--ion-color-form-header');
  submitColor = getComputedStyleProperty('--ion-color-form-submit');
  draftColor = getComputedStyleProperty('--ion-color-form-draft');
  backButtonColor = getComputedStyleProperty('--ion-color-form-back-button');
  logoutColor = getComputedStyleProperty('--ion-color-socket-bg-disconnect');
  continueSessionColor = getComputedStyleProperty('--ion-color-de-york');
  getSessionClear = "localStorage.getItem('sessionClear');";
  getForceLogout = "localStorage.getItem('forceLogout');";
  getClearForm = "localStorage.getItem('clearForm');";
  setClearForm = "localStorage.setItem('clearForm', 'true');";
  resetClearForm = "localStorage.setItem('clearForm', '');";
  setSessionClear =
    "localStorage.setItem('sessionClear', 'false');localStorage.setItem('isSessionTimeOutWarningShowing', 'false');setTimeout(function(){ document.getElementById('popup-div').style.display = 'none';document.getElementById('backdrop').style.display = 'none';}, 100);";
  getCloseButton = "localStorage.getItem('closeButton');";
  setCloseButton = "localStorage.setItem('closeButton', '');";
  getVideoCallButton = "localStorage.getItem('videobtn');";
  setVideoCallButton = "localStorage.setItem('videobtn', '');";
  setSaveAsDraftOnSessionTimeOut = "localStorage.setItem('saveAsDraftOnSessionTimeOut', '0');";
  resetSaveAsDraftOnSessionTimeOut = "localStorage.setItem('saveAsDraftOnSessionTimeOut', '');";
  getSaveAsDraftOnSessionTimeOut = "localStorage.getItem('saveAsDraftOnSessionTimeOut');";
  setSavAsDraftButtonValue = "$('#save_as_draft_submit_form').css({opacity:'1 !important'}).val('Save As Draft');";
  triggerSaveAsDraft = "document.getElementById('save_as_draft_submit_form').click();";
  videoCallPopCustomStyle = `${environment.apiServer}/webapp/www/css/video-style.css`;
  inAppBrowserExec({
    exitConfirm = true,
    clearButtonHide = false,
    pageHeading = '',
    isFormViewFrame = true,
    message = '',
    isAndroid,
    labels = {}
  }: {
    exitConfirm?: boolean;
    clearButtonHide?: boolean;
    pageHeading?: string;
    isFormViewFrame?: boolean;
    message?: string;
    isAndroid: boolean;
    labels: Record<string, string>;
  }) {
    return {
      cssData: `.form-header{background: ${
        this.headerColor
      };border: none !important;position: fixed;height: 50px;z-index: 9999;left:0;top:0;${
        !exitConfirm ? 'top:0;' : ''
      }width: 100%;text-align:center;}${
        theme.alias === 'matrixcare'
          ? ''
          : `.form-header:after {position: absolute;width: 100%;height: 30px;left: 0px;top: 46px;content: '';background: url(${this.headerBottomBg}) no-repeat top left !important;background-size: 100% 30px !important;}`
      }
      .form_description{display:none !important;}
      .form_description h2 {text-align: center;}
      div.back-div {float: left;padding: 17px;position: absolute;font-size: 17px !important;font-weight: 500;}
      label.back-label{margin-left: 2px;color: ${this.backButtonColor};}
      .message-bar {margin-top: ${!clearButtonHide ? '88px' : '133px'};width: 100%;color: #000000;font-size: 20px;}
      .message-bar .font-weight-bold {font-size: 16px !important;}
      .popup-div{padding: 27px 6px 10px 6px; display: none; color: #555555; -webkit-box-shadow: 0px 0px 15px 5px rgba(0,0,0,0.2); -moz-box-shadow: 0px 0px 15px 5px rgba(0,0,0,0.2); box-shadow: 0px 0px 15px 5px rgba(0,0,0,0.2); background: #ffffff; background: -o-linear-gradient(#ffffff, #ffffff); background: -moz-linear-gradient(#ffffff, #ffffff); background: linear-gradient(#ffffff, #ffffff); line-height: 23px; border-radius: 3px !important; font-size: 17px; height: auto; width: 75%; position: fixed; top: 50%; left: 50%; transform: translateX(-50%) translateY(-50%); }
      .popup-buttons-div { text-align:center;display: none; align-items: center; flex-direction: row; justify-content: space-evenly; margin: 10px;}
      .popup-text-div { text-align:center;font-size: 16px;}
      .logout-session {color:${this.logoutColor};margin:5px;font-size: 17px !important;}
      .continue-session { color:${this.continueSessionColor};font-size: 17px !important;}
      .backdrop { position: fixed; top: 0px; left: 0px; z-index: 999; height: 100%; width: 100%; background: rgba(0, 0, 0, 0.2); display: none; }
      .swal2-center{padding-top:100px;}
      .manage-role-btn{background: ${
        this.submitColor
      };border-radius: 20px;color: #ffffff;float: right;padding: 5px 8px;margin-left: 0px;margin-top: 0px;border:0px}
      #video-call-notify-cnt{display:none;}
      #main_body .button_text {background:${this.submitColor} !important }
      #main_body #save_as_draft_submit_form {background:${this.draftColor} !important }
      .form-header .font-weight-bold {color: #fff; position: absolute; left: 60px; right: 60px; top: 15px; font-weight: 500; font-size: 17px !important; white-space: nowrap; text-overflow: ellipsis; overflow: hidden; text-transform: capitalize;}
      ${
        isFormViewFrame
          ? '.read-only-form{ pointer-events: none;};.button_text{opacity: 0.3 !important;};'
          : '.clear-form .swal-footer{text-align :center !important}'
      }
      ${exitConfirm ? '.close-form .swal-footer{text-align :center !important}' : ''} `,
      executeScript: `(function () {localStorage.setItem('closeButton', '');localStorage.setItem('forceLogout','false');var body = document.querySelector('body');var headerDiv = document.createElement('div');headerDiv.classList.add('form-header');body.prepend(headerDiv);
      var backDiv = document.createElement('div');backDiv.classList.add('back-div');headerDiv.append(backDiv);var backlabel = document.createElement('label');backlabel.innerHTML = 'Back';backlabel.classList.add('back-label');backDiv.append(backlabel);
      var headeSpan = document.createElement('span');headeSpan.innerHTML = "${pageHeading}";headeSpan.classList.add('font-weight-bold');headerDiv.append(headeSpan);
      ${isFormViewFrame ? ` $('form').addClass('read-only-form');` : ''}
      var messageDiv;if(document.getElementsByClassName('message-bar').length){var elements = document.getElementsByClassName('message-bar');while(elements.length > 0){elements[0].parentNode.removeChild(elements[0]);}}messageDiv = document.createElement('div');messageDiv.classList.add('message-bar');headerDiv.after(messageDiv);var messageSpan = document.createElement('span');messageSpan.innerHTML = "${message}";messageSpan.classList.add('font-weight-bold');messageDiv.append(messageSpan); 
      var messageDivSaveAsDraft = document.createElement('div');messageDivSaveAsDraft.classList.add('message-save-as-draft');messageDiv.append(messageDivSaveAsDraft); 
      ${
        !isFormViewFrame
          ? " var clearButton = document.createElement('button');clearButton.innerHTML = 'Clear Form';clearButton.classList.add('manage-role-btn');messageDiv.append(clearButton);clearButton.addEventListener ('click', function(evt) {Swal.fire({title: 'Are you sure?',text: 'You are going to clear this Form',icon: 'warning',showCancelButton: true,confirmButtonText: 'Ok',cancelButtonText: 'Cancel',customClass: {popup: 'clear-form',confirmButton: 'swal-button--confirm',cancelButton: 'swal-button--cancel swal-send-submit-cancel-btn'}}).then(function(result) {if (result.isConfirmed) { localStorage.setItem('clearForm', 'true'); } else {evt.preventDefault();}});$('.swal2-actions').css('text-align', 'center !important');setTimeout(function(){$('.swal-button--confirm').css({'background-color': '#f39834', 'color': 'white', 'order': '2'});$('.swal-button--cancel').css({'order': '1'});}, 0);}); "
          : ''
      }
      ${
        !exitConfirm
          ? `localStorage.setItem('closeButton', ' ');$('.back-div').click(function(evt){localStorage.setItem('closeButton', 'close');});`
          : `localStorage.setItem('closeButton', ' ');$('.back-div').click(function(evt){Swal.fire({title: 'Are you sure?',text: 'Going back will exit this form. Are you sure you want to leave your form?',icon: 'warning',showCancelButton: true,confirmButtonText: 'Ok',cancelButtonText: 'Cancel',customClass: {popup: 'close-form',confirmButton: 'swal-button--confirm',cancelButton: 'swal-button--cancel swal-send-submit-cancel-btn'}}).then(function(result){ if (result.isConfirmed){ ${
              isAndroid ? `if(typeof Android !== 'undefined' && Android !== null) {Android.customBackButton();	};` : ''
            }localStorage.setItem('closeButton', 'close');}else{ evt.preventDefault(); return false;}});$('.swal2-actions').css('text-align', 'center !important');setTimeout(function(){$('.swal-button--confirm').css({'background-color': '#f39834', 'color': 'white', 'box-shadow':'unset', 'order': '2'});$('.swal-button--cancel').css({'order': '1'});}, 0);});`
      }
      var backdrop = document.createElement('div');backdrop.id='backdrop';backdrop.classList.add('backdrop');body.append(backdrop);
      var popupDiv = document.createElement('div');popupDiv.id='popup-div';popupDiv.classList.add('popup-div');backdrop.append(popupDiv);
      var popupTextDiv = document.createElement('div');popupTextDiv.id='popup-text-div';popupTextDiv.classList.add('popup-text-div');popupDiv.append(popupTextDiv);
      var popupButtonsDiv = document.createElement('div');popupButtonsDiv.id='popup-buttons-div';popupButtonsDiv.classList.add('popup-buttons-div');popupDiv.append(popupButtonsDiv); 
      var logoutSessionButton= document.createElement('label');logoutSessionButton.textContent='${
        labels.logoutLabel
      }';logoutSessionButton.id='logout-session';logoutSessionButton.className='logout-session';popupButtonsDiv.append(logoutSessionButton);
      var continueSessionButton= document.createElement('label');continueSessionButton.textContent='${
        labels.continueSessionLabel
      }';continueSessionButton.id='continue-session';continueSessionButton.className='continue-session';popupButtonsDiv.append(continueSessionButton);
      $('#logout-session').click(function(){localStorage.setItem('forceLogout','true');});
      $('#continue-session').click(function(){localStorage.setItem('sessionClear','true');});
      $('body').on({click: function() {hidesession();},keypress: function() {hidesession();}});localStorage.getItem('sessionClear', 'false');localStorage.setItem('isSessionTimeOutWarningShowing', 'false');function hidesession(){if(localStorage.getItem('isSessionTimeOutWarningShowing')!='true'){localStorage.setItem('sessionClear', 'true');document.getElementById('popup-div').style.display = 'none';document.getElementById('backdrop').style.display = 'none';}}
      $('#save_as_draft_submit_form').click(function(){ var timelocal=new Date().toLocaleTimeString(); $('.saveasdrafttime').val(timelocal); $('#save_as_draft_submit_form').val('Saving..').css({opacity: '0.5 !important'});});
      ${isFormViewFrame ? ` $('#form_container').contents().find('.button_text').attr('disabled', true);` : ''}
      $('#save_as_draft_submit_form').click(function(){ var timelocal=new Date().toLocaleTimeString(); $('.saveasdrafttime').val(timelocal); $('#save_as_draft_submit_form').val('Saving...').css({opacity: '0.5 !important'});});
      localStorage.setItem('videobtn', '');var videoPopupDiv = document.createElement('div'); videoPopupDiv.id='video-call-notify-cnt'; body.append(videoPopupDiv);
      var videopopupContainer = document.createElement('div');videopopupContainer.id='video-call-notify';videopopupContainer.classList.add('video_call_notify');videoPopupDiv.append(videopopupContainer);
      var videoPopupMsg= document.createElement('div'); videoPopupMsg.id='video-notify-cntnt';videoPopupMsg.classList.add('video_notify_cntnt');videopopupContainer.append(videoPopupMsg);
      var videoPopupbtns= document.createElement('div');videoPopupbtns.classList.add('video_notify_btns');videopopupContainer.append(videoPopupbtns);
      var videoPopupbtnYes= document.createElement('BUTTON');videoPopupbtnYes.textContent='Accept';videoPopupbtnYes.id='acceptCall';videoPopupbtnYes.className='vid-btn vid-btn-success';videoPopupbtns.append(videoPopupbtnYes);
      var videoPopupbtnNo= document.createElement('BUTTON');videoPopupbtnNo.textContent='Reject';videoPopupbtnNo.id='rejectCall';videoPopupbtnNo.className='vid-btn vid-btn-danger';videoPopupbtns.append(videoPopupbtnNo);
      $('#acceptCall').click(function(){localStorage.setItem('videobtn', 'acceptCall');clearVideoPart();});$('#rejectCall').click(function(){localStorage.setItem('videobtn', 'rejectCall');clearVideoPart();});function clearVideoPart(){ document.getElementById('video-call-notify-cnt').style.display = 'none';document.getElementById('video-call-notify').style.display ='none'; document.getElementById('video-notify-cntnt').innerHTML ='';}
      })();`
    };
  }
  /**
   * addonScript used to fetch script for already running inapp browser session from different areas.
   * @returns string
   */
  addonScript({ message, from }: { message: string; from: string }): string {
    let script = message || '';
    if (Object.keys(Constants.inAppAddOnFromPath).includes(from)) {
      if (script != '') {
        /* displays popup-div */
        script = script.replace(/`/g, '&#96');
        script = `var innerData = document.getElementById('popup-text-div');innerData.textContent = '${script}';document.getElementById('popup-buttons-div').style.display = 'none';document.getElementById('backdrop').style.display = 'block';document.getElementById('popup-div').style.display = 'block';`;
      }
      if (from === Constants.inAppAddOnFromPath.session) {
        script = `${script}document.getElementById('popup-buttons-div').style.display = 'flex';localStorage.setItem('isSessionTimeOutWarningShowing', 'true');`;
      }
      if (from === Constants.inAppAddOnFromPath.draft) {
        /* Set button value and autohide popup. */
        /* TODO saveAsDraft socket event enable button display scenarios. */
        /* TODO 'Save As Draft' move to en.json when implementing socket event scenarios */
        script = `${script}setTimeout(function(){ document.getElementById('popup-div').style.display = 'none';document.getElementById('backdrop').style.display = 'none';}, 3000);$('#save_as_draft_submit_form').css({opacity:'1 !important'}).val('Save As Draft');`;
      }
      if (from === Constants.inAppAddOnFromPath.toast) {
        /* Autohide toast. */
        /* TODO Usage of toast. */
        script = `${script}setTimeout(function(){ document.getElementById('popup-div').style.display = 'none';document.getElementById('backdrop').style.display = 'none';}, 4000);`;
      }
      if (from === Constants.inAppAddOnFromPath.videoCall) {
        /* Display videocall popup */
        /* TODO Usage of videocall popup */
        script = `var videoinnerData = document.getElementById('video-call-notify-cnt');videoinnerData.style.display = 'block'; var videoContainer = document.getElementById('video-call-notify');videoContainer.style.display = 'block';var messageData = document.getElementById('video-notify-cntnt');messageData.innerHTML = '${message}';`;
      }
    }
    return script;
  }
}
